{"HOME": {"TITLE": "Hello Angular with ngx-translate!", "SELECT": "Change language"}, "TELAINICIO": {"TITULO": "Home", "BEMVINDO": "Welcome to Medicine For You.", "BEMVINDO2": "Welcome to", "TITULOVIDEO": "First Steps", "SUBTITULOVIDEO": "Watch the video and learn how to use Medicine for you.", "BOTAODESABILITARVIDEO": "Disable Tutorial", "BOTAOPROXIMOPASSOVIDEO": "Next Step", "CONSULTAS": "Consultations", "AGENDAMENTO": "Scheduling", "LIFELINE": "Life Line", "HISTORICO": "Historic", "CONSULTA": "Consultation", "DIAAGENDAMENTO": "Day Scheduling:", "OBSERVACAOPARAACONSULTA": "Note for the consultation:", "DIA": "Day:", "PACIENTE": "Patient:", "TIPOAGENDAMENTO": "Schedule Type:", "ESPECIALIDADE": "Specialty:", "CONVERSA": "Conversation", "ANOTACOESANONIMAS": "Anonymous Annotations", "ANEXO": "Attachment", "USUARIO": "User", "DATA": "Date", "MEDICO": "Doctor:"}, "TELAPERFIL": {"TITULO": "Profile", "NOME": "Name", "EMAIL": "Email", "TELEFONE": "Telephone", "CELULAR": "Cell phone", "TELEFONECOMERCIAL": "Commercial phone", "SENHAATUAL": "Current password", "ALTERARSENHA": "Change Password", "SALVAR": "Save", "EMAILINVALIDO": "Invalid email", "PRECISASERPREENCHIDO": "This field must be completed.", "TELEFONEINVALIDO": "Invalid Phone", "FOTODEPERFIL": "Profile picture", "GIRARAESQUERDA": "Turn left", "GIRARADIREITA": "Turn right", "VIRARHORIZONTALMENTE": "<PERSON><PERSON>", "VIRARVERTIVALEMENTE": "Flip vertically", "CORTAR": "Cut", "ERROCAMPO": "This field must be completed.", "ERRONAOEVALIDO": "It is not valid", "ERRONAOEVALIDA": "Not valid", "ERROCAMPO8CARACTERES": "This field must be longer than 8 characters", "SALVOCOMSUCESSO": "Profile Saved. ✔", "SENHAINCORRETA": "Incorrect Password", "NOVASENHAMUITOCURTA": "Nova senha muito curta", "EMAILJARESGISTRADONOSISTEMA": "Email already registered in the System!", "TELEFONEJAREGISTRADONOSISTEMA": "Phone already registered in the System!", "PERFILSALVOCOMSUCESSO": "Profile successfully saved. ✔", "ERROSALVARPERFIL": "Error saving profile! Check the fields."}, "TELACADASTROMEDICO": {"SHOWMESSAGESUCCESS": "Record successfully saved!", "TITULO": "Medical Registration", "VOLTAR": "Back", "DESATIVAR": "Deactivate", "ACCOUNT": "Personal data", "NOME": "Name", "EMAIL": "Email", "DATADENASCIMENTO": "Date of birth", "ERRODATA": "Invalid Date", "ERROCAMPO": "This field must be completed.", "TELEFONEINVALIDO": "Invalid Phone", "ERRONAOEVALIDO": "It is not valid", "SEXO": "Gender", "CPF": "cpf", "TELEFONE": "Telephone", "ERROTELEFONE": "Invalid Phone", "CELUAR": "Cell phone", "TELEFONECOMERCIAL": "Commercial phone", "WORK": "Professional Data", "CRM": "CRM", "ESPECIALIDADES": "Specialties", "ERROESPECIALIDADES": "A specialty must be selected", "CLINICAS": "Corresponding Clinics", "ERROCLINICAS": "A clinic needs to be selected", "ENDERECO": "Address", "RUA": "Street Address", "NUMERO": "n°", "COMPLEMENTO": "Complement", "UF": "UF", "MUNICIPIO": "City", "BAIRRO": "Neighborhood", "CEP": "Zip code", "DIASPROGRAMACAO": "Schedule Days", "SEGUNDA": "Monday", "TERCA": "Tuesday", "QUARTA": "Wednesday", "QUINTA": "Thursday", "SEXTA": "Friday", "SABADO": "Saturday", "DOMINGO": "Sunday", "HABILITARDESABILITAR": "Enable/Disable", "INICIO": "Outset", "INICIOINTERVALO": "start of break", "FIMINTERVALO": "End of break", "Fim": "End", "TEMPOPORCONSULTA": "Time Per Consultation", "LIMPAR": "Clean", "SALVAR": "Save", "CLIENTEJAREGISTRADO": "Customer already registered in the system, check if the clinic is registered for him", "EMAILJAREGISTRADO": "Email already registered in the System!", "TELEFONEJAREGISTRADO": "Phone already registered in the System!", "PRECISATERUMHORARIOVALIDO": "This field must have a valid time", "CADASTROSALVOCOMSUCESSO": "Registration Saved Successfully. ✔", "ERROAOSALVAR": "Error saving!", "EMAILJAREGISTRADOEMOUTROUSUARIO": "Email already registered in the system in another user!", "TELEFONEJAREGISTRADOEMOUTROUSUARIO": "Phone already registered in the system in another user!", "FOTODEPERFIL": "Profile picture", "FOTO": "Photograph", "UFNAOENCONTRADA": "UF not found.", "GIRARAESQUERDA": "Turn left", "GIRARADIREITA": "Turn right", "VIRARHORIZONTALMENTE": "<PERSON><PERSON>", "VIRARVERTIVALEMENTE": "Flip vertically", "CORTAR": "Cut", "MASCULINO": "Male", "FEMININO": "Female", "VALORCONSULTA": "Consultation Price", "CODIGOCONVENIO": "Agreement Code", "RETIDO": "Withheld"}, "TELAPESQUISAMEDICO": {"TITULO": "Doctor", "BUSCAR": "Search", "ADICIONAR": "Add Doctor", "MOSTRARFOTO": "Show Photo", "INATIVOS": "Inactive", "LEGENDA": "Subtitle", "LIFELINE": "Life line", "ARIALIFELINE": "Life line", "CRIARAGENDAMENTO": "Create Schedule", "ARIAEMAILENVIADO": "Patient's Personal Record", "EMAILENVIADO": "Email sent", "EMAILBOASVINDAS": "Welcome Email", "EDITAR": "Edit", "EXCLUIR": "Delete", "ATIVAR": "Enable", "NOME": "Name", "DADOS": "Data", "ACOES": "Actions", "CRM": "CRM: ", "DATADECADASTRO": "Registration date: ", "PROXIMACONSULTA": "Next consultation: ", "ULTIMACONSULTA": "Last consultation: ", "ARIAAGENDAMENTOSDOPACIENTE": "Patient Schedule", "ARIACADASTROPESSOALDOPACIENTE": "Patient's Personal Record", "ARIAATUALIZARCADASTRO": "Update Registration", "ARIAEXCLUIRCADASTRO": "Delete Registration", "ARIAATIVARCADASTRO": "Enable Registration", "TITULOB": "Doctor", "AGENDAMENTOS": "Schedulings", "EMAIL": "Email", "ARIAENCIODEEMAIL": "Email Submission", "ARIAEDITARLINHASELECIONADA": "Edit selected line", "DELETAR": "Delete", "ARIADELETARLINHASELECIONADA": "Delete selected line", "DESATIVAR": "Deactivate", "CARREGARMAIS": "Load More", "EXCLUIRESTECADASTRO": " Are you sure you want to delete this user ? ", "EXCLUIDOPARMANENTEMENTE": "User will be permanently deleted", "NAO": " NO ", "SIM": " YES ", "ENVIARESTEEMAIL": " Are you sure you want to send this email? ", "EMAILCOMASENHA": "User will receive email with password", "PACIENTES": "Patients", "FILTRARRESULTADOS": " Filter Results", "DATADOCADASTRO": "Registration date", "FILTRAR": "Filter", "LIFELINEPACIENTE": "Life-Line Patient", "NENHUMREGISTROENCONTRATO": " No records found!", "CONVERSA": "Conversation", "ANOTACOESANONIMAS": "Anonymous Annotations", "ANEXO": "Attachment", "USUARIO": "User", "DATA": "Date", "DOWNLOADO": "Download", "ATIVARUSUARIO": " Are you sure you want to enable this user? ", "OUSUARIOTERAACESSOAOSISTEMA": "The User will have access to the system!", "DESEJAIRPARAAAGENDADESTEMEDICO": " Do you want to go to this Doctor's Agenda?", "AGENDA": "Schedule", "SAIR": "Exit", "USUARIOATIVOCOMSUCESSO": "Successful Active User", "ERROAOATIVAROUSUARIO": "Error Activating User!", "ERROAOINATIVAR": "Inactivate Error", "USUARIOEXCLUIDOCOMSUCESSO": "Successfully deleted user.  ✔ ", "EXCLUIDOCOMSUCESSO": "Successfully deleted. ✔", "EMAILENVIADOV": "Email sent. ✔", "EMAILENVIADOCOMSUCESSOV": "<PERSON><PERSON> successfully sent.  ✔", "ERROAOCARREGAR": "Error Loading!", "CADASTROESTAINATIVO": "WARNING THIS REGISTRATION IS INACTIVE", "ATIVANOVAMENTE": "Sending this email will activate it again!"}, "TELALOGIN": {"SELECIONEAFORMADEEDENCITICACAO": "Select form of identification", "CPF": "cpf", "CELULAR": "Cell phone", "EMAIL": "Email", "SENHA": "Password", "LOGININVALIDO": "Invalid login", "ENTRAR": "Log in", "ESQUECEUSUASENHA": "Forgot your password ?", "DESENVOLVIDOPOR": "Developed by", "SITESEGURO": "Medicina Para Você is a safe site"}, "TELALOGINRECUPERARSENHA": {"CPF": "cpf", "EMAIL": "Email", "EMAILOUCPFINVALIDO": "Invalid Email or cpf", "ENVIAR": "Submit", "VOLTAR": "Back", "EMAILINVALIDOCOMUMANOVASENHA": "Email sent with a new password.  ✔"}, "TELACADASTROUSUARIO": {"VOLTAR": "Back", "CADASTROATENDENTE": "Employee Registration", "DESATIVAR": "Deactivate", "DADOSPESSOAIS": "Personal data", "FOTO": "Photograph", "NOME": "Name", "DATADENASCIMENTO": "Date of birth ", "DATAINVALIDA": "Invalid Date", "ESSECAMPOPRECISASERPREENCHIDO": "This field must be completed", "SEXO": "Gender", "CPF": "cpf", "EMAIL": "Email", "TELEFONE": "Telephone", "CELULAR": "Cell phone", "TELEFONEINVALIDO": "Invalid Phone", "TELEFONECOMERCIAL": "Commercial phone", "CLINICASCORRESPONDENTE": "Corresponding Clinics", "UMACLINICAPRECISASERSELECIONADA": "A clinic needs to be selected", "ENDEREÇO": "Address", "RUA": "Street Address", "NUMERO": "n°", "COMPLEMENTO": "Complement", "UF": "UF", "MUNICIPIO": "City", "BAIRRO": "Neighborhood", "CEP": "Zip code", "LIMPAR": "Clean", "SALVAR": "Save", "ERRODATA": "This field must be completed.", "ERROEMAIL": "Email is not valid", "ERRONAOEVALIDO": "It is not valid", "ERRONAOEVALIDA": "Not valid", "ERROAOSALVAR": "Error saving!", "CADASTROSALVOCOMSUCESSO": "Registration Saved Successfully. ✔", "ERROAOCARREGARUSUARIO": "Error Loading User!", "ERRONORETORNOCIDADEPORUF": "error returning City by uf", "TELEFONEJAREGISTRADOEMOUTROUSUARIO": "Phone already registered in the system in another user!", "EMAILJAREGISTRADOEMOUTROUSUARIO": "Email already registered in the system in another user!", "TELEFONEJAREGISTRADO": "Phone already registered in the System!", "EMAILJAREGISTRADO": "Email already registered in the System!", "USUARIOJAREGISTRADOVERIFIQUEACLINICA": "User already registered in the system, check if the clinic is registered for him", "ERROAOCARREGARCIDADE": "Error loading City", "ERROAOCARREGARUF": "Error loading UF", "FOTODEPERFIL": "Profile Picture", "UFNAOENCONTRADA": "UF not found.", "MASCULINO": "Male", "FEMININO": "Female"}, "TELAPESQUISAUSUARIO": {"CADASTROESTAINATIVO": "WARNING THIS REGISTRATION IS INACTIVE", "ATIVANOVAMENTE": "Sending this email will activate it again!", "BUSCAR": "Search", "TITULO": "Employee", "ADICIONARATENDENTE": "Add Employee", "MOSTRARFOTO": "Show Photo", "INATIVOS": "Inactive", "LEGENDA": "Subtitle", "EMAILDEBOASVINDAS": "Welcome Email", "EMAILENVIADO": "Email sent", "EDITAR": "Edit", "EXCLUIR": "Delete", "ATIVAR": "Enable", "NOME": "Name", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "DATADECADASTRO": "Registration date:", "ATENDENTE": "Employee", "DATADENASCIMENTO": "Date of birth", "EMAIL": "Email", "DELETAR": "Delete", "CARREGARMAIS": "Load More", "DESEJAENVIARESSEEMAIL": "Are you sure you want to send this email?", "RECEBERAOEMAILCOMASENHA": "User will receive email with password", "NAO": "NO", "SIM": "YES", "DESEJAEXCLUIRESTEUSUARIO": "Are you sure you want to delete this user?", "OUSUARIOFICARAINATIVO": "User will be inactive", "DESEJAATIVARESTEUSUARIO": "Are you sure you want to enable this user?", "OUSUARIOTERAACESSOAOSISTEMA": "The User will have access to the system!", "USUARIOEXCLUIDOCOMSUCESSO": "Successfully deleted user.  ✔", "EXCLUIDOCOMSUCESSO": "Successfully deleted. ✔", "FECHAR": "Close", "EMAILENVIADOV": "Email sent. ✔", "EMAILENVIADOCOMSUCESSOV": "<PERSON><PERSON> successfully sent.  ✔", "USUARIOATIVOCOMSUCESSO": "Successful Active User", "ERROAOATIVAROUSUARIO": "Error Activating User !", "ERROAOINATIVAR": "Inactivate Error", "PREENCHERDIAS": "Number of days has to be filled!", "CIDINVALIDO": "Invalid CID, fill in a valid one!", "NAOHAMEDICAMENTOSNARECEITA": "There are no prescription drugs!", "ENVIAREMAIL": "Send Email"}, "TELACADASTROPACIENTE": {"REGISTROSALVOCOMSUCESSO": "Record successfully saved!", "VOLTAR": "Back", "CADASTROPACIENTE": "Patient Registration", "DESATIVAR": "Deactivate", "DADOSPACIENTE": "Patient Data", "NOME": "Name", "DATADENASCIMENTO": "Date of birth", "DATAINVALIDA": "Invalid Date", "ESSECAMPOPRECISASERPREENCHIDO": "This field must be completed", "SEXO": "Gender", "NATURALIDADE": "Place of birth", "NACIONALIDADE": "Citizenship", "CPF": "cpf", "EMAIL": "Email", "TELEFONE": "Telephone", "CELULAR": "Cell phone", "TELEFONECOMERCIAL": "Commercial phone", "TELEFONEINVALIDO": "Invalid Phone", "CLINICASCORRESPONDENTE": "Corresponding Clinics", "UMACLINICAPRECISASERSELECIONADA": "A clinic needs to be selected", "DADOSPESSOAIS": "Personal data", "PESO": "Weight", "ALTURA": "Height", "IMC": "BMI", "PRESSAO": "Pressure", "BATIMENTO": "Heartbeat", "TEMPERATURA": "Temperature °C", "CONVENIO": "Agreement", "PLANODESAUDE": "Health plan", "MATRICULA": "Registration", "PROCEDENCIA": "Provenance", "ENDEREÇO": "Address", "RUA": "Street Address", "NUMERO": "n°", "COMPLEMENTO": "Complement", "UF": "UF", "MUNICIPIO": "City", "BAIRRO": "Neighborhood", "CEP": "Zip code", "LIMPAR": "Clean", "SALVAR": "Save", "NOSSOSTERMOSDEUSO": "Our Terms of Use", "VOCECONCORDACOMOSNOSSOSTERMOS": "Do you agree with our terms?", "NAO": "NO", "SIM": "YES", "ERROCAMPO": "This field must be completed", "ERRONAOEVALIDO": "It is not valid", "ERRONAOEVALIDA": "Not valid", "ERROAOCARREGARUF": "Error loading UF", "ERROAOCARREGARCIDADE": "Error loading City", "CLINICAJAREGISTRADA": "Customer already registered in the system, check if the clinic is registered for him", "EMAILJAREGISTRADO": "Email already registered in the System!", "TELEFONEJAREGISTRADO": "Phone already registered in the System!", "ERROAOCARREGARPACIENTE": "Error Loading Patient", "USUARIOSALVOCOMSUCESSO": "User saved successfully!", "ERROAOSALVAR": "Error saving!", "EMAILJAREGISTRADOEMOUTROUSUARIO": "Email already registered in the system in another user!", "TELEFONEJAREGISTRADOEMOUTROUSUARIO": "Phone already registered in the system in another user!", "ERROAOCARREGAR": "Error Loading!", "FOTODEPERFIL": "Profile picture", "FOTO": "Photograph", "UFNAOENCONTRADA": "UF not found.", "GIRARAESQUERDA": "Turn left", "GIRARADIREITA": "Turn right", "VIRARHORIZONTALMENTE": "<PERSON><PERSON>", "VIRARVERTIVALEMENTE": "Flip vertically", "CORTAR": "Cut", "PROFISSAO": "Occupation", "MASCULINO": "Male", "FEMININO": "Female"}, "TELAPESQUISAPACIENTES": {"TITULO": "Patients", "BUSCAR": "Search", "ADICIONARPACIENTES": "Add Patients", "MOSTRARFOTO": "Show Photo", "INATIVOS": "Inactive", "LEGENDA": "Subtitle", "LIFELINE": "Life Line", "CRIARAGENDAMENTO": "Create Schedule", "EMAILDEBOASVINDAS": "Welcome Email", "EMAILENVIADO": "Email sent", "EDITAR": "Edit", "EXCLUIR": "Delete", "ATIVAR": "Enable", "NOME": "Name", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "TELEFONE": "Telephone", "DATADECADASTRO": "Registration date:", "PROXIMACONSULTA": "Next consultation:", "ULTIMACONSULTA": "Last consultation:", "PACIENTE": "Patient", "AGENDAMENTO": "Scheduling", "EMAIL": "Email", "DELETAR": "Delete", "CARREGARMAIS": "Load More", "VOCETEMCERTEZAQUEDESEJAENVIARESSEEMAIL": "Are you sure you want to send this email?", "OUSUARIORECEBERAOEMAILCOMASENHA": "User will receive email with password", "CADASTROINATIVO": "WARNING THIS REGISTRATION IS INACTIVE", "ATIVARNOVAMENTE": "Sending this email will activate it again!", "NAO": "NO", "SIM": "YES", "EXCLUIRESTEUSUARIO": "Are you sure you want to delete this user?", "OUSUARIOFICARAINATIVO": "The User will be inactive!", "ATIVARESTEUSUARIO": "Are you sure you want to enable this user?", "OUSUARIOTERAACESSOAOSISTEMA": "The User will have access to the system!", "FILTRARRESULTADOS": "Filter Results", "CPF": "cpf", "FILTRAR": "Filter", "INFORMACOESRELATIVASDOAGENDAMENTO": "Schedule Information", "FACASEUAGENDAMENTO": "Make your consultation", "ESPECIALIDADE": "Specialty", "MEDICOS": "Doctor", "AGENDAR": "Schedule", "LIFELINEPACIENTE": "Life-Line Patient", "NENHUMREGISTROENCONTRADO": "No records found!", "CONVERSA": "Conversation", "ANOTACOESANONIMAS": "Anonymous Annotations", "ANEXO": "Attachment", "USUARIO": "User", "DATA": "Date", "USUARIOEXCLUIDOCOMSUCESSO": "Successfully deleted user.  ✔", "EXCLUIDOCOMSUCESSO": "Successfully deleted. ✔", "FECHAR": "Close", "EMAILENVIADOV": "Email sent. ✔", "EMAILENVIADOCOMSUCESSOV": "<PERSON><PERSON> successfully sent.  ✔", "USUARIOATIVOCOMSUCESSO": "Successful Active User", "ERROAOATIVAROUSUARIO": "Error Activating User!", "ERROAOCARREGAR": "Error Loading!", "MOTIVODAEXCLUSAO": "Reason for Exclusion", "MOTIVODAINATIVACAO": "Reason for Inactivation"}, "TELAGERADORDEPERGUNTAS": {"TITULO": "Standard Online consultation Questions", "PERGUNTASPADRAO": "Standard question", "BUSCAR": "Search", "LEGENDAS": "Subtitle", "EDITAR": "Edit", "EXCLUIR": "Delete", "PERGUNTAS": "Question", "ACOES": "Actions", "EXCLUIRPERGUNTA": "Are you sure you want to delete this Question?", "NAO": "NO", "SIM": "YES", "PERGUNTASALVA": "Question saved successfully. ✔", "EDITARPERGUNTA": "Edit Question", "EDITARPERGUNTASALVA": "Successfully edited question ✔", "DELETARPERGUNTA": "Successfully deleted question ✔"}, "TELAGERADORTIPOAGENDAMENTO": {"TITULO": "Schedule Types", "TIPODEAGENDAMENTO": "Schedule Types", "BUSCAR": "Search", "LEGENDA": "Subtitle", "EDITAR": "Edit", "EXCLUIR": "Delete", "PERGUNTAS": "Question", "ACOES": "Actions", "EXCLUIRAGENDAMENTO": "Are you sure you want to delete this Schedule Type?", "NAO": "NO", "SIM": "YES"}, "TELAFINANCAS": {"CONTASPAGAR": "Bills to Pay", "CONTASRECEBER": "Bills to Receive", "DESCRICAOCONTA": "Account Description", "DATAVENCIMENTO": "Due Date", "DATARECEBIMENTO": "Due Date", "DATABAIXA": "Discharge Date", "OBSERVACOESCONTA": "Account Notes", "PARCELAS": "Plots", "FORMAPAGAMENTO": "Form of Payment", "AVISTADINHEIRO": "In cash (cash)", "AVISTACARTAO": "In cash (credit card)", "PARCELADO": "<PERSON><PERSON>eled out", "BOLETO": "Bank slip", "OUTROS": "Others", "FORNECEDOR": "Provider", "VALOR": "Value", "NOTAFISCAL": "Invoice", "SERIENOTAFISCAL": "Invoice Series"}, "TELACONSULTAS": {"TITULO": "Doctor's consultation", "BUSCA": "Search", "STATUSDACONSULTA": "Consultation Status", "DATADECONSULTA": "Inquiry Date", "DATAINVALIDA": "Invalid Date", "MOSTRARFOTO": "Show photo", "CRIARAGENDAMENTO": "Create schedule", "LEGENDA": "Subtitle", "EDITAR": "Edit", "CANCELAR": "Cancel", "AVALIACAODACONSULTA": "Consultation Evaluation", "TESTEDECONEXAO": "Connection test, audio and camera", "NOME": "Name", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "MEDICO": "Doctor", "TIPODEAGENDAMENTO": "Schedule Type:", "TEMPODACONSULTA": "Consultation Time:", "DATADACONSULTA": "Date of Consultation:", "TIPODACONSULTA": "Consultation Type:", "VIDEOCHAMADA": "Video call", "PACIENTES": "Patients", "PACIENTE": "Patient", "FILTRARRESULTADOS": "Filter Results", "CPF": "cpf", "DATADECADASTRO": "Registration date", "FILTRAR": "Filter", "FACASEUAGENDAMENTO": "Make your consultation", "ESPECIALIDADE": "Specialty", "MEDICOS": "Doctor", "AGENDAR": "Schedule", "DESEJACANCELARESSEHORARIO": "Do you want to cancel this time?", "MOTIVODOCANCELAMENTO": "Reason for Cancellation", "MOTIVODESERPREENCHIDO": "Reason to be filled", "NAO": "NO", "SIM": "YES", "DESEJAEDITARESSEHORARIO": "Do you want to edit this time?", "NOVADATA": "New date", "NOVOHORARIO": "New time", "BUSQUEPELOCPF": "Search Patient for cpf", "OBSERVACAO": "Note", "LIFELINEATENDIMENTOS": "Life-Line service", "CONVERSA": "Conversation", "ANOTACOESANONIMAS": "Anonymous Annotations", "ANEXO": "Attachment", "USUARIO": "User", "DATA": "Date", "DADOSCANCELAMENTO": "Data cancellation", "CANCELAMENTOFEITO": "Cancellation Done", "DATACANCELAMENTO": "Date Cancellation", "MOTIVOCANCELAMENTO": "Cavernous Reason", "SAIR": "Exit", "AVALIACAODOPACIENTE": "Patient Assessment", "AVALIACAONAOENVIADA": "Rating not submitted", "PELOPACIENTE": "By the patient", "FACAOTESTEDECONEXAO": "Take the connection test,", "CAMERAEAUDIO": "Camera and audio", "IRPARAOTESTE": "Go to the test", "DADOS": "Data", "EMAIL": "Email", "CELULAR": "Cell phone", "TELEFONE": "Telephone", "TELEFONECOM": "Commercial phone", "TEXTOPERANTEAVALIACAO": "Evaluation text", "FACASUAAVALIACAOPENDENTE": "Write a review Pending", "ALGUMAOBSERVACAO": "Any observations?", "ENVIAR": "Submit", "ERROCAMPO": "This field must be completed.", "ERRONAOEVALIDA": "Not valid", "CARIMBOFINALIZADA": "assets/build/img/Finished.png", "CARIMBOCANCELADA": "assets/build/img/Canceled.png", "PERFILDOPACIENTE": "Patient Profile.", "INICIARCONSULTA": "Start Consultation", "INFORMACOESPACIENTE": "Patient Information.", "PRIMEIRACONSULTA": "First consultation", "CONSULTASDEHOJE": "Consultation of Today", "PACIENTEPRONTOPARACONSULTA": "Patient Ready for Consultation", "CONSULTAEMANDAMENTO": "Consultation in Progress", "CONVENIO": "Agreement", "CONFIRMARCHEGADA": "Confirm Coming", "AGENDADA": "Scheduled", "FINALIZADA": "Completed", "CANCELADA": "Canceled"}, "TELAMENU": {"TITULO": "Start", "PERFIL": "Profile", "CADASTRO": "Register", "MEDICOS": "Doctor", "ATENDIMENTO": "Employee", "PACIENTES": "Patient", "GERADORDEPERGUNTAS": "Question Generator", "TIPODEAGENDAMENTO": "Schedule Types", "CADDECONVENIO": "Agreement reg", "DOCUMENTOS": "Documents", "CONSULTA": "Consultation", "CALENDARIO": "Calendar", "STREAMVIDEOS": "Stream-Videos", "TESTECONEXAO": "Connection-Test", "PRIVACIDADE": "Privacy", "AGENDA": "Schedule", "STREAM2": "Stream2", "MENU": "<PERSON><PERSON>", "MEDICINAPARA": "Medicine for", "VOCE": "you", "FAVORITO": "Favorite", "CONSULTAAGENDADA": "You have an consultation scheduled", "PARA": "for", "INICIARCONSULTA": "Start Consultation", "SAIR": "Exit", "CLINICAS": "Clinics", "NOME": "Name", "TIPOUSUARIO": "<PERSON><PERSON><PERSON>", "ACOES": "Ações", "DATADECADASTRO": "Registration date:", "CNPJ": "CNPJ", "ESPECIALIZACAO": "Specialization:", "MEDICAMENTOS": "Medicines", "SAIRDACLINICA": "Leave the Clinic", "RELATORIOS": "Reports", "RELATORIOSUSUARIO": "User Reports", "RELATORIOSCLINICA": "Clinic Reports", "FINANCAS": "Finances"}, "TELACOLUNADIREITA": {"CONSULTAS": "Consultation", "CONSULTA": "Consultation", "PACIENTES": "Patients", "VOTACOES": "Score", "TOTALDEPACIENTES": "Total Patients", "TOTALDECONSULTAS": "Total consultations", "LEGENDA": "Subtitle", "TOTAL": "Total", "PRIMEIRACONSULTA": "First consultation", "EXAME": "Exam", "RETORNO": "Return", "CIRURGIAS": "Surgeries", "PACIENTEEMESPERA": "Waiting patient", "PACIENTEATENDIDO": "<PERSON><PERSON> attended", "PACIENTEDESMARCOU": "Patient Cleared", "FALTOU": "Missed", "CARTADEBOASVINDASPENDENTES": "Welcome letter pending.", "CARTASDEBOASVINDASTELEMEDICINA": "TeleMedicine Welcome Letters", "DATADECADASTRO": "Registration date", "CPF:": "cpf:", "NOME": "Name", "CPF": "cpf", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "MEDICO": "Doctor", "DTADECADASTRO": "Registration date", "ULTIMACONSULTA": "Last consultation", "ENVIARESSEEMAIL": "Are you sure you want to send this email?", "RECEBERAOEMAILCOMASENHA": "User will receive email with password", "NAO": "NO", "SIM": "YES", "CONSULTAAGENDADA": "You have an consultation scheduled for", "INICIARCONSULTA": "Start Consultation", "LOGO": "Logo", "ENVIAREMAIL": "Send Email"}, "TELACONVENIO": {"REGISTROSALVO": "Record successfully saved!", "VOLTAR": "Back", "DESATIVAR": "Deactivate", "CONVENIO": "Agreement", "DADOSDOVONVENIO": "Agreement Data", "CNPJ": "CNPJ", "REGCLINICA": "Reg. Clinic", "DESCRICAO": "Description", "OBSERVACAO": "Note", "CONTATO": "Contact", "EMAIL": "Email", "TELEFONE": "Telephone", "TELEFONEMOVEL": "Mobile phone", "TELEFONECOMERCIAL": "Commercial phone", "ENDERECO": "Address", "RUA": "Street Address", "NUMERO": "N°", "COMPLEMENTO": "Complement", "UF": "UF", "MUNICIPIO": "City", "BAIRRO": "Neighborhood", "CEP": "Zip code", "LIMPAR": "Clean", "SALVAR": "Save", "NOSSOSTERMOSDEUSO": "Our Terms of Use", "CONCORDACOMTERMOS": "Do you agree with our terms?", "NAO": "NO", "SIM": "YES", "EXCLUIRESTEUSUARIO": "Are you sure you want to delete this User?", "SERAEXCLUIDOPERMANENTEMENTE": "User will be permanently deleted", "ERROCAMPO": "This field must be completed", "ERRONAOEVALIDO": "It is not valid", "UFNAOENCONTRADA": "UF not found.", "VALORCONSULTA": "Consultation price", "PERIODORETORNO": "Return Period"}, "TELADOCUMENTACAO": {"ATESTADO": "Medical certificate", "DECLARACAO": "Declaration", "RECEITUARIO": "Medical prescription", "GERARATESTADO": "Generate Medical certificate", "BUSQUEOPACIENTEPELOCPF": "Search Patient for cpf", "PACIENTE": "Patient", "DATA": "Date", "MEDICOS": "Doctor", "ATIVIDADESNORMAIS": "You will not be able to perform normal activities for a period of", "DIA": "day (s)", "NAOIMPRIMIRDESCRICAODOCID": "Do not print CID description", "CID": "CID", "DESCRICAODOCID": "CID Description", "GERARSALVAR": "Generate Save", "FECHAR": "Close", "GERARDECLARACAO": "Generate Statement", "COMPARECEUNOPERIUDO": "He attended this office in the period:", "PERIUDO": "Period", "GERARESALVAR": "Generate and Save", "BUSQUEOMEDICAMENTO": "Search for Medicine", "PERGUNTA": "Question", "IMPRIMIRENDERECODOPACIENTE": "Print Patient Address", "DADOS": "Data", "NOME": "Name", "EMAIL": "Email", "CELULAR": "Cell phone", "TELEFONE": "Telephone", "TELEFONECOM": "Com. phone", "SAIR": "Exit", "TELACAMPO": "This field must be completed", "TELANAOEVALIDA": "Not valid", "CHECKMEDICAMENTOS": "Scheduled Medicines", "INFORMACOESPACIENTE": "Patient Information.", "PREENCHERDIAS": "Number of days has to be filled!", "CIDINVALIDO": "Invalid CID, fill in a valid one!", "CODNAOCORRESPONDEADOENCA": "Code does not correspond to any disease", "NAOHAMEDICAMENTOSNARECEITA": "There are no prescription drugs!"}, "TELATESTECONEXAO": {"CONSULTADEVIDEO": "Start video consultation session", "SESSAODECONSULTADEVIDEO": "Set up your webcam, microphone and network before starting the video consultation session", "SUPORTACHAMADASDEVIDEO": "Make sure your web browser supports video calling.", "CAMERAEMICROFONE": "Grant your browser permission to access your camera and microphone", "DESEMPENHOCOMAVERSAO": "On your computer, video calling performs best with the latest version of", "GOOGLECHROME": "Google Chrome", "OUDO": "or from", "MOZILLAFIREFOX": "Mozilla Firefox", "USANDOOUTROSDISPOSITIVOS": "Using other devices?", "VERSAOMAISRECENTE": "For android web browsers or tablets, upgrade to the latest version of Chrome or Firefox.", "IOSNAOESUPORTADA": "Video calling in iOS tablet web browser is not supported", "VIRTUALPRACTICEAPP": "If you are using a smartphone, download the Virtual Practice app, available from", "GOOGLEPLAY": "Google Play", "ANDROIDSUPERIOR": "(Android 4.1 or higher) or", "APPSTORE": "App Store", "IOSSUPERIOR": "(iOS 6 or higher)", "COMPATIBILIDADECOM": "Check your webcam and microphone compatibility", "CONECTADOSCORRETAMENTE": "Make sure webcam and microphone devices are connected properly", "NOTADAPAGINA": "Note: This page only checks the accessibility of your webcam and microphone from this browser.", "VERIFIQUEAQUALIDADE": "Check the quality of your internet connection", "DESCUBRASUAVELOCIDADE": "Discover your internet connection speed and network compatibility", "VELOCIDADEATUAL": "The speed of your Internet currently is", "VELOCIDADEFICTICIA": "Speed ​​56.9 MBs", "RECOMENDAMOSNOMINIMO": "We recommend at least 500 kbps connection speed", "APERFORMANCEEMELHOR": "Performance is best on a Wi-Fi or 4G network.", "VOLTARPARAAPAGINAANTERIOR": "Back to the previous page"}, "TELAAGENDA": {"ESPECIALIDADE": "Specialty", "MEDICO": "Doctor", "MES": "Month", "DIA": "Day", "CALCULARDIAS": "Calculate Days", "HOJE": "Today", "LEGENDA": "Subtitle", "CANCELARAGENDAMENTO": "Cancel Schedule", "EDITARAGEND": "Edit Schedule", "MOTIVOCANCEL": "Reason Cancellation", "PRIMEIRACONSULTA": "First consultation", "CONSULTACANCELADA": "Consultation Canceled", "CONSULTAPADRAO": "Standard Consultation", "MARCARHORARIO": "Do you want to schedule this time?", "BUSQUEOPACIENTEPELOCPF": "Search Patient for cpf", "PACIENTE": "Patient", "DATAEHORA": "Date and time", "NOVADATA": "New date", "NOVOHORARIO": "New time", "MEDICOS": "Doctor", "TIPOAGENDAMENTO": "Schedule Type", "OBSERVACAO": "Note", "ADICIONARPACIENTE": "Add Patient", "CANCELAR": "Cancel", "SALVAR": "Save", "DESEJAMARCARESSEHORARIO": "Do you want to schedule this time?", "DATA": "Date", "HORA": "Hour", "DESEJACANCELARESSEHORARIO": "Do you want to cancel this time?", "MOTIVODOCANCELAMENTO": "Reason for Cancellation", "MOTIVODESERPREENCHIDO": "Reason to be filled", "NAO": "NO", "SIM": "YES", "DIANAOCADASTRADO": "This day of the week is not registered for this doctor.", "AGENDARMESMOASSIM": "Do you still want to schedule a time?", "ESCOLHAOMEDICO": "Choose the Doctor for the consultation!", "FILTRARESPECIALIDADE": "Filter Doctor by <PERSON><PERSON>", "NOVOCLIENTE": "New customer", "CPF": "cpf", "NOME": "Name", "DATADENASCIMENTO": "Date of birth", "DATAINVALIDA": "Invalid Date", "EMAIL": "Email", "CELULAR": "Cell phone", "PROCEDENCIA": "Provenance", "CLINICASCORRESPONDENTE": "Corresponding Clinics", "UMACLINICAPRECISASERSELECIONADA": "A clinic needs to be selected", "ENVIARBOASVINDAS": "Send Welcome", "LIMPAR": "Clean", "DADOS": "Data", "TELEFONE": "Telephone", "TELEFONECOM": "Com. phone", "SAIR": "Exit", "DADOSCANCELAMENTO": "Data Cancellation", "CANCELAMENTOFEITO": "Cancellation Done", "DATACANCELAMENTO": "Date Cancellation", "MOTIVOCANCELAMENTO": "Cavernous Reason", "ERROCAMPO": "This field must be completed.", "ERRONAOEVALIDA": "Not valid", "ERRONAOEVALIDO": "It is not valid", "ERROEMAIL": "This email is not valid", "ERRODATA": "This field needs valid date", "CADASTROSALVO": "Registration Saved Successfully. ✔", "ERROAOSALVAR": "Error saving!", "EMAILENVIADO": "Email sent. ✔", "NOVOHORARIOERRO": "Invalid new time!", "INFORMACOESPACIENTE": "Patient Information.", "NAOHAESPECIALIDADEPARAESSEMEDICO": "There are no specialties for this Doctor.", "AGENDADEESPERA": "Waiting Schedule", "TELEFONECONTATO": "Contact Phone", "MENSAGEMDODIA": "Message of the Day", "MENSAGEMDODIA2": "Message", "PARA": "to", "ENVIAR": "Send", "PAGAMENTO": "Payment", "VALOR": "Cost", "CONVENIO": "Agreement", "CODIGOCONVENIO": "Agreement Code", "RETORNO": "Return", "PAGAMENTODINHEIRO": "Money", "PAGAMENTOCONVENIO": "Agreement"}, "TELAAGENDACONTATO": {"CONTATOS": "Contacts", "ADICIONARCONTATO": "Add Contact", "PESQUISARPOR": "Search for:", "NOME": "Name", "EMAIL": "Email", "TELEFONEMOVELCOMERCIAL": "Phone/Mobile/Commercial", "BUSCAR": "Search", "LEGENDA": "Subtitle", "EDITAR": "Edit", "EXCLUIR": "Delete", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "TELEFONE:": "Telephone:", "CELULAR:": "Cell phone:", "TELCOMERCIAL:": "Commercial Phone:", "SITE:": "Website:", "NOVOCONTATO": "New contact", "TELEFONE": "Telephone", "CELULAR": "Cell phone", "TELEFONECOMERCIAL": "Commercial phone", "TELEFONEINVALIDO": "Invalid Phone", "ESSECAMPOPRECISASERPREECHIDO": "This field must be completed.", "SALVAR": "Save", "LIMPAR": "Clean", "EXCLUIRESTECONTATO": "Are you sure you want to delete this Contact?", "EXCLUIDOPERMANENTEMENTE": "Contact will be permanently deleted", "NAO": "NO", "SIM": "YES", "ERROCAMPO": "This field must be completed.", "ERROEMAIL": "Email is not valid", "ERRONAOEVALIDO": "Not valid", "SITE": "Website", "EDITARCONTATO": "Edit ", "EXCLUIRCONTATO": "Delete "}, "TELATERMOPRIVACIDADE": {"ACEITARTERMOS": "I accept the privacy terms", "ACEITAR": "Accept", "RECUSAR": "Refuse"}, "TELASTREAMING": {"CONSULTADIA": "Consultation day", "MEDICO": "Doctor:", "PRONTUARIO": "Record:", "ANONIMO": "Anonymous", "ANEXOS": "Attachments", "USUARIOSATIVOS": "Active Users", "SALVAR": "Save", "AREAPARAIMPORTARARQUIVOS": "Use this area to import new files by dragging and dropping your file here.", "OU": "or", "CARREGARARQUIVO": "Upload File", "DADOSPACIENTE": "Patient Data", "PESO": "Weight", "ALTURA": "Height", "IMC": "BMI", "PRESSAO": "Pressure", "BATIMENTO": "Beat", "TEMPERATURA": "Temperature °C", "LIMPAR": "Clean", "DOCUMENTOS": "Documents", "PERGUNTAS": "Questions", "MEDICAMENTOS": "Medicines", "HISTORICOPACIENTE": "Patient History", "CONVERSA": "Conversation", "ALGUMCHATFEITOPARAOUSUARIO": "Some chat made for the user", "ANOTACOESANONIMAS": "Anonymous Annotations", "PACIENTE": "Patient", "TESTOPERANTEAVALIACAO": "Test before assessment", "FACASUAAVALIACAO": "Write a Review", "ALGUMAOBSERVACAO": "Any Observations?", "ENVIAR": "Submit", "AVALIACAOREALIZADACOMSUCESSO": "Successful Assessment!", "FECHAR": "Close", "NAOSETRATADEUMACONSULTA": "This service is not a consultation and therefore does not replace face-to-face care, does not provide diagnoses and does not prescribe medication. Its purpose is only to provide guidance to its users.", "ACEITAREINICIAR": "ACCEPT AND START", "SAIR": "Exit", "FINALIZARCONSULTA": "End Consultation", "CONTINUARNASALA": "At the end of the conference you will be able to continue in the room, consultation options remain active.", "CANCELAR": "Cancel", "FINALIZAR": "Finish", "LAYOUTALTERNATIVO": "alternate layout", "ESCOLHAUMLAYOUT": "Choose a layout that best suits your consultation", "AVALIACAOFEITACOMSUCESSO": "Evaluation made successfully!  ✔", "CONSULTAS": "Consultation", "DADOSPESSOAIS": "Personal data", "INFOPESSOAIS": "Personal information", "LOCALIZACAO": "Location", "ATESTADO": "Medical certificate", "RECEITUARIO": "Medical prescription", "DECALARACAO": "Declaration", "GERAR": "Generate", "GERAREENVIAR": "Generate and Submit", "TEMPERATURAC": "Temperature °C", "IMPORTARNOVOSARQUIVOS": "Use this area to import new files by dragging and dropping your file here.", "SALVOCOMSUCESSO": "Saved successfully!  ✔ ", "OARQUIVOPRECISATERNOMAXIMO30MBS": "The file must be at most 30mbs. ", "RECEITA": "Prescription", "CONSULTA": "Query", "GERAL": "General", "CECLARACAO": "Declaration", "ANOTACOES": "Notes", "HISTORICO": "Historic", "EXPANDIR": "Expand", "DESLIGARCONFERENCIA": "Hang up conference", "CODNAOCORRESPONDEADOENCA": "Code does not correspond to any disease", "USESEUCELULARDEITADO": "For a better experience, use your mobile phone lying down.", "DATA": "Date", "HORA": "Hour", "TEMPOCONSULTA": "Consultation Time", "CONCLUIRCONSULTA": "Complete Consultation"}, "TELAMEDICAMENTOS": {"DESCRICAO": "Description", "VOLTAR": "Back", "CADASTROMEDICPRESCR": "Prescription Drug Registration", "MEDICAMENTO": "Medication", "NOVOSMEDICAMENTOS": "Register medicine", "POSOLOGIAPADRAO": "Standard Posology", "DESCARTARITEMNALISTA": "Highlight List Item", "MEDICAMENTO:": "Medication:", "POSOLOGIA:": "Posology:", "EXCLUIRMEDICAMENTO": "Are you sure you want to delete this medicine?", "EXCLUIRPERMANENTEMENTE": "The drug will be permanently deleted", "NAO": "NO", "SIM": "YES", "ERROCAMPO": "This field must be completed", "ERRONAOEVALIDO": "It is not valid", "EDITAR": "Edit", "EXCLUIR": "Delete"}, "TELACADASTROCLINICA": {"CRM-": "CRM -", "LOGO": "Logo", "DESATIVAR": "Deactivate", "VOLTAR": "Back", "CLINICA": "Clinic", "CADASTROCLINICA": "Register Clinic", "DADOSDACLINICA": "Clinic Data", "DATAINVALIDA": "Invalid Date", "NOME": "Name", "DATADALICENCA": "License Date", "CARACTERIZACAO": "Description", "CNPJ": "CNPJ/CPF", "ISS/CCM": "ISS/CCM", "CRM": "CRM", "CNES": "CNES", "WEBSITE": "Website", "EMAIL": "Email", "TELEFONE": "Telephone", "CELULAR": "Cell phone", "TELEFONECOMERCIAL": "Commercial phone", "TELEFONEINVALIDA": "Invalid Phone", "ERROCAMPO": "This field must be completed", "ENDERECO": "Address", "RUA": "Street Address", "NUMERO": "n°", "COMPLEMENTO": "Complement", "UF": "UF", "MUNICIPIO": "City", "BAIRRO": "Neighborhood", "CEP": "Zip code", "LIMPAR": "Clean", "SALVAR": "Save", "FOTODEPERFIL": "Profile picture", "GIRARAESQUERDA": "Turn left", "GIRARADIREITA": "Turn right", "VIRARHORIZONTALMENTE": "<PERSON><PERSON>", "VIRARVERTICALMENTE": "Flip vertically", "CORTAR": "Cut", "ERRONAOEVALIDO": "It is not valid", "ERRONAOEVALIDA": "Not valid", "ERROEMAILNAOEVALIDO": "Email is not valid", "ERROAOCARREGARCLINICA": "Error Loading Clinica!", "CADASTROSLAVOCOMSUCESSO": "Registration Saved Successfully. ✔", "ERROAOSALVAR": "Error saving!", "ERRONORETORNOPORUF": "Error returning City by UF", "LOGODOPERFIL": "Profile Logo", "UFNAOENCONTRADA": "UF not found.", "SOMENTEPRONTUARIOS": "Medical Records Only", "PERFILDACLINICA": "Clinic Profile"}, "TELAPESQUISACLINICA": {"CLINICA": "Clinic", "BUSCAR": "Search", "ADICIONARCLINICA": "Add Clinic", "MOSTRARLOGO": "Show Logo", "INATIVOS": "Inactive", "LEGENDA": "Subtitle", "EMAILDEBOASVINDAS": "Welcome Email", "EMAILENVIADO": "Email sent", "EDITAR": "Edit", "EXCLUIR": "Delete", "ATIVAR": "Enable", "NOME": "Name", "TIPOUSUARIO": "User Type", "ACOES": "Actions", "DATADECADASTRO": "Registration date:", "CNPJ:": "CNPJ:", "ESPECIALIZACAO:": "Specialization:", "CNPJ": "CNPJ", "ESPECIALIZACAO": "Specialization", "EMAIL": "Email", "INATIVAR": "Inactivate", "EXCLUIRESTECLIENTE": "Are you sure you want to delete this clinic?", "ACLINICAFICAINATIVADA": "The clinic is inactivated", "NAO": "NO", "SIM": "YES", "ATIVARESTACLINICA": "Are you sure you want to activate this clinic?", "USUARIOTERAACESSOAOSISTEMA": "Clinic users had access to the system!", "ERROAOCARREGAR": "Error Loading!", "SUCESSOAOATIVAR": "Successful Active Clinic", "ERROAOATIVAR": "Error Activating User!", "CLINICACOMSUCESSO": "Clinic successfully unavailable!", "ERROAOINATIVAR": "Inactivate Error", "EDITARCLINICA": "Edit", "EXCLUIRCLINICA": "Delete", "ATIVARCADASTRO": "Enable", "CONVENIO": "Agreements", "CONVENIO2": "Agreement", "ADICIONARCONVENIO": "Add Agreement", "TELEFONE": "Telephone"}, "TELARELATORIOS": {"GERARRELATORIOSCLINICA": "Generate report - Clinic:", "DATAINICIO": "Start Date", "DATAFIM": "End Date", "DATAINVALIDA": "Invalid Date", "CONSULTAS": "Consultation", "CADASTROSMEDICOS": "Medical Records", "CADASTROSPACIENTES": "Patient Records", "RELATORIOUSUARIO": "User report", "GERARRELATORIO": "Generate report", "FECHAR": "Close", "NENHUMDADOFOIMARCADOPARAGERARORELATORIO": "No data has been marked to generate the report.", "TROCARDECLINICA": "Change Clinic", "CLINICAS": "Clinics", "ATIVARCADASTRO": "Enable Registration", "TELACAMPO": "This field must be completed", "TIPOUSUARIO": "User Type", "USUARIO": "User", "PREENCHACAMPOSDATA": "Fill in the date inputs areas.", "USUARIONAORALIZOUACOES": "The user did not perform actions during this period.", "PESQUISAR": "Search"}}
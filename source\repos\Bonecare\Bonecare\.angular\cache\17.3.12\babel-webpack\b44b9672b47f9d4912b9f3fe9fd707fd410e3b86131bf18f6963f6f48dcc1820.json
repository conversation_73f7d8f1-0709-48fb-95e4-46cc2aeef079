{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Router, RouterModule } from '@angular/router';\nimport { ConsultaService } from 'src/app/service/consulta.service';\nimport { SpinnerService } from 'src/app/service/spinner.service';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { ConsultaAgoraDialogComponent } from './consulta-agora-dialog/consulta-agora-dialog.component';\nimport { MedicosZeroDialogComponent } from './medicos-zero-dialog/medicos-zero-dialog.component';\nimport { SairConsultaDialogComponent } from './sair-consulta-dialog/sair-consulta-dialog.component';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { ChangeDetectorRef } from '@angular/core';\nimport { FilaEsperaPacienteService } from 'src/app/service/fila-espera-paciente.service';\n// import { SignalHubService } from 'src/app/service/signalHub.service';\nimport { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';\nimport { PatientQueueIntegrationService } from 'src/app/service/patient-queue-integration.service';\nimport { ModalColetaDadosVittaltecComponent } from './modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\nimport { firstValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/service/spinner.service\";\nimport * as i3 from \"src/app/service/consulta.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/service/fila-espera-paciente.service\";\nimport * as i6 from \"src/app/service/signalHub-guest.service\";\nimport * as i7 from \"src/app/service/patient-queue-integration.service\";\nexport let FilaEsperaComponent = /*#__PURE__*/(() => {\n  class FilaEsperaComponent {\n    router;\n    spinner;\n    consultaService;\n    dialog;\n    cdr;\n    filaEsperaPacienteService;\n    signalHubGuestService;\n    patientQueueIntegrationService;\n    constructor(router, spinner, consultaService, dialog, cdr, filaEsperaPacienteService,\n    // private signalHubService: SignalHubService,\n    signalHubGuestService, patientQueueIntegrationService) {\n      this.router = router;\n      this.spinner = spinner;\n      this.consultaService = consultaService;\n      this.dialog = dialog;\n      this.cdr = cdr;\n      this.filaEsperaPacienteService = filaEsperaPacienteService;\n      this.signalHubGuestService = signalHubGuestService;\n      this.patientQueueIntegrationService = patientQueueIntegrationService;\n    }\n    PesicaoFila = 0;\n    timeInterval;\n    ConsultaAgoraTeste = false;\n    dadosQuestionario = null;\n    dadosVittalTec = null; // Data collected from VittalTec modal\n    tokenConexao = null;\n    subscriptions = [];\n    // Indicadores de status\n    signalRConectado = false;\n    cadastrandoNaFila = false;\n    pacienteRegistrado = false;\n    ngOnInit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.carregarDadosQuestionario();\n        _this.carregarDadosVittalTec();\n        if (!_this.dadosQuestionario) {\n          _this.router.navigate(['/pre-consulta-questionario']);\n          return;\n        }\n        const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\n        if (!tokenSalvo) {\n          _this.tokenConexao = CriptografarUtil.gerarHashToken(_this.dadosQuestionario.cpf ?? \"naotemcpf\", new Date().toISOString());\n          yield _this.cadastrarNaFilaDeslogado();\n        } else {\n          _this.tokenConexao = tokenSalvo;\n          yield _this.recuperarPosicaoFila();\n        }\n        _this.configurarSignalR();\n        yield _this.inicializarTokenConexao();\n        yield _this.conectarSignalRGuest();\n      })();\n    }\n    quitQueue() {\n      this.CancelarConsulta();\n    }\n    inicializarTokenConexao() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\n        if (tokenSalvo) _this2.tokenConexao = tokenSalvo;else CriptografarUtil.localStorageCriptografado('tokenFilaEspera', _this2.tokenConexao);\n      })();\n    }\n    conectarSignalRGuest() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.tokenConexao) {\n          console.error('Token de conexão não disponível');\n          return;\n        }\n        try {\n          yield _this3.aguardarSignalRPronto();\n          _this3.signalHubGuestService.connectGuestUser(_this3.tokenConexao);\n          _this3.signalRConectado = true;\n          _this3.verificarEstadoConexao();\n          // Register patient data after SignalR connection is established\n          yield _this3.garantirColetaDadosCompletos();\n          yield _this3.registrarDadosPaciente();\n        } catch (error) {\n          console.error('❌ Erro ao conectar guest ao SignalR:', error);\n          _this3.signalRConectado = false;\n        }\n      })();\n    }\n    aguardarSignalRPronto() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        return new Promise(resolve => {\n          if (_this4.signalHubGuestService.isHubConnected()) {\n            resolve();\n            return;\n          }\n          const subscription = _this4.signalHubGuestService.changeConsulta$.subscribe(connected => {\n            if (connected) {\n              subscription.unsubscribe();\n              resolve();\n            }\n          });\n          setTimeout(() => {\n            subscription.unsubscribe();\n            resolve();\n          }, 5000);\n        });\n      })();\n    }\n    configurarSignalR() {\n      const subAtualizaFila = this.signalHubGuestService.OnAtualizaChamaPacienteFila.subscribe(() => {\n        this.buscarPosicaoAtual();\n      });\n      const subConviteReuniao = this.signalHubGuestService.OnConviteReuniao.subscribe(convite => {\n        if (convite.token === this.tokenConexao) {\n          this.IniciarConsulta();\n        }\n      });\n      const subConvidarPacienteComToken = this.signalHubGuestService.OnConvidarPacienteComToken.subscribe(dados => {\n        if (dados.token === this.tokenConexao) {\n          this.IniciarConsulta();\n        }\n      });\n      const subChamaPaciente = this.signalHubGuestService.OnChamaPacienteFila.subscribe(() => {\n        this.buscarPosicaoAtual();\n      });\n      this.subscriptions.push(subAtualizaFila, subConviteReuniao, subConvidarPacienteComToken, subChamaPaciente);\n    }\n    carregarDadosQuestionario() {\n      const dadosStorage = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');\n      if (dadosStorage) {\n        try {\n          this.dadosQuestionario = JSON.parse(dadosStorage);\n        } catch (error) {\n          console.error('Erro ao parsing dos dados do questionário:', error);\n          this.dadosQuestionario = null;\n        }\n      }\n    }\n    getDadosQuestionario() {\n      return this.dadosQuestionario;\n    }\n    cadastrarNaFilaDeslogado() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this5.cadastrandoNaFila = true;\n          _this5.spinner.show();\n          if (_this5.tokenConexao) _this5.filaEsperaPacienteService.salvarTokenFila(_this5.tokenConexao);\n          const dadosComToken = {\n            ..._this5.dadosQuestionario,\n            tokenGerado: _this5.tokenConexao\n          };\n          const response = yield firstValueFrom(_this5.consultaService.cadastrarFilaEsperaDeslogado(dadosComToken));\n          if (response.sucesso) {\n            yield _this5.buscarPosicaoAtual();\n            _this5.iniciarMonitoramentoPosicao();\n            _this5.cadastrandoNaFila = false;\n            _this5.cdr.detectChanges();\n            _this5.spinner.hide();\n          } else {\n            console.error('❌ Erro ao cadastrar na fila:', response.Mensagem);\n            _this5.cadastrandoNaFila = false;\n            _this5.spinner.hide();\n          }\n        } catch (error) {\n          console.error('❌ Erro ao cadastrar na fila de espera:', error);\n          _this5.cadastrandoNaFila = false;\n          _this5.spinner.hide();\n        }\n      })();\n    }\n    CancelarConsulta() {\n      this.removerDaFilaEspera().then(() => {\n        this.Logoff();\n        this.spinner.hide();\n      }).catch(err => {\n        console.error('Erro ao cancelar consulta:', err);\n        this.Logoff();\n        this.spinner.hide();\n      });\n    }\n    Logoff() {\n      if (this.timeInterval) {\n        clearInterval(this.timeInterval);\n      }\n      if (this.tokenConexao) {\n        this.removerDaFilaEspera();\n        this.filaEsperaPacienteService.limparDadosFilaEspera();\n      }\n      this.router.navigate(['pre-consulta-questionario']);\n    }\n    IniciarConsulta() {\n      if (!this.tokenConexao) {\n        console.error('❌ Token não encontrado! Não é possível iniciar a consulta.');\n        return;\n      }\n      const idConsultaTemporario = this.gerarIdConsultaTemporario();\n      this.router.navigate(['/streaming-paciente'], {\n        queryParams: {\n          idConsulta: idConsultaTemporario,\n          token: this.tokenConexao\n        }\n      });\n    }\n    gerarIdConsultaTemporario() {\n      return Date.now();\n    }\n    // Método de debug para verificar estado da conexão\n    verificarEstadoConexao() {\n      const estadoConexao = this.signalHubGuestService.getEstadoConexao();\n      // Verificar se tokens correspondem\n      if (this.tokenConexao !== estadoConexao.tokenConexao) {\n        console.warn('⚠️ PROBLEMA: Tokens não correspondem!');\n        console.warn('  - Token Componente:', this.tokenConexao);\n        console.warn('  - Token Service:', estadoConexao.tokenConexao);\n      }\n      // Testar se consegue enviar dados para o servidor\n      setTimeout(() => {\n        try {\n          this.signalHubGuestService.enviaServer('TestConnection', this.tokenConexao);\n        } catch (error) {\n          console.error('❌ Erro no teste de envio:', error);\n        }\n      }, 2000);\n    }\n    abrirDialogSairConsulta() {\n      const dialogRef = this.dialog.open(SairConsultaDialogComponent, {\n        width: '400px',\n        disableClose: true\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result === 'confirmar') {\n          this.CancelarConsulta();\n        }\n      });\n    }\n    abrirDialogMedicosZero() {\n      const dialogRef = this.dialog.open(MedicosZeroDialogComponent, {\n        width: '400px',\n        disableClose: true\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result === 'sair') {\n          this.CancelarConsulta();\n        }\n      });\n    }\n    abrirDialogConsultaAgora() {\n      const dialogRef = this.dialog.open(ConsultaAgoraDialogComponent, {\n        width: '500px',\n        disableClose: true\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result === 'iniciar') this.IniciarConsulta();else if (result === 'cancelar') {\n          this.CancelarConsulta();\n        }\n      });\n    }\n    beforeUnloadHandler() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (_this6.tokenConexao) {\n          yield _this6.removerDaFilaEspera();\n        }\n      })();\n    }\n    removerDaFilaEspera() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        if (_this7.tokenConexao) {\n          yield firstValueFrom(_this7.filaEsperaPacienteService.removerDaFilaEspera(_this7.tokenConexao));\n        }\n      })();\n    }\n    iniciarMonitoramentoPosicao() {\n      var _this8 = this;\n      if (this.tokenConexao) {\n        this.timeInterval = setInterval( /*#__PURE__*/_asyncToGenerator(function* () {\n          try {\n            const response = yield firstValueFrom(_this8.consultaService.consultarPosicaoFila(_this8.tokenConexao));\n            if (response.sucesso) {\n              _this8.PesicaoFila = response.posicaoFila;\n              _this8.cdr.detectChanges();\n              if (_this8.PesicaoFila === 0) {\n                _this8.ConsultaAgoraTeste = true;\n                _this8.abrirDialogConsultaAgora();\n                clearInterval(_this8.timeInterval);\n              }\n            }\n          } catch (error) {\n            console.error('Erro ao consultar posição:', error);\n          }\n        }), 30000);\n      }\n    }\n    buscarPosicaoAtual() {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this9.tokenConexao) {\n          console.warn('⚠️ Token não encontrado para buscar posição');\n          return;\n        }\n        try {\n          const response = yield firstValueFrom(_this9.consultaService.consultarPosicaoFila(_this9.tokenConexao));\n          if (response.sucesso) {\n            _this9.PesicaoFila = response.posicaoFila;\n            _this9.cdr.detectChanges();\n          } else console.error('❌ Erro ao consultar posição:', response.mensagem);\n        } catch (error) {\n          console.error('❌ Erro ao buscar posição atual:', error);\n          if (error.status === 404) console.error('🚨 ERRO 404: Endpoint não encontrado!');\n        }\n      })();\n    }\n    /**\n     * Ensures all necessary data is collected before registration\n     */\n    garantirColetaDadosCompletos() {\n      var _this10 = this;\n      return _asyncToGenerator(function* () {\n        console.log('🔍 Verificando completude dos dados...');\n        // Check if questionnaire data is available\n        if (!_this10.dadosQuestionario) {\n          console.warn('⚠️ Dados do questionário não encontrados, tentando carregar...');\n          _this10.carregarDadosQuestionario();\n        }\n        // Check if VittalTec data is available\n        if (!_this10.dadosVittalTec) {\n          console.log('📊 Dados VittalTec não encontrados, tentando carregar do localStorage...');\n          _this10.carregarDadosVittalTec();\n          // If still no VittalTec data, that's okay - registration can proceed without it\n          if (!_this10.dadosVittalTec) {\n            console.log('ℹ️ Nenhum dado VittalTec disponível - registro continuará apenas com dados do questionário');\n          }\n        }\n        console.log('✅ Verificação de dados concluída:', {\n          questionario: !!_this10.dadosQuestionario,\n          vittalTec: !!_this10.dadosVittalTec,\n          token: !!_this10.tokenConexao\n        });\n      })();\n    }\n    /**\n     * Registers patient data in the database using the same token as SignalR\n     * Collects data from both questionnaire and VittalTec modal\n     */\n    registrarDadosPaciente() {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this11.tokenConexao || !_this11.dadosQuestionario) {\n          console.warn('⚠️ Token ou dados do questionário não disponíveis para registro');\n          return;\n        }\n        if (_this11.pacienteRegistrado) {\n          console.log('✅ Paciente já registrado, pulando registro');\n          return;\n        }\n        try {\n          if (!_this11.dadosVittalTec) {\n            _this11.carregarDadosVittalTec();\n          }\n          // Convert questionnaire data to patient registration format (includes VittalTec if available)\n          const patientData = _this11.converterQuestionarioParaRegistro();\n          // Register patient using the integration service\n          const response = yield firstValueFrom(_this11.patientQueueIntegrationService.registerPatientInQueue(patientData));\n          if (response.success) {\n            _this11.pacienteRegistrado = true;\n            console.log('✅ Paciente registrado com sucesso:', {\n              token: response.patientToken,\n              nome: response.patientName,\n              tipoFila: response.queueType,\n              comDadosVittalTec: !!_this11.dadosVittalTec,\n              dadosEnviados: Object.keys(patientData).length\n            });\n            // Save registration status to localStorage\n            CriptografarUtil.localStorageCriptografado('paciente-registrado', JSON.stringify({\n              token: response.patientToken,\n              timestamp: new Date().toISOString(),\n              dadosCompletos: !!_this11.dadosVittalTec\n            }));\n          } else {\n            console.warn('⚠️ Falha ao registrar paciente:', response.message);\n            console.warn('Erros:', response.errors);\n            // Don't prevent queue entry if patient registration fails\n          }\n        } catch (error) {\n          console.error('❌ Erro ao registrar dados do paciente:', error);\n          // Don't prevent queue entry if patient registration fails\n        }\n      })();\n    }\n    /**\n     * Converts questionnaire data to patient registration format including VittalTec data\n     */\n    converterQuestionarioParaRegistro() {\n      if (!this.dadosQuestionario || !this.tokenConexao) {\n        throw new Error('Dados do questionário ou token não disponíveis');\n      }\n      // Format date of birth as string for API\n      let dataNascimento;\n      if (this.dadosQuestionario.dataNascimento) {\n        try {\n          const date = new Date(this.dadosQuestionario.dataNascimento);\n          if (!isNaN(date.getTime())) {\n            dataNascimento = date.toISOString().split('T')[0]; // YYYY-MM-DD format\n          }\n        } catch (error) {\n          console.warn('⚠️ Erro ao converter data de nascimento:', error);\n        }\n      }\n      let sintomas = this.dadosQuestionario.sintomasOutros;\n      let vittalTecData = {};\n      if (this.dadosVittalTec) {\n        try {\n          const data = this.dadosVittalTec.data || this.dadosVittalTec;\n          if (data) {\n            vittalTecData = {\n              pressaoSistolica: this.convertVitalSignToString(data.pressaoSistolica || data.systolic),\n              pressaoDiastolica: this.convertVitalSignToString(data.pressaoDiastolica || data.diastolic),\n              temperatura: this.convertTemperatureToString(data.temperatura),\n              saturacaoOxigenio: this.convertVitalSignToString(data.oxigenacao || data.oxygenSaturation),\n              frequenciaCardiaca: this.convertVitalSignToString(data.batimento || data.heartRate)\n            };\n            console.log('📊 Dados vitais VittalTec incluídos:', vittalTecData);\n          }\n        } catch (error) {\n          console.warn('⚠️ Erro ao processar dados VittalTec:', error);\n        }\n      }\n      // Convert questionnaire intensity and duration to strings\n      const intensidadeDorString = this.convertToStringValue(this.dadosQuestionario.intensidadeDor);\n      const duracaoSintomasString = this.convertToStringValue(this.converterTempoSintomas(this.dadosQuestionario.tempoSintomas));\n      const patientData = {\n        token: this.tokenConexao,\n        // Use the same token as SignalR\n        nome: this.dadosQuestionario.nome,\n        cpf: this.dadosQuestionario.cpf || '',\n        email: this.dadosQuestionario.email,\n        telefone: this.dadosQuestionario.telefone,\n        dataNascimento: dataNascimento,\n        sintomas: sintomas,\n        intensidadeDor: intensidadeDorString,\n        duracaoSintomas: duracaoSintomasString,\n        alergias: this.dadosQuestionario.alergias,\n        doencasPrevias: this.dadosQuestionario.doencasPrevias,\n        observacoesAdicionais: this.dadosQuestionario.observacoes,\n        queueType: 'consulta',\n        // Default queue type\n        // Include VittalTec vital signs data\n        ...vittalTecData\n      };\n      console.log('✅ Dados finais para registro:', patientData);\n      return patientData;\n    }\n    /**\n     * Converts symptom duration text to numeric value (in days)\n     */\n    converterTempoSintomas(tempoTexto) {\n      if (!tempoTexto) return undefined;\n      const texto = tempoTexto.toLowerCase();\n      if (texto.includes('hoje') || texto.includes('horas')) {\n        return 0; // Same day\n      } else if (texto.includes('ontem') || texto.includes('1 dia')) {\n        return 1;\n      } else if (texto.includes('dias')) {\n        const match = texto.match(/(\\d+)\\s*dias?/);\n        return match ? parseInt(match[1]) : undefined;\n      } else if (texto.includes('semana')) {\n        const match = texto.match(/(\\d+)\\s*semanas?/);\n        return match ? parseInt(match[1]) * 7 : 7;\n      } else if (texto.includes('mês') || texto.includes('mes')) {\n        const match = texto.match(/(\\d+)\\s*m[eê]s/);\n        return match ? parseInt(match[1]) * 30 : 30;\n      }\n      return undefined;\n    }\n    /**\n     * Helper methods for data conversion\n     */\n    convertToStringValue(value) {\n      if (value === null || value === undefined) {\n        return undefined;\n      }\n      return String(value);\n    }\n    convertVitalSignToString(value) {\n      if (value === null || value === undefined) {\n        return undefined;\n      }\n      // Handle numeric values\n      if (typeof value === 'number') {\n        return value.toString();\n      }\n      // Handle string values\n      if (typeof value === 'string') {\n        return value.trim() || undefined;\n      }\n      return String(value);\n    }\n    convertTemperatureToString(temperature) {\n      if (temperature === null || temperature === undefined) {\n        return undefined;\n      }\n      try {\n        let tempValue;\n        if (typeof temperature === 'string') {\n          tempValue = parseFloat(temperature);\n        } else if (typeof temperature === 'number') {\n          tempValue = temperature;\n        } else {\n          return undefined;\n        }\n        if (isNaN(tempValue)) {\n          return undefined;\n        }\n        // Convert to tenths (e.g., 36.5°C becomes \"365\")\n        return Math.round(tempValue * 10).toString();\n      } catch (error) {\n        console.warn('⚠️ Erro ao converter temperatura:', error);\n        return undefined;\n      }\n    }\n    /**\n     * Loads VittalTec data from encrypted localStorage if available\n     */\n    carregarDadosVittalTec() {\n      try {\n        let dadosVittalTec = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');\n        console.log(\"this.dadosVittalTec\", dadosVittalTec);\n        this.dadosVittalTec = dadosVittalTec;\n      } catch (error) {\n        console.error('❌ Erro geral ao carregar dados VittalTec:', error);\n        this.dadosVittalTec = null;\n      }\n    }\n    /**\n     * Opens VittalTec modal for data collection (public method for template access)\n     */\n    abrirModalVittalTec() {\n      return new Promise(resolve => {\n        const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\n          width: '600px',\n          height: '500px',\n          disableClose: true,\n          data: {}\n        });\n        dialogRef.afterClosed().subscribe(result => {\n          if (result && result.action === 'continuar' && result.data) {\n            this.dadosVittalTec = result.data;\n            console.log('📊 Dados VittalTec coletados via modal:', this.dadosVittalTec);\n          }\n          resolve(result);\n        });\n      });\n    }\n    recuperarPosicaoFila() {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this12.tokenConexao) return;\n        try {\n          const response = yield firstValueFrom(_this12.consultaService.consultarPosicaoFila(_this12.tokenConexao));\n          if (response.sucesso) {\n            _this12.PesicaoFila = response.posicaoFila;\n            _this12.iniciarMonitoramentoPosicao();\n            _this12.cdr.detectChanges();\n          } else {\n            yield _this12.cadastrarNaFilaDeslogado();\n          }\n        } catch (error) {\n          console.error('Erro ao recuperar posição:', error);\n          yield _this12.cadastrarNaFilaDeslogado();\n        }\n      })();\n    }\n    static ɵfac = function FilaEsperaComponent_Factory(t) {\n      return new (t || FilaEsperaComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SpinnerService), i0.ɵɵdirectiveInject(i3.ConsultaService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.FilaEsperaPacienteService), i0.ɵɵdirectiveInject(i6.SignalHubGuestService), i0.ɵɵdirectiveInject(i7.PatientQueueIntegrationService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FilaEsperaComponent,\n      selectors: [[\"app-fila-espera\"]],\n      hostBindings: function FilaEsperaComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"beforeunload\", function FilaEsperaComponent_beforeunload_HostBindingHandler($event) {\n            return ctx.beforeUnloadHandler($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 59,\n      vars: 2,\n      consts: [[1, \"main-container\"], [1, \"queue-card\", \"fade-in\"], [1, \"status-indicator\"], [1, \"status-dot\"], [1, \"status-text\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [1, \"user-type-indicator\"], [1, \"fas\", \"fa-user\", 2, \"margin-right\", \"8px\"], [1, \"position-container\"], [\"id\", \"queuePosition\", 2, \"display\", \"block\"], [1, \"position-label\"], [1, \"position-circle\"], [1, \"position-number\"], [\"id\", \"enterButton\", 2, \"display\", \"none\"], [\"onclick\", \"enterConsultation()\", 1, \"enter-button\"], [1, \"fas\", \"fa-video\", 2, \"margin-right\", \"8px\"], [\"id\", \"quitSection\", 2, \"display\", \"block\"], [1, \"quit-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"margin-right\", \"8px\"], [1, \"instructions-card\"], [1, \"instructions-header\"], [1, \"fas\", \"fa-info-circle\", 2, \"margin-right\", \"8px\"], [1, \"instructions-content\"], [1, \"instruction-item\"], [1, \"instruction-number\"], [1, \"instruction-text\"]],\n      template: function FilaEsperaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Sistema Online\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"h1\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"Voc\\u00EA ser\\u00E1 chamado em breve para o atendimento\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 7);\n          i0.ɵɵelement(11, \"i\", 8);\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"Acesso R\\u00E1pido\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"p\", 11);\n          i0.ɵɵtext(17, \"Sua Posi\\u00E7\\u00E3o\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 12)(19, \"span\", 13);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"button\", 15);\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵtext(24, \" Entrar \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 17)(26, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function FilaEsperaComponent_Template_button_click_26_listener() {\n            return ctx.quitQueue();\n          });\n          i0.ɵɵelement(27, \"i\", 19);\n          i0.ɵɵtext(28, \" Desistir da Consulta \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\", 21);\n          i0.ɵɵelement(31, \"i\", 22);\n          i0.ɵɵtext(32, \" Instru\\u00E7\\u00F5es Importantes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24)(35, \"div\", 25);\n          i0.ɵɵtext(36, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 26);\n          i0.ɵɵtext(38, \" Feche todos os aplicativos abertos, inclusive WhatsApp, mantendo apenas o navegador aberto. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 24)(40, \"div\", 25);\n          i0.ɵɵtext(41, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 26);\n          i0.ɵɵtext(43, \" Aumente o volume do seu dispositivo e desconecte fones bluetooth. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 24)(45, \"div\", 25);\n          i0.ɵɵtext(46, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 26);\n          i0.ɵɵtext(48, \" Conecte-se a uma rede WiFi est\\u00E1vel para garantir a qualidade do atendimento. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 24)(50, \"div\", 25);\n          i0.ɵɵtext(51, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 26);\n          i0.ɵɵtext(53, \" Aceite o acesso \\u00E0 c\\u00E2mera e microfone quando solicitado pelo sistema. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 24)(55, \"div\", 25);\n          i0.ɵɵtext(56, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 26);\n          i0.ɵɵtext(58, \" Se a conex\\u00E3o falhar, acesse novamente a plataforma e solicite um novo atendimento. \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"Bem-vindo\", (ctx.dadosQuestionario == null ? null : ctx.dadosQuestionario.nome) ? \", \" + (ctx.dadosQuestionario == null ? null : ctx.dadosQuestionario.nome) : \"\", \"!\");\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.PesicaoFila);\n        }\n      },\n      dependencies: [MatInputModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, MatFormFieldModule, MatDialogModule],\n      styles: [\"@charset \\\"UTF-8\\\";*[_ngcontent-%COMP%]{box-sizing:border-box;margin:0;padding:0}.main-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#f0fff0,#e8f5e8);padding:20px;display:flex;align-items:center;justify-content:center;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.main-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:fixed;top:0;left:0;width:100%;height:100%;background-image:radial-gradient(circle at 25% 25%,rgba(0,168,107,.03) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(76,175,80,.03) 0%,transparent 50%);pointer-events:none;z-index:-1}.queue-card[_ngcontent-%COMP%]{background:#fff;border-radius:24px;box-shadow:0 10px 30px #00a86b1a;padding:40px;width:100%;max-width:70vmax;position:relative;overflow:hidden;transition:all .3s ease}.queue-card[_ngcontent-%COMP%]:hover{box-shadow:0 15px 40px #00a86b33;transform:translateY(-2px)}.queue-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,#00a86b,#4caf50,#00c851)}.fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin-bottom:30px;padding:12px 20px;background:linear-gradient(135deg,#00a86b1a,#4caf501a);border-radius:50px;border:1px solid rgba(0,168,107,.2)}.status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{width:12px;height:12px;background:#00c851;border-radius:50%;margin-right:10px;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 0 #00c851b3}.status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#006b47;font-weight:600;font-size:14px;letter-spacing:.5px}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #00c851b3}70%{box-shadow:0 0 0 10px #00c85100}to{box-shadow:0 0 #00c85100}}.welcome-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:#006b47;text-align:center;margin-bottom:10px;background:linear-gradient(135deg,#00a86b,#4caf50);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.welcome-subtitle[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;text-align:center;margin-bottom:40px;font-weight:400;line-height:1.5}.position-container[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px}#queuePosition[_ngcontent-%COMP%]   .position-label[_ngcontent-%COMP%]{font-size:1rem;color:#006b47;font-weight:600;margin-bottom:20px;text-transform:uppercase;letter-spacing:1px}#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;background:linear-gradient(135deg,#00a86b,#4caf50);display:flex;align-items:center;justify-content:center;margin:0 auto;box-shadow:0 8px 25px #00a86b4d;position:relative}#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:3px;border-radius:50%;background:#fff}#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%]   .position-number[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:800;color:#00a86b;z-index:1;position:relative}.enter-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00c851,#00a86b);color:#fff;border:none;padding:18px 40px;font-size:1.2rem;font-weight:600;border-radius:50px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;margin:0 auto;box-shadow:0 6px 20px #00c8514d;text-transform:uppercase;letter-spacing:.5px}.enter-button[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 10px 30px #00c85166;background:linear-gradient(135deg,#00a86b,#00c851)}.enter-button[_ngcontent-%COMP%]:active{transform:translateY(-1px)}.enter-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.3rem}.quit-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a5a);color:#fff;border:none;padding:12px 30px;font-size:1rem;font-weight:500;border-radius:50px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;margin:0 auto 30px;box-shadow:0 4px 15px #ff6b6b4d}.quit-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #ff6b6b66;background:linear-gradient(135deg,#ee5a5a,#ff6b6b)}.quit-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.instructions-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00a86b0d,#4caf500d);border-radius:16px;padding:25px;border:1px solid rgba(0,168,107,.1);position:relative}.instructions-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:2px;background:linear-gradient(90deg,#00a86b,#4caf50);border-radius:16px 16px 0 0}.instructions-header[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:1.2rem;font-weight:700;color:#006b47;margin-bottom:20px}.instructions-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#00a86b;font-size:1.3rem}.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:20px}.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]   .instruction-number[_ngcontent-%COMP%]{width:32px;height:32px;background:linear-gradient(135deg,#00a86b,#4caf50);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:.9rem;margin-right:15px;flex-shrink:0;box-shadow:0 3px 10px #00a86b33}.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%]{color:#555;line-height:1.6;font-size:.95rem;padding-top:4px}@media (max-width: 768px){.main-container[_ngcontent-%COMP%]{padding:15px}.queue-card[_ngcontent-%COMP%]{padding:30px 20px;border-radius:16px}.welcome-title[_ngcontent-%COMP%]{font-size:2rem}.welcome-subtitle[_ngcontent-%COMP%]{font-size:1rem}.position-circle[_ngcontent-%COMP%]{width:100px!important;height:100px!important}.position-circle[_ngcontent-%COMP%]   .position-number[_ngcontent-%COMP%]{font-size:2rem!important}.enter-button[_ngcontent-%COMP%]{padding:15px 30px;font-size:1.1rem}.instructions-card[_ngcontent-%COMP%]{padding:20px}.instruction-item[_ngcontent-%COMP%]   .instruction-number[_ngcontent-%COMP%]{width:28px!important;height:28px!important;font-size:.8rem!important;margin-right:12px!important}.instruction-item[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%]{font-size:.9rem!important}}@media (max-width: 480px){.welcome-title[_ngcontent-%COMP%]{font-size:1.8rem}.queue-card[_ngcontent-%COMP%]{padding:25px 15px}.enter-button[_ngcontent-%COMP%]{padding:12px 25px;font-size:1rem}}.questionnaire-summary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9ff,#e8f2ff);border-radius:16px;padding:20px;margin:25px 0;border:1px solid rgba(0,168,107,.1);box-shadow:0 4px 15px #00a86b0d}.questionnaire-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:600;font-size:1.1rem;color:#006b47;margin-bottom:15px;padding-bottom:10px;border-bottom:2px solid rgba(0,168,107,.1)}.questionnaire-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#00a86b;font-size:1.2rem}.questionnaire-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]{display:grid;gap:12px}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;background:#ffffffb3;border-radius:8px;border-left:3px solid #00a86b}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%]{font-weight:500;color:#555;font-size:.9rem}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%]{font-weight:600;color:#006b47;font-size:.95rem;text-align:right;max-width:60%;word-wrap:break-word}.user-type-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background:#00a86b1a;border-radius:20px;padding:8px 16px;margin:15px 0;font-size:.9rem;font-weight:500;color:#006b47}.user-type-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#00a86b}@media (max-width: 768px){.questionnaire-summary[_ngcontent-%COMP%]{margin:20px 0;padding:15px}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:5px}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%]{font-size:.8rem}.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%]{font-size:.9rem;max-width:100%}.user-type-indicator[_ngcontent-%COMP%]{font-size:.8rem;padding:6px 12px}}\"]\n    });\n  }\n  return FilaEsperaComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
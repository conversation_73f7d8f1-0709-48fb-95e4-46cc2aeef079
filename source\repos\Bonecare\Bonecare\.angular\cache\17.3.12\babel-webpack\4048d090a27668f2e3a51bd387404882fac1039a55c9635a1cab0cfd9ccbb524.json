{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let VoiceRecorderService = /*#__PURE__*/(() => {\n  class VoiceRecorderService {\n    recognition;\n    audioContext = null;\n    analyser = null;\n    microphone = null;\n    mediaStream = null;\n    isRecording = false;\n    silenceTimer;\n    speechDetected = false;\n    lastSpeechTime = 0;\n    ambientNoiseLevel = 0;\n    speechThreshold = 0.02;\n    silenceThreshold = 0.01;\n    calibrationComplete = false;\n    resultSubject = new Subject();\n    errorSubject = new Subject();\n    recordingSubject = new BehaviorSubject(false);\n    recordingEventSubject = new Subject();\n    result$ = this.resultSubject.asObservable();\n    error$ = this.errorSubject.asObservable();\n    recording$ = this.recordingSubject.asObservable();\n    recordingEvent$ = this.recordingEventSubject.asObservable();\n    constructor() {\n      this.initializeSpeechRecognition();\n    }\n    //#region Initialization\n    initializeSpeechRecognition() {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      if (!SpeechRecognition) {\n        this.errorSubject.next('Reconhecimento de voz não suportado neste navegador');\n        return;\n      }\n      this.recognition = new SpeechRecognition();\n      this.configureRecognition();\n      this.setupRecognitionEvents();\n    }\n    configureRecognition() {\n      this.recognition.continuous = true;\n      this.recognition.interimResults = true;\n      this.recognition.lang = 'pt-BR';\n      this.recognition.maxAlternatives = 3;\n      // Configurações adicionais para melhorar reconhecimento de números\n      try {\n        const SpeechGrammarList = window.SpeechGrammarList || window.webkitSpeechGrammarList;\n        if (SpeechGrammarList && this.recognition.grammars !== undefined) {\n          const speechRecognitionList = new SpeechGrammarList();\n          // Grammar para números (CPF, telefone, etc.)\n          const grammar = '#JSGF V1.0; grammar numbers; public <number> = <digit>+; <digit> = zero | um | dois | três | quatro | cinco | seis | sete | oito | nove;';\n          speechRecognitionList.addFromString(grammar, 1);\n          this.recognition.grammars = speechRecognitionList;\n          console.log('✅ Grammar configurada para melhor reconhecimento de números');\n        } else {\n          console.log('⚠️ SpeechGrammarList não disponível neste navegador');\n        }\n      } catch (error) {\n        console.log('⚠️ Erro ao configurar grammar, continuando sem ela:', error);\n      }\n    }\n    initializeAudioContext() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          _this.analyser = _this.audioContext.createAnalyser();\n          _this.analyser.fftSize = 256;\n          _this.analyser.smoothingTimeConstant = 0.8;\n          _this.mediaStream = yield navigator.mediaDevices.getUserMedia({\n            audio: {\n              echoCancellation: true,\n              noiseSuppression: true,\n              autoGainControl: true,\n              sampleRate: 44100,\n              channelCount: 1\n            }\n          });\n          _this.microphone = _this.audioContext.createMediaStreamSource(_this.mediaStream);\n          _this.microphone.connect(_this.analyser);\n          yield _this.calibrateAmbientNoise();\n        } catch (error) {\n          _this.errorSubject.next('Erro ao acessar microfone');\n        }\n      })();\n    }\n    calibrateAmbientNoise() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.analyser) return;\n        const bufferLength = _this2.analyser.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        const samples = [];\n        for (let i = 0; i < 30; i++) {\n          _this2.analyser.getByteFrequencyData(dataArray);\n          const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;\n          samples.push(average / 255);\n          yield new Promise(resolve => setTimeout(resolve, 100));\n        }\n        _this2.ambientNoiseLevel = samples.reduce((sum, sample) => sum + sample, 0) / samples.length;\n        _this2.speechThreshold = Math.max(_this2.ambientNoiseLevel * 3, 0.02);\n        _this2.silenceThreshold = Math.max(_this2.ambientNoiseLevel * 1.5, 0.01);\n        _this2.calibrationComplete = true;\n      })();\n    }\n    setupRecognitionEvents() {\n      this.recognition.onstart = () => {\n        this.isRecording = true;\n        this.speechDetected = false;\n        this.lastSpeechTime = 0;\n        this.recordingSubject.next(true);\n        this.emitEvent('started');\n        this.startAudioMonitoring();\n      };\n      this.recognition.onresult = event => {\n        this.handleRecognitionResult(event);\n      };\n      this.recognition.onerror = event => {\n        this.handleRecognitionError(event);\n      };\n      this.recognition.onend = () => {\n        this.handleRecognitionEnd();\n      };\n      this.recognition.onspeechstart = () => {\n        this.handleSpeechStart();\n      };\n      this.recognition.onspeechend = () => {\n        this.handleSpeechEnd();\n      };\n    }\n    //#endregion\n    //#region Audio Monitoring\n    startAudioMonitoring() {\n      if (!this.analyser || !this.calibrationComplete) return;\n      const bufferLength = this.analyser.frequencyBinCount;\n      const dataArray = new Uint8Array(bufferLength);\n      const monitor = () => {\n        if (!this.isRecording) return;\n        this.analyser.getByteFrequencyData(dataArray);\n        const currentLevel = this.calculateAudioLevel(dataArray);\n        this.processAudioLevel(currentLevel);\n        requestAnimationFrame(monitor);\n      };\n      monitor();\n    }\n    calculateAudioLevel(dataArray) {\n      const weightedSum = dataArray.reduce((sum, value, index) => {\n        const frequency = index / dataArray.length * (this.audioContext.sampleRate / 2);\n        const weight = this.getFrequencyWeight(frequency);\n        return sum + value * weight;\n      }, 0);\n      return weightedSum / (dataArray.length * 255);\n    }\n    getFrequencyWeight(frequency) {\n      if (frequency < 300) return 0.3;\n      if (frequency < 3000) return 1.0;\n      if (frequency < 8000) return 0.7;\n      return 0.2;\n    }\n    processAudioLevel(currentLevel) {\n      const now = Date.now();\n      const isSpeech = currentLevel > this.speechThreshold;\n      const isAmbientNoise = currentLevel > this.silenceThreshold && currentLevel <= this.speechThreshold;\n      if (isSpeech) {\n        this.lastSpeechTime = now;\n        if (!this.speechDetected) {\n          this.speechDetected = true;\n          this.clearSilenceTimer();\n          this.emitEvent('speech_detected');\n        }\n      } else if (isAmbientNoise) {\n        this.emitEvent('ambient_noise_detected', {\n          level: currentLevel\n        });\n      }\n      if (this.speechDetected && now - this.lastSpeechTime > 3000) {\n        this.startSilenceTimer();\n      }\n    }\n    //#endregion\n    //#region Event Handlers\n    handleSpeechStart() {\n      this.speechDetected = true;\n      this.lastSpeechTime = Date.now();\n      this.clearSilenceTimer();\n      this.emitEvent('speech_detected');\n    }\n    handleSpeechEnd() {\n      this.startSilenceTimer();\n      this.emitEvent('silence_detected');\n    }\n    handleRecognitionResult(event) {\n      let finalTranscript = '';\n      let bestConfidence = 0;\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        if (result.isFinal) {\n          const transcript = result[0].transcript;\n          const confidence = this.calculateBestConfidence(result);\n          if (confidence > bestConfidence) {\n            finalTranscript = transcript;\n            bestConfidence = confidence;\n          }\n        }\n      }\n      if (finalTranscript.trim()) {\n        const cleanText = this.cleanTranscript(finalTranscript);\n        console.log('🎤 Transcript recebido:', finalTranscript);\n        console.log('🧹 Texto limpo:', cleanText);\n        console.log('📊 Confiança:', bestConfidence);\n        console.log('✅ Válido:', this.isValidTranscript(cleanText, bestConfidence));\n        if (this.isValidTranscript(cleanText, bestConfidence)) {\n          this.resultSubject.next({\n            text: cleanText,\n            confidence: bestConfidence,\n            success: true\n          });\n          this.stopRecording();\n        } else {\n          console.log('❌ Transcript rejeitado - não passou na validação');\n        }\n      }\n    }\n    handleRecognitionError(event) {\n      this.resetState();\n      const errorMessages = {\n        'no-speech': 'Nenhuma fala detectada. Fale mais próximo do microfone.',\n        'audio-capture': 'Erro no microfone. Verifique as configurações de áudio.',\n        'not-allowed': 'Permissão negada. Permita o acesso ao microfone.',\n        'network': 'Erro de conexão. Verifique sua internet.',\n        'service-not-allowed': 'Serviço de reconhecimento indisponível.',\n        'aborted': 'Reconhecimento cancelado.'\n      };\n      const errorMessage = errorMessages[event.error] || 'Erro no reconhecimento de voz. Tente novamente.';\n      if (event.error !== 'aborted') {\n        this.errorSubject.next(errorMessage);\n        this.resultSubject.next({\n          text: '',\n          confidence: 0,\n          success: false,\n          error: errorMessage\n        });\n      }\n    }\n    handleRecognitionEnd() {\n      if (this.isRecording) {\n        this.resetState();\n        this.emitEvent('ended_automatically');\n      }\n    }\n    //#endregion\n    //#region Audio Quality Enhancement\n    calculateBestConfidence(result) {\n      let totalConfidence = 0;\n      const alternatives = Math.min(result.length, 3);\n      for (let i = 0; i < alternatives; i++) {\n        totalConfidence += result[i].confidence || 0.5;\n      }\n      return totalConfidence / alternatives;\n    }\n    cleanTranscript(text) {\n      return text.trim().replace(/\\s+/g, ' ').replace(/[^\\w\\sáàâãéèêíìîóòôõúùûç.,!?-]/gi, '').toLowerCase();\n    }\n    isValidTranscript(text, confidence) {\n      const minLength = 3;\n      const minConfidence = 0.4;\n      // Aceita palavras com letras OU sequências de números\n      const hasValidWords = /[a-záàâãéèêíìîóòôõúùûç]{3,}/i.test(text);\n      const hasValidNumbers = /\\d{3,}/i.test(text);\n      const hasValidContent = hasValidWords || hasValidNumbers;\n      // Conta palavras (incluindo números) com mais de 2 caracteres\n      const wordCount = text.split(/\\s+/).filter(word => word.length > 2).length;\n      // Para números longos (como CPF), aceita mesmo com confiança menor\n      const isLongNumber = /^\\d{8,}$/.test(text.replace(/\\s/g, ''));\n      const adjustedMinConfidence = isLongNumber ? 0.3 : minConfidence;\n      console.log('🔍 Validação transcript:', {\n        text,\n        length: text.length,\n        confidence,\n        hasValidWords,\n        hasValidNumbers,\n        hasValidContent,\n        wordCount,\n        isLongNumber,\n        adjustedMinConfidence,\n        lengthOk: text.length >= minLength,\n        confidenceOk: confidence >= adjustedMinConfidence,\n        contentOk: hasValidContent,\n        wordCountOk: wordCount >= 1\n      });\n      return text.length >= minLength && confidence >= adjustedMinConfidence && hasValidContent && wordCount >= 1;\n    }\n    //#endregion\n    //#region Silence Detection\n    startSilenceTimer() {\n      this.clearSilenceTimer();\n      this.silenceTimer = setTimeout(() => {\n        if (this.isRecording && this.speechDetected) {\n          const timeSinceLastSpeech = Date.now() - this.lastSpeechTime;\n          if (timeSinceLastSpeech >= 3000) {\n            this.stopRecording();\n          }\n        }\n      }, 3500);\n    }\n    clearSilenceTimer() {\n      if (this.silenceTimer) {\n        clearTimeout(this.silenceTimer);\n        this.silenceTimer = null;\n      }\n    }\n    //#endregion\n    //#region Public Methods\n    startRecording() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.recognition) {\n          _this3.errorSubject.next('Reconhecimento de voz não disponível');\n          return false;\n        }\n        if (_this3.isRecording) {\n          return true;\n        }\n        try {\n          if (!_this3.audioContext) {\n            yield _this3.initializeAudioContext();\n          }\n          _this3.recognition.start();\n          return true;\n        } catch (error) {\n          if (error.name === 'InvalidStateError') {\n            _this3.isRecording = true;\n            _this3.recordingSubject.next(true);\n            return true;\n          }\n          _this3.errorSubject.next('Erro ao iniciar gravação');\n          return false;\n        }\n      })();\n    }\n    stopRecording() {\n      if (!this.isRecording) return;\n      try {\n        this.recognition?.stop();\n        this.resetState();\n        this.emitEvent('stopped');\n      } catch (error) {\n        this.resetState();\n      }\n    }\n    forceStop() {\n      try {\n        this.recognition?.abort();\n      } catch (error) {\n        // Ignore errors\n      }\n      this.resetState();\n    }\n    isCurrentlyRecording() {\n      return this.isRecording;\n    }\n    isSupported() {\n      return !!this.recognition;\n    }\n    restartRecording() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        _this4.stopRecording();\n        yield new Promise(resolve => setTimeout(resolve, 100));\n        return _this4.startRecording();\n      })();\n    }\n    getAmbientNoiseLevel() {\n      return this.ambientNoiseLevel;\n    }\n    getSpeechThreshold() {\n      return this.speechThreshold;\n    }\n    //#endregion\n    //#region Helper Methods\n    resetState() {\n      this.isRecording = false;\n      this.speechDetected = false;\n      this.lastSpeechTime = 0;\n      this.recordingSubject.next(false);\n      this.clearSilenceTimer();\n      this.cleanupAudioResources();\n    }\n    cleanupAudioResources() {\n      if (this.mediaStream) {\n        this.mediaStream.getTracks().forEach(track => track.stop());\n        this.mediaStream = null;\n      }\n      if (this.microphone) {\n        this.microphone.disconnect();\n        this.microphone = null;\n      }\n      if (this.audioContext && this.audioContext.state !== 'closed') {\n        this.audioContext.close();\n        this.audioContext = null;\n      }\n      this.analyser = null;\n      this.calibrationComplete = false;\n    }\n    emitEvent(type, data) {\n      this.recordingEventSubject.next({\n        type,\n        timestamp: Date.now(),\n        data\n      });\n    }\n    static ɵfac = function VoiceRecorderService_Factory(t) {\n      return new (t || VoiceRecorderService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VoiceRecorderService,\n      factory: VoiceRecorderService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return VoiceRecorderService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
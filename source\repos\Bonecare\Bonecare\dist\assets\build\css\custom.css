* {
    margin: 0;
    padding: 0;
    list-style: none;
    scrollbar-width: thin;
}

tr {
    cursor: unset !important;
}
 /* .cdk-overlay-pane  {
    margin-top: 30px !important;
} */

.modal-avaliacao {
    justify-content: center;
    text-align: center;
}
.modal-avaliacao .nsm-content {
    width: 500px;
    padding: 5px !important;
    text-align: center;
    justify-content: center;
}
.modal-avaliacao .input-align {
    margin-right: 0px !important;
}

body {
    min-width: 100%;
    width: auto;
    height: auto;
    min-height: 100%;
    font-family: "Roboto", sans-serif;
}

.buttons-mobilet {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}

.buttons-lateral {
    margin-right: 5px !important;
    margin-left: 5px !important;
}

.lateral {
    position: absolute;
    right: 0;
    bottom: 0;
    margin-right: auto;
    display: flex;
}

.grid-buttons {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 60px;
}



.mother-div {
    height: 87vh;
}


.overlay {
    overflow-x: hidden;
    overflow-y: hidden;
    height: 100vh;
    width: 100vw;
}

.aviso-span {
    margin-left: 17px !important;
}

.title {
    font-size: 25px;
    font-weight: 600;
    color: #321a1a;
}

.img-card {
    width: 100%;
    border-radius: 50%;
}

.required {
    font-size: 65%;
    color: #f44336;
    font-weight: 600;
    display: flex;
    /* margin-top: -11px; */
    margin-left: 15px;
}

.align-name {
    margin-left: 25px;
}

.hidden {
    visibility: hidden;
}

.p-10 {
    padding: 10px;
}

.mt-30 {
    margin-top: 30px;
}

.b-10 {
    bottom: 10px !important;
}

.f-r {
    float: right;
}

.f-l {
    float: left;
}

.m-t-10 {
    margin-top: 10px !important;
}

.m-t-20 {
    margin-top: 20px !important;
}

.m-b-10 {
    margin-bottom: 10px !important;
}

.m-b-20 {
    margin-bottom: 20px !important;
}

.mother-div {
    margin-left: 20px;
    z-index: unset;
}

.m-r-10 {
    margin-right: 10px !important;
}

.m-l-10 {
    margin-left: 10px !important;
}

.truncate {
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100px;
    overflow: hidden;
    margin-bottom: -5px;
}

.l-truncate {
    width: 50px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: -5px;
}

.p-t-10 {
    padding-top: 10px !important;
}

.p-t-20 {
    padding-top: 20px !important;
}

.p-b-10 {
    padding-bottom: 10px !important;
}

.p-b-20 {
    padding-bottom: 20px !important;
}

.p-b-50 {
    padding-bottom: 50px !important;
}

.p-l-10 {
    padding-left: 10px !important;
}

.fw-500 {
    font-weight: 500;
}

.p-t-30 {
    padding-top: 30px;
}

.p-20 {
    padding: 20px;
}

.fw-600 {
    font-weight: 600;
}

.fw-700 {
    font-weight: 700;
}

.align-center {
    display: flex;
    justify-content: center;
}

.w-10 {
    width: 10%;
}

.w-15 {
    width: 15%;
}

.w-20 {
    width: 20%;
}
/* Swiper  */

.swiper-container {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    font-size: 18px;
    height: auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 !important;
}
/*Meu grid pessoal */
/* FLIP CSS */

.flip-container {
    perspective: 1000px;
}
/* flip the pane when hovered */

.flip-container:hover .flipper,
.flip-container.hover .flipper {
    transform: rotateY(180deg);
}

.flip-container,
.front,
.back {
    /* background: white; */
    /* height: auto; */
    height: 130px;
}
/* flip speed goes here */

.flipper {
    transition: 0.6s;
    transform-style: preserve-3d;
    position: relative;
}
/* hide back of pane during swap */

.front,
.back {
    backface-visibility: hidden;
    padding-top: 20px;
    position: absolute;
    top: 0;
    left: 0;
}
/* front pane, placed above back */

.front {
    z-index: 2;
    width: 100%;
    transform: rotateY(0deg);
    height: auto;
}

.back {
    width: 100%;
    transform: rotateY(180deg);
    height: auto;
    /* background: #000; */
}
/* Fim flip */

.smokewhitecolor {
    color: rgba(0, 0, 0, 0.54);
    font-size: 15px;
}

.title-modal {
    border-bottom: 1px solid #9096b1;
    text-align: center;
}

.cols {
    float: left;
}

.cols-12 {
    width: 8.33%;
}

.cols-12-5x {
    width: 41.65%;
}

.cols-6 {
    width: 16.66%;
}

.cols-6-3x {
    width: 50%;
}

.cols-6-5x {
    width: 83.3%;
}

.cols-3 {
    width: 33.33%;
}

.cols-3-2x {
    width: 66.64%;
}

.container-fluid {
    width: 97% !important;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    margin-top: 10px;
}

.acoes {
    width: 20%;
}

.content-size {
    word-wrap: break-word;
}

.mom-container {
    z-index: 2;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}
/* Color Font  */

.alert {
    color: #ff9800 !important;
    font-weight: 600;
}

.alert-icon {
    color: #ff9800;
}

.success {
    color: #8bc34a !important;
    font-weight: 600;
}

.success1 {
    color: #36bf00 !important;
}

.danger {
    color: #dc3545 !important;
    font-weight: 600;
}

.h-doc {
    height: 400px;
}

.primary {
    color: #0f86ff !important;
}

.green-success {
    color: #0ad07c;
}
/* Fim color font  */

.btn-danger {
    background: #e90909 !important;
    color: #fff !important;
}

.btn-success {
    background: #0ad07c !important;
}

.btn-alert {
    background-color: #ff9800 !important;
}

.text-area-doc {
    min-height: 50vh;
    max-height: 50vh;
}

.align-btn {
    margin-right: 10px;
    margin-left: 10px;
}

.icon-danger {
    color: #e90909 !important;
}

.ngx-gallery-icons-wrapper .ngx-gallery-icon {
    position: relative;
    margin-right: 5px;
    margin-top: 5px;
    font-size: 20px;
    color: #f90909e0 !important;
    cursor: pointer;
}
/* Email modal  */
/* Background Modal  */

.emailmodal .nsm-content {
    padding: 0 !important;
}
.nsm-content {
    border-radius: 30px !important;
    top: 30px;
}
.emailmodalAgenda {
    overflow-y: visible !important;
}
.emailmodalInfo {
    height: 108%;
}
.Button-sheet .nsm-content {
    width: 80% !important;
}

.label_paciente {
    margin-left: 5px;
    word-break: break-all;
}
/* .Button-sheet .nsm-overlay-open{

} */

.form-modal .nsm-content {
    padding: 0 !important;
}

.pagamento-modal .nsm-content {
    padding: 0 !important;
}

.p-b-5 {
    padding-bottom: 5px;
}

.title-filter {
    font-size: 20px;
    width: 175px;
    text-align: left;
    padding-left: 12px;
}

.form-modal .nsm-dialog-btn-close {
    border: 0;
    height: 30px;
    width: 30px;
    background: white;
    color: #2d2d2d;
    position: absolute;
    top: -40px;
    right: 0px;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
    
}

.medium-modal .nsm-dialog-btn-close {
    border: 0;
    height: 30px;
    width: 30px;
    background: white;
    color: #2d2d2d;
    position: absolute;
    top: -40px;
    right: 0px;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
    
}

.nsm-dialog-btn-close img {
    width: 12px;
}

.emailmodal .nsm-dialog-btn-close {
    border: 0;
    height: 35px;
    width: 35px;
    background: white;
    color: #2d2d2d;
    position: absolute;
    top: -40px;
    right: 0px;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(34, 33, 33, 0.14), 0 1px 0px 0 rgba(22, 22, 22, 0.5);
    pointer-events: none;
    display: none;
}

.mat-drawer-inner-container {
    width: 100%;
    height: 100%;
    overflow: auto !important;
    /* -webkit-overflow-scrolling: touch; */
}
/* .mat-drawer-inner-container::-webkit-scrollbar-thumb {
	background-color: #fff;
	border-radius: 10px;  
}
.mat-drawer-inner-container::-webkit-scrollbar {
	border-radius: 10px;
  width: 1px;
} */

@media (max-width: 800px) {
    .nsm-dialog-btn-close{
        margin-top: -6px;
        margin-right: 12px;
    }
    /*.emailmodal .nsm-dialog-btn-close {
        top: -95px;
    }
    .container-life .emailmodal .nsm-dialog-btn-close {
        top: -40px !important;
    }
    .modal-agenda-horario .nsm-dialog-btn-close {
        top: -30px !important;
    }*/
    
}

.title-modal {
    text-transform: uppercase;
    padding-bottom: 20px;
}

.emailmodal {
    
    justify-content: center;
    align-items: center;
    display: flex;
    border-radius: 15px;
}

.spacer-icon {
    margin-right: 8px;
}

.form-modal {
    /*max-width: 1000px !important;*/
    margin-top: 55px !important;
}

.modal-info {
    padding: 15px;
    line-height: 20px;
    font-size: 1rem;
    color: #666;
    text-align: center;
}

.background-email {
    background-image: url(/assets/build/img/background.png) !important;
    background-size: 572px;
    background-position: -35px -70px;
    background-repeat: no-repeat;
}

.background-Iniciar {
    background-image: url(/assets/build/img/background-primary.png) !important;
    background-size: 525px;
    background-position: bottom center;
    background-repeat: no-repeat;
    height: 300px;
    width: 460px;
}

.background-cliente {
    background-size: 553px !important;
    background-image: url(/assets/build/img/background-primary.png) !important;
    background-size: 430px;
    background-position: bottom center;
    background-repeat: no-repeat;
    height: 208px;
    width: 405px;
}

.background-vacation {
    background-image: url(/assets/build/img/background-yellow.png) !important;
    background-size: 612px;
    background-position: bottom center;
    background-repeat: no-repeat;
    height: 300px;
    width: 520px;
}

.big-btn {
    margin-left: 10px;
    width: 70%;
    border-radius: 20px !important;
    text-transform: uppercase;
    padding: 2px;
    font-size: 1rem;
}



.title-layout {
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    margin-top: 5px;
    text-transform: uppercase;
}

.img-layout {
    /* border: 1px solid #fff; */
    margin-left: 20px;
    margin-top: 25px;
    width: 130px;
    transition: all 0.3s;
}

.img-layout:hover {
    width: 140px;
    border: 3px solid #1265b9;
    transform: scale(1);
}

.layout-active {
    border: 3px solid #126589;
}

.text-area-archives > span > label {
    margin-top: 10px;
    margin-left: 10px;
}

.small-droptext {
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 11px;
}

.streaming-drop {
    width: 400px;
    /* will-change: transform; */
    height: 275px;
    border-radius: 10px;
    background: #2f3031;
    margin-top: -200px;
    margin-left: -130px;
    left: -55px !important;
}

.form-content {
    padding-top: 20px;
    padding-bottom: 160px;
}
.mat-calendar-table {
    border-spacing: 0;
    border-collapse: inherit !important;
    width: 100%;
}
.form-agendamento {
    background: #0a86d5;
    background-image: url(/assets/build/img/Agendamento.png) !important;
    background-size: 390px;
    /* background-position: 60px 0px; */
    background-repeat: no-repeat;
    /* height: 500px; */
    background-position: top center;
    padding: 0 !important;
}

.form-avaliacao {
    background-size: 415px !important;
    background-image: url(/assets/build/img/avaliacao.png) !important;
    background-repeat: no-repeat !important;
    height: 500px;
    background-position: top center !important;
    padding: 0 !important;
    background: #0a86d5;
}

.info-background {
    position: absolute;
    bottom: 30px;
    width: 100%;
    text-align: center;
    font-weight: 600;
    color: white;
}

.background-delete {
    background-image: url(/assets/build/img/background-red.png) !important;
    /* background-size: 580px; */
    /* background-position: -27px -110px; */
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: bottom center;
    background-repeat: no-repeat;
    height: 250px;
    width: 100%;
}

.background-off {
    background-image: url(/assets/build/img/background-red.png) !important;
    background-repeat: no-repeat;
    background-position: bottom center;
    height: 170px;
    background-size: 557px;
    width: 519px;
}

.modal-info small {
    font-weight: 600;
    font-size: 15px !important;
}
/* Background Modal  */
/* Life line  */

.align-button {
    top: 40px;
    margin-left: 5.5%;
}

.align-buttonII {
    margin-left: 38px;
    top: 40px;
}

.mini-mini {
    width: 10px;
    height: 10px;
    background: #348bc1;
}

.mini-miniI {
    margin-right: 109px;
}

.mini-miniII {
    margin-right: 77px;
}

.mini-miniIII {
    margin-right: 110px;
}

.life-line-content {
    width: 50%;
    margin-left: 9px;
    margin-bottom: -30px;
    line-height: 17px;
}
/* Track */

::-webkit-scrollbar-track {
    background: #f1f1f1;
}
/* Handle */

::-webkit-scrollbar-thumb {
    background: #888;
}
/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
/* Style font */

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    margin-bottom: 0.5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
}

h4 {
    font-size: 1.25rem;
    color: #214163;
}

.Right-button {
    left: unset !important;
}

.Questions-List {
    max-width: 475px !important;
}

.anot-button {
    max-width: 693px !important;
}

.dropdown-notification-arrow {
    border-color: transparent;
    border-style: dashed dashed solid;
    border-width: 0 8.5px 8.5px;
    position: absolute;
    z-index: 1;
    height: 0;
    width: 0;
    border-bottom-color: #ccc;
    border-bottom-color: white;
    top: 59px;
    left: 93.5%;
    z-index: 2;
}

.img-dropdown img {
    width: 70px;
    height: 70px;
    border: 2px solid #fff;
    border-radius: 50%;
}

.center-item {
    margin-right: auto;
    margin-left: auto;
}

.height_min {
    min-height: 490px;
}

.history_content {
    top: 90px;
    min-width: 1000px;
    min-height: 560px !important;
}

.img-perfil-menu img {
    width: 60px;
    height: 60px;
    margin: 5px 5px;
}

.a-perfil .row {
    margin-top: 5px;
    margin-bottom: 5px;
}

.m-perfil {
    margin: 0px;
    text-align: left;
    font-size: 13px;
}

ul.menu-principal {
    height: 100%;
    float: right;
    margin: 0;
    /* width: 100%; */
    padding-top: 0px;
    display: inline-flex;
}

.menu-principal li {
    float: left;
}

.menu-principal li p {
    margin: 0;
    text-align: center !important;
    font-size: 11.5px !important;
    margin-right: 5px;
    margin-top: 15px;
    line-height: 12px;
    margin-left: 10px;
}

.menu-principal li:first-child a {
    padding: 0;
    background: 0 none;
    margin-top: -11px;
    font-size: 12px;
}

.menu-principal li .a-perfil {
    margin-top: -6px;
}

.menu-principal li a {
    font-size: 20px;
    padding: 5px 5px 0px 10px;
    color: #fff;
}

.menu-principal li a:hover {
    text-decoration: none;
    background: none;
    color: #fff;
}

.a-perfil .row {
    margin-top: 5px;
    margin-bottom: 5px;
}

.little-align {
    left: 130px !important;
}

.notification-img {
    width: 100%;
}

ul.msg_list li .time {
    font-size: 11px;
    font-style: italic;
    font-weight: 500;
    position: absolute;
    color: #214163 !important;
    right: 35px;
}

ul.msg_list li .message {
    display: block !important;
    font-size: 11px;
}

ul.msg_list li .dropdown-name {
    font-weight: 600;
    position: absolute;
    font-size: 13px;
    color: #214163;
}
/*Adjust - item */

.icon-navbar-adjust {
    margin-top: 2px;
    padding-right: 16%;
}

.img-icon {
    top: 10%;
    width: 40%;
    position: absolute;
    right: 30%;
}

.content {
    color: rgb(72, 72, 72);
}

.input-align {
    margin-right: 10px !important;
}

.title-item {
    bottom: 6vh;
}

.button-top {
    bottom: 50px;
}

.sublinhado {
    text-decoration: underline;
}

.no-h {
    height: 0px;
}
/* Swiper */

.myCard {
    color: #666;
    font-weight: 700;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
    display: block;
    padding: 24px;
    border-radius: 2px;
    border: 1px solid #bbb;
    position: relative;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.56) 10%,
        rgb(255, 255, 255) 45%,
        rgba(210, 210, 210, 0.55) 95%
    );
}

.p-btn {
    /* padding-left: 0!important;  */
    padding-bottom: 10px !important;
}
/* Modal  */
/* Content */

.active {
    color: #fff !important;
    display: block;
    position: relative;
}
/* #5ebd6f  
#26B99A     
*/

.label {
    color: #666;
    font-weight: 700;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
    display: block;
    padding: 24px;
    border-radius: 2px;
    border: 1px solid #bbb;
    position: relative;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.56) 10%,
        rgb(255, 255, 255) 45%,
        rgba(210, 210, 210, 0.55) 95%
    );
}

.icon {
    -webkit-transition: all 0.3s linear;
    transition: all 0.3s linear;
    position: absolute;
    top: 0px;
    right: 85px;
    z-index: 0;
    font-size: 100px;
    color: rgba(0, 0, 0, 0.09);
}

.negative-button {
    right: 65%;
    bottom: 10px;
}

.plus-button {
    left: 65%;
    bottom: 10px;
}

.img-responsive {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto;
}

.w-100 {
    width: 100%;
}
/* CSS MODAL -  */

.nsm-content {
    margin: 0 !important;
}

.little-modal {
    width: 460px;
    padding: 15px;
    min-height: 100px;
}



.modal-list {
    border-bottom: 1px solid #d5d5d5;
}

.cal-header {
    background: #1265b9 !important;
    color: white !important;
    font-weight: 500 !important;
}

.little-title {
    font-size: 19px;
    color: #020e4c;
}

.modal-title {
    font-weight: 500;
    color: #020e4c;
    font-size: 30px;
}

.report-icon {
    position: relative;
    left: 37px;
    bottom: 5px;
}

.content-card {
    background-color: #3f5fb5 !important;
    color: white !important;
}

.l-text {
    font-size: 17px;
    font-weight: 600;
}

.l-card {
    padding: 40px !important;
}

.Button-sheet {
    z-index: 1044 !important;
    position: absolute !important;
    padding-left: 150px;
    padding-right: 150px;
    justify-content: center;
    min-height: 0 !important;
    max-width: 100% !important;
}

.photo-btn {
    bottom: 50px;
}

button {
    cursor: pointer;
}

.mini-img {
    border-radius: 25%;
    border: 1px solid #000;
    width: 65px;
    margin-left: 10px;
}

.icon-inicio {
    font-size: 45px;
    margin-right: 16px;
}

.header_content {
    font-size: 12px;
    color: #2b4f75;
    font-weight: 600;
}

.img-circle {
    border-radius: 50%;
    width: 75%;
}

.center-img {
    margin-left: auto;
    margin-right: auto;
}

.img-slide {
    width: 250px;
}

.app-title {
    font-size: 24px;
}

.box-title {
    font-size: 20px;
}

.box-container {
    box-sizing: border-box;
    width: 800px;
    height: 500px;
    max-width: 90%;
    max-height: 90%;
    padding: 24px;
    margin: 24px auto;
    border-radius: 4px;
    color: #555;
    background-color: #eee;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18), 0 6px 6px rgba(0, 0, 0, 0.25);
}

.info-container {
    padding: 12px 16px;
    line-height: 24px;
}

.action-container,
.content-container {
    position: relative;
    margin: 8px 16px;
    border-radius: 4px;
    background-color: #fff;
}

.action-container {
    padding: 16px;
}

.vertical-container,
.horizontal-container {
    min-height: 0 !important;
}

.action-button {
    box-sizing: border-box;
    width: calc(100% - 16px);
    min-height: 35px;
    padding: 4px 16px;
    margin: 8px;
    border: 1px solid #555;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    line-height: 14px;
    text-align: center;
}

.action-button:hover {
    color: #fff;
    background-color: #555;
}

.mat-content {
    flex: unset !important;
}

.hovered-item {
    cursor: pointer;
}

iframe {
    width: 100% !important;
    height: 91vh !important;
    @media (max-width: 1000px) {
        width: 87% !important;
        margin-left: 13% !important;
        height: 100% !important;
    }
}

.loc-button {
    max-width: 379px !important;
    /* height: 91%; */
    background: #fff;
}

.black-h {
    background: rgba(41, 45, 56, 0.97);
}

.swiper-container {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.swiper-slide {
    color: #aaa;
    background-color: #eee;
}

.swiper-slide-active {
    color: #fff;
    background-color: #aaa;
}

.title-card {
    font-size: 15px;
}

.h-disc {
    font-size: 12px;
    color: #2b4f75;
    font-weight: 600;
}

.table-scroller {
    overflow: auto;
    height: 90vh;
}

.position-item {
    position: inherit;
    bottom: 5px;
    margin-right: 5px;
}

.divider {
    padding-top: 50px;
}

.d-item {
    border-top: 1px solid #cbcbcb;
}

.mat-mdc-tooltip {
    z-index: 99999;
}

.gallery-modal {
    min-width: 600px;
    background: white;
    box-shadow: 0 0 0 10px white;
    position: relative;
    min-height: 540px;
}

.overlay.nsm-overlay-open {
    background: rgba(0, 0, 0, 0.7) !important;
}
/* Table Base */

table {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    font-family: arial;
    border: 1px solid #ddd !important;
}

table thead {
    background: #1265b9;
    color: white;
}

tr:hover {
    background: #60afff59;
    cursor: pointer;
}

table {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    font-family: arial;
    border: 1px solid #ddd !important;
}

.table {
    border-bottom: #ddd solid 1px;
    width: 100%;
    margin-bottom: 20px;
}

.table td:last-child {
    border-right: 0;
}

.table thead th {
    font-weight: normal;
    background-color: #2b4f75;
    color: #fff;
    font-size: 15px;
    border: 0;
}

.table {
    border-bottom: #ddd solid 1px;
    width: 100%;
    margin-bottom: 20px;
}

.table th,
.table td {
    border: 1px solid #cacaca;
    font-size: 12px;
    padding: 8px;
    line-height: 20px;
    text-align: left;
    vertical-align: middle;
}

.table td:last-child {
    border-right: 0;
}

.table thead th {
    font-weight: normal;
    background-color: #1265b9;
    color: #fff;
    font-size: 15px;
    border: 0;
}

.clickable-list {
    cursor: pointer;
    border-bottom: 1px solid #ddd;
}

.spacer-button {
    margin-right: 25px !important;
}

tr.clickable-list:hover {
    background: #2b4f752b !important;
}

.hover-underline-animation {
    display: inline-block;
    position: relative;
}

.hover-underline-animation::after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #2b4f75;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
}

.hover-underline-animation:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.img {
    width: 100%;
}

.height-table {
    max-height: 100vh;
}

.example-card {
    max-width: 400px;
}

.example-header-image {
    background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
    background-size: cover;
}

html {
    overflow-x: hidden;
}

.header {
    color: #214193;
}

.header i {
    font-size: 2em;
}

.header mat-icon {
    font-size: 2em;
}

.header label {
    font-size: 23px;
}

.icon-table {
    font-size: 17px;
}

.button-interative:hover {
    /* font-size: 34px !important; */
    margin-right: 2vh;
    transform: rotate(360deg);
}

.bold {
    font-weight: 700;
}

.baloon-alert {
    padding: 20px;
    background: #ffcbc8;
    border-radius: 20px;
    width: 100%;
    margin-bottom: 20px;
}

.modal-header {
    padding: 25px !important;
}

.baloon-success {
    padding: 20px;
    background: #c3ffc5;
    border-radius: 20px;
    width: 75%;
    margin-bottom: 20px;
}

.concordo {
    font-weight: 700;
    color: #041677;
}

.margin-icons {
    margin-right: 2vh !important;
}

.button-top {
    bottom: 50px !important;
}

.example-card {
    max-width: 400px;
}

.example-header-image {
    background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
    background-size: cover;
}

.card-margin-mobile {
    margin-bottom: 30px;
}

.thumb_up:hover {
    color: #00d000;
}

.thumb_down:hover {
    color: #e90909;
}

.li-content {
    font-size: 2vh;
}

.icon-align {
    padding-right: 4vh;
    color: #6d6d6d;
}

.row-icon {
    padding-top: 3vh;
}

.Vistoria-sub {
    font-weight: 700;
    font-size: 15px;
    margin-bottom: 2vh;
    border-radius: 5px;
    margin-top: 2vh;
    left: 20px;
}

.p-b-a-t {
    padding-bottom: 0px;
    padding-top: 0px;
}

.icon-title {
    background: #0f86ff;
    width: 40px !important;
    height: 40px !important;
    text-align: center;
    color: white;
    border-radius: 5px;
    padding-top: 8px;
    margin-right: 10px;
    margin-left: 10px;
}

.button-top {
    bottom: 50px !important;
}

.example-card {
    max-width: 400px;
}

.example-header-image {
    background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
    background-size: cover;
}

.dropdown-notification {
    left: -180px !important;
    width: 350px;
}

.center-img {
    margin-right: auto;
    margin-left: auto;
}

.card-margin-mobile {
    margin-bottom: 30px;
}

.thumb_up:hover {
    color: #00d000;
}

.thumb_down:hover {
    color: #e90909;
}

.li-content {
    font-size: 2vh;
}

.icon-align {
    padding-right: 4vh;
    color: #6d6d6d;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.row-icon {
    padding-top: 3vh;
}

.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-x: hidden;
    overflow-y: auto;
    transition: background-color 500ms;
    background-color: transparent;
    z-index: 999 !important;
}

.p-b-25 {
    padding-bottom: 25px;
}

.h2-color {
    color: #466280;
    font-size: 1.4rem;
    word-break: break-word;
}
/* Card mobile  */

.cal-month-view .cal-day-cell {
    touch-action: auto !important;
    /* width: 10px!important; */
}

@media (min-width: 650px) {
    .header-card {
        display: none !important;
    }
}

@media (max-width: 600px) {
    .button-filter {
        top: 2px;
        z-index: 1043 !important;
        width: 100% !important;
        max-width: unset !important;
    }
    .header-card {
        margin-top: 70px;
        margin-bottom: 20px;
    }
    .header-card .mat-mdc-card-header {
        width: 100%;
        justify-content: center;
    }
    .justify-center {
        justify-content: center;
    }
    .header-card .mat-mdc-card-subtitle {
        margin-left: 15px;
    }
    .center-content {
        text-align: center;
    }
    .img-circle {
        border-radius: 50%;
        width: 110px !important;
        height: 110px !important;
    }
    .label-paciente {
        color: #666;
        font-weight: bold;
    }
    .div_paciente {
        margin-left: 0 !important;
    }
    .div_img {
        margin-left: 0 !important;
        display: flex;
        justify-content: center;
    }
    .panel-button {
        width: 100%;
    }
    .button-card {
        text-transform: uppercase;
        color: #666 !important;
        font-weight: 600;
        padding-top: 15px;
        padding-bottom: 15px;
        border: 1px solid #666;
        padding-right: 10px;
        border-radius: 10px;
        padding-left: 10px;
    }
    .emailmodal {
        width: 75% !important;
    }
}
/* Media Querie */

.filter_content {
    top: 64px;
    z-index: 1043;
    width: 100% !important;
    max-width: unset !important;
}

@media (min-width: 1700px) and (max-width: 2000px) {
    .title-card {
        top: 2vh;
    }
    .conf-item {
        margin-right: 1.3vh;
    }
}

@media (max-width: 900px) and (min-width: 600px) {
    .title-card {
        font-size: 10px;
    }
    .content-size {
        font-size: 13px;
        font-weight: 500;
    }
    .button-interative {
        font-size: 20px !important;
    }
    .row-icon {
        padding-top: 3vh;
        padding-left: 0 !important;
        padding-right: 0 !important;
        padding-bottom: 0 !important;
    }
    .margin-icons {
        margin-right: unset !important;
    }
}

@media (min-width: 1000px) {
    .panel-content {
        margin-top: -7px;
    }
}

@media (min-width: 1600px) {
    .title-card {
        top: 2vh;
    }
}

@media (min-width: 600px) {
    .body-card {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 0px !important;
    }
}

@media (max-width: 800px) {
    .content-icon {
        padding-bottom: 0 !important;
    }
}

@media (min-width: 768px) {
    .m-t-5 {
        margin-top: 5%;
        margin-left: 5%;
        margin-right: 1%;
    }
}

@media (max-width: 2600px) and (min-width: 2000px) {
    .title-card {
        font-size: 25px;
    }
}

@media (min-width: 1440px) and (max-width: 2000px) {
    .title-card {
        font-size: 15px;
    }
}

@media (min-width: 768px) {
    .m-t-5 {
        margin-top: 5%;
        margin-left: 5%;
        margin-right: 1%;
    }
}

@media (max-width: 2000px) and (min-width: 1900px) {
    .conf-item {
        margin-right: 3vh;
    }
    .centralize-button {
        right: 50px !important;
    }
}

@media (max-width: 2400px) and (min-width: 2000px) {
    .conf-item {
        /* padding-left:53px; */
        margin-right: 6px;
    }
}

@media (min-width: 2600px) {
    .conf-item {
        margin-right: -7px;
    }
}

@media (min-width: 780px) {
    .desktop-none {
        display: none !important;
    }
}

@media (max-width: 1440px) and (min-width: 1024px) {
    .conf-item {
        margin-right: 4vh;
    }
    .centralize-button {
        right: 36px;
    }
}

@media (max-width: 768px) and (min-width: 425px) {
    .conf-item {
        margin-right: 32px;
    }
    .centralize-button {
        right: 30px;
    }
}

@media (max-width: 425px) and (min-width: 380px) {
    .conf-item {
        margin-right: 18px;
    }
    .centralize-button {
        right: 38px;
    }
    .mobile-content {
        padding-right: 10px;
        padding-left: 10px;
    }
    #spinner.backdrop {
        top: 0;
        left: 0;
        height: 20vh;
        width: 100vw;
        background-color: #f1f1f1;
        display: flex;
        align-items: center;
        justify-content: center;
        -webkit-transform: none;
        transform: none;
    }
}

@media (max-width: 350px) and (min-width: 320px) {
    .conf-item {
        margin-right: 3px;
    }
}

@media (min-width: 1920px) {
    .report-icon {
        left: 45%;
    }
    .content-card {
        margin-right: 15px;
        margin-bottom: 20px;
        margin-left: 30px;
        flex: 0 0 46% !important;
    }
}

@media (max-width: 1920px) {
    .report-icon {
        left: 45%;
    }
}

@media (max-width: 1440px) {
    .report-icon {
        position: relative;
        left: 70px;
        bottom: 5px;
    }
}

@media (max-width: 380px) {
    .little-modal {
        width: 350px;
        padding-left: 0;
        padding-right: 20px;
    }
    .report-icon {
        left: 123px;
    }
}

@media (min-width: 1200px) and (max-width: 1800px) {
    .content-card {
        background-color: #3f5fb5;
        color: white;
        flex: 0 0 46% !important;
        margin-right: 17px;
        margin-bottom: 20px;
        margin-left: 10px;
    }
}



@media (max-width: 780px) and (min-width: 430px) {
    
    .modal-title {
        font-weight: 500;
        color: #041677;
        font-size: 30px;
    }
}

@media (min-width: 750px) {
    ::-webkit-scrollbar {
        width: 5px;
    }
    /* Track */
    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }
    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }
    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    ::-ms-overflow-style {
        width: 5px;
        background: #f1f1f1;
    }
    .label {
        color: #666;
        font-weight: 700;
        box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
        display: block;
        padding: 24px;
        border-radius: 2px;
        border: 1px solid #bbb;
        position: relative;
        background: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 0.56) 10%,
            rgb(255, 255, 255) 45%,
            rgba(210, 210, 210, 0.55) 95%
        );
    }
    .icon {
        -webkit-transition: all 0.3s linear;
        transition: all 0.3s linear;
        position: absolute;
        top: 0px;
        right: 85px;
        z-index: 0;
        font-size: 100px;
        color: rgba(0, 0, 0, 0.09);
    }
    .negative-button {
        right: 65%;
        bottom: 10px;
    }
    .plus-button {
        left: 65%;
        bottom: 10px;
    }
    .img-responsive {
        display: block;
        /* max-width: 65%; */
        height: auto;
        margin-left: auto;
        margin-right: auto;
    }
    .w-100 {
        width: 100%;
    }
    /* CSS MODAL -  */
    .nsm-content {
        margin: 0 !important;
    }
    .little-modal {
        width: 460px;
        padding: 15px;
        min-height: 100px;
    }
   
    .modal-list {
        border-bottom: 1px solid #d5d5d5;
    }
    .cal-header {
        background: #1265b9 !important;
        color: white !important;
        font-weight: 500 !important;
    }
    .little-title {
        font-size: 19px;
        color: #020e4c;
    }
    .modal-title {
        font-weight: 500;
        color: #020e4c;
        font-size: 30px;
    }
    .report-icon {
        position: relative;
        left: 37px;
        bottom: 5px;
    }
    .content-card {
        background-color: #3f5fb5 !important;
        color: white !important;
    }
    .l-text {
        font-size: 17px;
        font-weight: 600;
    }
    .l-card {
        padding: 40px !important;
    }
    .Button-sheet {
        z-index: 1044 !important;
        position: absolute !important;
        padding-left: 150px;
        padding-right: 150px;
        justify-content: center;
        min-height: 0 !important;
        max-width: 100% !important;
    }
    .photo-btn {
        bottom: 50px;
    }
    button {
        cursor: pointer;
    }
    .mini-img {
        border-radius: 25%;
        border: 1px solid #000;
        width: 65px;
        margin-left: 10px;
    }
    .icon-inicio {
        font-size: 45px;
        margin-right: 16px;
    }
    .header_content {
        font-size: 12px;
        color: #2b4f75;
        font-weight: 600;
    }
    .img-circle {
        border-radius: 50%;
        width: 75%;
    }
    .center-img {
        margin-left: auto;
        margin-right: auto;
    }
    .img-slide {
        width: 250px;
    }
    .app-title {
        font-size: 24px;
    }
    .box-title {
        font-size: 20px;
    }
    .box-container {
        box-sizing: border-box;
        width: 800px;
        height: 500px;
        max-width: 90%;
        max-height: 90%;
        padding: 24px;
        margin: 24px auto;
        border-radius: 4px;
        color: #555;
        background-color: #eee;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18), 0 6px 6px rgba(0, 0, 0, 0.25);
    }
    .info-container {
        padding: 12px 16px;
        line-height: 24px;
    }
    .action-container,
    .content-container {
        position: relative;
        margin: 8px 16px;
        border-radius: 4px;
        background-color: #fff;
    }
    .action-container {
        padding: 16px;
    }
    .vertical-container,
    .horizontal-container {
        min-height: 0 !important;
    }
    .action-button {
        box-sizing: border-box;
        width: calc(100% - 16px);
        min-height: 35px;
        padding: 4px 16px;
        margin: 8px;
        border: 1px solid #555;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        line-height: 14px;
        text-align: center;
    }
    .action-button:hover {
        color: #fff;
        background-color: #555;
    }
    .mat-content {
        flex: unset !important;
        margin-bottom: -25px;
        margin-top: -25px;
    }
    .hovered-item {
        cursor: pointer;
    }
    iframe {
        width: 100% !important;
        height: 100% !important;
    }
    .loc-button {
        max-width: 379px !important;
        /* height: 91%; */
        background: #fff;
    }
    .black-h {
        background: rgba(41, 45, 56, 0.97);
    }
    .swiper-container {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }
    .swiper-slide {
        color: #aaa;
        background-color: #eee;
    }
    .swiper-slide-active {
        color: #fff;
        background-color: #aaa;
    }
    .title-card {
        font-size: 15px;
    }
    .h-disc {
        font-size: 12px;
        color: #2b4f75;
        font-weight: 600;
    }
    .table-scroller {
        overflow: auto;
        height: 90vh;
    }
    .position-item {
        position: inherit;
        bottom: 5px;
        margin-right: 5px;
    }
    .divider {
        padding-top: 50px;
    }
    .d-item {
        border-top: 1px solid #cbcbcb;
    }
    .mat-mdc-tooltip {
        z-index: 99999;
    }
    .gallery-modal {
        min-width: 600px;
        background: white;
        box-shadow: 0 0 0 10px white;
        position: relative;
        min-height: 540px;
    }
    .overlay.nsm-overlay-open {
        background: rgba(0, 0, 0, 0.7) !important;
    }
    /* Table Base */
    table {
        max-width: 100%;
        background-color: transparent;
        border-collapse: collapse;
        border-spacing: 0;
        font-family: arial;
        border: 1px solid #ddd !important;
    }
    table thead {
        background: #1265b9;
        color: white !important;
    }
    tr:hover {
        background: #60afff59;
        cursor: pointer;
    }
    table {
        max-width: 100%;
        background-color: transparent;
        border-collapse: collapse;
        border-spacing: 0;
        font-family: arial;
        border: 1px solid #ddd !important;
    }
    .table {
        border-bottom: #ddd solid 1px;
        width: 100%;
        margin-bottom: 20px;
    }
    .table td:last-child {
        border-right: 0;
    }
    .table thead th {
        font-weight: normal;
        background-color: #2b4f75;
        color: #fff;
        font-size: 15px;
        border: 0;
    }
    .table {
        border-bottom: #ddd solid 1px;
        width: 100%;
        margin-bottom: 20px;
    }
    .table th,
    .table td {
        border: 1px solid #cacaca;
        font-size: 12px;
        padding: 8px;
        line-height: 20px;
        text-align: left;
        vertical-align: middle;
    }
    .table td:last-child {
        border-right: 0;
    }
    .table thead th {
        font-weight: normal;
        background-color: #1265b9;
        color: #fff;
        font-size: 15px;
        border: 0;
    }
    .clickable-list {
        cursor: pointer;
        border-bottom: 1px solid #ddd;
    }
    .spacer-button {
        margin-right: 25px !important;
    }
    tr.clickable-list:hover {
        background: #2b4f752b !important;
    }
    .hover-underline-animation {
        display: inline-block;
        position: relative;
    }
    .hover-underline-animation::after {
        content: "";
        position: absolute;
        width: 100%;
        transform: scaleX(0);
        height: 2px;
        bottom: 0;
        left: 0;
        background-color: #2b4f75;
        transform-origin: bottom right;
        transition: transform 0.25s ease-out;
    }
    .hover-underline-animation:hover::after {
        transform: scaleX(1);
        transform-origin: bottom left;
    }
    .img {
        width: 100%;
    }
    .height-table {
        max-height: 100vh;
    }
    .example-card {
        max-width: 400px;
    }
    .example-header-image {
        background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
        background-size: cover;
    }
    html {
        overflow-x: hidden;
    }
    .header {
        color: #214193;
    }
    .header i {
        font-size: 2em;
    }
    .header mat-icon {
        font-size: 2em;
    }
    .header label {
        font-size: 23px;
    }
    .icon-table {
        font-size: 17px;
    }
    .button-interative:hover {
        /* font-size: 34px !important; */
        margin-right: 2vh;
        transform: rotate(360deg);
    }
    .bold {
        font-weight: 700;
    }
    .baloon-alert {
        padding: 20px;
        background: #ffcbc8;
        border-radius: 20px;
        width: 100%;
        margin-bottom: 20px;
    }
    .modal-header {
        padding: 25px !important;
    }
    .baloon-success {
        padding: 20px;
        background: #c3ffc5;
        border-radius: 20px;
        width: 75%;
        margin-bottom: 20px;
    }
    .concordo {
        font-weight: 700;
        color: #041677;
    }
    .margin-icons {
        margin-right: 2vh !important;
    }
    .button-top {
        bottom: 50px !important;
    }
    .example-card {
        max-width: 400px;
    }
    .example-header-image {
        background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
        background-size: cover;
    }
    .card-margin-mobile {
        margin-bottom: 30px;
    }
    .thumb_up:hover {
        color: #00d000;
    }
    .thumb_down:hover {
        color: #e90909;
    }
    .li-content {
        font-size: 2vh;
    }
    .icon-align {
        padding-right: 4vh;
        color: #6d6d6d;
    }
    .row-icon {
        padding-top: 3vh;
    }
    .Vistoria-sub {
        font-weight: 700;
        font-size: 15px;
        margin-bottom: 2vh;
        border-radius: 5px;
        margin-top: 2vh;
        left: 20px;
    }
    .p-b-a-t {
        padding-bottom: 0px;
        padding-top: 0px;
    }
    .icon-title {
        background: #0f86ff;
        width: 40px !important;
        height: 40px !important;
        text-align: center;
        color: white;
        border-radius: 5px;
        padding-top: 8px;
        margin-right: 10px;
        margin-left: 10px;
    }
    .button-top {
        bottom: 50px !important;
    }
    .example-card {
        max-width: 400px;
    }
    .example-header-image {
        background-image: url("https://material.angular.io/assets/img/examples/shiba1.jpg");
        background-size: cover;
    }
    .dropdown-notification {
        left: -180px !important;
        width: 350px;
    }
    .center-img {
        margin-right: auto;
        margin-left: auto;
    }
    .card-margin-mobile {
        margin-bottom: 30px;
    }
    .thumb_up:hover {
        color: #00d000;
    }
    .thumb_down:hover {
        color: #e90909;
    }
    .li-content {
        font-size: 2vh;
    }
    .icon-align {
        padding-right: 4vh;
        color: #6d6d6d;
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    input[type="number"] {
        -moz-appearance: textfield;
    }
    .row-icon {
        padding-top: 3vh;
    }
    .overlay {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        overflow-x: hidden;
        overflow-y: auto;
        transition: background-color 500ms;
        background-color: transparent;
        z-index: 999 !important;
    }
    .p-b-25 {
        padding-bottom: 25px;
    }
    .h2-color {
        color: #466280;
        font-size: 1.4rem;
        word-break: break-word;
    }
}
/* Card mobile  */

@media (min-width: 650px) {
    .header-card {
        display: none !important;
    }
}

@media (max-width: 600px) {
    .button-filter {
        top: 2px;
        z-index: 1043 !important;
        width: 100% !important;
        max-width: unset !important;
    }
    .header-card {
        margin-top: 70px;
        margin-bottom: 20px;
    }
    .header-card .mat-mdc-card-header {
        width: 100%;
        justify-content: center;
    }
    .justify-center {
        justify-content: center;
    }
    .header-card .mat-mdc-card-subtitle {
        margin-left: 15px;
    }
    .center-content {
        text-align: center;
    }
    .img-circle {
        border-radius: 50%;
        width: 110px !important;
        height: 110px !important;
    }
    .label-paciente {
        color: #666;
        font-weight: bold;
    }
    .div_paciente {
        margin-left: 0 !important;
    }
    .div_img {
        margin-left: 0 !important;
        display: flex;
        justify-content: center;
    }
    .panel-button {
        width: 100%;
    }
    .button-card {
        text-transform: uppercase;
        color: #666 !important;
        font-weight: 600;
        padding-top: 15px;
        padding-bottom: 15px;
        border: 1px solid #666;
        padding-right: 10px;
        border-radius: 10px;
        padding-left: 10px;
    }
}
/* Media Querie */

.filter_content {
    top: 64px;
    z-index: 1043;
    width: 100% !important;
    max-width: unset !important;
}

@media (min-width: 1700px) and (max-width: 2000px) {
    .title-card {
        top: 2vh;
    }
    .conf-item {
        margin-right: 1.3vh;
    }
}

@media (max-width: 900px) and (min-width: 600px) {
    .title-card {
        font-size: 10px;
    }
    .content-size {
        font-size: 13px;
        font-weight: 500;
    }
    .button-interative {
        font-size: 20px !important;
    }
    .row-icon {
        padding-top: 3vh;
        padding-left: 0 !important;
        padding-right: 0 !important;
        padding-bottom: 0 !important;
    }
    .margin-icons {
        margin-right: unset !important;
    }
}

@media (min-width: 1000px) {
    .panel-content {
        margin-top: -7px;
    }
}

@media (min-width: 1600px) {
    .title-card {
        top: 2vh;
    }
}

@media (min-width: 600px) {
    .body-card {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-bottom: 0px !important;
        padding-top: 0px !important;
    }
}

@media (max-width: 800px) {
    .content-icon {
        padding-bottom: 0 !important;
    }
}

@media (min-width: 768px) {
    .m-t-5 {
        margin-top: 5%;
        margin-left: 5%;
        margin-right: 1%;
    }
}

@media (max-width: 2600px) and (min-width: 2000px) {
    .title-card {
        font-size: 25px;
    }
}

@media (min-width: 1440px) and (max-width: 2000px) {
    .title-card {
        font-size: 15px;
    }
}

@media (min-width: 768px) {
    .m-t-5 {
        margin-top: 5%;
        margin-left: 5%;
        margin-right: 1%;
    }
}

@media (max-width: 2000px) and (min-width: 1900px) {
    .conf-item {
        margin-right: 3vh;
    }
    .centralize-button {
        right: 50px !important;
    }
}

@media (max-width: 2400px) and (min-width: 2000px) {
    .conf-item {
        /* padding-left:53px; */
        margin-right: 6px;
    }
}

@media (min-width: 2600px) {
    .conf-item {
        margin-right: -7px;
    }
}
/* @media (min-width: 992px) {
         .desktop-none {
             display: none!important;
         }
     } */

@media (max-width: 1440px) and (min-width: 1024px) {
    .conf-item {
        margin-right: 4vh;
    }
    .centralize-button {
        right: 36px;
    }
}
/* @media (max-width: 992px) and (max-height: 560px) {
         .no-mobile {
             display: none;
         }
     } */

@media (max-width: 768px) and (min-width: 425px) {
    .conf-item {
        margin-right: 32px;
    }
    .centralize-button {
        right: 30px;
    }
}

@media (max-width: 425px) and (min-width: 380px) {
    .conf-item {
        margin-right: 18px;
    }
    .centralize-button {
        right: 38px;
    }
    .mobile-content {
        padding-right: 10px;
        padding-left: 10px;
    }
    #spinner.backdrop {
        top: 0;
        left: 0;
        height: 20vh;
        width: 100vw;
        background-color: #f1f1f1;
        display: flex;
        align-items: center;
        justify-content: center;
        -webkit-transform: none;
        transform: none;
    }
}

@media (max-width: 350px) and (min-width: 320px) {
    .conf-item {
        margin-right: 3px;
    }
}

@media (min-width: 761px) {
    .no-desktop {
        display: none !important;
    }
}

@media (max-width: 601px) {
    .no-mobile-card {
        display: none;
    }
}
/* @media(max-width:760px) {
        .no-mobile-card {
            display: none;
            ;
        }
    } */

@media (min-width: 1920px) {
    .report-icon {
        left: 45%;
    }
    .content-card {
        margin-right: 15px;
        margin-bottom: 20px;
        margin-left: 30px;
        flex: 0 0 46% !important;
    }
}

@media (max-width: 1920px) {
    .report-icon {
        left: 45%;
    }
}

@media (max-width: 1440px) {
    .report-icon {
        position: relative;
        left: 70px;
        bottom: 5px;
    }
}

@media (max-width: 380px) {
    .little-modal {
        width: 350px;
        padding-left: 0;
        padding-right: 20px;
    }
    .report-icon {
        left: 123px;
    }
}

@media (min-width: 1200px) and (max-width: 1800px) {
    .content-card {
        background-color: #3f5fb5;
        color: white;
        flex: 0 0 46% !important;
        margin-right: 17px;
        margin-bottom: 20px;
        margin-left: 10px;
    }
}

@media (max-width: 1440px) {
    
}

@media (max-width: 780px) and (min-width: 430px) {
    
    .modal-title {
        font-weight: 500;
        color: #041677;
        font-size: 30px;
    }
}

@media (min-width: 750px) {
    ::-webkit-scrollbar {
        width: 5px;
    }
    /* Track */
    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }
    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }
    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    ::-ms-overflow-style {
        width: 5px;
        background: #f1f1f1;
    }
}

@media (max-width: 768px) and (min-width: 680px) {
    .menu-principal li {
        float: none;
    }
}

@media (max-width: 1250px) {
    .menu-principal li p {
        font-size: 9px !important;
    }
}
/* Dropdown - card responsive */

@media (max-width: 1500px) {
    .dropdown-notification-arrow {
        left: 91.2%;
    }
}

@media (max-width: 1050px) {
}

@media (max-width: 320px) {
    .panel-button {
        display: inline-flex;
    }
    .button-config {
        padding: 0 !important;
        min-width: 50px !important;
    }
}
/*Card - responsive - Inicio */

@media (max-width: 1440px) and (min-width: 1024px) {
    .mat-mdc-card-title {
        font-size: 20px !important;
    }
    .m-h-180 {
        max-height: 360px !important;
    }
    .left-modal {
        right: 80px;
        left: unset;
    }
    video {
        width: 550px !important;
        height: 300px;
    }
}

@media (min-width: 1600px) {
    .icon-list {
        font-size: 22px !important;
    }
}

@media (max-width: 1024px) and (min-width: 800px) {
    .mat-mdc-card-title {
        font-size: 13px !important;
        font-weight: 600 !important;
    }
    .vistoria-nome {
        margin-left: 8%;
    }
    .report-icon {
        left: 40px;
        bottom: 5px;
    }
    .content-card {
        background-color: #3f5fb5;
        color: white;
        flex: 0 0 48% !important;
        margin-right: 5px;
        margin-bottom: 20px;
        margin-left: 8px;
    }
    video {
        width: 515px !important;
        height: 225px;
    }
    .left-modal {
        right: 11px;
    }
    .dropdown-notification-arrow {
        left: 86.9%;
        top: 54px;
    }
}
/* Media Querie 768px  */
/* New Comment */

@media (max-width: 768px) {
    .left-modal {
        right: 85px;
    }
    /* body {
        overflow-x: hidden;
    } */
    .baloon-alert {
        margin-left: 0;
    }
    .report-icon {
        position: relative;
        left: 55px;
        bottom: 5px;
    }
    .l-card {
        padding: 5px !important;
        margin-top: 25px;
        width: 100%;
    }
    .dropdown-notification-arrow {
        left: 81.9%;
        top: 54px;
    }
    .menulateral {
        display: none;
    }
    .mobile-container {
        width: 100%;
    }
    .title-card {
        font-size: 17px;
        top: 15px;
    }
    .font-small-card {
        white-space: nowrap;
    }
    .h2-color {
        font-size: 1.1rem;
        word-break: inherit;
    }
    .w-15 {
        width: 15%;
    }
    .w-20 {
        width: 20%;
    }
    .button-top {
        bottom: 40px !important;
    }
    .title-card-imovel {
        font-size: 15px;
        position: relative;
        top: 5px;
    }
}

@media (max-width: 680px) {
    .dropdown-notification-arrow {
        left: 82.3%;
        top: 54px;
    }
    .icon-item {
        left: 410px;
    }
}

@media (min-width: 800px) {
    .just-tablet {
        display: none;
    }
}
/* Media Querie 425px */

@media (max-width: 360px) and (max-height: 640px) {
    .Vistoria-sub {
        bottom: 42vh !important;
        left: 34vh !important;
        width: auto !important;
    }
}

@media (max-width: 720px) and (min-width: 450px) {
    video {
        width: 320px !important;
        height: 100% !important;
    }
    .left-modal {
        left: 40px;
    }
    .selo-img {
        width: 30% !important;
    }
    .left-modal {
        left: 89px;
    }
    .dropdown-notification-arrow {
        left: 80.6%;
        top: 54px;
    }
    .mat-mdc-card-title {
        font-size: 21px;
    }
}

@media (max-width: 620px) and (min-width: 490px) {
    .img-circle {
        width: 30%;
        margin-left: auto !important;
        margin-right: auto;
    }
    .icon-item {
        left: 390px !important;
    }
}

@media (max-width: 420px) and (max-height: 730px) {
    .Vistoria-sub {
        border: 2px solid #2f72b7;
        width: unset !important;
        left: 39vh;
        bottom: 42vh;
    }
}

@media (max-width: 411px) and (max-height: 823px) {
    .Vistoria-sub {
        border: 2px solid #2f72b7;
        width: unset !important;
        left: 35vh;
        bottom: 37vh;
    }
}

@media (max-width: 375px) and (max-height: 667px) {
    .Vistoria-sub {
        bottom: 42vh !important;
    }
}

@media (max-width: 320px) and (min-height: 860px) {
    .Vistoria-sub {
        bottom: 30vh !important;
        left: 22vh !important;
    }
}

@media (max-width: 414px) and (min-height: 736px) {
    .Vistoria-sub {
        bottom: 42vh;
    }
}

@media (max-width: 1024px) and (min-height: 1366px) {
    .icon-align {
        padding-right: 2vh !important;
    }
    .Vistoria-sub {
        border: 2px solid #2f72b7;
        right: 0px;
        background: white;
        font-weight: 700;
        font-size: 15px;
        width: auto !important;
        margin-bottom: 2vh;
        border-radius: 5px;
        margin-top: unset;
    }
    .content-size {
        bottom: 55px;
    }
    /* .img-r-card{ max-width: 85%; height: auto; margin: 18px;} */
}

@media (max-width: 780px) {
    .no-mobile {
        display: none !important;
    }
    
    .form-modal .nsm-content {
        padding: 0 !important;
        width: 400px;
    }
}

@media (max-width: 425px) and (min-width: 320px) {
    .img-responsive {
        width: 60% !important;
    }
    .background-vacation {
        display: none !important;
    }
    .modal-info {
        width: 100%;
    }
    h4 {
        font-size: 18px;
    }
    .title-filter {
        font-size: 20px;
        width: 140px;
        text-align: left;
        padding-left: 12px;
    }
    /* .mother-div{
    margin-left: 0px;
  } */
    .dropdown-notification {
        left: -60px !important;
    }
    .ad-pp {
        display: none;
    }
    .img-circle {
        margin-left: auto;
        margin-right: auto;
        width: 40%;
    }
    .acoes {
        width: 25%;
    }
    /* .no-mobile {
             display: none !important;
         } */
    .mat-mdc-card {
        padding: 10px !important;
    }
    body {
        overflow-x: hidden;
    }
    h1 {
        font-size: 25px;
    }
    p {
        font-size: 15px;
    }
    video {
        width: 300px;
        height: 100%;
    }
    .left-modal {
        right: 0px;
        left: 30px;
    }
    .button-top {
        bottom: 38px !important;
    }
    .row-button {
        /* width: 283px; */
        display: block !important;
        padding-bottom: 10px;
    }
    .title-card {
        bottom: 3vh;
        font-size: 12px !important;
        left: 37px;
    }
    .container-fluid {
        width: 96%;
    }
    .mat-step-icon {
        display: none !important;
    }
    .mat-step-icon-not-touched {
        display: none !important;
    }
    .mat-stepper-horizontal-line {
        min-width: 0px !important;
    }
    .dropdown-notification-arrow {
        left: 72.5%;
        top: 43px;
    }
   
    .modal-title {
        font-weight: 600;
        color: #041677;
        font-size: 20px;
    }
    .report-icon {
        left: 145px;
    }
    .little-modal {
        width: 390px;
    }
    .content-card {
        background-color: #3f5fb5;
        color: white;
        flex: 0 0 46% !important;
        margin-right: 5px;
        margin-bottom: 20px;
        margin-left: 8px;
    }
    .icon-inicio {
        font-size: 35px;
    }
    .textm-center {
        text-align: center !important;
    }
    .Vistoria-sub {
        border: 2px solid #2f72b7;
        width: unset !important;
        left: 48vh;
        bottom: 54vh;
    }
    .title-item {
        left: 2vh;
        top: unset;
    }
    .panel-content {
        bottom: 6vh;
    }
    html {
        overflow-x: hidden;
    }
    .title-card {
        top: 3vh;
        width: 405px !important;
        bottom: 7vh;
        font-size: 12px !important;
        left: 37px;
        width: 360px !important;
        top: unset;
    }
    .body-card {
        padding-left: 15px;
        padding-right: 15px;
    }
    .mat-step-label {
        font-size: 11px !important;
        font-weight: 400;
    }
    .conf-item {
        margin-right: 10px;
    }
}
/* Media Querie 375px */

@media (max-width: 1150px) {
    .mother-div {
        margin-right: 5px;
    }
}

@media (max-width: 991px) {
    .mother-div {
        margin-right: 20px;
    }
}

@media (max-width: 900px) and (max-height: 1000) {
    .main {
        height: auto;
        min-height: 90vh;
    }
}

@media (max-width: 375px) and (min-width: 325px) {
    body {
        overflow-x: hidden !important;
    }
    .content-icon {
        padding-right: 20px;
        padding-left: 20px;
    }
    .content-size {
        padding-right: 20px !important;
        padding-left: 20px !important;
    }
    .mother-div {
        margin-left: 7px;
        margin-top: 10px;
    }
}

.container-fluid {
    width: 92%;
}

.button-top {
    bottom: 38px !important;
}

.body-card {
    padding: 0 !important;
}

.dropdown-notification-arrow {
    left: 62.5%;
}



.icon-inicio {
    font-size: 30px;
}

.Vistoria-sub {
    left: 38vh !important;
    bottom: 48vh !important;
    width: auto !important;
}

.title-card {
    left: 15px;
    width: 330px !important;
    bottom: 9vh;
    top: unset !important;
}

.conf-item {
    margin-right: 7px;
}

video {
    width: 310px !important;
    height: 100%;
}

.left-modal {
    left: 0px !important;
}
/* Media Querie 320px */

@media (max-width: 320px) {
    body {
        overflow-x: hidden !important;
    }
    .container-fluid {
        width: 90%;
    }
   
    .little-modal {
        width: 330px;
    }
    .row-button-camera {
        display: flex;
        padding-bottom: 10px;
    }
    .report-icon {
        left: 96px;
    }
    .content-card {
        background-color: #3f5fb5;
        color: white;
        flex: 0 0 44% !important;
        margin-right: 7px;
        margin-bottom: 20px;
        margin-left: 11px;
    }
    .title-card {
        left: 15px;
        padding-left: 0 !important;
        padding-right: 0 !important;
        bottom: 8vh;
        width: 325px !important;
    }
    .Vistoria-sub {
        bottom: 42vh !important;
        left: 34vh !important;
        width: auto !important;
    }
    .button-top {
        bottom: 38px !important;
    }
    .panel-content {
        bottom: 50px;
        padding-right: 10px;
        padding-left: 30px;
    }
    video {
        width: 320px;
        height: 100%;
    }
    .content-size {
        bottom: 5vh;
    }
    .dropdown-menu {
        left: 5%;
        top: 7.5%;
    }
    .btn-primary {
        font-size: 11px;
        cursor: pointer;
    }
    .spacer-button {
        margin-right: 11px !important;
    }
    .m-h-180 {
        max-height: 480px !important;
    }
    .row-button {
        display: block !important;
        padding-bottom: 10px;
        padding-bottom: 20px;
    }
    .centralize-button {
        right: 30px !important;
    }
    .nsm-content {
        width: 100%;
    }
    .left-modal {
        right: 0px;
        left: unset;
    }
    .mat-step-icon {
        display: none !important;
    }
    .dropdown-notification-arrow {
        left: 62.5%;
        top: 47px;
    }
}

@media (max-height: 490px) and (max-width: 375px) {
    video {
        width: 180px !important;
    }
    .left-modal {
        left: 35px !important;
    }
}

.fab {
    position: absolute;
    bottom: 10px;
    right: 10px;
}

.fab button {
    cursor: pointer;
    width: 48px;
    height: 48px;
    border-radius: 30px;
    background-color: #0983ff;
    border: none;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    font-size: 24px;
    color: white;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}

.fab button:focus {
    outline: none;
}

.fab button.mainb {
    position: absolute;
    width: 45px;
    height: 45px;
    border-radius: 30px;
    background-color: #1265b9;
    right: 0;
    bottom: 0;
    z-index: 20;
}

.fab button.mainb:before {
    content: "settings";
}

.fab ul {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 0;
    padding-right: 5px;
    margin: 0;
    list-style: none;
    z-index: 10;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}

.fab ul li {
    display: flex;
    justify-content: flex-start;
    position: relative;
    margin-bottom: -10%;
    opacity: 0;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}

.fab ul li label {
    margin-right: 10px;
    white-space: nowrap;
    display: block;
    margin-top: -19px;
    color: #fff;
    background-color: #178aff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    height: 35px;
    font-size: 10px;
    pointer-events: none;
    transition: 0.2s ease-out;
    width: 80px;
    text-align: center;
    padding-top: 15px;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-family: "Roboto", sans-serif;
    transition: 0.2s ease-out;
    font-weight: bold;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}

.fab button.mainb:active,
.fab button.mainb:focus {
    outline: none;
    background-color: #1265b9;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5);
}

.fab button.mainb:active:before,
.fab button.mainb:focus:before {
    content: "↑";
}

.fab button.mainb:active + ul,
.fab button.mainb:focus + ul {
    bottom: 70px;
}

.fab button.mainb:active + ul li,
.fab button.mainb:focus + ul li {
    margin-bottom: 10px;
    opacity: 1;
}

.fab button.mainb:active + ul li:hover label,
.fab button.mainb:focus + ul li:hover label {
    opacity: 1;
}



@media (max-width: 900px) and (min-width: 750px) {
    .div_paciente {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 25px;
        text-align: center;
    }
    .div_img {
        margin-left: 48px;
        margin-right: auto;
    }
}
/* 
@media (max-width: 600px){
  .cal-month-view {
      height: 75vh !important;
      overflow: scroll !important;
  }
} */

@media (max-width: 425px) {
    .custom-span {
        display: none;
    }
    .height-modal {
        height: 63.5vh;
        overflow: auto;
    }
    .md-chip {
        margin-left: 0;
    }
    .cal-month-view .cal-day-cell {
        min-height: 85px;
        /* width: 10px!important; */
    }
    .cal-month-view .cal-cell-top {
        min-height: 75px;
        flex: 1;
        text-align: center;
    }
    .emailmodal {
        margin-top: -3px !important;
    }
    .mother-div {
        margin-left: 20px;
        margin-top: 20px;
    }
    .clinic_modal {
        padding-left: 0px;
        padding-right: 0px;
    }
    .nsm-dialog {
        margin-top: 20px !important;
    }
    .receit .nsm-content {
        margin-top: 47px !important;
    }
    .clinic_modal .nsm-content {
        width: 100% !important;
        margin-top: 80px !important;
    }
    .table-scroller {
        padding: 0;
        overflow: auto;
        height: 78vh;
    }
    .icon-title {
        margin-right: 5px;
        margin-left: 5px;
    }
    .custom-margim {
        margin-top: 45px !important;
    }
    .background-deleteAtendente {
        height: 135px !important;
        width: 100%;
    }
    .emailmodalAtestado .nsm-content {
        margin-top: 30px !important;
    }
    .emailmodalAgenda {
        margin-right: 8px !important;
    }
    .emailmodalAgendaContato {
        height: 623px;
        overflow-y: visible;
    }

    .emailmodal .nsm-dialog-btn-close {
        top: -30px;
    }

    /* iframe {
             height: 52vh!important;
         } */
}

/* @media(max-height:450px) {
         iframe {
             height: 206px!important;
         }
     }
      */
@media (max-width: 450px) and (max-height: 800px) {
    .background-Iniciar {
        background-size: 430px;
        background-position: bottom center;
        background-repeat: no-repeat;
        height: 208px;
        width: 405px;
    }
}

@media (max-width: 800px) {
    .background-Iniciar {
        background-image: url(/assets/build/img/background-primary.png) !important;
        background-size: 440px;
        background-position: bottom center;
        background-repeat: no-repeat;
        height: 208px;
        width: 403px;
    }
    .icone-custom {
        margin-left: 30% !important;
        width: 440px !important;
        margin-top: 5px !important;
        color: white !important;
        font-size: 175px !important;
    }
    .background-cliente {
        display: none;
    }
    .form-agendamento {
        display: none !important;
    }
}

@media (max-width: 490px) and (max-height: 700px) {
    .icone-custom {
        width: 440px !important;
        margin-top: 5px !important;
        color: white !important;
        font-size: 175px !important;
        margin-left: 20% !important;
    }
    .background-Iniciar {
        background-image: url(/assets/build/img/background-primary.png) !important;
        background-size: 381px;
        background-position: bottom center;
        background-repeat: no-repeat;
        height: 208px;
        width: 350px;
    }
    .form-agendamento {
        display: none !important;
    }
    .background-email {
        display: none;
    }
    .background-vacation {
        display: none;
    }
    .background-cliente {
        display: none;
    }
    .background-delete {
        display: none;
    }
    .background-off {
        display: none;
    }
}

@media (max-width: 460px) {
    .icone-custom {
        margin-left: auto !important;
        margin-right: auto !important;
        width: 100%;
        text-align: center;
    }
    .form-modal .nsm-content {
        padding: 0 !important;
        /*width: 300px;*/
    }
}

@media (max-width: 375px) and (max-height: 812px) and (min-height: 750px) {
    .emailmodal {
        margin-top: -31px !important;
    }
}

.mat-expansion-panel-header.mat-expanded:focus,
.mat-expansion-panel-header.mat-expanded:hover {
    background: none !important;
}
.mat-calendar-table-header th {
    text-align: center;
    padding: 8px 0 0px 0 !important;
}
/* .mat-form-field-appearance-outline .mat-form-field-flex{
    padding: 0 .75em 0 .75em;
    margin-top: -.25em;
    position: relative;
    height: 210px;
} */

@media (max-width: 360px) {
    .div_paciente h4 {
        font-size: 14px;
    }
}

@media (max-width: 400px) {
    /* TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version. */
    .div-cadastro-medicamentos .mat-slide-toggle-label {
        display: block;
        text-align: center;
        justify-content: center;
        margin: 0 auto;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version. */
    .div-cadastro-medicamentos .mat-slide-toggle-bar {
        margin: 10px auto;
        justify-content: center;
        text-align: center;
        align-items: center;
    }
}


@media (max-width: 530px) {
    .modal-avaliacao .nsm-content {
        max-width: 400px;
    }
}

@media (max-width: 460px) {
    .modal-avaliacao .nsm-content {
        max-width: 390px;
    }
}


/*@media (max-width: 500px) {
    .modal-cid .nsm-content {
        max-width: 300px !important;
    }
}*/

@media (max-width: 780px) {
    .modal-cid .nsm-content {
        left: 50px;
    }
}

@media (max-width: 425px) {
    .modal-cid .nsm-content {
        top: 25px;
        max-width: 310px !important;
    }
}

@media (max-width: 390px){
    .modal-cid .nsm-content {
        left: 10% !important;
        position: relative;
    }
    .modal-avaliacao .nsm-content {
        max-width: 310px !important;
    }
}

@media (max-width: 345px) {
    .modal-cid .nsm-content {
        left: 6%;
    }
}

@media (max-width: 330px) {
    .modal-cid .nsm-content {
        left: 3% !important;
    }
}

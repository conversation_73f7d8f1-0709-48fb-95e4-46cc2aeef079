import { Injectable } from '@angular/core';
import { Subject, BehaviorSubject } from 'rxjs';

export interface VoiceResult {
  text: string;
  confidence: number;
  success: boolean;
  error?: string;
}

export interface VoiceRecordingEvent {
  type: 'started' | 'stopped' | 'ended_automatically' | 'speech_detected' | 'silence_detected' | 'ambient_noise_detected';
  timestamp: number;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class VoiceRecorderService {
  private recognition: any;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private mediaStream: MediaStream | null = null;
  
  private isRecording = false;
  private silenceTimer: any;
  private speechDetected = false;
  private lastSpeechTime = 0;
  private ambientNoiseLevel = 0;
  private speechThreshold = 0.02;
  private silenceThreshold = 0.01;
  private calibrationComplete = false;

  private readonly resultSubject = new Subject<VoiceResult>();
  private readonly errorSubject = new Subject<string>();
  private readonly recordingSubject = new BehaviorSubject<boolean>(false);
  private readonly recordingEventSubject = new Subject<VoiceRecordingEvent>();

  readonly result$ = this.resultSubject.asObservable();
  readonly error$ = this.errorSubject.asObservable();
  readonly recording$ = this.recordingSubject.asObservable();
  readonly recordingEvent$ = this.recordingEventSubject.asObservable();

  constructor() {
    this.initializeSpeechRecognition();
  }

  //#region Initialization
  private initializeSpeechRecognition(): void {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      this.errorSubject.next('Reconhecimento de voz não suportado neste navegador');
      return;
    }

    this.recognition = new SpeechRecognition();
    this.configureRecognition();
    this.setupRecognitionEvents();
  }

  private configureRecognition(): void {
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = 'pt-BR';
    this.recognition.maxAlternatives = 3;

    // Configurações adicionais para melhorar reconhecimento de números
    try {
      const SpeechGrammarList = (window as any).SpeechGrammarList || (window as any).webkitSpeechGrammarList;
      if (SpeechGrammarList && this.recognition.grammars !== undefined) {
        const speechRecognitionList = new SpeechGrammarList();
        // Grammar para números (CPF, telefone, etc.)
        const grammar = '#JSGF V1.0; grammar numbers; public <number> = <digit>+; <digit> = zero | um | dois | três | quatro | cinco | seis | sete | oito | nove;';
        speechRecognitionList.addFromString(grammar, 1);
        this.recognition.grammars = speechRecognitionList;
        console.log('✅ Grammar configurada para melhor reconhecimento de números');
      } else {
        console.log('⚠️ SpeechGrammarList não disponível neste navegador');
      }
    } catch (error) {
      console.log('⚠️ Erro ao configurar grammar, continuando sem ela:', error);
    }
  }

  private async initializeAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;

      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        }
      });

      this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);
      this.microphone.connect(this.analyser);
      
      await this.calibrateAmbientNoise();
    } catch (error) {
      this.errorSubject.next('Erro ao acessar microfone');
    }
  }

  private async calibrateAmbientNoise(): Promise<void> {
    if (!this.analyser) return;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const samples: number[] = [];

    for (let i = 0; i < 30; i++) {
      this.analyser.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
      samples.push(average / 255);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.ambientNoiseLevel = samples.reduce((sum, sample) => sum + sample, 0) / samples.length;
    this.speechThreshold = Math.max(this.ambientNoiseLevel * 3, 0.02);
    this.silenceThreshold = Math.max(this.ambientNoiseLevel * 1.5, 0.01);
    this.calibrationComplete = true;
  }

  private setupRecognitionEvents(): void {
    this.recognition.onstart = () => {
      this.isRecording = true;
      this.speechDetected = false;
      this.lastSpeechTime = 0;
      this.recordingSubject.next(true);
      this.emitEvent('started');
      this.startAudioMonitoring();
    };

    this.recognition.onresult = (event: any) => {
      this.handleRecognitionResult(event);
    };

    this.recognition.onerror = (event: any) => {
      this.handleRecognitionError(event);
    };

    this.recognition.onend = () => {
      this.handleRecognitionEnd();
    };

    this.recognition.onspeechstart = () => {
      this.handleSpeechStart();
    };

    this.recognition.onspeechend = () => {
      this.handleSpeechEnd();
    };
  }
  //#endregion

  //#region Audio Monitoring
  private startAudioMonitoring(): void {
    if (!this.analyser || !this.calibrationComplete) return;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const monitor = () => {
      if (!this.isRecording) return;

      this.analyser!.getByteFrequencyData(dataArray);
      const currentLevel = this.calculateAudioLevel(dataArray);
      
      this.processAudioLevel(currentLevel);
      requestAnimationFrame(monitor);
    };

    monitor();
  }

  private calculateAudioLevel(dataArray: Uint8Array): number {
    const weightedSum = dataArray.reduce((sum, value, index) => {
      const frequency = (index / dataArray.length) * (this.audioContext!.sampleRate / 2);
      const weight = this.getFrequencyWeight(frequency);
      return sum + (value * weight);
    }, 0);

    return weightedSum / (dataArray.length * 255);
  }

  private getFrequencyWeight(frequency: number): number {
    if (frequency < 300) return 0.3;
    if (frequency < 3000) return 1.0;
    if (frequency < 8000) return 0.7;
    return 0.2;
  }

  private processAudioLevel(currentLevel: number): void {
    const now = Date.now();
    const isSpeech = currentLevel > this.speechThreshold;
    const isAmbientNoise = currentLevel > this.silenceThreshold && currentLevel <= this.speechThreshold;

    if (isSpeech) {
      this.lastSpeechTime = now;
      if (!this.speechDetected) {
        this.speechDetected = true;
        this.clearSilenceTimer();
        this.emitEvent('speech_detected');
      }
    } else if (isAmbientNoise) {
      this.emitEvent('ambient_noise_detected', { level: currentLevel });
    }

    if (this.speechDetected && (now - this.lastSpeechTime) > 3000) {
      this.startSilenceTimer();
    }
  }
  //#endregion

  //#region Event Handlers
  private handleSpeechStart(): void {
    this.speechDetected = true;
    this.lastSpeechTime = Date.now();
    this.clearSilenceTimer();
    this.emitEvent('speech_detected');
  }

  private handleSpeechEnd(): void {
    this.startSilenceTimer();
    this.emitEvent('silence_detected');
  }

  private handleRecognitionResult(event: any): void {
    let finalTranscript = '';
    let bestConfidence = 0;

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i];

      if (result.isFinal) {
        const transcript = result[0].transcript;
        const confidence = this.calculateBestConfidence(result);

        if (confidence > bestConfidence) {
          finalTranscript = transcript;
          bestConfidence = confidence;
        }
      }
    }

    if (finalTranscript.trim()) {
      const cleanText = this.cleanTranscript(finalTranscript);
      console.log('🎤 Transcript recebido:', finalTranscript);
      console.log('🧹 Texto limpo:', cleanText);
      console.log('📊 Confiança:', bestConfidence);
      console.log('✅ Válido:', this.isValidTranscript(cleanText, bestConfidence));

      if (this.isValidTranscript(cleanText, bestConfidence)) {
        this.resultSubject.next({
          text: cleanText,
          confidence: bestConfidence,
          success: true
        });
        this.stopRecording();
      } else {
        console.log('❌ Transcript rejeitado - não passou na validação');
      }
    }
  }

  private handleRecognitionError(event: any): void {
    this.resetState();
    
    const errorMessages = {
      'no-speech': 'Nenhuma fala detectada. Fale mais próximo do microfone.',
      'audio-capture': 'Erro no microfone. Verifique as configurações de áudio.',
      'not-allowed': 'Permissão negada. Permita o acesso ao microfone.',
      'network': 'Erro de conexão. Verifique sua internet.',
      'service-not-allowed': 'Serviço de reconhecimento indisponível.',
      'aborted': 'Reconhecimento cancelado.'
    };

    const errorMessage = errorMessages[event.error as keyof typeof errorMessages] || 
                        'Erro no reconhecimento de voz. Tente novamente.';

    if (event.error !== 'aborted') {
      this.errorSubject.next(errorMessage);
      this.resultSubject.next({
        text: '',
        confidence: 0,
        success: false,
        error: errorMessage
      });
    }
  }

  private handleRecognitionEnd(): void {
    if (this.isRecording) {
      this.resetState();
      this.emitEvent('ended_automatically');
    }
  }
  //#endregion

  //#region Audio Quality Enhancement
  private calculateBestConfidence(result: any): number {
    let totalConfidence = 0;
    const alternatives = Math.min(result.length, 3);
    
    for (let i = 0; i < alternatives; i++) {
      totalConfidence += result[i].confidence || 0.5;
    }
    
    return totalConfidence / alternatives;
  }

  private cleanTranscript(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\sáàâãéèêíìîóòôõúùûç.,!?-]/gi, '')
      .toLowerCase();
  }

  private isValidTranscript(text: string, confidence: number): boolean {
    const minLength = 3;
    const minConfidence = 0.4;

    // Aceita palavras com letras OU sequências de números
    const hasValidWords = /[a-záàâãéèêíìîóòôõúùûç]{3,}/i.test(text);
    const hasValidNumbers = /\d{3,}/i.test(text);
    const hasValidContent = hasValidWords || hasValidNumbers;

    // Conta palavras (incluindo números) com mais de 2 caracteres
    const wordCount = text.split(/\s+/).filter(word => word.length > 2).length;

    // Para números longos (como CPF), aceita mesmo com confiança menor
    const isLongNumber = /^\d{8,}$/.test(text.replace(/\s/g, ''));
    const adjustedMinConfidence = isLongNumber ? 0.3 : minConfidence;

    console.log('🔍 Validação transcript:', {
      text,
      length: text.length,
      confidence,
      hasValidWords,
      hasValidNumbers,
      hasValidContent,
      wordCount,
      isLongNumber,
      adjustedMinConfidence,
      lengthOk: text.length >= minLength,
      confidenceOk: confidence >= adjustedMinConfidence,
      contentOk: hasValidContent,
      wordCountOk: wordCount >= 1
    });

    return text.length >= minLength &&
           confidence >= adjustedMinConfidence &&
           hasValidContent &&
           wordCount >= 1;
  }
  //#endregion

  //#region Silence Detection
  private startSilenceTimer(): void {
    this.clearSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      if (this.isRecording && this.speechDetected) {
        const timeSinceLastSpeech = Date.now() - this.lastSpeechTime;
        if (timeSinceLastSpeech >= 3000) {
          this.stopRecording();
        }
      }
    }, 3500);
  }

  private clearSilenceTimer(): void {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }
  //#endregion

  //#region Public Methods
  async startRecording(): Promise<boolean> {
    if (!this.recognition) {
      this.errorSubject.next('Reconhecimento de voz não disponível');
      return false;
    }

    if (this.isRecording) {
      return true;
    }

    try {
      if (!this.audioContext) {
        await this.initializeAudioContext();
      }

      this.recognition.start();
      return true;
    } catch (error: any) {
      if (error.name === 'InvalidStateError') {
        this.isRecording = true;
        this.recordingSubject.next(true);
        return true;
      }
      
      this.errorSubject.next('Erro ao iniciar gravação');
      return false;
    }
  }

  stopRecording(): void {
    if (!this.isRecording) return;

    try {
      this.recognition?.stop();
      this.resetState();
      this.emitEvent('stopped');
    } catch (error) {
      this.resetState();
    }
  }

  forceStop(): void {
    try {
      this.recognition?.abort();
    } catch (error) {
      // Ignore errors
    }
    this.resetState();
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  isSupported(): boolean {
    return !!this.recognition;
  }

  async restartRecording(): Promise<boolean> {
    this.stopRecording();
    await new Promise(resolve => setTimeout(resolve, 100));
    return this.startRecording();
  }

  getAmbientNoiseLevel(): number {
    return this.ambientNoiseLevel;
  }

  getSpeechThreshold(): number {
    return this.speechThreshold;
  }
  //#endregion

  //#region Helper Methods
  private resetState(): void {
    this.isRecording = false;
    this.speechDetected = false;
    this.lastSpeechTime = 0;
    this.recordingSubject.next(false);
    this.clearSilenceTimer();
    this.cleanupAudioResources();
  }

  private cleanupAudioResources(): void {
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
    
    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.analyser = null;
    this.calibrationComplete = false;
  }

  private emitEvent(type: VoiceRecordingEvent['type'], data?: any): void {
    this.recordingEventSubject.next({
      type,
      timestamp: Date.now(),
      data
    });
  }
  //#endregion
}
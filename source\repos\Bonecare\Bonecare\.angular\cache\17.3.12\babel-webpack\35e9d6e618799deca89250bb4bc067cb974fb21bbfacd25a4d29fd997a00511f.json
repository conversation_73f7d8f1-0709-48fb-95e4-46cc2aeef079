{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class VoiceRecorderService {\n  recognition;\n  audioContext = null;\n  analyser = null;\n  microphone = null;\n  mediaStream = null;\n  isRecording = false;\n  silenceTimer;\n  speechDetected = false;\n  lastSpeechTime = 0;\n  ambientNoiseLevel = 0;\n  speechThreshold = 0.02;\n  silenceThreshold = 0.01;\n  calibrationComplete = false;\n  resultSubject = new Subject();\n  errorSubject = new Subject();\n  recordingSubject = new BehaviorSubject(false);\n  recordingEventSubject = new Subject();\n  result$ = this.resultSubject.asObservable();\n  error$ = this.errorSubject.asObservable();\n  recording$ = this.recordingSubject.asObservable();\n  recordingEvent$ = this.recordingEventSubject.asObservable();\n  constructor() {\n    this.initializeSpeechRecognition();\n  }\n  //#region Initialization\n  initializeSpeechRecognition() {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      this.errorSubject.next('Reconhecimento de voz não suportado neste navegador');\n      return;\n    }\n    this.recognition = new SpeechRecognition();\n    this.configureRecognition();\n    this.setupRecognitionEvents();\n  }\n  configureRecognition() {\n    this.recognition.continuous = true;\n    this.recognition.interimResults = true;\n    this.recognition.lang = 'pt-BR';\n    this.recognition.maxAlternatives = 3;\n    // Configurações adicionais para melhorar reconhecimento de números\n    try {\n      const SpeechGrammarList = window.SpeechGrammarList || window.webkitSpeechGrammarList;\n      if (SpeechGrammarList && this.recognition.grammars !== undefined) {\n        const speechRecognitionList = new SpeechGrammarList();\n        // Grammar para números (CPF, telefone, etc.)\n        const grammar = '#JSGF V1.0; grammar numbers; public <number> = <digit>+; <digit> = zero | um | dois | três | quatro | cinco | seis | sete | oito | nove;';\n        speechRecognitionList.addFromString(grammar, 1);\n        this.recognition.grammars = speechRecognitionList;\n        console.log('✅ Grammar configurada para melhor reconhecimento de números');\n      } else {\n        console.log('⚠️ SpeechGrammarList não disponível neste navegador');\n      }\n    } catch (error) {\n      console.log('⚠️ Erro ao configurar grammar, continuando sem ela:', error);\n    }\n  }\n  initializeAudioContext() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        _this.analyser = _this.audioContext.createAnalyser();\n        _this.analyser.fftSize = 256;\n        _this.analyser.smoothingTimeConstant = 0.8;\n        _this.mediaStream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        _this.microphone = _this.audioContext.createMediaStreamSource(_this.mediaStream);\n        _this.microphone.connect(_this.analyser);\n        yield _this.calibrateAmbientNoise();\n      } catch (error) {\n        _this.errorSubject.next('Erro ao acessar microfone');\n      }\n    })();\n  }\n  calibrateAmbientNoise() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.analyser) return;\n      const bufferLength = _this2.analyser.frequencyBinCount;\n      const dataArray = new Uint8Array(bufferLength);\n      const samples = [];\n      for (let i = 0; i < 30; i++) {\n        _this2.analyser.getByteFrequencyData(dataArray);\n        const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;\n        samples.push(average / 255);\n        yield new Promise(resolve => setTimeout(resolve, 100));\n      }\n      _this2.ambientNoiseLevel = samples.reduce((sum, sample) => sum + sample, 0) / samples.length;\n      _this2.speechThreshold = Math.max(_this2.ambientNoiseLevel * 3, 0.02);\n      _this2.silenceThreshold = Math.max(_this2.ambientNoiseLevel * 1.5, 0.01);\n      _this2.calibrationComplete = true;\n    })();\n  }\n  setupRecognitionEvents() {\n    this.recognition.onstart = () => {\n      this.isRecording = true;\n      this.speechDetected = false;\n      this.lastSpeechTime = 0;\n      this.recordingSubject.next(true);\n      this.emitEvent('started');\n      this.startAudioMonitoring();\n    };\n    this.recognition.onresult = event => {\n      this.handleRecognitionResult(event);\n    };\n    this.recognition.onerror = event => {\n      this.handleRecognitionError(event);\n    };\n    this.recognition.onend = () => {\n      this.handleRecognitionEnd();\n    };\n    this.recognition.onspeechstart = () => {\n      this.handleSpeechStart();\n    };\n    this.recognition.onspeechend = () => {\n      this.handleSpeechEnd();\n    };\n  }\n  //#endregion\n  //#region Audio Monitoring\n  startAudioMonitoring() {\n    if (!this.analyser || !this.calibrationComplete) return;\n    const bufferLength = this.analyser.frequencyBinCount;\n    const dataArray = new Uint8Array(bufferLength);\n    const monitor = () => {\n      if (!this.isRecording) return;\n      this.analyser.getByteFrequencyData(dataArray);\n      const currentLevel = this.calculateAudioLevel(dataArray);\n      this.processAudioLevel(currentLevel);\n      requestAnimationFrame(monitor);\n    };\n    monitor();\n  }\n  calculateAudioLevel(dataArray) {\n    const weightedSum = dataArray.reduce((sum, value, index) => {\n      const frequency = index / dataArray.length * (this.audioContext.sampleRate / 2);\n      const weight = this.getFrequencyWeight(frequency);\n      return sum + value * weight;\n    }, 0);\n    return weightedSum / (dataArray.length * 255);\n  }\n  getFrequencyWeight(frequency) {\n    if (frequency < 300) return 0.3;\n    if (frequency < 3000) return 1.0;\n    if (frequency < 8000) return 0.7;\n    return 0.2;\n  }\n  processAudioLevel(currentLevel) {\n    const now = Date.now();\n    const isSpeech = currentLevel > this.speechThreshold;\n    const isAmbientNoise = currentLevel > this.silenceThreshold && currentLevel <= this.speechThreshold;\n    if (isSpeech) {\n      this.lastSpeechTime = now;\n      if (!this.speechDetected) {\n        this.speechDetected = true;\n        this.clearSilenceTimer();\n        this.emitEvent('speech_detected');\n      }\n    } else if (isAmbientNoise) {\n      this.emitEvent('ambient_noise_detected', {\n        level: currentLevel\n      });\n    }\n    if (this.speechDetected && now - this.lastSpeechTime > 3000) {\n      this.startSilenceTimer();\n    }\n  }\n  //#endregion\n  //#region Event Handlers\n  handleSpeechStart() {\n    this.speechDetected = true;\n    this.lastSpeechTime = Date.now();\n    this.clearSilenceTimer();\n    this.emitEvent('speech_detected');\n  }\n  handleSpeechEnd() {\n    this.startSilenceTimer();\n    this.emitEvent('silence_detected');\n  }\n  handleRecognitionResult(event) {\n    let finalTranscript = '';\n    let bestConfidence = 0;\n    for (let i = event.resultIndex; i < event.results.length; i++) {\n      const result = event.results[i];\n      if (result.isFinal) {\n        const transcript = result[0].transcript;\n        const confidence = this.calculateBestConfidence(result);\n        if (confidence > bestConfidence) {\n          finalTranscript = transcript;\n          bestConfidence = confidence;\n        }\n      }\n    }\n    if (finalTranscript.trim()) {\n      const cleanText = this.cleanTranscript(finalTranscript);\n      console.log('🎤 Transcript recebido:', finalTranscript);\n      console.log('🧹 Texto limpo:', cleanText);\n      console.log('📊 Confiança:', bestConfidence);\n      console.log('✅ Válido:', this.isValidTranscript(cleanText, bestConfidence));\n      if (this.isValidTranscript(cleanText, bestConfidence)) {\n        this.resultSubject.next({\n          text: cleanText,\n          confidence: bestConfidence,\n          success: true\n        });\n        this.stopRecording();\n      } else {\n        console.log('❌ Transcript rejeitado - não passou na validação');\n      }\n    }\n  }\n  handleRecognitionError(event) {\n    this.resetState();\n    const errorMessages = {\n      'no-speech': 'Nenhuma fala detectada. Fale mais próximo do microfone.',\n      'audio-capture': 'Erro no microfone. Verifique as configurações de áudio.',\n      'not-allowed': 'Permissão negada. Permita o acesso ao microfone.',\n      'network': 'Erro de conexão. Verifique sua internet.',\n      'service-not-allowed': 'Serviço de reconhecimento indisponível.',\n      'aborted': 'Reconhecimento cancelado.'\n    };\n    const errorMessage = errorMessages[event.error] || 'Erro no reconhecimento de voz. Tente novamente.';\n    if (event.error !== 'aborted') {\n      this.errorSubject.next(errorMessage);\n      this.resultSubject.next({\n        text: '',\n        confidence: 0,\n        success: false,\n        error: errorMessage\n      });\n    }\n  }\n  handleRecognitionEnd() {\n    if (this.isRecording) {\n      this.resetState();\n      this.emitEvent('ended_automatically');\n    }\n  }\n  //#endregion\n  //#region Audio Quality Enhancement\n  calculateBestConfidence(result) {\n    let totalConfidence = 0;\n    const alternatives = Math.min(result.length, 3);\n    for (let i = 0; i < alternatives; i++) {\n      totalConfidence += result[i].confidence || 0.5;\n    }\n    return totalConfidence / alternatives;\n  }\n  cleanTranscript(text) {\n    return text.trim().replace(/\\s+/g, ' ').replace(/[^\\w\\sáàâãéèêíìîóòôõúùûç.,!?-]/gi, '').toLowerCase();\n  }\n  isValidTranscript(text, confidence) {\n    const minLength = 3;\n    const minConfidence = 0.4;\n    // Aceita palavras com letras OU sequências de números\n    const hasValidWords = /[a-záàâãéèêíìîóòôõúùûç]{3,}/i.test(text);\n    const hasValidNumbers = /\\d{3,}/i.test(text);\n    const hasValidContent = hasValidWords || hasValidNumbers;\n    // Conta palavras (incluindo números) com mais de 2 caracteres\n    const wordCount = text.split(/\\s+/).filter(word => word.length > 2).length;\n    // Para números longos (como CPF), aceita mesmo com confiança menor\n    const isLongNumber = /^\\d{8,}$/.test(text.replace(/\\s/g, ''));\n    const adjustedMinConfidence = isLongNumber ? 0.3 : minConfidence;\n    console.log('🔍 Validação transcript:', {\n      text,\n      length: text.length,\n      confidence,\n      hasValidWords,\n      hasValidNumbers,\n      hasValidContent,\n      wordCount,\n      isLongNumber,\n      adjustedMinConfidence,\n      lengthOk: text.length >= minLength,\n      confidenceOk: confidence >= adjustedMinConfidence,\n      contentOk: hasValidContent,\n      wordCountOk: wordCount >= 1\n    });\n    return text.length >= minLength && confidence >= adjustedMinConfidence && hasValidContent && wordCount >= 1;\n  }\n  //#endregion\n  //#region Silence Detection\n  startSilenceTimer() {\n    this.clearSilenceTimer();\n    this.silenceTimer = setTimeout(() => {\n      if (this.isRecording && this.speechDetected) {\n        const timeSinceLastSpeech = Date.now() - this.lastSpeechTime;\n        if (timeSinceLastSpeech >= 3000) {\n          this.stopRecording();\n        }\n      }\n    }, 3500);\n  }\n  clearSilenceTimer() {\n    if (this.silenceTimer) {\n      clearTimeout(this.silenceTimer);\n      this.silenceTimer = null;\n    }\n  }\n  //#endregion\n  //#region Public Methods\n  startRecording() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.recognition) {\n        _this3.errorSubject.next('Reconhecimento de voz não disponível');\n        return false;\n      }\n      if (_this3.isRecording) {\n        return true;\n      }\n      try {\n        if (!_this3.audioContext) {\n          yield _this3.initializeAudioContext();\n        }\n        _this3.recognition.start();\n        return true;\n      } catch (error) {\n        if (error.name === 'InvalidStateError') {\n          _this3.isRecording = true;\n          _this3.recordingSubject.next(true);\n          return true;\n        }\n        _this3.errorSubject.next('Erro ao iniciar gravação');\n        return false;\n      }\n    })();\n  }\n  stopRecording() {\n    if (!this.isRecording) return;\n    try {\n      this.recognition?.stop();\n      this.resetState();\n      this.emitEvent('stopped');\n    } catch (error) {\n      this.resetState();\n    }\n  }\n  forceStop() {\n    try {\n      this.recognition?.abort();\n    } catch (error) {\n      // Ignore errors\n    }\n    this.resetState();\n  }\n  isCurrentlyRecording() {\n    return this.isRecording;\n  }\n  isSupported() {\n    return !!this.recognition;\n  }\n  restartRecording() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.stopRecording();\n      yield new Promise(resolve => setTimeout(resolve, 100));\n      return _this4.startRecording();\n    })();\n  }\n  getAmbientNoiseLevel() {\n    return this.ambientNoiseLevel;\n  }\n  getSpeechThreshold() {\n    return this.speechThreshold;\n  }\n  //#endregion\n  //#region Helper Methods\n  resetState() {\n    this.isRecording = false;\n    this.speechDetected = false;\n    this.lastSpeechTime = 0;\n    this.recordingSubject.next(false);\n    this.clearSilenceTimer();\n    this.cleanupAudioResources();\n  }\n  cleanupAudioResources() {\n    if (this.mediaStream) {\n      this.mediaStream.getTracks().forEach(track => track.stop());\n      this.mediaStream = null;\n    }\n    if (this.microphone) {\n      this.microphone.disconnect();\n      this.microphone = null;\n    }\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n      this.audioContext = null;\n    }\n    this.analyser = null;\n    this.calibrationComplete = false;\n  }\n  emitEvent(type, data) {\n    this.recordingEventSubject.next({\n      type,\n      timestamp: Date.now(),\n      data\n    });\n  }\n  static ɵfac = function VoiceRecorderService_Factory(t) {\n    return new (t || VoiceRecorderService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: VoiceRecorderService,\n    factory: VoiceRecorderService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Subject", "BehaviorSubject", "VoiceRecorderService", "recognition", "audioContext", "analyser", "microphone", "mediaStream", "isRecording", "silenceTimer", "speechDetected", "lastSpeechTime", "ambientNoiseLevel", "speechT<PERSON><PERSON>old", "silenceT<PERSON><PERSON>old", "calibrationComplete", "resultSubject", "errorSubject", "recordingSubject", "recordingEventSubject", "result$", "asObservable", "error$", "recording$", "recordingEvent$", "constructor", "initializeSpeechRecognition", "SpeechRecognition", "window", "webkitSpeechRecognition", "next", "configureRecognition", "setupRecognitionEvents", "continuous", "interimResults", "lang", "maxAlternatives", "SpeechGrammarList", "webkitSpeechGrammarList", "grammars", "undefined", "speechRecognitionList", "grammar", "addFromString", "console", "log", "error", "initializeAudioContext", "_this", "_asyncToGenerator", "AudioContext", "webkitAudioContext", "create<PERSON><PERSON>yser", "fftSize", "smoothingTimeConstant", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "createMediaStreamSource", "connect", "calibrateAmbientNoise", "_this2", "bufferLength", "frequencyBinCount", "dataArray", "Uint8Array", "samples", "i", "getByteFrequencyData", "average", "reduce", "sum", "value", "push", "Promise", "resolve", "setTimeout", "sample", "length", "Math", "max", "onstart", "emitEvent", "startAudioMonitoring", "on<PERSON>ult", "event", "handleRecognitionResult", "onerror", "handleRecognitionError", "onend", "handleRecognitionEnd", "onspeechstart", "handleSpeechStart", "onsp<PERSON>chend", "handleSpeechEnd", "monitor", "currentLevel", "calculateAudioLevel", "processAudioLevel", "requestAnimationFrame", "weightedSum", "index", "frequency", "weight", "getFrequencyWeight", "now", "Date", "isSpeech", "isAmbientNoise", "clearSilenceTimer", "level", "startSilenceTimer", "finalTranscript", "bestConfidence", "resultIndex", "results", "result", "isFinal", "transcript", "confidence", "calculateBestConfidence", "trim", "cleanText", "cleanTranscript", "isValidTranscript", "text", "success", "stopRecording", "resetState", "errorMessages", "errorMessage", "totalConfidence", "alternatives", "min", "replace", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "minConfidence", "hasValid<PERSON>ords", "test", "hasValidNumbers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordCount", "split", "filter", "word", "isLongNumber", "adjustedMinConfidence", "lengthOk", "confidenceOk", "contentOk", "wordCountOk", "timeSinceLastSpeech", "clearTimeout", "startRecording", "_this3", "start", "name", "stop", "forceStop", "abort", "isCurrentlyRecording", "isSupported", "restartRecording", "_this4", "getAmbientNoiseLevel", "getSpeechThreshold", "cleanupAudioResources", "getTracks", "for<PERSON>ach", "track", "disconnect", "state", "close", "type", "data", "timestamp", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\Service\\voice-recorder.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Subject, BehaviorSubject } from 'rxjs';\r\n\r\nexport interface VoiceResult {\r\n  text: string;\r\n  confidence: number;\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\nexport interface VoiceRecordingEvent {\r\n  type: 'started' | 'stopped' | 'ended_automatically' | 'speech_detected' | 'silence_detected' | 'ambient_noise_detected';\r\n  timestamp: number;\r\n  data?: any;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class VoiceRecorderService {\r\n  private recognition: any;\r\n  private audioContext: AudioContext | null = null;\r\n  private analyser: AnalyserNode | null = null;\r\n  private microphone: MediaStreamAudioSourceNode | null = null;\r\n  private mediaStream: MediaStream | null = null;\r\n  \r\n  private isRecording = false;\r\n  private silenceTimer: any;\r\n  private speechDetected = false;\r\n  private lastSpeechTime = 0;\r\n  private ambientNoiseLevel = 0;\r\n  private speechThreshold = 0.02;\r\n  private silenceThreshold = 0.01;\r\n  private calibrationComplete = false;\r\n\r\n  private readonly resultSubject = new Subject<VoiceResult>();\r\n  private readonly errorSubject = new Subject<string>();\r\n  private readonly recordingSubject = new BehaviorSubject<boolean>(false);\r\n  private readonly recordingEventSubject = new Subject<VoiceRecordingEvent>();\r\n\r\n  readonly result$ = this.resultSubject.asObservable();\r\n  readonly error$ = this.errorSubject.asObservable();\r\n  readonly recording$ = this.recordingSubject.asObservable();\r\n  readonly recordingEvent$ = this.recordingEventSubject.asObservable();\r\n\r\n  constructor() {\r\n    this.initializeSpeechRecognition();\r\n  }\r\n\r\n  //#region Initialization\r\n  private initializeSpeechRecognition(): void {\r\n    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;\r\n\r\n    if (!SpeechRecognition) {\r\n      this.errorSubject.next('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    this.recognition = new SpeechRecognition();\r\n    this.configureRecognition();\r\n    this.setupRecognitionEvents();\r\n  }\r\n\r\n  private configureRecognition(): void {\r\n    this.recognition.continuous = true;\r\n    this.recognition.interimResults = true;\r\n    this.recognition.lang = 'pt-BR';\r\n    this.recognition.maxAlternatives = 3;\r\n\r\n    // Configurações adicionais para melhorar reconhecimento de números\r\n    try {\r\n      const SpeechGrammarList = (window as any).SpeechGrammarList || (window as any).webkitSpeechGrammarList;\r\n      if (SpeechGrammarList && this.recognition.grammars !== undefined) {\r\n        const speechRecognitionList = new SpeechGrammarList();\r\n        // Grammar para números (CPF, telefone, etc.)\r\n        const grammar = '#JSGF V1.0; grammar numbers; public <number> = <digit>+; <digit> = zero | um | dois | três | quatro | cinco | seis | sete | oito | nove;';\r\n        speechRecognitionList.addFromString(grammar, 1);\r\n        this.recognition.grammars = speechRecognitionList;\r\n        console.log('✅ Grammar configurada para melhor reconhecimento de números');\r\n      } else {\r\n        console.log('⚠️ SpeechGrammarList não disponível neste navegador');\r\n      }\r\n    } catch (error) {\r\n      console.log('⚠️ Erro ao configurar grammar, continuando sem ela:', error);\r\n    }\r\n  }\r\n\r\n  private async initializeAudioContext(): Promise<void> {\r\n    try {\r\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\r\n      this.analyser = this.audioContext.createAnalyser();\r\n      this.analyser.fftSize = 256;\r\n      this.analyser.smoothingTimeConstant = 0.8;\r\n\r\n      this.mediaStream = await navigator.mediaDevices.getUserMedia({\r\n        audio: {\r\n          echoCancellation: true,\r\n          noiseSuppression: true,\r\n          autoGainControl: true,\r\n          sampleRate: 44100,\r\n          channelCount: 1\r\n        }\r\n      });\r\n\r\n      this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);\r\n      this.microphone.connect(this.analyser);\r\n      \r\n      await this.calibrateAmbientNoise();\r\n    } catch (error) {\r\n      this.errorSubject.next('Erro ao acessar microfone');\r\n    }\r\n  }\r\n\r\n  private async calibrateAmbientNoise(): Promise<void> {\r\n    if (!this.analyser) return;\r\n\r\n    const bufferLength = this.analyser.frequencyBinCount;\r\n    const dataArray = new Uint8Array(bufferLength);\r\n    const samples: number[] = [];\r\n\r\n    for (let i = 0; i < 30; i++) {\r\n      this.analyser.getByteFrequencyData(dataArray);\r\n      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;\r\n      samples.push(average / 255);\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n    }\r\n\r\n    this.ambientNoiseLevel = samples.reduce((sum, sample) => sum + sample, 0) / samples.length;\r\n    this.speechThreshold = Math.max(this.ambientNoiseLevel * 3, 0.02);\r\n    this.silenceThreshold = Math.max(this.ambientNoiseLevel * 1.5, 0.01);\r\n    this.calibrationComplete = true;\r\n  }\r\n\r\n  private setupRecognitionEvents(): void {\r\n    this.recognition.onstart = () => {\r\n      this.isRecording = true;\r\n      this.speechDetected = false;\r\n      this.lastSpeechTime = 0;\r\n      this.recordingSubject.next(true);\r\n      this.emitEvent('started');\r\n      this.startAudioMonitoring();\r\n    };\r\n\r\n    this.recognition.onresult = (event: any) => {\r\n      this.handleRecognitionResult(event);\r\n    };\r\n\r\n    this.recognition.onerror = (event: any) => {\r\n      this.handleRecognitionError(event);\r\n    };\r\n\r\n    this.recognition.onend = () => {\r\n      this.handleRecognitionEnd();\r\n    };\r\n\r\n    this.recognition.onspeechstart = () => {\r\n      this.handleSpeechStart();\r\n    };\r\n\r\n    this.recognition.onspeechend = () => {\r\n      this.handleSpeechEnd();\r\n    };\r\n  }\r\n  //#endregion\r\n\r\n  //#region Audio Monitoring\r\n  private startAudioMonitoring(): void {\r\n    if (!this.analyser || !this.calibrationComplete) return;\r\n\r\n    const bufferLength = this.analyser.frequencyBinCount;\r\n    const dataArray = new Uint8Array(bufferLength);\r\n\r\n    const monitor = () => {\r\n      if (!this.isRecording) return;\r\n\r\n      this.analyser!.getByteFrequencyData(dataArray);\r\n      const currentLevel = this.calculateAudioLevel(dataArray);\r\n      \r\n      this.processAudioLevel(currentLevel);\r\n      requestAnimationFrame(monitor);\r\n    };\r\n\r\n    monitor();\r\n  }\r\n\r\n  private calculateAudioLevel(dataArray: Uint8Array): number {\r\n    const weightedSum = dataArray.reduce((sum, value, index) => {\r\n      const frequency = (index / dataArray.length) * (this.audioContext!.sampleRate / 2);\r\n      const weight = this.getFrequencyWeight(frequency);\r\n      return sum + (value * weight);\r\n    }, 0);\r\n\r\n    return weightedSum / (dataArray.length * 255);\r\n  }\r\n\r\n  private getFrequencyWeight(frequency: number): number {\r\n    if (frequency < 300) return 0.3;\r\n    if (frequency < 3000) return 1.0;\r\n    if (frequency < 8000) return 0.7;\r\n    return 0.2;\r\n  }\r\n\r\n  private processAudioLevel(currentLevel: number): void {\r\n    const now = Date.now();\r\n    const isSpeech = currentLevel > this.speechThreshold;\r\n    const isAmbientNoise = currentLevel > this.silenceThreshold && currentLevel <= this.speechThreshold;\r\n\r\n    if (isSpeech) {\r\n      this.lastSpeechTime = now;\r\n      if (!this.speechDetected) {\r\n        this.speechDetected = true;\r\n        this.clearSilenceTimer();\r\n        this.emitEvent('speech_detected');\r\n      }\r\n    } else if (isAmbientNoise) {\r\n      this.emitEvent('ambient_noise_detected', { level: currentLevel });\r\n    }\r\n\r\n    if (this.speechDetected && (now - this.lastSpeechTime) > 3000) {\r\n      this.startSilenceTimer();\r\n    }\r\n  }\r\n  //#endregion\r\n\r\n  //#region Event Handlers\r\n  private handleSpeechStart(): void {\r\n    this.speechDetected = true;\r\n    this.lastSpeechTime = Date.now();\r\n    this.clearSilenceTimer();\r\n    this.emitEvent('speech_detected');\r\n  }\r\n\r\n  private handleSpeechEnd(): void {\r\n    this.startSilenceTimer();\r\n    this.emitEvent('silence_detected');\r\n  }\r\n\r\n  private handleRecognitionResult(event: any): void {\r\n    let finalTranscript = '';\r\n    let bestConfidence = 0;\r\n\r\n    for (let i = event.resultIndex; i < event.results.length; i++) {\r\n      const result = event.results[i];\r\n\r\n      if (result.isFinal) {\r\n        const transcript = result[0].transcript;\r\n        const confidence = this.calculateBestConfidence(result);\r\n\r\n        if (confidence > bestConfidence) {\r\n          finalTranscript = transcript;\r\n          bestConfidence = confidence;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (finalTranscript.trim()) {\r\n      const cleanText = this.cleanTranscript(finalTranscript);\r\n      console.log('🎤 Transcript recebido:', finalTranscript);\r\n      console.log('🧹 Texto limpo:', cleanText);\r\n      console.log('📊 Confiança:', bestConfidence);\r\n      console.log('✅ Válido:', this.isValidTranscript(cleanText, bestConfidence));\r\n\r\n      if (this.isValidTranscript(cleanText, bestConfidence)) {\r\n        this.resultSubject.next({\r\n          text: cleanText,\r\n          confidence: bestConfidence,\r\n          success: true\r\n        });\r\n        this.stopRecording();\r\n      } else {\r\n        console.log('❌ Transcript rejeitado - não passou na validação');\r\n      }\r\n    }\r\n  }\r\n\r\n  private handleRecognitionError(event: any): void {\r\n    this.resetState();\r\n    \r\n    const errorMessages = {\r\n      'no-speech': 'Nenhuma fala detectada. Fale mais próximo do microfone.',\r\n      'audio-capture': 'Erro no microfone. Verifique as configurações de áudio.',\r\n      'not-allowed': 'Permissão negada. Permita o acesso ao microfone.',\r\n      'network': 'Erro de conexão. Verifique sua internet.',\r\n      'service-not-allowed': 'Serviço de reconhecimento indisponível.',\r\n      'aborted': 'Reconhecimento cancelado.'\r\n    };\r\n\r\n    const errorMessage = errorMessages[event.error as keyof typeof errorMessages] || \r\n                        'Erro no reconhecimento de voz. Tente novamente.';\r\n\r\n    if (event.error !== 'aborted') {\r\n      this.errorSubject.next(errorMessage);\r\n      this.resultSubject.next({\r\n        text: '',\r\n        confidence: 0,\r\n        success: false,\r\n        error: errorMessage\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleRecognitionEnd(): void {\r\n    if (this.isRecording) {\r\n      this.resetState();\r\n      this.emitEvent('ended_automatically');\r\n    }\r\n  }\r\n  //#endregion\r\n\r\n  //#region Audio Quality Enhancement\r\n  private calculateBestConfidence(result: any): number {\r\n    let totalConfidence = 0;\r\n    const alternatives = Math.min(result.length, 3);\r\n    \r\n    for (let i = 0; i < alternatives; i++) {\r\n      totalConfidence += result[i].confidence || 0.5;\r\n    }\r\n    \r\n    return totalConfidence / alternatives;\r\n  }\r\n\r\n  private cleanTranscript(text: string): string {\r\n    return text\r\n      .trim()\r\n      .replace(/\\s+/g, ' ')\r\n      .replace(/[^\\w\\sáàâãéèêíìîóòôõúùûç.,!?-]/gi, '')\r\n      .toLowerCase();\r\n  }\r\n\r\n  private isValidTranscript(text: string, confidence: number): boolean {\r\n    const minLength = 3;\r\n    const minConfidence = 0.4;\r\n\r\n    // Aceita palavras com letras OU sequências de números\r\n    const hasValidWords = /[a-záàâãéèêíìîóòôõúùûç]{3,}/i.test(text);\r\n    const hasValidNumbers = /\\d{3,}/i.test(text);\r\n    const hasValidContent = hasValidWords || hasValidNumbers;\r\n\r\n    // Conta palavras (incluindo números) com mais de 2 caracteres\r\n    const wordCount = text.split(/\\s+/).filter(word => word.length > 2).length;\r\n\r\n    // Para números longos (como CPF), aceita mesmo com confiança menor\r\n    const isLongNumber = /^\\d{8,}$/.test(text.replace(/\\s/g, ''));\r\n    const adjustedMinConfidence = isLongNumber ? 0.3 : minConfidence;\r\n\r\n    console.log('🔍 Validação transcript:', {\r\n      text,\r\n      length: text.length,\r\n      confidence,\r\n      hasValidWords,\r\n      hasValidNumbers,\r\n      hasValidContent,\r\n      wordCount,\r\n      isLongNumber,\r\n      adjustedMinConfidence,\r\n      lengthOk: text.length >= minLength,\r\n      confidenceOk: confidence >= adjustedMinConfidence,\r\n      contentOk: hasValidContent,\r\n      wordCountOk: wordCount >= 1\r\n    });\r\n\r\n    return text.length >= minLength &&\r\n           confidence >= adjustedMinConfidence &&\r\n           hasValidContent &&\r\n           wordCount >= 1;\r\n  }\r\n  //#endregion\r\n\r\n  //#region Silence Detection\r\n  private startSilenceTimer(): void {\r\n    this.clearSilenceTimer();\r\n    this.silenceTimer = setTimeout(() => {\r\n      if (this.isRecording && this.speechDetected) {\r\n        const timeSinceLastSpeech = Date.now() - this.lastSpeechTime;\r\n        if (timeSinceLastSpeech >= 3000) {\r\n          this.stopRecording();\r\n        }\r\n      }\r\n    }, 3500);\r\n  }\r\n\r\n  private clearSilenceTimer(): void {\r\n    if (this.silenceTimer) {\r\n      clearTimeout(this.silenceTimer);\r\n      this.silenceTimer = null;\r\n    }\r\n  }\r\n  //#endregion\r\n\r\n  //#region Public Methods\r\n  async startRecording(): Promise<boolean> {\r\n    if (!this.recognition) {\r\n      this.errorSubject.next('Reconhecimento de voz não disponível');\r\n      return false;\r\n    }\r\n\r\n    if (this.isRecording) {\r\n      return true;\r\n    }\r\n\r\n    try {\r\n      if (!this.audioContext) {\r\n        await this.initializeAudioContext();\r\n      }\r\n\r\n      this.recognition.start();\r\n      return true;\r\n    } catch (error: any) {\r\n      if (error.name === 'InvalidStateError') {\r\n        this.isRecording = true;\r\n        this.recordingSubject.next(true);\r\n        return true;\r\n      }\r\n      \r\n      this.errorSubject.next('Erro ao iniciar gravação');\r\n      return false;\r\n    }\r\n  }\r\n\r\n  stopRecording(): void {\r\n    if (!this.isRecording) return;\r\n\r\n    try {\r\n      this.recognition?.stop();\r\n      this.resetState();\r\n      this.emitEvent('stopped');\r\n    } catch (error) {\r\n      this.resetState();\r\n    }\r\n  }\r\n\r\n  forceStop(): void {\r\n    try {\r\n      this.recognition?.abort();\r\n    } catch (error) {\r\n      // Ignore errors\r\n    }\r\n    this.resetState();\r\n  }\r\n\r\n  isCurrentlyRecording(): boolean {\r\n    return this.isRecording;\r\n  }\r\n\r\n  isSupported(): boolean {\r\n    return !!this.recognition;\r\n  }\r\n\r\n  async restartRecording(): Promise<boolean> {\r\n    this.stopRecording();\r\n    await new Promise(resolve => setTimeout(resolve, 100));\r\n    return this.startRecording();\r\n  }\r\n\r\n  getAmbientNoiseLevel(): number {\r\n    return this.ambientNoiseLevel;\r\n  }\r\n\r\n  getSpeechThreshold(): number {\r\n    return this.speechThreshold;\r\n  }\r\n  //#endregion\r\n\r\n  //#region Helper Methods\r\n  private resetState(): void {\r\n    this.isRecording = false;\r\n    this.speechDetected = false;\r\n    this.lastSpeechTime = 0;\r\n    this.recordingSubject.next(false);\r\n    this.clearSilenceTimer();\r\n    this.cleanupAudioResources();\r\n  }\r\n\r\n  private cleanupAudioResources(): void {\r\n    if (this.mediaStream) {\r\n      this.mediaStream.getTracks().forEach(track => track.stop());\r\n      this.mediaStream = null;\r\n    }\r\n    \r\n    if (this.microphone) {\r\n      this.microphone.disconnect();\r\n      this.microphone = null;\r\n    }\r\n    \r\n    if (this.audioContext && this.audioContext.state !== 'closed') {\r\n      this.audioContext.close();\r\n      this.audioContext = null;\r\n    }\r\n    \r\n    this.analyser = null;\r\n    this.calibrationComplete = false;\r\n  }\r\n\r\n  private emitEvent(type: VoiceRecordingEvent['type'], data?: any): void {\r\n    this.recordingEventSubject.next({\r\n      type,\r\n      timestamp: Date.now(),\r\n      data\r\n    });\r\n  }\r\n  //#endregion\r\n}"], "mappings": ";AACA,SAASA,OAAO,EAAEC,eAAe,QAAQ,MAAM;;AAkB/C,OAAM,MAAOC,oBAAoB;EACvBC,WAAW;EACXC,YAAY,GAAwB,IAAI;EACxCC,QAAQ,GAAwB,IAAI;EACpCC,UAAU,GAAsC,IAAI;EACpDC,WAAW,GAAuB,IAAI;EAEtCC,WAAW,GAAG,KAAK;EACnBC,YAAY;EACZC,cAAc,GAAG,KAAK;EACtBC,cAAc,GAAG,CAAC;EAClBC,iBAAiB,GAAG,CAAC;EACrBC,eAAe,GAAG,IAAI;EACtBC,gBAAgB,GAAG,IAAI;EACvBC,mBAAmB,GAAG,KAAK;EAElBC,aAAa,GAAG,IAAIhB,OAAO,EAAe;EAC1CiB,YAAY,GAAG,IAAIjB,OAAO,EAAU;EACpCkB,gBAAgB,GAAG,IAAIjB,eAAe,CAAU,KAAK,CAAC;EACtDkB,qBAAqB,GAAG,IAAInB,OAAO,EAAuB;EAElEoB,OAAO,GAAG,IAAI,CAACJ,aAAa,CAACK,YAAY,EAAE;EAC3CC,MAAM,GAAG,IAAI,CAACL,YAAY,CAACI,YAAY,EAAE;EACzCE,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACG,YAAY,EAAE;EACjDG,eAAe,GAAG,IAAI,CAACL,qBAAqB,CAACE,YAAY,EAAE;EAEpEI,YAAA;IACE,IAAI,CAACC,2BAA2B,EAAE;EACpC;EAEA;EACQA,2BAA2BA,CAAA;IACjC,MAAMC,iBAAiB,GAAIC,MAAc,CAACD,iBAAiB,IAAKC,MAAc,CAACC,uBAAuB;IAEtG,IAAI,CAACF,iBAAiB,EAAE;MACtB,IAAI,CAACV,YAAY,CAACa,IAAI,CAAC,qDAAqD,CAAC;MAC7E;IACF;IAEA,IAAI,CAAC3B,WAAW,GAAG,IAAIwB,iBAAiB,EAAE;IAC1C,IAAI,CAACI,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQD,oBAAoBA,CAAA;IAC1B,IAAI,CAAC5B,WAAW,CAAC8B,UAAU,GAAG,IAAI;IAClC,IAAI,CAAC9B,WAAW,CAAC+B,cAAc,GAAG,IAAI;IACtC,IAAI,CAAC/B,WAAW,CAACgC,IAAI,GAAG,OAAO;IAC/B,IAAI,CAAChC,WAAW,CAACiC,eAAe,GAAG,CAAC;IAEpC;IACA,IAAI;MACF,MAAMC,iBAAiB,GAAIT,MAAc,CAACS,iBAAiB,IAAKT,MAAc,CAACU,uBAAuB;MACtG,IAAID,iBAAiB,IAAI,IAAI,CAAClC,WAAW,CAACoC,QAAQ,KAAKC,SAAS,EAAE;QAChE,MAAMC,qBAAqB,GAAG,IAAIJ,iBAAiB,EAAE;QACrD;QACA,MAAMK,OAAO,GAAG,0IAA0I;QAC1JD,qBAAqB,CAACE,aAAa,CAACD,OAAO,EAAE,CAAC,CAAC;QAC/C,IAAI,CAACvC,WAAW,CAACoC,QAAQ,GAAGE,qBAAqB;QACjDG,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC5E,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MACpE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEC,KAAK,CAAC;IAC3E;EACF;EAEcC,sBAAsBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClC,IAAI;QACFD,KAAI,CAAC5C,YAAY,GAAG,KAAKwB,MAAM,CAACsB,YAAY,IAAKtB,MAAc,CAACuB,kBAAkB,EAAC,CAAE;QACrFH,KAAI,CAAC3C,QAAQ,GAAG2C,KAAI,CAAC5C,YAAY,CAACgD,cAAc,EAAE;QAClDJ,KAAI,CAAC3C,QAAQ,CAACgD,OAAO,GAAG,GAAG;QAC3BL,KAAI,CAAC3C,QAAQ,CAACiD,qBAAqB,GAAG,GAAG;QAEzCN,KAAI,CAACzC,WAAW,SAASgD,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAC3DC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEFf,KAAI,CAAC1C,UAAU,GAAG0C,KAAI,CAAC5C,YAAY,CAAC4D,uBAAuB,CAAChB,KAAI,CAACzC,WAAW,CAAC;QAC7EyC,KAAI,CAAC1C,UAAU,CAAC2D,OAAO,CAACjB,KAAI,CAAC3C,QAAQ,CAAC;QAEtC,MAAM2C,KAAI,CAACkB,qBAAqB,EAAE;MACpC,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdE,KAAI,CAAC/B,YAAY,CAACa,IAAI,CAAC,2BAA2B,CAAC;MACrD;IAAC;EACH;EAEcoC,qBAAqBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MACjC,IAAI,CAACkB,MAAI,CAAC9D,QAAQ,EAAE;MAEpB,MAAM+D,YAAY,GAAGD,MAAI,CAAC9D,QAAQ,CAACgE,iBAAiB;MACpD,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACH,YAAY,CAAC;MAC9C,MAAMI,OAAO,GAAa,EAAE;MAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BN,MAAI,CAAC9D,QAAQ,CAACqE,oBAAoB,CAACJ,SAAS,CAAC;QAC7C,MAAMK,OAAO,GAAGL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGV,YAAY;QAC/EI,OAAO,CAACO,IAAI,CAACJ,OAAO,GAAG,GAAG,CAAC;QAC3B,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACxD;MAEAd,MAAI,CAACvD,iBAAiB,GAAG4D,OAAO,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEM,MAAM,KAAKN,GAAG,GAAGM,MAAM,EAAE,CAAC,CAAC,GAAGX,OAAO,CAACY,MAAM;MAC1FjB,MAAI,CAACtD,eAAe,GAAGwE,IAAI,CAACC,GAAG,CAACnB,MAAI,CAACvD,iBAAiB,GAAG,CAAC,EAAE,IAAI,CAAC;MACjEuD,MAAI,CAACrD,gBAAgB,GAAGuE,IAAI,CAACC,GAAG,CAACnB,MAAI,CAACvD,iBAAiB,GAAG,GAAG,EAAE,IAAI,CAAC;MACpEuD,MAAI,CAACpD,mBAAmB,GAAG,IAAI;IAAC;EAClC;EAEQiB,sBAAsBA,CAAA;IAC5B,IAAI,CAAC7B,WAAW,CAACoF,OAAO,GAAG,MAAK;MAC9B,IAAI,CAAC/E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACC,cAAc,GAAG,CAAC;MACvB,IAAI,CAACO,gBAAgB,CAACY,IAAI,CAAC,IAAI,CAAC;MAChC,IAAI,CAAC0D,SAAS,CAAC,SAAS,CAAC;MACzB,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC;IAED,IAAI,CAACtF,WAAW,CAACuF,QAAQ,GAAIC,KAAU,IAAI;MACzC,IAAI,CAACC,uBAAuB,CAACD,KAAK,CAAC;IACrC,CAAC;IAED,IAAI,CAACxF,WAAW,CAAC0F,OAAO,GAAIF,KAAU,IAAI;MACxC,IAAI,CAACG,sBAAsB,CAACH,KAAK,CAAC;IACpC,CAAC;IAED,IAAI,CAACxF,WAAW,CAAC4F,KAAK,GAAG,MAAK;MAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC;IAED,IAAI,CAAC7F,WAAW,CAAC8F,aAAa,GAAG,MAAK;MACpC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC;IAED,IAAI,CAAC/F,WAAW,CAACgG,WAAW,GAAG,MAAK;MAClC,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC;EACH;EACA;EAEA;EACQX,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACpF,QAAQ,IAAI,CAAC,IAAI,CAACU,mBAAmB,EAAE;IAEjD,MAAMqD,YAAY,GAAG,IAAI,CAAC/D,QAAQ,CAACgE,iBAAiB;IACpD,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACH,YAAY,CAAC;IAE9C,MAAMiC,OAAO,GAAGA,CAAA,KAAK;MACnB,IAAI,CAAC,IAAI,CAAC7F,WAAW,EAAE;MAEvB,IAAI,CAACH,QAAS,CAACqE,oBAAoB,CAACJ,SAAS,CAAC;MAC9C,MAAMgC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACjC,SAAS,CAAC;MAExD,IAAI,CAACkC,iBAAiB,CAACF,YAAY,CAAC;MACpCG,qBAAqB,CAACJ,OAAO,CAAC;IAChC,CAAC;IAEDA,OAAO,EAAE;EACX;EAEQE,mBAAmBA,CAACjC,SAAqB;IAC/C,MAAMoC,WAAW,GAAGpC,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAE6B,KAAK,KAAI;MACzD,MAAMC,SAAS,GAAID,KAAK,GAAGrC,SAAS,CAACc,MAAM,IAAK,IAAI,CAAChF,YAAa,CAAC0D,UAAU,GAAG,CAAC,CAAC;MAClF,MAAM+C,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACF,SAAS,CAAC;MACjD,OAAO/B,GAAG,GAAIC,KAAK,GAAG+B,MAAO;IAC/B,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOH,WAAW,IAAIpC,SAAS,CAACc,MAAM,GAAG,GAAG,CAAC;EAC/C;EAEQ0B,kBAAkBA,CAACF,SAAiB;IAC1C,IAAIA,SAAS,GAAG,GAAG,EAAE,OAAO,GAAG;IAC/B,IAAIA,SAAS,GAAG,IAAI,EAAE,OAAO,GAAG;IAChC,IAAIA,SAAS,GAAG,IAAI,EAAE,OAAO,GAAG;IAChC,OAAO,GAAG;EACZ;EAEQJ,iBAAiBA,CAACF,YAAoB;IAC5C,MAAMS,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;IACtB,MAAME,QAAQ,GAAGX,YAAY,GAAG,IAAI,CAACzF,eAAe;IACpD,MAAMqG,cAAc,GAAGZ,YAAY,GAAG,IAAI,CAACxF,gBAAgB,IAAIwF,YAAY,IAAI,IAAI,CAACzF,eAAe;IAEnG,IAAIoG,QAAQ,EAAE;MACZ,IAAI,CAACtG,cAAc,GAAGoG,GAAG;MACzB,IAAI,CAAC,IAAI,CAACrG,cAAc,EAAE;QACxB,IAAI,CAACA,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACyG,iBAAiB,EAAE;QACxB,IAAI,CAAC3B,SAAS,CAAC,iBAAiB,CAAC;MACnC;IACF,CAAC,MAAM,IAAI0B,cAAc,EAAE;MACzB,IAAI,CAAC1B,SAAS,CAAC,wBAAwB,EAAE;QAAE4B,KAAK,EAAEd;MAAY,CAAE,CAAC;IACnE;IAEA,IAAI,IAAI,CAAC5F,cAAc,IAAKqG,GAAG,GAAG,IAAI,CAACpG,cAAc,GAAI,IAAI,EAAE;MAC7D,IAAI,CAAC0G,iBAAiB,EAAE;IAC1B;EACF;EACA;EAEA;EACQnB,iBAAiBA,CAAA;IACvB,IAAI,CAACxF,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAGqG,IAAI,CAACD,GAAG,EAAE;IAChC,IAAI,CAACI,iBAAiB,EAAE;IACxB,IAAI,CAAC3B,SAAS,CAAC,iBAAiB,CAAC;EACnC;EAEQY,eAAeA,CAAA;IACrB,IAAI,CAACiB,iBAAiB,EAAE;IACxB,IAAI,CAAC7B,SAAS,CAAC,kBAAkB,CAAC;EACpC;EAEQI,uBAAuBA,CAACD,KAAU;IACxC,IAAI2B,eAAe,GAAG,EAAE;IACxB,IAAIC,cAAc,GAAG,CAAC;IAEtB,KAAK,IAAI9C,CAAC,GAAGkB,KAAK,CAAC6B,WAAW,EAAE/C,CAAC,GAAGkB,KAAK,CAAC8B,OAAO,CAACrC,MAAM,EAAEX,CAAC,EAAE,EAAE;MAC7D,MAAMiD,MAAM,GAAG/B,KAAK,CAAC8B,OAAO,CAAChD,CAAC,CAAC;MAE/B,IAAIiD,MAAM,CAACC,OAAO,EAAE;QAClB,MAAMC,UAAU,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACE,UAAU;QACvC,MAAMC,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAACJ,MAAM,CAAC;QAEvD,IAAIG,UAAU,GAAGN,cAAc,EAAE;UAC/BD,eAAe,GAAGM,UAAU;UAC5BL,cAAc,GAAGM,UAAU;QAC7B;MACF;IACF;IAEA,IAAIP,eAAe,CAACS,IAAI,EAAE,EAAE;MAC1B,MAAMC,SAAS,GAAG,IAAI,CAACC,eAAe,CAACX,eAAe,CAAC;MACvD1E,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyE,eAAe,CAAC;MACvD1E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmF,SAAS,CAAC;MACzCpF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE0E,cAAc,CAAC;MAC5C3E,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACqF,iBAAiB,CAACF,SAAS,EAAET,cAAc,CAAC,CAAC;MAE3E,IAAI,IAAI,CAACW,iBAAiB,CAACF,SAAS,EAAET,cAAc,CAAC,EAAE;QACrD,IAAI,CAACvG,aAAa,CAACc,IAAI,CAAC;UACtBqG,IAAI,EAAEH,SAAS;UACfH,UAAU,EAAEN,cAAc;UAC1Ba,OAAO,EAAE;SACV,CAAC;QACF,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC,MAAM;QACLzF,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MACjE;IACF;EACF;EAEQiD,sBAAsBA,CAACH,KAAU;IACvC,IAAI,CAAC2C,UAAU,EAAE;IAEjB,MAAMC,aAAa,GAAG;MACpB,WAAW,EAAE,yDAAyD;MACtE,eAAe,EAAE,yDAAyD;MAC1E,aAAa,EAAE,kDAAkD;MACjE,SAAS,EAAE,0CAA0C;MACrD,qBAAqB,EAAE,yCAAyC;MAChE,SAAS,EAAE;KACZ;IAED,MAAMC,YAAY,GAAGD,aAAa,CAAC5C,KAAK,CAAC7C,KAAmC,CAAC,IACzD,iDAAiD;IAErE,IAAI6C,KAAK,CAAC7C,KAAK,KAAK,SAAS,EAAE;MAC7B,IAAI,CAAC7B,YAAY,CAACa,IAAI,CAAC0G,YAAY,CAAC;MACpC,IAAI,CAACxH,aAAa,CAACc,IAAI,CAAC;QACtBqG,IAAI,EAAE,EAAE;QACRN,UAAU,EAAE,CAAC;QACbO,OAAO,EAAE,KAAK;QACdtF,KAAK,EAAE0F;OACR,CAAC;IACJ;EACF;EAEQxC,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACxF,WAAW,EAAE;MACpB,IAAI,CAAC8H,UAAU,EAAE;MACjB,IAAI,CAAC9C,SAAS,CAAC,qBAAqB,CAAC;IACvC;EACF;EACA;EAEA;EACQsC,uBAAuBA,CAACJ,MAAW;IACzC,IAAIe,eAAe,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGrD,IAAI,CAACsD,GAAG,CAACjB,MAAM,CAACtC,MAAM,EAAE,CAAC,CAAC;IAE/C,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,YAAY,EAAEjE,CAAC,EAAE,EAAE;MACrCgE,eAAe,IAAIf,MAAM,CAACjD,CAAC,CAAC,CAACoD,UAAU,IAAI,GAAG;IAChD;IAEA,OAAOY,eAAe,GAAGC,YAAY;EACvC;EAEQT,eAAeA,CAACE,IAAY;IAClC,OAAOA,IAAI,CACRJ,IAAI,EAAE,CACNa,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAC/CC,WAAW,EAAE;EAClB;EAEQX,iBAAiBA,CAACC,IAAY,EAAEN,UAAkB;IACxD,MAAMiB,SAAS,GAAG,CAAC;IACnB,MAAMC,aAAa,GAAG,GAAG;IAEzB;IACA,MAAMC,aAAa,GAAG,8BAA8B,CAACC,IAAI,CAACd,IAAI,CAAC;IAC/D,MAAMe,eAAe,GAAG,SAAS,CAACD,IAAI,CAACd,IAAI,CAAC;IAC5C,MAAMgB,eAAe,GAAGH,aAAa,IAAIE,eAAe;IAExD;IACA,MAAME,SAAS,GAAGjB,IAAI,CAACkB,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACnE,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;IAE1E;IACA,MAAMoE,YAAY,GAAG,UAAU,CAACP,IAAI,CAACd,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7D,MAAMa,qBAAqB,GAAGD,YAAY,GAAG,GAAG,GAAGT,aAAa;IAEhEnG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACtCsF,IAAI;MACJ/C,MAAM,EAAE+C,IAAI,CAAC/C,MAAM;MACnByC,UAAU;MACVmB,aAAa;MACbE,eAAe;MACfC,eAAe;MACfC,SAAS;MACTI,YAAY;MACZC,qBAAqB;MACrBC,QAAQ,EAAEvB,IAAI,CAAC/C,MAAM,IAAI0D,SAAS;MAClCa,YAAY,EAAE9B,UAAU,IAAI4B,qBAAqB;MACjDG,SAAS,EAAET,eAAe;MAC1BU,WAAW,EAAET,SAAS,IAAI;KAC3B,CAAC;IAEF,OAAOjB,IAAI,CAAC/C,MAAM,IAAI0D,SAAS,IACxBjB,UAAU,IAAI4B,qBAAqB,IACnCN,eAAe,IACfC,SAAS,IAAI,CAAC;EACvB;EACA;EAEA;EACQ/B,iBAAiBA,CAAA;IACvB,IAAI,CAACF,iBAAiB,EAAE;IACxB,IAAI,CAAC1G,YAAY,GAAGyE,UAAU,CAAC,MAAK;MAClC,IAAI,IAAI,CAAC1E,WAAW,IAAI,IAAI,CAACE,cAAc,EAAE;QAC3C,MAAMoJ,mBAAmB,GAAG9C,IAAI,CAACD,GAAG,EAAE,GAAG,IAAI,CAACpG,cAAc;QAC5D,IAAImJ,mBAAmB,IAAI,IAAI,EAAE;UAC/B,IAAI,CAACzB,aAAa,EAAE;QACtB;MACF;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEQlB,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC1G,YAAY,EAAE;MACrBsJ,YAAY,CAAC,IAAI,CAACtJ,YAAY,CAAC;MAC/B,IAAI,CAACA,YAAY,GAAG,IAAI;IAC1B;EACF;EACA;EAEA;EACMuJ,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhH,iBAAA;MAClB,IAAI,CAACgH,MAAI,CAAC9J,WAAW,EAAE;QACrB8J,MAAI,CAAChJ,YAAY,CAACa,IAAI,CAAC,sCAAsC,CAAC;QAC9D,OAAO,KAAK;MACd;MAEA,IAAImI,MAAI,CAACzJ,WAAW,EAAE;QACpB,OAAO,IAAI;MACb;MAEA,IAAI;QACF,IAAI,CAACyJ,MAAI,CAAC7J,YAAY,EAAE;UACtB,MAAM6J,MAAI,CAAClH,sBAAsB,EAAE;QACrC;QAEAkH,MAAI,CAAC9J,WAAW,CAAC+J,KAAK,EAAE;QACxB,OAAO,IAAI;MACb,CAAC,CAAC,OAAOpH,KAAU,EAAE;QACnB,IAAIA,KAAK,CAACqH,IAAI,KAAK,mBAAmB,EAAE;UACtCF,MAAI,CAACzJ,WAAW,GAAG,IAAI;UACvByJ,MAAI,CAAC/I,gBAAgB,CAACY,IAAI,CAAC,IAAI,CAAC;UAChC,OAAO,IAAI;QACb;QAEAmI,MAAI,CAAChJ,YAAY,CAACa,IAAI,CAAC,0BAA0B,CAAC;QAClD,OAAO,KAAK;MACd;IAAC;EACH;EAEAuG,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC7H,WAAW,EAAE;IAEvB,IAAI;MACF,IAAI,CAACL,WAAW,EAAEiK,IAAI,EAAE;MACxB,IAAI,CAAC9B,UAAU,EAAE;MACjB,IAAI,CAAC9C,SAAS,CAAC,SAAS,CAAC;IAC3B,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACd,IAAI,CAACwF,UAAU,EAAE;IACnB;EACF;EAEA+B,SAASA,CAAA;IACP,IAAI;MACF,IAAI,CAAClK,WAAW,EAAEmK,KAAK,EAAE;IAC3B,CAAC,CAAC,OAAOxH,KAAK,EAAE;MACd;IAAA;IAEF,IAAI,CAACwF,UAAU,EAAE;EACnB;EAEAiC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/J,WAAW;EACzB;EAEAgK,WAAWA,CAAA;IACT,OAAO,CAAC,CAAC,IAAI,CAACrK,WAAW;EAC3B;EAEMsK,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzH,iBAAA;MACpByH,MAAI,CAACrC,aAAa,EAAE;MACpB,MAAM,IAAIrD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,OAAOyF,MAAI,CAACV,cAAc,EAAE;IAAC;EAC/B;EAEAW,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/J,iBAAiB;EAC/B;EAEAgK,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC/J,eAAe;EAC7B;EACA;EAEA;EACQyH,UAAUA,CAAA;IAChB,IAAI,CAAC9H,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACO,gBAAgB,CAACY,IAAI,CAAC,KAAK,CAAC;IACjC,IAAI,CAACqF,iBAAiB,EAAE;IACxB,IAAI,CAAC0D,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACtK,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACuK,SAAS,EAAE,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACZ,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAC7J,WAAW,GAAG,IAAI;IACzB;IAEA,IAAI,IAAI,CAACD,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAAC2K,UAAU,EAAE;MAC5B,IAAI,CAAC3K,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI,IAAI,CAACF,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8K,KAAK,KAAK,QAAQ,EAAE;MAC7D,IAAI,CAAC9K,YAAY,CAAC+K,KAAK,EAAE;MACzB,IAAI,CAAC/K,YAAY,GAAG,IAAI;IAC1B;IAEA,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACU,mBAAmB,GAAG,KAAK;EAClC;EAEQyE,SAASA,CAAC4F,IAAiC,EAAEC,IAAU;IAC7D,IAAI,CAAClK,qBAAqB,CAACW,IAAI,CAAC;MAC9BsJ,IAAI;MACJE,SAAS,EAAEtE,IAAI,CAACD,GAAG,EAAE;MACrBsE;KACD,CAAC;EACJ;;qBAheWnL,oBAAoB;EAAA;;WAApBA,oBAAoB;IAAAqL,OAAA,EAApBrL,oBAAoB,CAAAsL,IAAA;IAAAC,UAAA,EAFnB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport { VoiceRecorderService } from './Service/voice-recorder.service';\nimport { SpeakerService } from './Service/speaker.service';\nimport { AiQuestionarioApiService } from './Service/ai-questionario-api.service';\nimport { AlertComponent } from 'src/app/alert/alert.component';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"./Service/voice-recorder.service\";\nimport * as i4 from \"./Service/speaker.service\";\nimport * as i5 from \"./Service/ai-questionario-api.service\";\nimport * as i6 from \"src/app/alert/alert.component\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/tooltip\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nfunction PreConsultaQuestionarioComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n    i0.ɵɵelement(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵelement(9, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Vou coletar suas informa\\u00E7\\u00F5es atrav\\u00E9s de uma conversa natural\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.iniciarAtendimento());\n    });\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Iniciar Atendimento\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 15);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 38);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.displayedText || ctx_r1.aiResponse);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6, \"Processando sua resposta...\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46)(3, \"mat-icon\", 47);\n    i0.ɵɵtext(4, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"A informa\\u00E7\\u00E3o est\\u00E1 correta?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.confirmarInformacao());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Confirmar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.tentarNovamente());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Tentar Novamente \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lastItem_r6 = ctx.ngIf;\n    i0.ɵɵproperty(\"matTooltip\", lastItem_r6.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r6.value);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61);\n    i0.ɵɵelement(2, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getDadosPreenchidos().length, \"/\", ctx_r1.getTotalCampos(), \" campos preenchidos\");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u00DAltima Informa\\u00E7\\u00E3o Coletada\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openHistoryModal());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"history\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, PreConsultaQuestionarioComponent_div_2_div_21_div_8_Template, 5, 3, \"div\", 55)(9, PreConsultaQuestionarioComponent_div_2_div_21_div_9_Template, 5, 4, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getUltimaVariavelPreenchida());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"send\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSendText());\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_mat_hint_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 71)(1, \"mat-icon\", 72);\n    i0.ɵɵtext(2, \"volume_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Aguarde o t\\u00E9rmino da fala... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"mat-form-field\", 66)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Sua resposta\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_div_22_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.userInput, $event) || (ctx_r1.userInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function PreConsultaQuestionarioComponent_div_2_div_22_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template, 3, 1, \"button\", 68)(7, PreConsultaQuestionarioComponent_div_2_div_22_mat_hint_7_Template, 4, 0, \"mat-hint\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.userInput);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isInputDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canSendText());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSpeaking);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 78);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵtemplate(2, PreConsultaQuestionarioComponent_div_2_div_23_div_2_Template, 1, 0, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 76)(4, \"mat-icon\", 77);\n    i0.ɵɵtext(5, \"mic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Gravando... \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_24_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 83);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 80)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PreConsultaQuestionarioComponent_div_2_div_24_div_4_Template, 1, 0, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"recording\", ctx_r1.isRecording)(\"processing\", ctx_r1.isProcessing)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording && !ctx_r1.isProcessing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isRecording ? \"mic\" : ctx_r1.isProcessing ? \"hourglass_empty\" : ctx_r1.isAguardandoResposta ? \"hearing\" : \"mic_off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isRecording ? \"Ouvindo...\" : ctx_r1.isProcessing ? \"Processando...\" : ctx_r1.isAguardandoResposta ? \"Carregando microfone...\" : ctx_r1.isSpeaking ? \"Falando...\" : \"Aguardando...\", \" \");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"mat-icon\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-slide-toggle\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.isTextMode, $event) || (ctx_r1.isTextMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_change_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTextMode());\n    });\n    i0.ɵɵtext(6, \" Modo Texto \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"div\", 22)(9, \"div\", 23)(10, \"div\", 24)(11, \"div\", 25);\n    i0.ɵɵelement(12, \"div\", 26)(13, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, PreConsultaQuestionarioComponent_div_2_div_15_Template, 1, 0, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵtemplate(18, PreConsultaQuestionarioComponent_div_2_div_18_Template, 4, 2, \"div\", 31)(19, PreConsultaQuestionarioComponent_div_2_div_19_Template, 7, 1, \"div\", 32)(20, PreConsultaQuestionarioComponent_div_2_div_20_Template, 16, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, PreConsultaQuestionarioComponent_div_2_div_21_Template, 10, 3, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PreConsultaQuestionarioComponent_div_2_div_22_Template, 8, 4, \"div\", 35)(23, PreConsultaQuestionarioComponent_div_2_div_23_Template, 7, 3, \"div\", 36)(24, PreConsultaQuestionarioComponent_div_2_div_24_Template, 7, 10, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.isTextMode ? \"keyboard\" : \"mic\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.isTextMode);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing || ctx_r1.isSpeaking);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"processing\", ctx_r1.isProcessing)(\"listening\", ctx_r1.isRecording)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"talking\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing || ctx_r1.isRecording || ctx_r1.isAguardandoResposta);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.aiResponse);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showValidationButtons && !ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording && !ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTextMode);\n  }\n}\nexport class PreConsultaQuestionarioComponent {\n  router;\n  dialog;\n  voiceRecorder;\n  speaker;\n  aiService;\n  snackBar;\n  cdr;\n  isIdle = true;\n  isProcessing = false;\n  isTextMode = false;\n  isRecording = false;\n  isAguardandoResposta = false;\n  campoAtual = '';\n  isHistoryModalOpen = false;\n  showValidationButtons = false;\n  isSpeaking = false;\n  displayedText = '';\n  fullResponseText = '';\n  textDisplayInterval;\n  searchTerm = '';\n  availableFilters = [{\n    type: 'personal',\n    label: 'Dados Pessoais',\n    icon: 'person',\n    active: true\n  }, {\n    type: 'medical',\n    label: 'Informações Médicas',\n    icon: 'medical_services',\n    active: true\n  }, {\n    type: 'contact',\n    label: 'Contato',\n    icon: 'contact_phone',\n    active: true\n  }, {\n    type: 'optional',\n    label: 'Opcionais',\n    icon: 'info',\n    active: true\n  }];\n  conversationHistory = [];\n  currentToken = '';\n  aiResponse = '';\n  userInput = '';\n  dadosColetados = {\n    nome: '',\n    cpf: '',\n    email: '',\n    telefone: '',\n    dataNascimento: '',\n    alergias: '',\n    sintomas: '',\n    intensidadeDor: '',\n    tempoSintomas: '',\n    doencasPrevias: '',\n    observacoes: ''\n  };\n  destroy$ = new Subject();\n  constructor(router, dialog, voiceRecorder, speaker, aiService, snackBar, cdr) {\n    this.router = router;\n    this.dialog = dialog;\n    this.voiceRecorder = voiceRecorder;\n    this.speaker = speaker;\n    this.aiService = aiService;\n    this.snackBar = snackBar;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.preloadVoices();\n    this.startSpeakingMonitor();\n    this.voiceRecorder.recording$.pipe(takeUntil(this.destroy$)).subscribe(isRecording => {\n      this.isRecording = isRecording;\n      this.cdr.detectChanges();\n    });\n    this.voiceRecorder.result$.pipe(takeUntil(this.destroy$)).subscribe(result => {\n      if (result.success && result.text) {\n        this.adicionarRespostaUsuario(result.text);\n      }\n      this.cdr.detectChanges();\n    });\n    this.voiceRecorder.error$.pipe(takeUntil(this.destroy$)).subscribe(error => {\n      console.error('Erro de reconhecimento de voz:', error);\n      this.isRecording = false;\n      this.cdr.detectChanges();\n      if (!error.includes('aborted')) {\n        this.snackBar.falhaSnackbar(error);\n        if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {\n          this.iniciarGravacaoAutomatica();\n        }\n      }\n    });\n    this.voiceRecorder.recordingEvent$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {\n        if (this.speaker.isSpeaking()) {\n          this.speaker.waitUntilFinished().then(() => {\n            if (this.isAguardandoResposta && !this.isRecording) {\n              this.iniciarGravacaoAutomatica();\n            }\n          });\n        } else {\n          setTimeout(() => {\n            if (this.isAguardandoResposta && !this.isRecording) {\n              this.iniciarGravacaoAutomatica();\n            }\n          }, 500);\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.speaker.cancel();\n    this.voiceRecorder.stopRecording();\n    this.clearTextDisplayInterval();\n  }\n  iniciarAtendimento() {\n    this.isIdle = false;\n    this.cdr.detectChanges();\n    this.currentToken = this.generateToken();\n    this.conversationHistory = ['Iniciar atendimento'];\n    this.campoAtual = 'inicio';\n    if (!this.isTextMode) {\n      this.solicitarPermissaoMicrofone();\n    }\n    this.enviarMensagemParaIA();\n  }\n  toggleTextMode() {\n    const previousMode = this.isTextMode;\n    this.cdr.detectChanges();\n    this.userInput = '';\n    // Handle microphone state management based on mode transition\n    this.handleMicrophoneStateTransition(previousMode, this.isTextMode);\n  }\n  /**\n   * Manages microphone state when switching between text and voice modes\n   * @param previousTextMode - Previous state of isTextMode\n   * @param currentTextMode - Current state of isTextMode\n   */\n  handleMicrophoneStateTransition(previousTextMode, currentTextMode) {\n    // Switching FROM voice mode TO text mode\n    if (!previousTextMode && currentTextMode) {\n      this.deactivateMicrophoneForTextMode();\n    }\n    // Switching FROM text mode TO voice mode\n    else if (previousTextMode && !currentTextMode) {\n      this.activateMicrophoneForVoiceMode();\n    }\n  }\n  /**\n   * Deactivates microphone when switching to text mode\n   * - Stops any active recording immediately\n   * - Cancels pending voice input processing\n   * - Ensures complete microphone cleanup\n   */\n  deactivateMicrophoneForTextMode() {\n    console.log('🔇 Desativando microfone - mudança para modo texto');\n    try {\n      // Stop any active recording immediately\n      if (this.voiceRecorder.isCurrentlyRecording()) {\n        console.log('⏹️ Parando gravação ativa');\n        this.voiceRecorder.stopRecording();\n      }\n      // Update recording state\n      this.isRecording = false;\n      // Clear any pending voice input processing\n      this.isAguardandoResposta = false;\n      console.log('✅ Microfone desativado com sucesso para modo texto');\n    } catch (error) {\n      console.error('❌ Erro ao desativar microfone:', error);\n    }\n    this.cdr.detectChanges();\n  }\n  /**\n   * Activates microphone when switching to voice mode\n   * - Initializes microphone if AI response reading is complete\n   * - Requests permissions if needed\n   * - Prepares for immediate voice input\n   */\n  activateMicrophoneForVoiceMode() {\n    console.log('🎤 Ativando microfone - mudança para modo voz');\n    try {\n      // Only activate if AI is not currently speaking\n      if (this.speaker.isSpeaking()) {\n        console.log('🔊 Aguardando fim da fala da IA para ativar microfone');\n        this.speaker.waitUntilFinished().then(() => {\n          this.initializeMicrophoneForVoiceMode();\n        });\n      } else {\n        this.initializeMicrophoneForVoiceMode();\n      }\n    } catch (error) {\n      console.error('❌ Erro ao ativar microfone:', error);\n      this.snackBar.falhaSnackbar('Erro ao ativar o microfone. Tente novamente.');\n    }\n  }\n  /**\n   * Initializes microphone for voice mode operation\n   */\n  initializeMicrophoneForVoiceMode() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Request microphone permissions\n        yield _this.solicitarPermissaoMicrofone();\n        // If we're waiting for user response, start recording automatically\n        if (_this.isAguardandoResposta && !_this.isProcessing) {\n          console.log('🎯 Iniciando gravação automática após ativação do microfone');\n          setTimeout(() => {\n            if (!_this.isTextMode && _this.isAguardandoResposta) {\n              _this.iniciarGravacaoAutomatica();\n            }\n          }, 500);\n        }\n        console.log('✅ Microfone ativado com sucesso para modo voz');\n      } catch (error) {\n        console.error('❌ Erro ao inicializar microfone para modo voz:', error);\n        _this.snackBar.falhaSnackbar('Erro ao inicializar o microfone. Verifique as permissões.');\n      }\n      _this.cdr.detectChanges();\n    })();\n  }\n  enviarMensagemParaIA() {\n    this.isProcessing = true;\n    this.cdr.detectChanges();\n    const ultimaMensagem = this.conversationHistory.length > 0 ? this.conversationHistory[this.conversationHistory.length - 1] : '';\n    const payload = {\n      formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',\n      historicoConversa: [...this.conversationHistory],\n      ultimaMensagem: ultimaMensagem,\n      campoAtual: this.campoAtual,\n      token: this.currentToken\n    };\n    if (this.conversationHistory.length > 1 && !this.isTextMode) {\n      if (this.voiceRecorder.isCurrentlyRecording()) {\n        this.voiceRecorder.stopRecording();\n      }\n    }\n    this.aiService.enviarMensagens(payload).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.processarRespostaIA(response);\n      },\n      error: error => {\n        this.isProcessing = false;\n        this.cdr.detectChanges();\n        this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\n        console.error('Erro na comunicação com IA:', error);\n      }\n    });\n  }\n  processarRespostaIA(response) {\n    this.isProcessing = false;\n    this.aiResponse = response.textoResposta;\n    this.fullResponseText = response.textoResposta;\n    this.displayedText = '';\n    this.adicionarRespostaIA(response.textoResposta);\n    if (response.campoAtual) {\n      this.campoAtual = response.campoAtual;\n    }\n    // Handle validation confirmation\n    this.showValidationButtons = response.flgValidacaoCampoAtual || false;\n    this.cdr.detectChanges();\n    this.atualizarDadosColetados(response.dados);\n    this.cdr.detectChanges();\n    if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {\n      this.finalizarProcesso();\n    } else {\n      // Preparar texto para exibição, mas não iniciar ainda\n      this.displayedText = '';\n      this.cdr.detectChanges();\n      // Iniciar áudio com callback para sincronizar texto\n      this.speaker.speak(response.textoResposta, () => {\n        // Este callback é executado quando o áudio realmente começa a tocar\n        console.log('🎯 Iniciando sincronização de texto com áudio');\n        this.startSynchronizedTextDisplay();\n      }).then(() => {\n        // Only start waiting for response if validation buttons are not shown\n        if (!this.showValidationButtons) {\n          this.iniciarProcessoAguardandoResposta();\n        }\n      }).catch(error => {\n        console.error('Erro na reprodução da IA:', error);\n        this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro\n        this.cdr.detectChanges();\n        // Only start waiting for response if validation buttons are not shown\n        if (!this.showValidationButtons) {\n          this.iniciarProcessoAguardandoResposta();\n        }\n      });\n    }\n  }\n  adicionarRespostaIA(resposta) {\n    this.conversationHistory.push(`(resposta da ia) ${resposta}`);\n    this.cdr.detectChanges();\n  }\n  atualizarDadosColetados(novosdados) {\n    Object.keys(novosdados).forEach(key => {\n      if (novosdados[key] && novosdados[key].trim() !== '') {\n        this.dadosColetados[key] = novosdados[key];\n      }\n    });\n    this.cdr.detectChanges();\n  }\n  todosOsCamposPreenchidos() {\n    const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];\n    return camposObrigatorios.every(campo => this.dadosColetados[campo] && this.dadosColetados[campo].trim() !== '');\n  }\n  iniciarProcessoAguardandoResposta() {\n    this.isAguardandoResposta = true;\n    this.cdr.detectChanges();\n    if (!this.isTextMode) setTimeout(() => {\n      this.iniciarGravacaoContinua();\n    }, 500);\n  }\n  iniciarGravacaoContinua() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.voiceRecorder.isSupported()) {\n        console.error('Reconhecimento de voz não suportado');\n        _this2.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n        return;\n      }\n      // Redução de latência: monitorar mais frequentemente o fim da fala\n      yield _this2.waitForSpeechEndWithReducedLatency();\n      _this2.iniciarGravacaoAutomatica();\n      _this2.monitorarEstadoGravacao();\n    })();\n  }\n  monitorarEstadoGravacao() {\n    const intervalId = setInterval(() => {\n      if (!this.isAguardandoResposta) {\n        clearInterval(intervalId);\n        return;\n      }\n      if (this.isTextMode) {\n        return;\n      }\n      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n      const componentRecording = this.isRecording;\n      if (serviceRecording !== componentRecording) {\n        this.isRecording = serviceRecording;\n        this.cdr.detectChanges();\n      }\n      if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {\n        this.iniciarGravacaoAutomatica();\n      }\n    }, 2000);\n  }\n  finalizarProcessoAguardandoResposta() {\n    this.isAguardandoResposta = false;\n    this.cdr.detectChanges();\n    if (this.isRecording) {\n      this.voiceRecorder.stopRecording();\n    }\n  }\n  iniciarGravacaoAutomatica() {\n    if (!this.voiceRecorder.isSupported()) {\n      console.error('Reconhecimento de voz não suportado');\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n      return;\n    }\n    // Don't start recording if in text mode\n    if (this.isTextMode) {\n      console.log('📝 Modo texto ativo - não iniciando gravação automática');\n      return;\n    }\n    // Não iniciar gravação se o sistema estiver falando\n    if (this.speaker.isSpeaking()) {\n      console.log('🔇 Aguardando término da fala para iniciar gravação');\n      return;\n    }\n    const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n    const componentRecording = this.isRecording;\n    if (serviceRecording || componentRecording) {\n      return;\n    }\n    const success = this.voiceRecorder.startRecording();\n    if (!success) console.error('❌ Falha ao iniciar gravação automática');\n    this.cdr.detectChanges();\n  }\n  iniciarGravacao() {\n    if (!this.voiceRecorder.isSupported()) {\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n      return;\n    }\n    // Verificar se o sistema está falando\n    if (this.speaker.isSpeaking()) {\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');\n      return;\n    }\n    const success = this.voiceRecorder.startRecording();\n    if (!success) {\n      this.snackBar.falhaSnackbar('Erro ao iniciar gravação');\n    }\n    this.cdr.detectChanges();\n  }\n  pararGravacao() {\n    this.voiceRecorder.stopRecording();\n    this.cdr.detectChanges();\n  }\n  toggleRecording() {\n    if (this.isRecording) {\n      this.pararGravacao();\n    } else {\n      this.iniciarGravacao();\n    }\n  }\n  preloadVoices() {\n    const synthesis = window.speechSynthesis;\n    const voices = synthesis.getVoices();\n    if (voices.length === 0) {\n      synthesis.onvoiceschanged = () => {\n        const newVoices = synthesis.getVoices();\n        newVoices;\n      };\n      const silentUtterance = new SpeechSynthesisUtterance('');\n      silentUtterance.volume = 0;\n      synthesis.speak(silentUtterance);\n      synthesis.cancel();\n    }\n  }\n  testarFluxoCompleto() {\n    this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.', () => {\n      console.log('🎯 Teste: áudio iniciado');\n    }).then(() => {\n      this.iniciarProcessoAguardandoResposta();\n    });\n  }\n  enviarTexto() {\n    // Não permitir envio se o sistema estiver falando\n    if (this.speaker.isSpeaking()) {\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');\n      return;\n    }\n    if (this.userInput.trim()) {\n      this.adicionarRespostaUsuario(this.userInput);\n      this.userInput = '';\n      this.cdr.detectChanges();\n    }\n  }\n  adicionarRespostaUsuario(resposta) {\n    if (this.isAguardandoResposta) {\n      this.finalizarProcessoAguardandoResposta();\n    }\n    this.conversationHistory.push(`(resposta do usuário) ${resposta}`);\n    this.cdr.detectChanges();\n    this.enviarMensagemParaIA();\n  }\n  solicitarPermissaoMicrofone() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: true\n        });\n        stream.getTracks().forEach(track => track.stop());\n      } catch (error) {\n        console.error('Erro ao solicitar permissão de microfone:', error);\n        _this3.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');\n      }\n    })();\n  }\n  generateToken() {\n    const now = new Date();\n    const timestamp = now.getTime();\n    const random = Math.floor(Math.random() * 10000);\n    return `${timestamp}_${random}`;\n  }\n  // Métodos para controle de fala e exibição sincronizada\n  startSpeakingMonitor() {\n    setInterval(() => {\n      const currentlySpeaking = this.speaker.isSpeaking();\n      if (this.isSpeaking !== currentlySpeaking) {\n        this.isSpeaking = currentlySpeaking;\n        this.cdr.detectChanges();\n      }\n    }, 100); // Verificar a cada 100ms\n  }\n  startSynchronizedTextDisplay() {\n    this.clearTextDisplayInterval();\n    this.displayedText = '';\n    const words = this.fullResponseText.split(' ');\n    const totalDuration = this.estimateSpeechDuration(this.fullResponseText);\n    const intervalTime = totalDuration / words.length;\n    let currentWordIndex = 0;\n    this.textDisplayInterval = setInterval(() => {\n      if (currentWordIndex < words.length) {\n        if (currentWordIndex === 0) {\n          this.displayedText = words[currentWordIndex];\n        } else {\n          this.displayedText += ' ' + words[currentWordIndex];\n        }\n        currentWordIndex++;\n        this.cdr.detectChanges();\n      } else {\n        this.clearTextDisplayInterval();\n      }\n    }, intervalTime);\n  }\n  estimateSpeechDuration(text) {\n    const words = text.split(' ').length;\n    const wordsPerMinute = 150;\n    const durationInMinutes = words / wordsPerMinute;\n    return durationInMinutes * 60 * 1000;\n  }\n  clearTextDisplayInterval() {\n    if (this.textDisplayInterval) {\n      clearInterval(this.textDisplayInterval);\n      this.textDisplayInterval = null;\n    }\n  }\n  finalizarProcesso() {\n    this.cdr.detectChanges();\n    this.salvarDadosQuestionario();\n    this.abrirDialogColetaDadosVittalTec();\n  }\n  salvarDadosQuestionario() {\n    try {\n      const dadosParaSalvar = {\n        nome: this.dadosColetados.nome,\n        idade: this.calcularIdade(this.dadosColetados.dataNascimento),\n        cpf: this.dadosColetados.cpf,\n        email: this.dadosColetados.email,\n        telefone: this.dadosColetados.telefone,\n        dataNascimento: this.dadosColetados.dataNascimento,\n        sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],\n        sintomasOutros: this.dadosColetados.observacoes || '',\n        intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),\n        tempoSintomas: this.dadosColetados.tempoSintomas,\n        alergias: this.dadosColetados.alergias || '',\n        doencasPrevias: this.dadosColetados.doencasPrevias || '',\n        observacoes: this.dadosColetados.observacoes || '',\n        dataPreenchimento: new Date().toISOString()\n      };\n      CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));\n    } catch (error) {\n      this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');\n    }\n  }\n  calcularIdade(dataNascimento) {\n    if (!dataNascimento) return 0;\n    try {\n      const nascimento = new Date(dataNascimento);\n      const hoje = new Date();\n      let idade = hoje.getFullYear() - nascimento.getFullYear();\n      const mesAtual = hoje.getMonth();\n      const mesNascimento = nascimento.getMonth();\n      if (mesAtual < mesNascimento || mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate()) {\n        idade--;\n      }\n      return idade;\n    } catch {\n      return 0;\n    }\n  }\n  extrairNumeroIntensidade(intensidade) {\n    if (!intensidade) return 0;\n    const match = intensidade.match(/\\d+/);\n    return match ? parseInt(match[0], 10) : 0;\n  }\n  redirecionarParaFilaEspera() {\n    try {\n      this.router.navigate(['/filaespera']);\n    } catch (error) {\n      console.error('Erro ao redirecionar para fila de espera:', error);\n      this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');\n      this.router.navigate(['/filaespera']);\n    }\n  }\n  abrirDialogColetaDadosVittalTec() {\n    const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\n      disableClose: true,\n      width: '500px',\n      maxWidth: '85vw',\n      height: 'auto',\n      maxHeight: '70vh',\n      panelClass: 'vittaltec-modal-panel'\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.action === 'continuar') {\n        this.snackBar.sucessoSnackbar(\"Dados coletados com sucesso!\");\n        this.redirecionarParaFilaEspera();\n      } else if (result?.action === 'cancelar') {\n        this.snackBar.falhaSnackbar(\"Processo de coleta de dados cancelado.\");\n        CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');\n      }\n      this.cdr.detectChanges();\n    });\n  }\n  getDadosPreenchidos() {\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => ({\n      label: labels[key],\n      value: this.dadosColetados[key]\n    }));\n  }\n  getUltimaVariavelPreenchida() {\n    const dados = this.getDadosPreenchidos();\n    return dados.length > 0 ? dados[dados.length - 1] : null;\n  }\n  openHistoryModal() {\n    import('./components/history-modal-dialog.component').then(({\n      HistoryModalDialogComponent\n    }) => {\n      const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));\n      this.dialog.open(HistoryModalDialogComponent, {\n        width: '900px',\n        maxWidth: '98vw',\n        data: {\n          dadosColetados: dadosClone,\n          snackBar: this.snackBar,\n          cdr: this.cdr\n        },\n        panelClass: 'modern-modal-overlay',\n        autoFocus: false\n      });\n    });\n  }\n  getTotalCampos() {\n    return 10;\n  }\n  getProgressPercentage() {\n    const total = this.getTotalCampos();\n    const filled = this.getDadosPreenchidos().length;\n    return Math.round(filled / total * 100);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n  }\n  clearSearch() {\n    this.searchTerm = '';\n  }\n  toggleFilter(filter) {\n    filter.active = !filter.active;\n  }\n  getFilteredData() {\n    let data = this.getEnhancedDadosPreenchidos();\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      data = data.filter(item => item.label.toLowerCase().includes(searchLower) || item.value.toLowerCase().includes(searchLower));\n    }\n    const activeCategories = this.availableFilters.filter(f => f.active).map(f => f.type);\n    data = data.filter(item => activeCategories.includes(item.category));\n    return data;\n  }\n  getEnhancedDadosPreenchidos() {\n    const categoryMap = {\n      nome: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'person'\n      },\n      cpf: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'badge'\n      },\n      dataNascimento: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'cake'\n      },\n      email: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'email'\n      },\n      telefone: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'phone'\n      },\n      sintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'medical_services'\n      },\n      intensidadeDor: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'personal_injury'\n      },\n      tempoSintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'schedule'\n      },\n      alergias: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'medical_information'\n      },\n      observacoes: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'description'\n      }\n    };\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => {\n      const categoryInfo = categoryMap[key] || {\n        category: 'optional',\n        categoryLabel: 'Outros',\n        icon: 'info'\n      };\n      return {\n        label: labels[key] || key,\n        value: this.dadosColetados[key],\n        category: categoryInfo.category,\n        categoryLabel: categoryInfo.categoryLabel,\n        icon: categoryInfo.icon,\n        timestamp: new Date(),\n        validationStatus: this.getValidationStatusForField(key)\n      };\n    });\n  }\n  getValidationStatusForField(field) {\n    const value = this.dadosColetados[field];\n    if (!value || value.trim() === '') return 'error';\n    if (field === 'email' && !value.includes('@')) return 'warning';\n    if (field === 'cpf' && value.length < 11) return 'warning';\n    return 'valid';\n  }\n  trackByFn(index, item) {\n    index;\n    return item.label + item.value;\n  }\n  highlightSearchTerm(text) {\n    if (!this.searchTerm) return text;\n    const regex = new RegExp(`(${this.searchTerm})`, 'gi');\n    return text.replace(regex, '<mark>$1</mark>');\n  }\n  formatTimestamp(timestamp) {\n    return timestamp.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getValidationIcon(status) {\n    switch (status) {\n      case 'valid':\n        return 'check_circle';\n      case 'warning':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'info';\n    }\n  }\n  getValidationLabel(status) {\n    switch (status) {\n      case 'valid':\n        return 'Válido';\n      case 'warning':\n        return 'Atenção';\n      case 'error':\n        return 'Erro';\n      default:\n        return 'Info';\n    }\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\n    }).catch(() => {\n      this.snackBar.falhaSnackbar('Erro ao copiar texto');\n    });\n  }\n  editValue(item) {\n    const newValue = prompt(`Editar ${item.label}:`, item.value);\n    if (newValue !== null && newValue !== item.value) {\n      const field = Object.keys(this.dadosColetados).find(key => {\n        const labels = {\n          nome: 'Nome',\n          cpf: 'CPF',\n          email: 'Email',\n          telefone: 'Telefone',\n          dataNascimento: 'Data de Nascimento',\n          alergias: 'Alergias',\n          sintomas: 'Sintomas',\n          intensidadeDor: 'Intensidade da Dor',\n          tempoSintomas: 'Tempo dos Sintomas',\n          doencasPrevias: 'Doenças Prévias',\n          observacoes: 'Observações'\n        };\n        return labels[key] === item.label;\n      });\n      if (field) {\n        this.dadosColetados[field] = newValue;\n        this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\n        this.cdr.detectChanges();\n      }\n    }\n  }\n  clearAllData() {\n    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\n      this.dadosColetados = {\n        nome: '',\n        cpf: '',\n        email: '',\n        telefone: '',\n        dataNascimento: '',\n        alergias: '',\n        sintomas: '',\n        intensidadeDor: '',\n        tempoSintomas: '',\n        doencasPrevias: '',\n        observacoes: ''\n      };\n      this.snackBar.sucessoSnackbar('Todos os dados foram limpos');\n      this.cdr.detectChanges();\n    }\n  }\n  isInputDisabled() {\n    return this.speaker.isSpeaking() || this.isProcessing;\n  }\n  isMicrophoneDisabled() {\n    return this.speaker.isSpeaking() || this.isProcessing;\n  }\n  canSendText() {\n    return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();\n  }\n  waitForSpeechEndWithReducedLatency() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        const checkInterval = 50;\n        const maxWaitTime = 30000;\n        let elapsedTime = 0;\n        const intervalId = setInterval(() => {\n          elapsedTime += checkInterval;\n          if (!_this4.speaker.isSpeaking()) {\n            clearInterval(intervalId);\n            setTimeout(() => {\n              resolve();\n            }, 100);\n          } else if (elapsedTime >= maxWaitTime) {\n            clearInterval(intervalId);\n            console.warn('Timeout aguardando fim da fala');\n            resolve();\n          }\n        }, checkInterval);\n      });\n    })();\n  }\n  confirmarInformacao() {\n    this.showValidationButtons = false;\n    this.enviarMensagemSistema('(Processamento do sistema) Usuário confirmou a informação, campo preenchido corretamente.');\n  }\n  tentarNovamente() {\n    this.showValidationButtons = false;\n    this.enviarMensagemSistema('(processamento do sistema) Campo preenchido incorretamente, o usuário terá que repreencher o campo do 0');\n  }\n  enviarMensagemSistema(mensagem) {\n    this.conversationHistory.push(mensagem);\n    const payload = {\n      formaDeResposta: 'Sistema',\n      historicoConversa: [...this.conversationHistory],\n      ultimaMensagem: mensagem,\n      campoAtual: this.campoAtual,\n      token: this.currentToken\n    };\n    this.isProcessing = true;\n    this.cdr.detectChanges();\n    this.aiService.enviarMensagens(payload).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.processarRespostaIA(response);\n      },\n      error: error => {\n        this.isProcessing = false;\n        this.cdr.detectChanges();\n        this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\n        console.error('Erro na comunicação com IA:', error);\n      }\n    });\n  }\n  static ɵfac = function PreConsultaQuestionarioComponent_Factory(t) {\n    return new (t || PreConsultaQuestionarioComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.VoiceRecorderService), i0.ɵɵdirectiveInject(i4.SpeakerService), i0.ɵɵdirectiveInject(i5.AiQuestionarioApiService), i0.ɵɵdirectiveInject(i6.AlertComponent), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PreConsultaQuestionarioComponent,\n    selectors: [[\"app-pre-consulta-questionario\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"ai-questionnaire-container\"], [\"class\", \"idle-screen\", 4, \"ngIf\"], [\"class\", \"chat-interface\", 4, \"ngIf\"], [1, \"idle-screen\"], [1, \"idle-content\"], [1, \"ai-robot\", 2, \"margin-bottom\", \"9px\"], [1, \"robot-head\"], [1, \"robot-eyes\"], [1, \"eye\", \"left-eye\"], [1, \"eye\", \"right-eye\"], [1, \"robot-mouth\"], [1, \"robot-body\"], [1, \"robot-chest\"], [1, \"action-buttons\"], [1, \"start-btn\", 3, \"click\"], [1, \"btn-glow\"], [1, \"chat-interface\"], [1, \"controls-header\"], [1, \"mode-indicator\"], [1, \"mode-icon\"], [1, \"mode-toggle\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"disabled\"], [1, \"main-chat-area\"], [1, \"ai-section\"], [1, \"ai-avatar\"], [1, \"ai-face\"], [1, \"ai-eyes\"], [1, \"eye\"], [1, \"ai-mouth\"], [\"class\", \"ai-pulse\", 4, \"ngIf\"], [1, \"response-data-section\"], [1, \"response-section\"], [\"class\", \"ai-message\", 4, \"ngIf\"], [\"class\", \"processing-indicator\", 4, \"ngIf\"], [\"class\", \"validation-buttons\", 4, \"ngIf\"], [\"class\", \"data-section\", 4, \"ngIf\"], [\"class\", \"input-section\", 4, \"ngIf\"], [\"class\", \"audio-visualization\", 4, \"ngIf\"], [\"class\", \"voice-status-indicator\", 4, \"ngIf\"], [1, \"ai-pulse\"], [1, \"ai-message\"], [1, \"message-bubble\", \"scrollable-hidden\"], [1, \"processing-indicator\"], [1, \"typing-dots\"], [1, \"processing-text\"], [1, \"validation-buttons\"], [1, \"validation-container\"], [1, \"validation-message\"], [1, \"validation-icon\"], [1, \"button-group\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"confirm-btn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"retry-btn\", 3, \"click\", \"disabled\"], [1, \"data-section\"], [1, \"data-panel\"], [1, \"data-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Ver todas as vari\\u00E1veis\", 1, \"history-btn\", 3, \"click\"], [\"class\", \"data-item\", 3, \"matTooltip\", 4, \"ngIf\"], [\"class\", \"progress-info\", 4, \"ngIf\"], [1, \"data-item\", 3, \"matTooltip\"], [1, \"descInfoCategoria\"], [1, \"descInfovalue\"], [1, \"progress-info\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"input-section\"], [1, \"input-container\"], [\"appearance\", \"outline\", 1, \"user-input\"], [\"matInput\", \"\", \"placeholder\", \"Digite aqui...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"speaking-hint\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"click\", \"disabled\"], [1, \"speaking-hint\"], [1, \"hint-icon\"], [1, \"audio-visualization\"], [1, \"sound-wave\"], [\"class\", \"wave-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"recording-text\"], [1, \"recording-icon\"], [1, \"wave-bar\"], [1, \"voice-status-indicator\"], [1, \"status-icon\"], [\"class\", \"status-ripple\", 4, \"ngIf\"], [1, \"status-text\"], [1, \"status-ripple\"]],\n    template: function PreConsultaQuestionarioComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, PreConsultaQuestionarioComponent_div_1_Template, 17, 0, \"div\", 1)(2, PreConsultaQuestionarioComponent_div_2_Template, 25, 19, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isIdle);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isIdle);\n      }\n    },\n    dependencies: [CommonModule, i7.NgForOf, i7.NgIf, ReactiveFormsModule, i8.DefaultValueAccessor, i8.NgControlStatus, FormsModule, i8.NgModel, MatFormFieldModule, i9.MatFormField, i9.MatLabel, i9.MatHint, i9.MatSuffix, MatInputModule, i10.MatInput, MatButtonModule, i11.MatButton, i11.MatIconButton, MatCardModule, MatCheckboxModule, MatSlideToggleModule, i12.MatSlideToggle, MatIconModule, i13.MatIcon, MatDialogModule, MatTooltipModule, i14.MatTooltip, MatChipsModule, MatSnackBarModule],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.ai-questionnaire-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  height: 100dvh;\\n  max-height: 100vh;\\n  max-height: 100dvh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n  font-size: clamp(0.75rem, 1.8vw, 0.9rem);\\n  position: relative;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.idle-screen[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n  overflow: hidden;\\n}\\n\\n.idle-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\\n  max-width: min(80vw, 20rem);\\n  width: 100%;\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  max-height: 70vh;\\n  overflow: hidden;\\n}\\n.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: clamp(1rem, 2.5vw, 1.125rem);\\n  font-weight: 700;\\n  margin: clamp(0.25rem, 1vw, 0.5rem) 0 clamp(0.25rem, 0.5vw, 0.25rem) 0;\\n}\\n.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  margin-bottom: clamp(0.5rem, 2vw, 1rem);\\n  line-height: 1.3;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  align-items: center;\\n  width: 100%;\\n}\\n\\n\\n\\n.ai-robot[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(0.9);\\n  }\\n}\\n\\n.robot-head[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 5rem);\\n  height: clamp(3.5rem, 8vw, 5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  position: relative;\\n  margin: 0 auto clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 0.5rem 1.25rem rgba(102, 126, 234, 0.3);\\n}\\n\\n.robot-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: clamp(1rem, 4vw, 1.25rem) clamp(0.5rem, 3vw, 1rem) 0;\\n}\\n\\n.eye[_ngcontent-%COMP%] {\\n  width: clamp(0.5rem, 1.5vw, 0.75rem);\\n  height: clamp(0.5rem, 1.5vw, 0.75rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_blink 3s infinite;\\n}\\n.eye.left-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.1s;\\n}\\n.eye.right-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.2s;\\n}\\n\\n.robot-mouth[_ngcontent-%COMP%] {\\n  width: clamp(1rem, 2.5vw, 1.25rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 0 0 0.625rem 0.625rem;\\n  margin: clamp(0.4rem, 1vw, 0.5rem) auto 0;\\n}\\n\\n.robot-body[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.75rem);\\n  height: clamp(1.5rem, 4vw, 2.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(0.75rem, 2vw, 1rem);\\n  margin: 0 auto;\\n  position: relative;\\n}\\n\\n.robot-chest[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.start-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #f093fb);\\n  border: none;\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(2rem, 5vw, 2.8125rem);\\n  border-radius: 3.125rem;\\n  color: #ffffff;\\n  font-size: clamp(1.125rem, 3vw, 1.5rem);\\n  font-weight: 700;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(79, 172, 254, 0.3);\\n}\\n.start-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-0.125rem);\\n  box-shadow: 0 0.75rem 2.1875rem rgba(79, 172, 254, 0.4);\\n}\\n.start-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.btn-glow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.start-btn[_ngcontent-%COMP%]:hover   .btn-glow[_ngcontent-%COMP%] {\\n  left: 100%;\\n}\\n\\n\\n\\n\\n\\n.chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.controls-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 0.8vw, 0.5rem) clamp(0.5rem, 1.5vw, 1rem);\\n  border-radius: 0 0 clamp(0.5rem, 1.5vw, 1rem) clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  min-height: 44px;\\n}\\n\\n.mode-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: clamp(1.25rem, 2.5vw, 1.5rem);\\n  height: clamp(1.25rem, 2.5vw, 1.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  color: #ffffff;\\n  flex-shrink: 0;\\n}\\n.mode-indicator[_ngcontent-%COMP%]   .mode-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n  width: clamp(0.75rem, 1.5vw, 0.875rem);\\n  height: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.mode-toggle[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 1.2vw, 0.75rem);\\n  white-space: nowrap;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch {\\n  --mdc-switch-track-width: 28px;\\n  --mdc-switch-track-height: 16px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle {\\n  --mdc-switch-handle-width: 12px;\\n  --mdc-switch-handle-height: 12px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__ripple {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n\\n\\n.main-chat-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  padding: clamp(0.5rem, 1.5vw, 1rem) clamp(0.25rem, 1vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n  max-width: 100vw;\\n  margin: 0;\\n  width: 100%;\\n  min-height: 0;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.response-data-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  width: 100%;\\n  align-items: center;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    justify-content: space-between;\\n    gap: clamp(0.5rem, 1.5vw, 1rem);\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    gap: clamp(1rem, 2vw, 1.5rem);\\n  }\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n\\n.ai-avatar[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.5rem);\\n  height: clamp(2.5rem, 6vw, 3.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.ai-avatar.processing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(102, 126, 234, 0.5);\\n}\\n.ai-avatar.listening[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_listening-pulse 1s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(240, 147, 251, 0.5);\\n}\\n.ai-avatar.waiting[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(79, 172, 254, 0.5);\\n}\\n\\n.ai-face[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.5vw, 0.25rem);\\n}\\n\\n.ai-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.25rem, 0.8vw, 0.4rem);\\n}\\n.ai-eyes[_ngcontent-%COMP%]   .eye[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.3rem);\\n  height: clamp(0.2rem, 0.5vw, 0.3rem);\\n  background: #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_ai-blink 4s infinite;\\n}\\n\\n.ai-mouth[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.6rem);\\n  height: clamp(0.15rem, 0.4vw, 0.2rem);\\n  background: #667eea;\\n  border-radius: 0 0 0.3rem 0.3rem;\\n  transition: all 0.3s ease;\\n}\\n.ai-mouth.talking[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_mouth-talk 0.5s infinite alternate;\\n}\\n\\n.ai-pulse[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  left: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  right: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  bottom: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  border: clamp(1px, 0.3vw, 2px) solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse-ring 2s infinite;\\n}\\n\\n\\n\\n.response-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: flex-start;\\n    max-width: none;\\n  }\\n}\\n\\n.ai-message[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  flex-shrink: 0;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem);\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  max-height: 40vh;\\n  overflow-y: auto;\\n}\\n.message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: clamp(0.5rem, 2vw, 1rem);\\n  bottom: clamp(-0.25rem, -0.5vw, -0.3rem);\\n  width: 0;\\n  height: 0;\\n  border-left: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-right: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-top: clamp(0.25rem, 0.8vw, 0.3rem) solid #ffffff;\\n}\\n.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n\\n.processing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 100%;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  color: #718096;\\n  padding: clamp(1rem, 3vw, 1.5rem);\\n}\\n.processing-indicator[_ngcontent-%COMP%]   .processing-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  font-weight: 500;\\n  color: #fff;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.2rem, 0.5vw, 0.25rem);\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #fff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n\\n\\n.data-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n  overflow: hidden;\\n  max-height: 30vh;\\n}\\n@media (min-width: 1024px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: 0;\\n    max-width: min(35vw, 18rem);\\n    width: auto;\\n    min-width: 15rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: min(30vw, 20rem);\\n  }\\n}\\n\\n.data-panel[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  padding: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  max-height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 clamp(0.25rem, 1vw, 0.5rem) 0;\\n  color: #2d3748;\\n  font-size: clamp(0.875rem, 2vw, 1rem);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  border-bottom: 1px solid rgba(102, 126, 234, 0.1);\\n  padding-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.data-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  max-height: 20vh !important;\\n  overflow-y: auto !important;\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n.data-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: #f8fafc;\\n  border-radius: clamp(0.25rem, 1vw, 0.5rem);\\n  border-left: clamp(2px, 0.3vw, 3px) solid #4facfe;\\n  flex-shrink: 0;\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfoCategoria[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: clamp(0.75rem, 1.8vw, 0.875rem);\\n  color: #718096;\\n  font-weight: 500;\\n  margin-bottom: clamp(0.1rem, 0.3vw, 0.15rem);\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfovalue[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  margin-top: clamp(0.25rem, 1.5vw, 0.5rem);\\n  padding-top: clamp(0.25rem, 1vw, 0.5rem);\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  flex-shrink: 0;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: clamp(3px, 0.8vw, 4px);\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n  margin-bottom: clamp(0.25rem, 0.8vw, 0.25rem);\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  min-width: 44px !important;\\n  min-height: 44px !important;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  color: #667eea;\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.input-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\\n  min-height: 60px;\\n}\\n@media (max-width: 480px) {\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 56px;\\n  }\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  box-sizing: border-box;\\n}\\n\\n.user-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n}\\n.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 2vw, 0.875rem) !important;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem) !important;\\n  min-height: 44px;\\n}\\n.user-input[_ngcontent-%COMP%]   button[matSuffix][_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n.voice-display[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  text-align: center;\\n}\\n\\n.voice-input-field[_ngcontent-%COMP%]   .voice-placeholder[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\\n  font-style: italic;\\n}\\n\\n\\n\\n.audio-visualization[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(4rem, 8vw, 6rem);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(1rem, 3vw, 1.25rem);\\n  z-index: 1000;\\n  max-width: min(90vw, 25rem);\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n}\\n\\n.sound-wave[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.4vw, 0.1875rem);\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n\\n.wave-bar[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.25rem);\\n  background: linear-gradient(to top, #667eea, #f093fb);\\n  border-radius: clamp(1px, 0.2vw, 2px);\\n  animation: _ngcontent-%COMP%_wave-animation 1.5s infinite ease-in-out;\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.1s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.2s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(4) {\\n  animation-delay: 0.3s;\\n  height: clamp(1.75rem, 4vw, 2.1875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(5) {\\n  animation-delay: 0.4s;\\n  height: clamp(1.25rem, 3vw, 1.5625rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(6) {\\n  animation-delay: 0.5s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(7) {\\n  animation-delay: 0.6s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(8) {\\n  animation-delay: 0.7s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n\\n.recording-text[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.875rem, 2.5vw, 1.125rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n.recording-text[_ngcontent-%COMP%]   .recording-icon[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n\\n\\n\\n.voice-status-indicator[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(1.5rem, 4vw, 1.875rem);\\n  right: clamp(1.5rem, 4vw, 1.875rem);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.5rem, 1.5vw, 1rem);\\n  z-index: 1000;\\n}\\n@media (max-width: 480px) {\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(1rem, 3vw, 1.5rem);\\n    right: clamp(1rem, 3vw, 1.5rem);\\n    scale: 0.9;\\n  }\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 4.375rem);\\n  height: clamp(3.5rem, 8vw, 4.375rem);\\n  border-radius: 50%;\\n  border: none;\\n  background: linear-gradient(135deg, #718096, #a0aec0);\\n  color: #ffffff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(113, 128, 150, 0.4);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.status-icon.recording[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757, #ff3742);\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n.status-icon.processing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n}\\n.status-icon.waiting[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #10b981);\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n}\\n.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(1.25rem, 4vw, 1.75rem);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.status-ripple[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.3);\\n  animation: _ngcontent-%COMP%_ripple 1.5s infinite;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  font-weight: 600;\\n  color: #2d3748;\\n  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  white-space: nowrap;\\n}\\n\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ai-blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_mouth-talk {\\n  0% {\\n    transform: scaleY(1);\\n  }\\n  100% {\\n    transform: scaleY(1.5);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_processing-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_listening-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.8;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.9;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse-ring {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1.3);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_wave-animation {\\n  0%, 100% {\\n    transform: scaleY(0.5);\\n  }\\n  50% {\\n    transform: scaleY(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_recording-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(255, 71, 87, 0.8);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(2);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(16, 185, 129, 0.8);\\n  }\\n}\\n\\n\\n\\n\\n@media (max-width: 480px) {\\n  .ai-questionnaire-container[_ngcontent-%COMP%] {\\n    font-size: clamp(0.7rem, 1.8vw, 0.8rem);\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 3vw, 1rem) clamp(0.25rem, 2vw, 0.5rem);\\n    margin: 0.25rem;\\n    max-width: min(85vw, 18rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 3vw, 1rem);\\n    margin: clamp(0.25rem, 1.5vw, 0.5rem) 0 clamp(0.25rem, 1vw, 0.25rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    margin-bottom: clamp(0.5rem, 2.5vw, 1rem);\\n  }\\n  .chat-interface[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 0.5rem);\\n    gap: clamp(0.25rem, 1vw, 0.25rem);\\n    min-height: 40px;\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n    gap: clamp(0.25rem, 0.8vw, 0.25rem);\\n    overflow: hidden;\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: clamp(0.25rem, 1.5vw, 0.5rem);\\n    overflow: hidden;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 5vw, 2.5rem);\\n    height: clamp(2rem, 5vw, 2.5rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.8vw, 0.5rem) clamp(0.5rem, 2.5vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-height: 25vh;\\n    overflow-y: auto;\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    line-height: 1.3;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(0.25rem, 1vw, 0.25rem);\\n    max-height: 20vh;\\n    overflow: hidden;\\n  }\\n  .data-panel[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 50px;\\n  }\\n  .user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.8vw, 0.875rem) !important;\\n    padding: clamp(0.25rem, 1.2vw, 0.25rem) clamp(0.5rem, 1.8vw, 0.5rem) !important;\\n  }\\n  .audio-visualization[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-width: 75vw;\\n    min-height: 40px;\\n  }\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    right: clamp(0.25rem, 1.5vw, 0.5rem);\\n    scale: 0.75;\\n  }\\n  .status-icon[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 4.5vw, 2.5rem);\\n    height: clamp(2rem, 4.5vw, 2.5rem);\\n    min-width: 40px;\\n    min-height: 40px;\\n  }\\n  .status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.8vw, 1rem);\\n  }\\n  .status-text[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n    padding: clamp(0.25rem, 0.6vw, 0.25rem) clamp(0.25rem, 1.2vw, 0.5rem);\\n    max-width: 6rem;\\n  }\\n}\\n@media (min-width: 481px) and (max-width: 640px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(5rem, 10vw, 6rem) clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 3vw, 2rem);\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: clamp(1.5rem, 4vw, 2rem);\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    margin-top: clamp(1rem, 3vw, 2rem);\\n  }\\n}\\n@media (min-width: 641px) and (max-width: 768px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(6rem, 10vw, 7rem) clamp(1.5rem, 4vw, 2rem) clamp(2rem, 4vw, 3rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(2rem, 5vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(1.5rem, 4vw, 2rem);\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: center;\\n    width: 100%;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n    right: clamp(1rem, 2.5vw, 1.5rem);\\n    width: auto;\\n    justify-content: center;\\n  }\\n}\\n@media (min-width: 769px) and (max-width: 1024px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(90vw, 75rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(1.5rem, 3vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: clamp(1.5rem, 3vw, 2rem);\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (min-width: 1025px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(85vw, 85rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    gap: clamp(2rem, 2vw, 4rem);\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    max-width: none;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    width: auto;\\n    min-width: 20rem;\\n    margin-top: 0;\\n  }\\n}\\n@media (orientation: landscape) and (max-height: 600px) {\\n  .idle-screen[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vh, 1rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(1.5rem, 4vh, 2rem) clamp(1.5rem, 4vw, 3rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(1.125rem, 4vw, 1.25rem);\\n    margin: clamp(0.5rem, 2vh, 1rem) 0 clamp(0.25rem, 1vh, 0.5rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n    margin-bottom: clamp(1rem, 3vh, 1.5rem);\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(4rem, 8vh, 5rem) clamp(1.5rem, 4vw, 2rem) clamp(1rem, 2vh, 1.5rem);\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(4rem, 8vh, 5rem);\\n    height: clamp(4rem, 8vh, 5rem);\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .ai-avatar[_ngcontent-%COMP%], .robot-head[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@container (max-width: 480px) {\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vw, 1rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .ai-robot[_ngcontent-%COMP%], .ai-avatar[_ngcontent-%COMP%], .start-btn[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  .btn-glow[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .message-bubble[_ngcontent-%COMP%], .data-panel[_ngcontent-%COMP%], .controls-header[_ngcontent-%COMP%] {\\n    border: 2px solid #2d3748;\\n  }\\n  .start-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #ffffff;\\n  }\\n}\\n\\n\\n\\n\\n.modern-modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(15, 23, 42, 0.8);\\n  -webkit-backdrop-filter: blur(8px) saturate(180%);\\n          backdrop-filter: blur(8px) saturate(180%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n.modern-modal-overlay[_ngcontent-%COMP%]:focus-within {\\n  outline: 2px solid #4facfe;\\n  outline-offset: -2px;\\n}\\n\\n.modern-modal-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1.5rem;\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  width: 100%;\\n  max-width: 56rem;\\n  max-height: 90vh;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n@media (max-width: 768px) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    max-height: 95vh;\\n    margin: 0.5rem;\\n  }\\n}\\n\\n.modal-header-modern[_ngcontent-%COMP%] {\\n  padding: 2rem 2rem 1.5rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 3rem;\\n  height: 3rem;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 1rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  font-size: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #0f172a;\\n  line-height: 1.2;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: 0.75rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  color: #475569;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  color: #0f172a;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(239, 68, 68, 0.15);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #4facfe;\\n  font-weight: 700;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 0.5rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.search-filter-section[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  border: 1px solid #f1f5f9;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.3);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-personal[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(102, 126, 234, 0.1);\\n  --mdc-chip-selected-label-text-color: #667eea;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-medical[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(16, 185, 129, 0.1);\\n  --mdc-chip-selected-label-text-color: #10b981;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-contact[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(59, 130, 246, 0.1);\\n  --mdc-chip-selected-label-text-color: #3b82f6;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-optional[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(245, 158, 11, 0.1);\\n  --mdc-chip-selected-label-text-color: #f59e0b;\\n}\\n\\n.modal-body-modern[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem 2rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 1.5rem;\\n  text-align: center;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  width: 4rem;\\n  height: 4rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  max-width: 24rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n  grid-template-columns: 1fr;\\n}\\n@media (min-width: 1024px) {\\n  .modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #f1f5f9;\\n  border-radius: 1rem;\\n  padding: 1.5rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 3px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.2);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  transform: translateY(-2px);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-personal[_ngcontent-%COMP%] {\\n  border-left: 4px solid #667eea;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-medical[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-contact[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3b82f6;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-optional[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  background: #f8fafc;\\n  border-radius: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: #475569;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n  line-height: 1.3;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-category[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 0.5rem;\\n  background: #f8fafc;\\n  color: #475569;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: #ffffff;\\n  transform: scale(1.1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #0f172a;\\n  font-weight: 500;\\n  line-height: 1.5;\\n  word-break: break-word;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]     mark {\\n  background: rgba(79, 172, 254, 0.2);\\n  color: #4facfe;\\n  padding: 0.125rem 0.25rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 0.5rem;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f1f5f9;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-valid[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-warning[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-error[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n\\n.modal-footer-modern[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-top: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  color: #475569;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3b82f6;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 500;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  background: linear-gradient(135deg, #506be7, #694391);\\n}\\n@media (max-width: 640px) {\\n  .modal-footer-modern[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n    justify-content: stretch;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%], .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .modern-modal-container[_ngcontent-%COMP%], .data-card[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .card-action-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%], .primary-btn[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .progress-fill[_ngcontent-%COMP%]::after {\\n    animation: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    border: 2px solid #0f172a;\\n  }\\n  .data-card[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n  .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n}\\n\\n\\n\\n\\n.test-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;\\n  color: white !important;\\n  border: none !important;\\n  border-radius: 25px !important;\\n  padding: 12px 24px !important;\\n  font-weight: 600 !important;\\n  font-size: 0.9em !important;\\n  text-transform: uppercase !important;\\n  letter-spacing: 0.5px !important;\\n  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;\\n  transition: all 0.3s ease !important;\\n  position: relative !important;\\n  overflow: hidden !important;\\n}\\n.test-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) !important;\\n  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;\\n  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%) !important;\\n}\\n.test-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) !important;\\n}\\n.test-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px !important;\\n  font-size: 1.1em !important;\\n}\\n.test-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.test-button[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.idle-content[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  font-size: 0.85em !important;\\n  padding: 10px 20px !important;\\n}\\n\\n.controls-header[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n  font-size: 0.8em !important;\\n  padding: 8px 16px !important;\\n}\\n\\n\\n\\n.scrollable-hidden[_ngcontent-%COMP%] {\\n  overflow-y: scroll;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.scrollable-hidden[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n\\n\\n.validation-buttons[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 1.5rem;\\n  padding: 1.5rem;\\n  background: #f8fafc;\\n  border-radius: 12px;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #475569;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]   .validation-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 1.25rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  text-transform: none;\\n  letter-spacing: 0.025em;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  font-size: 1.1rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #10b981 100%);\\n  color: white;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\\n  color: white;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\\n}\\n@media (max-width: 480px) {\\n  .validation-buttons[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: unset;\\n  }\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [trigger('fadeInOut', [transition(':enter', [style({\n        opacity: 0\n      }), animate('300ms ease-in', style({\n        opacity: 1\n      }))]), transition(':leave', [animate('200ms ease-out', style({\n        opacity: 0\n      }))])]), trigger('slideInOut', [transition(':enter', [style({\n        transform: 'translateY(-50px)',\n        opacity: 0\n      }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      }))]), transition(':leave', [animate('300ms ease-in', style({\n        transform: 'translateY(-30px)',\n        opacity: 0\n      }))])]), trigger('cardAnimation', [transition(':enter', [style({\n        transform: 'scale(0.8)',\n        opacity: 0\n      }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'scale(1)',\n        opacity: 1\n      }))])])]\n    }\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "Router", "CommonModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatCheckboxModule", "MatSlideToggleModule", "MatCardModule", "MatFormFieldModule", "MatDialog", "MatDialogModule", "MatTooltipModule", "MatChipsModule", "MatSnackBarModule", "ReactiveFormsModule", "FormsModule", "trigger", "style", "transition", "animate", "ModalColetaDadosVittaltecComponent", "Subject", "takeUntil", "VoiceRecorderService", "SpeakerService", "AiQuestionarioApiService", "AlertComponent", "CriptografarUtil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "PreConsultaQuestionarioComponent_div_1_Template_button_click_13_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "iniciarAtendimento", "ɵɵproperty", "undefined", "ɵɵadvance", "ɵɵtextInterpolate", "displayedText", "aiResponse", "PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_8_listener", "_r4", "confirmarInformacao", "PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_12_listener", "tentarNovamente", "isProcessing", "lastItem_r6", "value", "label", "ɵɵstyleProp", "getProgressPercentage", "ɵɵtextInterpolate2", "getDadosPreenchidos", "length", "getTotalCampos", "PreConsultaQuestionarioComponent_div_2_div_21_Template_button_click_5_listener", "_r5", "openHistoryModal", "ɵɵtemplate", "PreConsultaQuestionarioComponent_div_2_div_21_div_8_Template", "PreConsultaQuestionarioComponent_div_2_div_21_div_9_Template", "getUltimaVariavelPreenchida", "PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template_button_click_0_listener", "_r8", "enviarTexto", "canSendText", "ɵɵtwoWayListener", "PreConsultaQuestionarioComponent_div_2_div_22_Template_input_ngModelChange_5_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "userInput", "PreConsultaQuestionarioComponent_div_2_div_22_Template_input_keyup_enter_5_listener", "PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template", "PreConsultaQuestionarioComponent_div_2_div_22_mat_hint_7_Template", "ɵɵtwoWayProperty", "isInputDisabled", "isSpeaking", "PreConsultaQuestionarioComponent_div_2_div_23_div_2_Template", "ɵɵpureFunction0", "_c0", "PreConsultaQuestionarioComponent_div_2_div_24_div_4_Template", "ɵɵclassProp", "isRecording", "isAguardandoResposta", "ɵɵtextInterpolate1", "PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_ngModelChange_5_listener", "_r3", "isTextMode", "PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_change_5_listener", "toggleTextMode", "PreConsultaQuestionarioComponent_div_2_div_15_Template", "PreConsultaQuestionarioComponent_div_2_div_18_Template", "PreConsultaQuestionarioComponent_div_2_div_19_Template", "PreConsultaQuestionarioComponent_div_2_div_20_Template", "PreConsultaQuestionarioComponent_div_2_div_21_Template", "PreConsultaQuestionarioComponent_div_2_div_22_Template", "PreConsultaQuestionarioComponent_div_2_div_23_Template", "PreConsultaQuestionarioComponent_div_2_div_24_Template", "showValidationButtons", "PreConsultaQuestionarioComponent", "router", "dialog", "voiceRecorder", "speaker", "aiService", "snackBar", "cdr", "isIdle", "campoAtual", "isHistoryModalOpen", "fullResponseText", "textDisplayInterval", "searchTerm", "availableFilters", "type", "icon", "active", "conversationHistory", "currentToken", "dadosColetados", "nome", "cpf", "email", "telefone", "dataNascimento", "<PERSON><PERSON><PERSON>", "sintomas", "intensidadeDor", "tempoSintomas", "doencasPrevia<PERSON>", "observacoes", "destroy$", "constructor", "ngOnInit", "preloadVoices", "startSpeakingMonitor", "recording$", "pipe", "subscribe", "detectChanges", "result$", "result", "success", "text", "adicionarRespostaUsuario", "error$", "error", "console", "includes", "falhaSnackbar", "iniciarGravacaoAutomatica", "recordingEvent$", "event", "waitUntilFinished", "then", "setTimeout", "ngOnDestroy", "next", "complete", "cancel", "stopRecording", "clearTextDisplayInterval", "generateToken", "solicitarPermissaoMicrofone", "enviarMensagemParaIA", "previousMode", "handleMicrophoneStateTransition", "previousTextMode", "currentTextMode", "deactivateMicrophoneForTextMode", "activateMicrophoneForVoiceMode", "log", "isCurrentlyRecording", "initializeMicrophoneForVoiceMode", "_this", "_asyncToGenerator", "ultimaMensagem", "payload", "formaDeResposta", "historicoConversa", "token", "enviarMensagens", "response", "processarRespostaIA", "textoResposta", "adicionarRespostaIA", "flgValidacaoCampoAtual", "atualizarDadosColetados", "dados", "flgFinalizar", "todosOsCamposPreenchidos", "finalizarProcesso", "speak", "startSynchronizedTextDisplay", "iniciarProcessoAguardandoResposta", "catch", "resposta", "push", "novosdados", "Object", "keys", "for<PERSON>ach", "key", "trim", "camposObrigatorios", "every", "campo", "iniciarGravacaoContinua", "_this2", "isSupported", "waitForSpeechEndWithReducedLatency", "monitorarEstadoGravacao", "intervalId", "setInterval", "clearInterval", "serviceRecording", "componentRecording", "finalizarProcessoAguardandoResposta", "startRecording", "iniciarGravacao", "pararGravacao", "toggleRecording", "synthesis", "window", "speechSynthesis", "voices", "getVoices", "onvoiceschanged", "newVoices", "silentUtterance", "SpeechSynthesisUtterance", "volume", "testarFluxoCompleto", "_this3", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "getTracks", "track", "stop", "now", "Date", "timestamp", "getTime", "random", "Math", "floor", "currentlySpeaking", "words", "split", "totalDuration", "estimateSpeechDuration", "intervalTime", "currentWordIndex", "wordsPerMinute", "durationInMinutes", "salvarDadosQuestionario", "abrirDialogColetaDadosVittalTec", "dadosParaSalvar", "idade", "calcularIdade", "map", "s", "sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "extrairNumeroIntensidade", "dataPreenchimento", "toISOString", "localStorageCriptografado", "JSON", "stringify", "nascimento", "hoje", "getFullYear", "mesAtual", "getMonth", "mesNascimento", "getDate", "intensidade", "match", "parseInt", "redirecionarParaFilaEspera", "navigate", "dialogRef", "open", "disableClose", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "panelClass", "afterClosed", "action", "sucessoSnackbar", "removerLocalStorageCriptografado", "labels", "filter", "HistoryModalDialogComponent", "dadosClone", "parse", "data", "autoFocus", "total", "filled", "round", "onSearchChange", "target", "clearSearch", "toggleFilter", "getFilteredData", "getEnhancedDadosPreenchidos", "searchLower", "toLowerCase", "item", "activeCategories", "f", "category", "categoryMap", "categoryLabel", "categoryInfo", "validationStatus", "getValidationStatusForField", "field", "trackByFn", "index", "highlightSearchTerm", "regex", "RegExp", "replace", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "getValidationIcon", "status", "getValidationLabel", "copyToClipboard", "clipboard", "writeText", "editValue", "newValue", "prompt", "find", "clearAllData", "confirm", "isMicrophoneDisabled", "_this4", "Promise", "resolve", "checkInterval", "maxWaitTime", "elapsedTime", "warn", "enviarMensagemSistema", "mensagem", "ɵɵdirectiveInject", "i1", "i2", "i3", "i4", "i5", "i6", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PreConsultaQuestionarioComponent_Template", "rf", "ctx", "PreConsultaQuestionarioComponent_div_1_Template", "PreConsultaQuestionarioComponent_div_2_Template", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i9", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "MatSuffix", "i10", "MatInput", "i11", "MatButton", "MatIconButton", "i12", "MatSlideToggle", "i13", "MatIcon", "i14", "MatTooltip", "styles", "animation", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\pre-consulta-questionario.component.ts", "C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\pre-consulta-questionario.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { trigger, style, transition, animate } from '@angular/animations';\r\nimport { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\r\nimport { QuestionarioPreConsultaDados, WebhookAiQuestionarioPayload, WebhookAiQuestionarioResponse } from './MapPalavrasModel';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { VoiceRecorderService } from './Service/voice-recorder.service';\r\nimport { SpeakerService } from './Service/speaker.service';\r\nimport { AiQuestionarioApiService } from './Service/ai-questionario-api.service';\r\nimport { AlertComponent } from 'src/app/alert/alert.component';\r\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\r\n\r\ninterface FilterOption {\r\n  type: string;\r\n  label: string;\r\n  icon: string;\r\n  active: boolean;\r\n}\r\n\r\ninterface EnhancedDataItem {\r\n  label: string;\r\n  value: string;\r\n  category: string;\r\n  categoryLabel: string;\r\n  icon: string;\r\n  timestamp?: Date;\r\n  validationStatus: 'valid' | 'warning' | 'error';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-pre-consulta-questionario',\r\n  templateUrl: './pre-consulta-questionario.component.html',\r\n  styleUrls: ['./pre-consulta-questionario.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatCheckboxModule,\r\n    MatSlideToggleModule,\r\n    MatIconModule,\r\n    MatDialogModule,\r\n    MatTooltipModule,\r\n    MatChipsModule,\r\n    MatSnackBarModule\r\n  ],\r\n  animations: [\r\n    trigger('fadeInOut', [\r\n      transition(':enter', [\r\n        style({ opacity: 0 }),\r\n        animate('300ms ease-in', style({ opacity: 1 }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('200ms ease-out', style({ opacity: 0 }))\r\n      ])\r\n    ]),\r\n    trigger('slideInOut', [\r\n      transition(':enter', [\r\n        style({ transform: 'translateY(-50px)', opacity: 0 }),\r\n        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n          style({ transform: 'translateY(0)', opacity: 1 }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('300ms ease-in',\r\n          style({ transform: 'translateY(-30px)', opacity: 0 }))\r\n      ])\r\n    ]),\r\n    trigger('cardAnimation', [\r\n      transition(':enter', [\r\n        style({ transform: 'scale(0.8)', opacity: 0 }),\r\n        animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n          style({ transform: 'scale(1)', opacity: 1 }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\nexport class PreConsultaQuestionarioComponent implements OnInit, OnDestroy {\r\n\r\n  isIdle = true;\r\n  isProcessing = false;\r\n  isTextMode = false;\r\n  isRecording = false;\r\n  isAguardandoResposta = false;\r\n  campoAtual = '';\r\n  isHistoryModalOpen = false;\r\n\r\n  showValidationButtons = false;\r\n\r\n  isSpeaking = false;\r\n  displayedText = '';\r\n  fullResponseText = '';\r\n  textDisplayInterval: any;\r\n\r\n  searchTerm = '';\r\n  availableFilters: FilterOption[] = [\r\n    { type: 'personal', label: 'Dados Pessoais', icon: 'person', active: true },\r\n    { type: 'medical', label: 'Informações Médicas', icon: 'medical_services', active: true },\r\n    { type: 'contact', label: 'Contato', icon: 'contact_phone', active: true },\r\n    { type: 'optional', label: 'Opcionais', icon: 'info', active: true }\r\n  ];\r\n\r\n  conversationHistory: string[] = [];\r\n  currentToken = '';\r\n  aiResponse = '';\r\n  userInput = '';\r\n\r\n  dadosColetados: QuestionarioPreConsultaDados = {\r\n    nome: '',\r\n    cpf: '',\r\n    email: '',\r\n    telefone: '',\r\n    dataNascimento: '',\r\n    alergias: '',\r\n    sintomas: '',\r\n    intensidadeDor: '',\r\n    tempoSintomas: '',\r\n    doencasPrevias: '',\r\n    observacoes: ''\r\n  };\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private dialog: MatDialog,\r\n    private voiceRecorder: VoiceRecorderService,\r\n    private speaker: SpeakerService,\r\n    private aiService: AiQuestionarioApiService,\r\n    private snackBar: AlertComponent,\r\n    private cdr: ChangeDetectorRef\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.preloadVoices();\r\n    this.startSpeakingMonitor();\r\n\r\n    this.voiceRecorder.recording$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(isRecording => {\r\n        this.isRecording = isRecording;\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    this.voiceRecorder.result$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(result => {\r\n        if (result.success && result.text) {\r\n          this.adicionarRespostaUsuario(result.text);\r\n        }\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    this.voiceRecorder.error$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(error => {\r\n        console.error('Erro de reconhecimento de voz:', error);\r\n        this.isRecording = false;\r\n        this.cdr.detectChanges();\r\n\r\n        if (!error.includes('aborted')) {\r\n          this.snackBar.falhaSnackbar(error);\r\n\r\n          if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {\r\n            this.iniciarGravacaoAutomatica();\r\n          }\r\n        }\r\n      });\r\n\r\n    this.voiceRecorder.recordingEvent$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(event => {\r\n        if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {\r\n          if (this.speaker.isSpeaking()) {\r\n            this.speaker.waitUntilFinished().then(() => {\r\n              if (this.isAguardandoResposta && !this.isRecording) {\r\n                this.iniciarGravacaoAutomatica();\r\n              }\r\n            });\r\n          } else {\r\n            setTimeout(() => {\r\n              if (this.isAguardandoResposta && !this.isRecording) {\r\n                this.iniciarGravacaoAutomatica();\r\n              }\r\n            }, 500);\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.speaker.cancel();\r\n    this.voiceRecorder.stopRecording();\r\n    this.clearTextDisplayInterval();\r\n  }\r\n\r\n  iniciarAtendimento(): void {\r\n    this.isIdle = false;\r\n    this.cdr.detectChanges();\r\n    this.currentToken = this.generateToken();\r\n    this.conversationHistory = ['Iniciar atendimento'];\r\n    this.campoAtual = 'inicio';\r\n\r\n    if (!this.isTextMode) {\r\n      this.solicitarPermissaoMicrofone();\r\n    }\r\n\r\n    this.enviarMensagemParaIA();\r\n  }\r\n\r\n  toggleTextMode(): void {\r\n    const previousMode = this.isTextMode;\r\n    this.cdr.detectChanges();\r\n    this.userInput = '';\r\n\r\n    // Handle microphone state management based on mode transition\r\n    this.handleMicrophoneStateTransition(previousMode, this.isTextMode);\r\n  }\r\n\r\n  /**\r\n   * Manages microphone state when switching between text and voice modes\r\n   * @param previousTextMode - Previous state of isTextMode\r\n   * @param currentTextMode - Current state of isTextMode\r\n   */\r\n  private handleMicrophoneStateTransition(previousTextMode: boolean, currentTextMode: boolean): void {\r\n    // Switching FROM voice mode TO text mode\r\n    if (!previousTextMode && currentTextMode) {\r\n      this.deactivateMicrophoneForTextMode();\r\n    }\r\n    // Switching FROM text mode TO voice mode\r\n    else if (previousTextMode && !currentTextMode) {\r\n      this.activateMicrophoneForVoiceMode();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivates microphone when switching to text mode\r\n   * - Stops any active recording immediately\r\n   * - Cancels pending voice input processing\r\n   * - Ensures complete microphone cleanup\r\n   */\r\n  private deactivateMicrophoneForTextMode(): void {\r\n    console.log('🔇 Desativando microfone - mudança para modo texto');\r\n\r\n    try {\r\n      // Stop any active recording immediately\r\n      if (this.voiceRecorder.isCurrentlyRecording()) {\r\n        console.log('⏹️ Parando gravação ativa');\r\n        this.voiceRecorder.stopRecording();\r\n      }\r\n\r\n      // Update recording state\r\n      this.isRecording = false;\r\n\r\n      // Clear any pending voice input processing\r\n      this.isAguardandoResposta = false;\r\n\r\n      console.log('✅ Microfone desativado com sucesso para modo texto');\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro ao desativar microfone:', error);\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * Activates microphone when switching to voice mode\r\n   * - Initializes microphone if AI response reading is complete\r\n   * - Requests permissions if needed\r\n   * - Prepares for immediate voice input\r\n   */\r\n  private activateMicrophoneForVoiceMode(): void {\r\n    console.log('🎤 Ativando microfone - mudança para modo voz');\r\n\r\n    try {\r\n      // Only activate if AI is not currently speaking\r\n      if (this.speaker.isSpeaking()) {\r\n        console.log('🔊 Aguardando fim da fala da IA para ativar microfone');\r\n        this.speaker.waitUntilFinished().then(() => {\r\n          this.initializeMicrophoneForVoiceMode();\r\n        });\r\n      } else {\r\n        this.initializeMicrophoneForVoiceMode();\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro ao ativar microfone:', error);\r\n      this.snackBar.falhaSnackbar('Erro ao ativar o microfone. Tente novamente.');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initializes microphone for voice mode operation\r\n   */\r\n  private async initializeMicrophoneForVoiceMode(): Promise<void> {\r\n    try {\r\n      // Request microphone permissions\r\n      await this.solicitarPermissaoMicrofone();\r\n\r\n      // If we're waiting for user response, start recording automatically\r\n      if (this.isAguardandoResposta && !this.isProcessing) {\r\n        console.log('🎯 Iniciando gravação automática após ativação do microfone');\r\n        setTimeout(() => {\r\n          if (!this.isTextMode && this.isAguardandoResposta) {\r\n            this.iniciarGravacaoAutomatica();\r\n          }\r\n        }, 500);\r\n      }\r\n\r\n      console.log('✅ Microfone ativado com sucesso para modo voz');\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro ao inicializar microfone para modo voz:', error);\r\n      this.snackBar.falhaSnackbar('Erro ao inicializar o microfone. Verifique as permissões.');\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private enviarMensagemParaIA(): void {\r\n    this.isProcessing = true;\r\n    this.cdr.detectChanges();\r\n\r\n    const ultimaMensagem = this.conversationHistory.length > 0\r\n      ? this.conversationHistory[this.conversationHistory.length - 1]\r\n      : '';\r\n\r\n    const payload: WebhookAiQuestionarioPayload = {\r\n      formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',\r\n      historicoConversa: [...this.conversationHistory],\r\n      ultimaMensagem: ultimaMensagem,\r\n      campoAtual: this.campoAtual,\r\n      token: this.currentToken\r\n    };\r\n\r\n    if (this.conversationHistory.length > 1 && !this.isTextMode) {\r\n      if (this.voiceRecorder.isCurrentlyRecording()) {\r\n        this.voiceRecorder.stopRecording();\r\n      }\r\n    }\r\n\r\n    this.aiService.enviarMensagens(payload)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response: WebhookAiQuestionarioResponse) => {\r\n          this.processarRespostaIA(response);\r\n        },\r\n        error: (error) => {\r\n          this.isProcessing = false;\r\n          this.cdr.detectChanges();\r\n          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\r\n          console.error('Erro na comunicação com IA:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  private processarRespostaIA(response: WebhookAiQuestionarioResponse): void {\r\n    this.isProcessing = false;\r\n    this.aiResponse = response.textoResposta;\r\n    this.fullResponseText = response.textoResposta;\r\n    this.displayedText = '';\r\n\r\n    this.adicionarRespostaIA(response.textoResposta);\r\n\r\n    if (response.campoAtual) {\r\n      this.campoAtual = response.campoAtual;\r\n    }\r\n\r\n    // Handle validation confirmation\r\n    this.showValidationButtons = response.flgValidacaoCampoAtual || false;\r\n\r\n    this.cdr.detectChanges();\r\n\r\n    this.atualizarDadosColetados(response.dados);\r\n    this.cdr.detectChanges();\r\n\r\n    if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {\r\n      this.finalizarProcesso();\r\n    } else {\r\n      // Preparar texto para exibição, mas não iniciar ainda\r\n      this.displayedText = '';\r\n      this.cdr.detectChanges();\r\n\r\n      // Iniciar áudio com callback para sincronizar texto\r\n      this.speaker.speak(response.textoResposta, () => {\r\n        // Este callback é executado quando o áudio realmente começa a tocar\r\n        console.log('🎯 Iniciando sincronização de texto com áudio');\r\n        this.startSynchronizedTextDisplay();\r\n      }).then(() => {\r\n        // Only start waiting for response if validation buttons are not shown\r\n        if (!this.showValidationButtons) {\r\n          this.iniciarProcessoAguardandoResposta();\r\n        }\r\n      }).catch(error => {\r\n        console.error('Erro na reprodução da IA:', error);\r\n        this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro\r\n        this.cdr.detectChanges();\r\n        // Only start waiting for response if validation buttons are not shown\r\n        if (!this.showValidationButtons) {\r\n          this.iniciarProcessoAguardandoResposta();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private adicionarRespostaIA(resposta: string): void {\r\n    this.conversationHistory.push(`(resposta da ia) ${resposta}`);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private atualizarDadosColetados(novosdados: QuestionarioPreConsultaDados): void {\r\n    Object.keys(novosdados).forEach(key => {\r\n      if (novosdados[key as keyof QuestionarioPreConsultaDados] &&\r\n        novosdados[key as keyof QuestionarioPreConsultaDados].trim() !== '') {\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados] =\r\n          novosdados[key as keyof QuestionarioPreConsultaDados];\r\n      }\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private todosOsCamposPreenchidos(): boolean {\r\n    const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];\r\n    return camposObrigatorios.every(campo =>\r\n      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados] &&\r\n      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados].trim() !== ''\r\n    );\r\n  }\r\n\r\n  private iniciarProcessoAguardandoResposta(): void {\r\n    this.isAguardandoResposta = true;\r\n    this.cdr.detectChanges();\r\n\r\n    if (!this.isTextMode)\r\n      setTimeout(() => {\r\n        this.iniciarGravacaoContinua();\r\n      }, 500);\r\n  }\r\n\r\n  private async iniciarGravacaoContinua(): Promise<void> {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      console.error('Reconhecimento de voz não suportado');\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Redução de latência: monitorar mais frequentemente o fim da fala\r\n    await this.waitForSpeechEndWithReducedLatency();\r\n    this.iniciarGravacaoAutomatica();\r\n    this.monitorarEstadoGravacao();\r\n  }\r\n\r\n  private monitorarEstadoGravacao(): void {\r\n    const intervalId = setInterval(() => {\r\n      if (!this.isAguardandoResposta) {\r\n        clearInterval(intervalId);\r\n        return;\r\n      }\r\n\r\n      if (this.isTextMode) {\r\n        return;\r\n      }\r\n\r\n      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\r\n      const componentRecording = this.isRecording;\r\n\r\n      if (serviceRecording !== componentRecording) {\r\n        this.isRecording = serviceRecording;\r\n        this.cdr.detectChanges();\r\n      }\r\n\r\n      if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {\r\n        this.iniciarGravacaoAutomatica();\r\n      }\r\n    }, 2000);\r\n  }\r\n\r\n  private finalizarProcessoAguardandoResposta(): void {\r\n    this.isAguardandoResposta = false;\r\n    this.cdr.detectChanges();\r\n\r\n    if (this.isRecording) {\r\n      this.voiceRecorder.stopRecording();\r\n    }\r\n  }\r\n\r\n  private iniciarGravacaoAutomatica(): void {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      console.error('Reconhecimento de voz não suportado');\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Don't start recording if in text mode\r\n    if (this.isTextMode) {\r\n      console.log('📝 Modo texto ativo - não iniciando gravação automática');\r\n      return;\r\n    }\r\n\r\n    // Não iniciar gravação se o sistema estiver falando\r\n    if (this.speaker.isSpeaking()) {\r\n      console.log('🔇 Aguardando término da fala para iniciar gravação');\r\n      return;\r\n    }\r\n\r\n    const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\r\n    const componentRecording = this.isRecording;\r\n\r\n    if (serviceRecording || componentRecording) {\r\n      return;\r\n    }\r\n\r\n    const success = this.voiceRecorder.startRecording();\r\n\r\n    if (!success)\r\n      console.error('❌ Falha ao iniciar gravação automática');\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  iniciarGravacao(): void {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Verificar se o sistema está falando\r\n    if (this.speaker.isSpeaking()) {\r\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');\r\n      return;\r\n    }\r\n\r\n    const success = this.voiceRecorder.startRecording();\r\n    if (!success) {\r\n      this.snackBar.falhaSnackbar('Erro ao iniciar gravação');\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  pararGravacao(): void {\r\n    this.voiceRecorder.stopRecording();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  toggleRecording(): void {\r\n    if (this.isRecording) {\r\n      this.pararGravacao();\r\n    } else {\r\n      this.iniciarGravacao();\r\n    }\r\n  }\r\n\r\n  private preloadVoices(): void {\r\n    const synthesis = window.speechSynthesis;\r\n    const voices = synthesis.getVoices();\r\n\r\n    if (voices.length === 0) {\r\n      synthesis.onvoiceschanged = () => {\r\n        const newVoices = synthesis.getVoices();\r\n        newVoices;\r\n      };\r\n\r\n      const silentUtterance = new SpeechSynthesisUtterance('');\r\n      silentUtterance.volume = 0;\r\n      synthesis.speak(silentUtterance);\r\n      synthesis.cancel();\r\n    }\r\n  }\r\n\r\n  testarFluxoCompleto(): void {\r\n    this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.', () => {\r\n      console.log('🎯 Teste: áudio iniciado');\r\n    }).then(() => {\r\n      this.iniciarProcessoAguardandoResposta();\r\n    });\r\n  }\r\n\r\n  enviarTexto(): void {\r\n    // Não permitir envio se o sistema estiver falando\r\n    if (this.speaker.isSpeaking()) {\r\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');\r\n      return;\r\n    }\r\n\r\n    if (this.userInput.trim()) {\r\n      this.adicionarRespostaUsuario(this.userInput);\r\n      this.userInput = '';\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private adicionarRespostaUsuario(resposta: string): void {\r\n    if (this.isAguardandoResposta) {\r\n      this.finalizarProcessoAguardandoResposta();\r\n    }\r\n\r\n    this.conversationHistory.push(`(resposta do usuário) ${resposta}`);\r\n    this.cdr.detectChanges();\r\n    this.enviarMensagemParaIA();\r\n  }\r\n\r\n  private async solicitarPermissaoMicrofone(): Promise<void> {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      stream.getTracks().forEach(track => track.stop());\r\n    } catch (error) {\r\n      console.error('Erro ao solicitar permissão de microfone:', error);\r\n      this.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');\r\n    }\r\n  }\r\n\r\n  private generateToken(): string {\r\n    const now = new Date();\r\n    const timestamp = now.getTime();\r\n    const random = Math.floor(Math.random() * 10000);\r\n    return `${timestamp}_${random}`;\r\n  }\r\n\r\n  // Métodos para controle de fala e exibição sincronizada\r\n  private startSpeakingMonitor(): void {\r\n    setInterval(() => {\r\n      const currentlySpeaking = this.speaker.isSpeaking();\r\n      if (this.isSpeaking !== currentlySpeaking) {\r\n        this.isSpeaking = currentlySpeaking;\r\n        this.cdr.detectChanges();\r\n      }\r\n    }, 100); // Verificar a cada 100ms\r\n  }\r\n\r\n  private startSynchronizedTextDisplay(): void {\r\n    this.clearTextDisplayInterval();\r\n    this.displayedText = '';\r\n\r\n    const words = this.fullResponseText.split(' ');\r\n    const totalDuration = this.estimateSpeechDuration(this.fullResponseText);\r\n    const intervalTime = totalDuration / words.length;\r\n\r\n    let currentWordIndex = 0;\r\n\r\n    this.textDisplayInterval = setInterval(() => {\r\n      if (currentWordIndex < words.length) {\r\n        if (currentWordIndex === 0) {\r\n          this.displayedText = words[currentWordIndex];\r\n        } else {\r\n          this.displayedText += ' ' + words[currentWordIndex];\r\n        }\r\n        currentWordIndex++;\r\n        this.cdr.detectChanges();\r\n      } else {\r\n        this.clearTextDisplayInterval();\r\n      }\r\n    }, intervalTime);\r\n  }\r\n\r\n  private estimateSpeechDuration(text: string): number {\r\n    const words = text.split(' ').length;\r\n    const wordsPerMinute = 150;\r\n    const durationInMinutes = words / wordsPerMinute;\r\n    return (durationInMinutes * 60 * 1000);\r\n  }\r\n\r\n  private clearTextDisplayInterval(): void {\r\n    if (this.textDisplayInterval) {\r\n      clearInterval(this.textDisplayInterval);\r\n      this.textDisplayInterval = null;\r\n    }\r\n  }\r\n\r\n  private finalizarProcesso(): void {\r\n    this.cdr.detectChanges();\r\n    this.salvarDadosQuestionario();\r\n    this.abrirDialogColetaDadosVittalTec();\r\n  }\r\n\r\n  private salvarDadosQuestionario(): void {\r\n    try {\r\n      const dadosParaSalvar = {\r\n        nome: this.dadosColetados.nome,\r\n        idade: this.calcularIdade(this.dadosColetados.dataNascimento),\r\n        cpf: this.dadosColetados.cpf,\r\n        email: this.dadosColetados.email,\r\n        telefone: this.dadosColetados.telefone,\r\n        dataNascimento: this.dadosColetados.dataNascimento,\r\n        sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],\r\n        sintomasOutros: this.dadosColetados.observacoes || '',\r\n        intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),\r\n        tempoSintomas: this.dadosColetados.tempoSintomas,\r\n        alergias: this.dadosColetados.alergias || '',\r\n        doencasPrevias: this.dadosColetados.doencasPrevias || '',\r\n        observacoes: this.dadosColetados.observacoes || '',\r\n        dataPreenchimento: new Date().toISOString()\r\n      };\r\n\r\n      CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));\r\n    } catch (error) {\r\n      this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');\r\n    }\r\n  }\r\n\r\n  private calcularIdade(dataNascimento: string): number {\r\n    if (!dataNascimento) return 0;\r\n\r\n    try {\r\n      const nascimento = new Date(dataNascimento);\r\n      const hoje = new Date();\r\n      let idade = hoje.getFullYear() - nascimento.getFullYear();\r\n      const mesAtual = hoje.getMonth();\r\n      const mesNascimento = nascimento.getMonth();\r\n\r\n      if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {\r\n        idade--;\r\n      }\r\n\r\n      return idade;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  private extrairNumeroIntensidade(intensidade: string): number {\r\n    if (!intensidade) return 0;\r\n\r\n    const match = intensidade.match(/\\d+/);\r\n    return match ? parseInt(match[0], 10) : 0;\r\n  }\r\n\r\n  private redirecionarParaFilaEspera(): void {\r\n    try {\r\n      this.router.navigate(['/filaespera']);\r\n    } catch (error) {\r\n      console.error('Erro ao redirecionar para fila de espera:', error);\r\n      this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');\r\n      this.router.navigate(['/filaespera']);\r\n    }\r\n  }\r\n\r\n  private abrirDialogColetaDadosVittalTec(): void {\r\n    const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\r\n      disableClose: true,\r\n      width: '500px',\r\n      maxWidth: '85vw',\r\n      height: 'auto',\r\n      maxHeight: '70vh',\r\n      panelClass: 'vittaltec-modal-panel',\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result?.action === 'continuar') {\r\n        this.snackBar.sucessoSnackbar(\"Dados coletados com sucesso!\");\r\n        this.redirecionarParaFilaEspera();\r\n      } else if (result?.action === 'cancelar') {\r\n        this.snackBar.falhaSnackbar(\"Processo de coleta de dados cancelado.\");\r\n        CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');\r\n      }\r\n\r\n      this.cdr.detectChanges();\r\n    });\r\n  }\r\n\r\n  getDadosPreenchidos(): Array<{ label: string, value: string }> {\r\n    const labels: { [key: string]: string } = {\r\n      nome: 'Nome',\r\n      cpf: 'CPF',\r\n      email: 'Email',\r\n      telefone: 'Telefone',\r\n      dataNascimento: 'Data de Nascimento',\r\n      alergias: 'Alergias',\r\n      sintomas: 'Sintomas',\r\n      intensidadeDor: 'Intensidade da Dor',\r\n      tempoSintomas: 'Tempo dos Sintomas',\r\n      doencasPrevias: 'Doenças Prévias',\r\n      observacoes: 'Observações'\r\n    };\r\n\r\n    return Object.keys(this.dadosColetados)\r\n      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')\r\n      .map(key => ({\r\n        label: labels[key],\r\n        value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados]\r\n      }));\r\n  }\r\n\r\n  getUltimaVariavelPreenchida(): { label: string, value: string } | null {\r\n    const dados = this.getDadosPreenchidos();\r\n    return dados.length > 0 ? dados[dados.length - 1] : null;\r\n  }\r\n\r\n  openHistoryModal(): void {\r\n    import('./components/history-modal-dialog.component').then(({ HistoryModalDialogComponent }) => {\r\n      const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));\r\n      this.dialog.open(HistoryModalDialogComponent, {\r\n        width: '900px',\r\n        maxWidth: '98vw',\r\n        data: {\r\n          dadosColetados: dadosClone,\r\n          snackBar: this.snackBar,\r\n          cdr: this.cdr\r\n        },\r\n        panelClass: 'modern-modal-overlay',\r\n        autoFocus: false\r\n      });\r\n    });\r\n  }\r\n\r\n  getTotalCampos(): number {\r\n    return 10;\r\n  }\r\n\r\n  getProgressPercentage(): number {\r\n    const total = this.getTotalCampos();\r\n    const filled = this.getDadosPreenchidos().length;\r\n    return Math.round((filled / total) * 100);\r\n  }\r\n\r\n  onSearchChange(event: any): void {\r\n    this.searchTerm = event.target.value;\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchTerm = '';\r\n  }\r\n\r\n  toggleFilter(filter: FilterOption): void {\r\n    filter.active = !filter.active;\r\n  }\r\n\r\n  getFilteredData(): EnhancedDataItem[] {\r\n    let data = this.getEnhancedDadosPreenchidos();\r\n\r\n    if (this.searchTerm) {\r\n      const searchLower = this.searchTerm.toLowerCase();\r\n      data = data.filter(item =>\r\n        item.label.toLowerCase().includes(searchLower) ||\r\n        item.value.toLowerCase().includes(searchLower)\r\n      );\r\n    }\r\n\r\n    const activeCategories = this.availableFilters\r\n      .filter(f => f.active)\r\n      .map(f => f.type);\r\n\r\n    data = data.filter(item => activeCategories.includes(item.category));\r\n\r\n    return data;\r\n  }\r\n\r\n  getEnhancedDadosPreenchidos(): EnhancedDataItem[] {\r\n    const categoryMap: { [key: string]: { category: string, categoryLabel: string, icon: string } } = {\r\n      nome: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'person' },\r\n      cpf: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'badge' },\r\n      dataNascimento: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'cake' },\r\n      email: { category: 'contact', categoryLabel: 'Contato', icon: 'email' },\r\n      telefone: { category: 'contact', categoryLabel: 'Contato', icon: 'phone' },\r\n      sintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'medical_services' },\r\n      intensidadeDor: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'personal_injury' },\r\n      tempoSintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'schedule' },\r\n      alergias: { category: 'optional', categoryLabel: 'Opcionais', icon: 'medical_information' },\r\n      observacoes: { category: 'optional', categoryLabel: 'Opcionais', icon: 'description' }\r\n    };\r\n\r\n    const labels: { [key: string]: string } = {\r\n      nome: 'Nome',\r\n      cpf: 'CPF',\r\n      email: 'Email',\r\n      telefone: 'Telefone',\r\n      dataNascimento: 'Data de Nascimento',\r\n      alergias: 'Alergias',\r\n      sintomas: 'Sintomas',\r\n      intensidadeDor: 'Intensidade da Dor',\r\n      tempoSintomas: 'Tempo dos Sintomas',\r\n      doencasPrevias: 'Doenças Prévias',\r\n      observacoes: 'Observações'\r\n    };\r\n\r\n    return Object.keys(this.dadosColetados)\r\n      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')\r\n      .map(key => {\r\n        const categoryInfo = categoryMap[key] || { category: 'optional', categoryLabel: 'Outros', icon: 'info' };\r\n        return {\r\n          label: labels[key] || key,\r\n          value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados],\r\n          category: categoryInfo.category,\r\n          categoryLabel: categoryInfo.categoryLabel,\r\n          icon: categoryInfo.icon,\r\n          timestamp: new Date(),\r\n          validationStatus: this.getValidationStatusForField(key) as 'valid' | 'warning' | 'error'\r\n        };\r\n      });\r\n  }\r\n\r\n  getValidationStatusForField(field: string): string {\r\n    const value = this.dadosColetados[field as keyof QuestionarioPreConsultaDados];\r\n    if (!value || value.trim() === '') return 'error';\r\n\r\n    if (field === 'email' && !value.includes('@')) return 'warning';\r\n    if (field === 'cpf' && value.length < 11) return 'warning';\r\n\r\n    return 'valid';\r\n  }\r\n\r\n  trackByFn(index: number, item: EnhancedDataItem): string {\r\n    index;\r\n    return item.label + item.value;\r\n  }\r\n\r\n  highlightSearchTerm(text: string): string {\r\n    if (!this.searchTerm) return text;\r\n\r\n    const regex = new RegExp(`(${this.searchTerm})`, 'gi');\r\n    return text.replace(regex, '<mark>$1</mark>');\r\n  }\r\n\r\n  formatTimestamp(timestamp: Date): string {\r\n    return timestamp.toLocaleTimeString('pt-BR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  }\r\n\r\n  getValidationIcon(status: string): string {\r\n    switch (status) {\r\n      case 'valid': return 'check_circle';\r\n      case 'warning': return 'warning';\r\n      case 'error': return 'error';\r\n      default: return 'info';\r\n    }\r\n  }\r\n\r\n  getValidationLabel(status: string): string {\r\n    switch (status) {\r\n      case 'valid': return 'Válido';\r\n      case 'warning': return 'Atenção';\r\n      case 'error': return 'Erro';\r\n      default: return 'Info';\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text).then(() => {\r\n      this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\r\n    }).catch(() => {\r\n      this.snackBar.falhaSnackbar('Erro ao copiar texto');\r\n    });\r\n  }\r\n\r\n  editValue(item: EnhancedDataItem): void {\r\n    const newValue = prompt(`Editar ${item.label}:`, item.value);\r\n    if (newValue !== null && newValue !== item.value) {\r\n      const field = Object.keys(this.dadosColetados).find(key => {\r\n        const labels: { [key: string]: string } = {\r\n          nome: 'Nome',\r\n          cpf: 'CPF',\r\n          email: 'Email',\r\n          telefone: 'Telefone',\r\n          dataNascimento: 'Data de Nascimento',\r\n          alergias: 'Alergias',\r\n          sintomas: 'Sintomas',\r\n          intensidadeDor: 'Intensidade da Dor',\r\n          tempoSintomas: 'Tempo dos Sintomas',\r\n          doencasPrevias: 'Doenças Prévias',\r\n          observacoes: 'Observações'\r\n        };\r\n        return labels[key] === item.label;\r\n      });\r\n\r\n      if (field) {\r\n        this.dadosColetados[field as keyof QuestionarioPreConsultaDados] = newValue;\r\n        this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\r\n        this.cdr.detectChanges();\r\n      }\r\n    }\r\n  }\r\n\r\n  clearAllData(): void {\r\n    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\r\n      this.dadosColetados = {\r\n        nome: '',\r\n        cpf: '',\r\n        email: '',\r\n        telefone: '',\r\n        dataNascimento: '',\r\n        alergias: '',\r\n        sintomas: '',\r\n        intensidadeDor: '',\r\n        tempoSintomas: '',\r\n        doencasPrevias: '',\r\n        observacoes: ''\r\n      };\r\n      this.snackBar.sucessoSnackbar('Todos os dados foram limpos');\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  isInputDisabled(): boolean {\r\n    return this.speaker.isSpeaking() || this.isProcessing;\r\n  }\r\n\r\n  isMicrophoneDisabled(): boolean {\r\n    return this.speaker.isSpeaking() || this.isProcessing;\r\n  }\r\n\r\n  canSendText(): boolean {\r\n    return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();\r\n  }\r\n\r\n  private async waitForSpeechEndWithReducedLatency(): Promise<void> {\r\n    return new Promise<void>((resolve) => {\r\n      const checkInterval = 50;\r\n      const maxWaitTime = 30000;\r\n      let elapsedTime = 0;\r\n\r\n      const intervalId = setInterval(() => {\r\n        elapsedTime += checkInterval;\r\n\r\n        if (!this.speaker.isSpeaking()) {\r\n          clearInterval(intervalId);\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 100);\r\n        } else if (elapsedTime >= maxWaitTime) {\r\n          clearInterval(intervalId);\r\n          console.warn('Timeout aguardando fim da fala');\r\n          resolve();\r\n        }\r\n      }, checkInterval);\r\n    });\r\n  }\r\n\r\n  confirmarInformacao(): void {\r\n    this.showValidationButtons = false;\r\n    this.enviarMensagemSistema('(Processamento do sistema) Usuário confirmou a informação, campo preenchido corretamente.');\r\n  }\r\n\r\n  tentarNovamente(): void {\r\n    this.showValidationButtons = false;\r\n    this.enviarMensagemSistema('(processamento do sistema) Campo preenchido incorretamente, o usuário terá que repreencher o campo do 0');\r\n  }\r\n\r\n  private enviarMensagemSistema(mensagem: string): void {\r\n\r\n    this.conversationHistory.push(mensagem);\r\n\r\n    const payload: WebhookAiQuestionarioPayload = {\r\n      formaDeResposta: 'Sistema',\r\n      historicoConversa: [...this.conversationHistory],\r\n      ultimaMensagem: mensagem,\r\n      campoAtual: this.campoAtual,\r\n      token: this.currentToken\r\n    };\r\n\r\n    this.isProcessing = true;\r\n    this.cdr.detectChanges();\r\n\r\n    this.aiService.enviarMensagens(payload)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response: WebhookAiQuestionarioResponse) => {\r\n          this.processarRespostaIA(response);\r\n        },\r\n        error: (error) => {\r\n          this.isProcessing = false;\r\n          this.cdr.detectChanges();\r\n          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\r\n          console.error('Erro na comunicação com IA:', error);\r\n        }\r\n      });\r\n  }\r\n}", "<div class=\"ai-questionnaire-container\">\r\n  <!-- <PERSON><PERSON> -->\r\n  <div class=\"idle-screen\" *ngIf=\"isIdle\">\r\n    <div class=\"idle-content\">\r\n      <!-- IA Robot Custom CSS -->\r\n      <div class=\"ai-robot\" style=\"margin-bottom: 9px;\">\r\n        <div class=\"robot-head\">\r\n          <div class=\"robot-eyes\">\r\n            <div class=\"eye left-eye\"></div>\r\n            <div class=\"eye right-eye\"></div>\r\n          </div>\r\n          <div class=\"robot-mouth\"></div>\r\n        </div>\r\n        <div class=\"robot-body\">\r\n          <div class=\"robot-chest\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <p>Vou coletar suas informações através de uma conversa natural</p>\r\n\r\n      <div class=\"action-buttons\">\r\n        <button class=\"start-btn\" (click)=\"iniciarAtendimento()\">\r\n          <span>Iniciar Atendimento</span>\r\n          <div class=\"btn-glow\"></div>\r\n        </button>\r\n\r\n        <!-- <PERSON><PERSON><PERSON> de teste na tela inicial -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Tela de Atendimento -->\r\n  <div class=\"chat-interface\" *ngIf=\"!isIdle\">\r\n    <!-- Controls Header - Position Absolute Top Left -->\r\n    <div class=\"controls-header\">\r\n      <div class=\"mode-indicator\">\r\n        <mat-icon class=\"mode-icon\">{{ isTextMode ? 'keyboard' : 'mic' }}</mat-icon>\r\n      </div>\r\n      <mat-slide-toggle\r\n        [(ngModel)]=\"isTextMode\"\r\n        (change)=\"toggleTextMode()\"\r\n        class=\"mode-toggle\"\r\n        [disabled]=\"isProcessing || isSpeaking\">\r\n        Modo Texto\r\n      </mat-slide-toggle>\r\n    </div>\r\n\r\n    <!-- Main Chat Area -->\r\n    <div class=\"main-chat-area\">\r\n      <!-- IA Section -->\r\n      <div class=\"ai-section\">\r\n        <div class=\"ai-avatar\" [class.processing]=\"isProcessing\" [class.listening]=\"isRecording\"\r\n          [class.waiting]=\"isAguardandoResposta && !isRecording\">\r\n          <div class=\"ai-face\">\r\n            <div class=\"ai-eyes\">\r\n              <div class=\"eye\"></div>\r\n              <div class=\"eye\"></div>\r\n            </div>\r\n            <div class=\"ai-mouth\" [class.talking]=\"isProcessing\"></div>\r\n          </div>\r\n          <div class=\"ai-pulse\" *ngIf=\"isProcessing || isRecording || isAguardandoResposta\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Response and Data Section -->\r\n      <div class=\"response-data-section\">\r\n        <div class=\"response-section\">\r\n          <div class=\"ai-message\" *ngIf=\"aiResponse\" [@fadeInOut]>\r\n            <div class=\"message-bubble scrollable-hidden\">\r\n              <p>{{ displayedText || aiResponse }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Processing Indicator -->\r\n          <div class=\"processing-indicator\" *ngIf=\"isProcessing\" [@fadeInOut]>\r\n            <div class=\"typing-dots\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n            <span class=\"processing-text\">Processando sua resposta...</span>\r\n          </div>\r\n\r\n          <!-- Validation Confirmation Buttons -->\r\n          <div class=\"validation-buttons\" *ngIf=\"showValidationButtons && !isProcessing\" [@fadeInOut]>\r\n            <div class=\"validation-container\">\r\n              <div class=\"validation-message\">\r\n                <mat-icon class=\"validation-icon\">help_outline</mat-icon>\r\n                <span>A informação está correta?</span>\r\n              </div>\r\n              <div class=\"button-group\">\r\n                <button\r\n                  mat-raised-button\r\n                  color=\"primary\"\r\n                  class=\"confirm-btn\"\r\n                  (click)=\"confirmarInformacao()\"\r\n                  [disabled]=\"isProcessing\">\r\n                  <mat-icon>check</mat-icon>\r\n                  Confirmar\r\n                </button>\r\n                <button\r\n                  mat-raised-button\r\n                  color=\"warn\"\r\n                  class=\"retry-btn\"\r\n                  (click)=\"tentarNovamente()\"\r\n                  [disabled]=\"isProcessing\">\r\n                  <mat-icon>refresh</mat-icon>\r\n                  Tentar Novamente\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Dados Preenchidos -->\r\n        <div class=\"data-section\" *ngIf=\"getDadosPreenchidos().length > 0\" [@slideInOut]>\r\n          <div class=\"data-panel\">\r\n            <div class=\"data-header\">\r\n              <h3>Última Informação Coletada</h3>\r\n              <button\r\n                mat-icon-button\r\n                (click)=\"openHistoryModal()\"\r\n                matTooltip=\"Ver todas as variáveis\"\r\n                class=\"history-btn\">\r\n                <mat-icon>history</mat-icon>\r\n              </button>\r\n            </div>\r\n            <div class=\"data-item\" *ngIf=\"getUltimaVariavelPreenchida() as lastItem\" [matTooltip]=\"lastItem.value\">\r\n              <span class=\"descInfoCategoria\">{{ lastItem.label }}</span>\r\n              <span class=\"descInfovalue\">{{ lastItem.value }}</span>\r\n            </div>\r\n            \r\n            <!-- Progress indicator -->\r\n            <div class=\"progress-info\" *ngIf=\"getDadosPreenchidos().length > 0\">\r\n              <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage()\"></div>\r\n              </div>\r\n              <span class=\"progress-text\">{{ getDadosPreenchidos().length }}/{{ getTotalCampos() }} campos preenchidos</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Input Area - Only show when in text mode -->\r\n    <div class=\"input-section\" *ngIf=\"isTextMode\">\r\n      <div class=\"input-container\">\r\n        <!-- Text Input -->\r\n        <mat-form-field appearance=\"outline\" class=\"user-input\">\r\n          <mat-label>Sua resposta</mat-label>\r\n          <input matInput [(ngModel)]=\"userInput\" placeholder=\"Digite aqui...\" (keyup.enter)=\"enviarTexto()\"\r\n            [disabled]=\"isInputDisabled()\">\r\n          <button mat-icon-button matSuffix *ngIf=\"canSendText()\" (click)=\"enviarTexto()\" [disabled]=\"!canSendText()\">\r\n            <mat-icon>send</mat-icon>\r\n          </button>\r\n          <!-- Indicador de que está aguardando fala terminar -->\r\n          <mat-hint *ngIf=\"isSpeaking\" class=\"speaking-hint\">\r\n            <mat-icon class=\"hint-icon\">volume_up</mat-icon>\r\n            Aguarde o término da fala...\r\n          </mat-hint>\r\n        </mat-form-field>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Audio Visualization -->\r\n    <div class=\"audio-visualization\" *ngIf=\"isRecording && !isTextMode\" [@fadeInOut]>\r\n      <div class=\"sound-wave\">\r\n        <div class=\"wave-bar\" *ngFor=\"let bar of [1,2,3,4,5,6,7,8]\"></div>\r\n      </div>\r\n      <span class=\"recording-text\">\r\n        <mat-icon class=\"recording-icon\">mic</mat-icon>\r\n        Gravando...\r\n      </span>\r\n    </div>\r\n\r\n    <!-- Voice Status Indicator (Floating) -->\r\n    <div class=\"voice-status-indicator\" *ngIf=\"!isTextMode\" [@fadeInOut]>\r\n      <div class=\"status-icon\"\r\n           [class.recording]=\"isRecording\"\r\n           [class.processing]=\"isProcessing\"\r\n           [class.waiting]=\"isAguardandoResposta && !isRecording && !isProcessing\">\r\n        <mat-icon>{{ isRecording ? 'mic' : isProcessing ? 'hourglass_empty' : isAguardandoResposta ? 'hearing' : 'mic_off' }}</mat-icon>\r\n        <div class=\"status-ripple\" *ngIf=\"isRecording\"></div>\r\n      </div>\r\n      <span class=\"status-text\">\r\n        {{ isRecording ? 'Ouvindo...' :\r\n        isProcessing ? 'Processando...' :\r\n        isAguardandoResposta ? 'Carregando microfone...' :\r\n        isSpeaking ? 'Falando...' :\r\n        'Aguardando...' }}\r\n      </span>\r\n    </div>\r\n  </div>\r\n\r\n</div>"], "mappings": ";AAAA,SAAuCA,iBAAiB,QAAQ,eAAe;AAC/E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,SAASC,kCAAkC,QAAQ,oFAAoF;AAEvI,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;IChBvDC,EALR,CAAAC,cAAA,aAAwC,aACZ,aAE0B,aACxB,aACE;IAEtBD,EADA,CAAAE,SAAA,aAAgC,aACC;IACnCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAI,MAAA,mFAA4D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGjEH,EADF,CAAAC,cAAA,eAA4B,kBAC+B;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,yEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACtDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAE,SAAA,eAA4B;IAMpCF,EALM,CAAAG,YAAA,EAAS,EAGL,EACF,EACF;;;;;IA+BEH,EAAA,CAAAE,SAAA,cAAwF;;;;;IASpFF,EAFJ,CAAAC,cAAA,cAAwD,cACR,QACzC;IAAAD,EAAA,CAAAI,MAAA,GAAiC;IAExCJ,EAFwC,CAAAG,YAAA,EAAI,EACpC,EACF;;;;IAJqCH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAEhDd,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAAQ,aAAA,IAAAR,MAAA,CAAAS,UAAA,CAAiC;;;;;IAMtClB,EADF,CAAAC,cAAA,cAAoE,cACzC;IAGrBD,EAFA,CAAAE,SAAA,WAAa,WACA,WACA;IACfF,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAI,MAAA,kCAA2B;IAC3DJ,EAD2D,CAAAG,YAAA,EAAO,EAC5D;;;IAPiDH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;;;;;;IAa7Dd,EAHN,CAAAC,cAAA,cAA4F,cACxD,cACA,mBACI;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACzDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,gDAA0B;IAClCJ,EADkC,CAAAG,YAAA,EAAO,EACnC;IAEJH,EADF,CAAAC,cAAA,cAA0B,iBAMI;IAD1BD,EAAA,CAAAK,UAAA,mBAAAc,+EAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,mBAAA,EAAqB;IAAA,EAAC;IAE/BrB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAK4B;IAD1BD,EAAA,CAAAK,UAAA,mBAAAiB,gFAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAE3BvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAI,MAAA,0BACF;IAGNJ,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IA3ByEH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAYnFd,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAAe,YAAA,CAAyB;IASzBxB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAAe,YAAA,CAAyB;;;;;IAuB7BxB,EADF,CAAAC,cAAA,cAAuG,eACrE;IAAAD,EAAA,CAAAI,MAAA,GAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAoB;IAClDJ,EADkD,CAAAG,YAAA,EAAO,EACnD;;;;IAHmEH,EAAA,CAAAa,UAAA,eAAAY,WAAA,CAAAC,KAAA,CAA6B;IACpE1B,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAgB,iBAAA,CAAAS,WAAA,CAAAE,KAAA,CAAoB;IACxB3B,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAgB,iBAAA,CAAAS,WAAA,CAAAC,KAAA,CAAoB;;;;;IAKhD1B,EADF,CAAAC,cAAA,cAAoE,cACxC;IACxBD,EAAA,CAAAE,SAAA,cAA2E;IAC7EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAA4E;IAC1GJ,EAD0G,CAAAG,YAAA,EAAO,EAC3G;;;;IAHyBH,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAA4B,WAAA,UAAAnB,MAAA,CAAAoB,qBAAA,QAAyC;IAE1C7B,EAAA,CAAAe,SAAA,GAA4E;IAA5Ef,EAAA,CAAA8B,kBAAA,KAAArB,MAAA,CAAAsB,mBAAA,GAAAC,MAAA,OAAAvB,MAAA,CAAAwB,cAAA,0BAA4E;;;;;;IAnBxGjC,EAHN,CAAAC,cAAA,cAAiF,cACvD,cACG,SACnB;IAAAD,EAAA,CAAAI,MAAA,gDAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,iBAIsB;IAFpBD,EAAA,CAAAK,UAAA,mBAAA6B,+EAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,gBAAA,EAAkB;IAAA,EAAC;IAG5BpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAErBJ,EAFqB,CAAAG,YAAA,EAAW,EACrB,EACL;IAONH,EANA,CAAAqC,UAAA,IAAAC,4DAAA,kBAAuG,IAAAC,4DAAA,kBAMnC;IAOxEvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzB6DH,EAAA,CAAAa,UAAA,gBAAAC,SAAA,CAAa;IAYpDd,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA+B,2BAAA,GAAoC;IAMhCxC,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAsB,mBAAA,GAAAC,MAAA,KAAsC;;;;;;IAmBpEhC,EAAA,CAAAC,cAAA,iBAA4G;IAApDD,EAAA,CAAAK,UAAA,mBAAAoC,wFAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkC,WAAA,EAAa;IAAA,EAAC;IAC7E3C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAChBJ,EADgB,CAAAG,YAAA,EAAW,EAClB;;;;IAFuEH,EAAA,CAAAa,UAAA,cAAAJ,MAAA,CAAAmC,WAAA,GAA2B;;;;;IAKzG5C,EADF,CAAAC,cAAA,mBAAmD,mBACrB;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAI,MAAA,0CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAW;;;;;;IAVXH,EAJN,CAAAC,cAAA,cAA8C,cACf,yBAE6B,gBAC3C;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,gBACiC;IADjBD,EAAA,CAAA6C,gBAAA,2BAAAC,sFAAAC,MAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAiD,kBAAA,CAAAxC,MAAA,CAAAyC,SAAA,EAAAH,MAAA,MAAAtC,MAAA,CAAAyC,SAAA,GAAAH,MAAA;MAAA,OAAA/C,EAAA,CAAAW,WAAA,CAAAoC,MAAA;IAAA,EAAuB;IAA8B/C,EAAA,CAAAK,UAAA,yBAAA8C,oFAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAeF,MAAA,CAAAkC,WAAA,EAAa;IAAA,EAAC;IAAlG3C,EAAA,CAAAG,YAAA,EACiC;IAKjCH,EAJA,CAAAqC,UAAA,IAAAe,+DAAA,qBAA4G,IAAAC,iEAAA,uBAIzD;IAMzDrD,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;;;;IAZgBH,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAsD,gBAAA,YAAA7C,MAAA,CAAAyC,SAAA,CAAuB;IACrClD,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAA8C,eAAA,GAA8B;IACGvD,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAmC,WAAA,GAAmB;IAI3C5C,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA+C,UAAA,CAAgB;;;;;IAW7BxD,EAAA,CAAAE,SAAA,cAAkE;;;;;IADpEF,EADF,CAAAC,cAAA,cAAiF,cACvD;IACtBD,EAAA,CAAAqC,UAAA,IAAAoB,4DAAA,kBAA4D;IAC9DzD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA6B,mBACM;IAAAD,EAAA,CAAAI,MAAA,UAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAI,MAAA,oBACF;IACFJ,EADE,CAAAG,YAAA,EAAO,EACH;;;IAR8DH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAEtCd,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA0D,eAAA,IAAAC,GAAA,EAAoB;;;;;IAe1D3D,EAAA,CAAAE,SAAA,cAAqD;;;;;IADrDF,EALJ,CAAAC,cAAA,cAAqE,cAIU,eACjE;IAAAD,EAAA,CAAAI,MAAA,GAA2G;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAChIH,EAAA,CAAAqC,UAAA,IAAAuB,4DAAA,kBAA+C;IACjD5D,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAI,MAAA,GAKF;IACFJ,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAfkDH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAE7Dd,EAAA,CAAAe,SAAA,EAA+B;IAE/Bf,EAFA,CAAA6D,WAAA,cAAApD,MAAA,CAAAqD,WAAA,CAA+B,eAAArD,MAAA,CAAAe,YAAA,CACE,YAAAf,MAAA,CAAAsD,oBAAA,KAAAtD,MAAA,CAAAqD,WAAA,KAAArD,MAAA,CAAAe,YAAA,CACsC;IAChExB,EAAA,CAAAe,SAAA,GAA2G;IAA3Gf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAAqD,WAAA,WAAArD,MAAA,CAAAe,YAAA,uBAAAf,MAAA,CAAAsD,oBAAA,yBAA2G;IACzF/D,EAAA,CAAAe,SAAA,EAAiB;IAAjBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAqD,WAAA,CAAiB;IAG7C9D,EAAA,CAAAe,SAAA,GAKF;IALEf,EAAA,CAAAgE,kBAAA,MAAAvD,MAAA,CAAAqD,WAAA,kBAAArD,MAAA,CAAAe,YAAA,sBAAAf,MAAA,CAAAsD,oBAAA,+BAAAtD,MAAA,CAAA+C,UAAA,uCAKF;;;;;;IA1JExD,EAJN,CAAAC,cAAA,cAA4C,cAEb,cACC,mBACE;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IACnEJ,EADmE,CAAAG,YAAA,EAAW,EACxE;IACNH,EAAA,CAAAC,cAAA,2BAI0C;IAHxCD,EAAA,CAAA6C,gBAAA,2BAAAoB,0FAAAlB,MAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAiD,kBAAA,CAAAxC,MAAA,CAAA0D,UAAA,EAAApB,MAAA,MAAAtC,MAAA,CAAA0D,UAAA,GAAApB,MAAA;MAAA,OAAA/C,EAAA,CAAAW,WAAA,CAAAoC,MAAA;IAAA,EAAwB;IACxB/C,EAAA,CAAAK,UAAA,oBAAA+D,mFAAA;MAAApE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA4D,cAAA,EAAgB;IAAA,EAAC;IAG3BrE,EAAA,CAAAI,MAAA,mBACF;IACFJ,EADE,CAAAG,YAAA,EAAmB,EACf;IASEH,EANR,CAAAC,cAAA,cAA4B,cAEF,cAEmC,eAClC,eACE;IAEnBD,EADA,CAAAE,SAAA,eAAuB,eACA;IACzBF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,eAA2D;IAC7DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqC,UAAA,KAAAiC,sDAAA,kBAAkF;IAEtFtE,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACH;IAkB5BD,EAjBA,CAAAqC,UAAA,KAAAkC,sDAAA,kBAAwD,KAAAC,sDAAA,kBAOY,KAAAC,sDAAA,mBAUwB;IA4B9FzE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAqC,UAAA,KAAAqC,sDAAA,mBAAiF;IA2BrF1E,EADE,CAAAG,YAAA,EAAM,EACF;IAkCNH,EA/BA,CAAAqC,UAAA,KAAAsC,sDAAA,kBAA8C,KAAAC,sDAAA,kBAoBmC,KAAAC,sDAAA,mBAWZ;IAgBvE7E,EAAA,CAAAG,YAAA,EAAM;;;;IA5J4BH,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAA0D,UAAA,sBAAqC;IAGjEnE,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAAsD,gBAAA,YAAA7C,MAAA,CAAA0D,UAAA,CAAwB;IAGxBnE,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAAe,YAAA,IAAAf,MAAA,CAAA+C,UAAA,CAAuC;IAShBxD,EAAA,CAAAe,SAAA,GAAiC;IACtDf,EADqB,CAAA6D,WAAA,eAAApD,MAAA,CAAAe,YAAA,CAAiC,cAAAf,MAAA,CAAAqD,WAAA,CAAgC,YAAArD,MAAA,CAAAsD,oBAAA,KAAAtD,MAAA,CAAAqD,WAAA,CAChC;IAM9B9D,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAA6D,WAAA,YAAApD,MAAA,CAAAe,YAAA,CAA8B;IAE/BxB,EAAA,CAAAe,SAAA,EAAyD;IAAzDf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAe,YAAA,IAAAf,MAAA,CAAAqD,WAAA,IAAArD,MAAA,CAAAsD,oBAAA,CAAyD;IAOvD/D,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAS,UAAA,CAAgB;IAONlB,EAAA,CAAAe,SAAA,EAAkB;IAAlBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAe,YAAA,CAAkB;IAUpBxB,EAAA,CAAAe,SAAA,EAA4C;IAA5Cf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAqE,qBAAA,KAAArE,MAAA,CAAAe,YAAA,CAA4C;IA+BpDxB,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAsB,mBAAA,GAAAC,MAAA,KAAsC;IA8BzChC,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA0D,UAAA,CAAgB;IAoBVnE,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAqD,WAAA,KAAArD,MAAA,CAAA0D,UAAA,CAAgC;IAW7BnE,EAAA,CAAAe,SAAA,EAAiB;IAAjBf,EAAA,CAAAa,UAAA,UAAAJ,MAAA,CAAA0D,UAAA,CAAiB;;;ADnF1D,OAAM,MAAOY,gCAAgC;EA+CjCC,MAAA;EACAC,MAAA;EACAC,aAAA;EACAC,OAAA;EACAC,SAAA;EACAC,QAAA;EACAC,GAAA;EAnDVC,MAAM,GAAG,IAAI;EACb/D,YAAY,GAAG,KAAK;EACpB2C,UAAU,GAAG,KAAK;EAClBL,WAAW,GAAG,KAAK;EACnBC,oBAAoB,GAAG,KAAK;EAC5ByB,UAAU,GAAG,EAAE;EACfC,kBAAkB,GAAG,KAAK;EAE1BX,qBAAqB,GAAG,KAAK;EAE7BtB,UAAU,GAAG,KAAK;EAClBvC,aAAa,GAAG,EAAE;EAClByE,gBAAgB,GAAG,EAAE;EACrBC,mBAAmB;EAEnBC,UAAU,GAAG,EAAE;EACfC,gBAAgB,GAAmB,CACjC;IAAEC,IAAI,EAAE,UAAU;IAAEnE,KAAK,EAAE,gBAAgB;IAAEoE,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAI,CAAE,EAC3E;IAAEF,IAAI,EAAE,SAAS;IAAEnE,KAAK,EAAE,qBAAqB;IAAEoE,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE;EAAI,CAAE,EACzF;IAAEF,IAAI,EAAE,SAAS;IAAEnE,KAAK,EAAE,SAAS;IAAEoE,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE;EAAI,CAAE,EAC1E;IAAEF,IAAI,EAAE,UAAU;IAAEnE,KAAK,EAAE,WAAW;IAAEoE,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAE,CACrE;EAEDC,mBAAmB,GAAa,EAAE;EAClCC,YAAY,GAAG,EAAE;EACjBhF,UAAU,GAAG,EAAE;EACfgC,SAAS,GAAG,EAAE;EAEdiD,cAAc,GAAiC;IAC7CC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;GACd;EAEOC,QAAQ,GAAG,IAAItH,OAAO,EAAQ;EAEtCuH,YACUhC,MAAc,EACdC,MAAiB,EACjBC,aAAmC,EACnCC,OAAuB,EACvBC,SAAmC,EACnCC,QAAwB,EACxBC,GAAsB;IANtB,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,GAAG,GAAHA,GAAG;EACT;EAEJ2B,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,CAACjC,aAAa,CAACkC,UAAU,CAC1BC,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACxD,WAAW,IAAG;MACvB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACwB,GAAG,CAACiC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACrC,aAAa,CAACsC,OAAO,CACvBH,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACG,MAAM,IAAG;MAClB,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,EAAE;QACjC,IAAI,CAACC,wBAAwB,CAACH,MAAM,CAACE,IAAI,CAAC;MAC5C;MACA,IAAI,CAACrC,GAAG,CAACiC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACrC,aAAa,CAAC2C,MAAM,CACtBR,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACQ,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAChE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACwB,GAAG,CAACiC,aAAa,EAAE;MAExB,IAAI,CAACO,KAAK,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC9B,IAAI,CAAC3C,QAAQ,CAAC4C,aAAa,CAACH,KAAK,CAAC;QAElC,IAAI,CAACA,KAAK,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC7D,UAAU,IAAI,CAAC,IAAI,CAAC3C,YAAY,EAAE;UAC5E,IAAI,CAAC0G,yBAAyB,EAAE;QAClC;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAAChD,aAAa,CAACiD,eAAe,CAC/Bd,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACc,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACtC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC/B,oBAAoB,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE;QACzF,IAAI,IAAI,CAACgB,OAAO,CAAC3B,UAAU,EAAE,EAAE;UAC7B,IAAI,CAAC2B,OAAO,CAACkD,iBAAiB,EAAE,CAACC,IAAI,CAAC,MAAK;YACzC,IAAI,IAAI,CAACvE,oBAAoB,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;cAClD,IAAI,CAACoE,yBAAyB,EAAE;YAClC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLK,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAACxE,oBAAoB,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;cAClD,IAAI,CAACoE,yBAAyB,EAAE;YAClC;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,CAAC;EACN;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,EAAE;IACpB,IAAI,CAAC1B,QAAQ,CAAC2B,QAAQ,EAAE;IACxB,IAAI,CAACvD,OAAO,CAACwD,MAAM,EAAE;IACrB,IAAI,CAACzD,aAAa,CAAC0D,aAAa,EAAE;IAClC,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAjI,kBAAkBA,CAAA;IAChB,IAAI,CAAC2E,MAAM,GAAG,KAAK;IACnB,IAAI,CAACD,GAAG,CAACiC,aAAa,EAAE;IACxB,IAAI,CAACrB,YAAY,GAAG,IAAI,CAAC4C,aAAa,EAAE;IACxC,IAAI,CAAC7C,mBAAmB,GAAG,CAAC,qBAAqB,CAAC;IAClD,IAAI,CAACT,UAAU,GAAG,QAAQ;IAE1B,IAAI,CAAC,IAAI,CAACrB,UAAU,EAAE;MACpB,IAAI,CAAC4E,2BAA2B,EAAE;IACpC;IAEA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA3E,cAAcA,CAAA;IACZ,MAAM4E,YAAY,GAAG,IAAI,CAAC9E,UAAU;IACpC,IAAI,CAACmB,GAAG,CAACiC,aAAa,EAAE;IACxB,IAAI,CAACrE,SAAS,GAAG,EAAE;IAEnB;IACA,IAAI,CAACgG,+BAA+B,CAACD,YAAY,EAAE,IAAI,CAAC9E,UAAU,CAAC;EACrE;EAEA;;;;;EAKQ+E,+BAA+BA,CAACC,gBAAyB,EAAEC,eAAwB;IACzF;IACA,IAAI,CAACD,gBAAgB,IAAIC,eAAe,EAAE;MACxC,IAAI,CAACC,+BAA+B,EAAE;IACxC;IACA;IAAA,KACK,IAAIF,gBAAgB,IAAI,CAACC,eAAe,EAAE;MAC7C,IAAI,CAACE,8BAA8B,EAAE;IACvC;EACF;EAEA;;;;;;EAMQD,+BAA+BA,CAAA;IACrCtB,OAAO,CAACwB,GAAG,CAAC,oDAAoD,CAAC;IAEjE,IAAI;MACF;MACA,IAAI,IAAI,CAACrE,aAAa,CAACsE,oBAAoB,EAAE,EAAE;QAC7CzB,OAAO,CAACwB,GAAG,CAAC,2BAA2B,CAAC;QACxC,IAAI,CAACrE,aAAa,CAAC0D,aAAa,EAAE;MACpC;MAEA;MACA,IAAI,CAAC9E,WAAW,GAAG,KAAK;MAExB;MACA,IAAI,CAACC,oBAAoB,GAAG,KAAK;MAEjCgE,OAAO,CAACwB,GAAG,CAAC,oDAAoD,CAAC;IAEnE,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;IAEA,IAAI,CAACxC,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEA;;;;;;EAMQ+B,8BAA8BA,CAAA;IACpCvB,OAAO,CAACwB,GAAG,CAAC,+CAA+C,CAAC;IAE5D,IAAI;MACF;MACA,IAAI,IAAI,CAACpE,OAAO,CAAC3B,UAAU,EAAE,EAAE;QAC7BuE,OAAO,CAACwB,GAAG,CAAC,uDAAuD,CAAC;QACpE,IAAI,CAACpE,OAAO,CAACkD,iBAAiB,EAAE,CAACC,IAAI,CAAC,MAAK;UACzC,IAAI,CAACmB,gCAAgC,EAAE;QACzC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACA,gCAAgC,EAAE;MACzC;IAEF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAACzC,QAAQ,CAAC4C,aAAa,CAAC,8CAA8C,CAAC;IAC7E;EACF;EAEA;;;EAGcwB,gCAAgCA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC5C,IAAI;QACF;QACA,MAAMD,KAAI,CAACX,2BAA2B,EAAE;QAExC;QACA,IAAIW,KAAI,CAAC3F,oBAAoB,IAAI,CAAC2F,KAAI,CAAClI,YAAY,EAAE;UACnDuG,OAAO,CAACwB,GAAG,CAAC,6DAA6D,CAAC;UAC1EhB,UAAU,CAAC,MAAK;YACd,IAAI,CAACmB,KAAI,CAACvF,UAAU,IAAIuF,KAAI,CAAC3F,oBAAoB,EAAE;cACjD2F,KAAI,CAACxB,yBAAyB,EAAE;YAClC;UACF,CAAC,EAAE,GAAG,CAAC;QACT;QAEAH,OAAO,CAACwB,GAAG,CAAC,+CAA+C,CAAC;MAE9D,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE4B,KAAI,CAACrE,QAAQ,CAAC4C,aAAa,CAAC,2DAA2D,CAAC;MAC1F;MAEAyB,KAAI,CAACpE,GAAG,CAACiC,aAAa,EAAE;IAAC;EAC3B;EAEQyB,oBAAoBA,CAAA;IAC1B,IAAI,CAACxH,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC8D,GAAG,CAACiC,aAAa,EAAE;IAExB,MAAMqC,cAAc,GAAG,IAAI,CAAC3D,mBAAmB,CAACjE,MAAM,GAAG,CAAC,GACtD,IAAI,CAACiE,mBAAmB,CAAC,IAAI,CAACA,mBAAmB,CAACjE,MAAM,GAAG,CAAC,CAAC,GAC7D,EAAE;IAEN,MAAM6H,OAAO,GAAiC;MAC5CC,eAAe,EAAE,IAAI,CAAC3F,UAAU,GAAG,gBAAgB,GAAG,6BAA6B;MACnF4F,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAAC9D,mBAAmB,CAAC;MAChD2D,cAAc,EAAEA,cAAc;MAC9BpE,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BwE,KAAK,EAAE,IAAI,CAAC9D;KACb;IAED,IAAI,IAAI,CAACD,mBAAmB,CAACjE,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACmC,UAAU,EAAE;MAC3D,IAAI,IAAI,CAACe,aAAa,CAACsE,oBAAoB,EAAE,EAAE;QAC7C,IAAI,CAACtE,aAAa,CAAC0D,aAAa,EAAE;MACpC;IACF;IAEA,IAAI,CAACxD,SAAS,CAAC6E,eAAe,CAACJ,OAAO,CAAC,CACpCxC,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAAC;MACTmB,IAAI,EAAGyB,QAAuC,IAAI;QAChD,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAAC;MACpC,CAAC;MACDpC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtG,YAAY,GAAG,KAAK;QACzB,IAAI,CAAC8D,GAAG,CAACiC,aAAa,EAAE;QACxB,IAAI,CAAClC,QAAQ,CAAC4C,aAAa,CAAC,8CAA8C,CAAC;QAC3EF,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACN;EAEQqC,mBAAmBA,CAACD,QAAuC;IACjE,IAAI,CAAC1I,YAAY,GAAG,KAAK;IACzB,IAAI,CAACN,UAAU,GAAGgJ,QAAQ,CAACE,aAAa;IACxC,IAAI,CAAC1E,gBAAgB,GAAGwE,QAAQ,CAACE,aAAa;IAC9C,IAAI,CAACnJ,aAAa,GAAG,EAAE;IAEvB,IAAI,CAACoJ,mBAAmB,CAACH,QAAQ,CAACE,aAAa,CAAC;IAEhD,IAAIF,QAAQ,CAAC1E,UAAU,EAAE;MACvB,IAAI,CAACA,UAAU,GAAG0E,QAAQ,CAAC1E,UAAU;IACvC;IAEA;IACA,IAAI,CAACV,qBAAqB,GAAGoF,QAAQ,CAACI,sBAAsB,IAAI,KAAK;IAErE,IAAI,CAAChF,GAAG,CAACiC,aAAa,EAAE;IAExB,IAAI,CAACgD,uBAAuB,CAACL,QAAQ,CAACM,KAAK,CAAC;IAC5C,IAAI,CAAClF,GAAG,CAACiC,aAAa,EAAE;IAExB,IAAI2C,QAAQ,CAACO,YAAY,IAAI,IAAI,CAACC,wBAAwB,EAAE,EAAE;MAC5D,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,MAAM;MACL;MACA,IAAI,CAAC1J,aAAa,GAAG,EAAE;MACvB,IAAI,CAACqE,GAAG,CAACiC,aAAa,EAAE;MAExB;MACA,IAAI,CAACpC,OAAO,CAACyF,KAAK,CAACV,QAAQ,CAACE,aAAa,EAAE,MAAK;QAC9C;QACArC,OAAO,CAACwB,GAAG,CAAC,+CAA+C,CAAC;QAC5D,IAAI,CAACsB,4BAA4B,EAAE;MACrC,CAAC,CAAC,CAACvC,IAAI,CAAC,MAAK;QACX;QACA,IAAI,CAAC,IAAI,CAACxD,qBAAqB,EAAE;UAC/B,IAAI,CAACgG,iCAAiC,EAAE;QAC1C;MACF,CAAC,CAAC,CAACC,KAAK,CAACjD,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC7G,aAAa,GAAG,IAAI,CAACyE,gBAAgB,CAAC,CAAC;QAC5C,IAAI,CAACJ,GAAG,CAACiC,aAAa,EAAE;QACxB;QACA,IAAI,CAAC,IAAI,CAACzC,qBAAqB,EAAE;UAC/B,IAAI,CAACgG,iCAAiC,EAAE;QAC1C;MACF,CAAC,CAAC;IACJ;EACF;EAEQT,mBAAmBA,CAACW,QAAgB;IAC1C,IAAI,CAAC/E,mBAAmB,CAACgF,IAAI,CAAC,oBAAoBD,QAAQ,EAAE,CAAC;IAC7D,IAAI,CAAC1F,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEQgD,uBAAuBA,CAACW,UAAwC;IACtEC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;MACpC,IAAIJ,UAAU,CAACI,GAAyC,CAAC,IACvDJ,UAAU,CAACI,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACrE,IAAI,CAACpF,cAAc,CAACmF,GAAyC,CAAC,GAC5DJ,UAAU,CAACI,GAAyC,CAAC;MACzD;IACF,CAAC,CAAC;IACF,IAAI,CAAChG,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEQmD,wBAAwBA,CAAA;IAC9B,MAAMc,kBAAkB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EAAE,eAAe,CAAC;IAChI,OAAOA,kBAAkB,CAACC,KAAK,CAACC,KAAK,IACnC,IAAI,CAACvF,cAAc,CAACuF,KAA2C,CAAC,IAChE,IAAI,CAACvF,cAAc,CAACuF,KAA2C,CAAC,CAACH,IAAI,EAAE,KAAK,EAAE,CAC/E;EACH;EAEQT,iCAAiCA,CAAA;IACvC,IAAI,CAAC/G,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACuB,GAAG,CAACiC,aAAa,EAAE;IAExB,IAAI,CAAC,IAAI,CAACpD,UAAU,EAClBoE,UAAU,CAAC,MAAK;MACd,IAAI,CAACoD,uBAAuB,EAAE;IAChC,CAAC,EAAE,GAAG,CAAC;EACX;EAEcA,uBAAuBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MACnC,IAAI,CAACiC,MAAI,CAAC1G,aAAa,CAAC2G,WAAW,EAAE,EAAE;QACrC9D,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;QACpD8D,MAAI,CAACvG,QAAQ,CAAC4C,aAAa,CAAC,qDAAqD,CAAC;QAClF;MACF;MAEA;MACA,MAAM2D,MAAI,CAACE,kCAAkC,EAAE;MAC/CF,MAAI,CAAC1D,yBAAyB,EAAE;MAChC0D,MAAI,CAACG,uBAAuB,EAAE;IAAC;EACjC;EAEQA,uBAAuBA,CAAA;IAC7B,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAAC,IAAI,CAAClI,oBAAoB,EAAE;QAC9BmI,aAAa,CAACF,UAAU,CAAC;QACzB;MACF;MAEA,IAAI,IAAI,CAAC7H,UAAU,EAAE;QACnB;MACF;MAEA,MAAMgI,gBAAgB,GAAG,IAAI,CAACjH,aAAa,CAACsE,oBAAoB,EAAE;MAClE,MAAM4C,kBAAkB,GAAG,IAAI,CAACtI,WAAW;MAE3C,IAAIqI,gBAAgB,KAAKC,kBAAkB,EAAE;QAC3C,IAAI,CAACtI,WAAW,GAAGqI,gBAAgB;QACnC,IAAI,CAAC7G,GAAG,CAACiC,aAAa,EAAE;MAC1B;MAEA,IAAI,IAAI,CAACxD,oBAAoB,IAAI,CAACoI,gBAAgB,IAAI,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAACjH,OAAO,CAAC3B,UAAU,EAAE,EAAE;QACvG,IAAI,CAAC0E,yBAAyB,EAAE;MAClC;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEQmE,mCAAmCA,CAAA;IACzC,IAAI,CAACtI,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACuB,GAAG,CAACiC,aAAa,EAAE;IAExB,IAAI,IAAI,CAACzD,WAAW,EAAE;MACpB,IAAI,CAACoB,aAAa,CAAC0D,aAAa,EAAE;IACpC;EACF;EAEQV,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAAChD,aAAa,CAAC2G,WAAW,EAAE,EAAE;MACrC9D,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;MACpD,IAAI,CAACzC,QAAQ,CAAC4C,aAAa,CAAC,qDAAqD,CAAC;MAClF;IACF;IAEA;IACA,IAAI,IAAI,CAAC9D,UAAU,EAAE;MACnB4D,OAAO,CAACwB,GAAG,CAAC,yDAAyD,CAAC;MACtE;IACF;IAEA;IACA,IAAI,IAAI,CAACpE,OAAO,CAAC3B,UAAU,EAAE,EAAE;MAC7BuE,OAAO,CAACwB,GAAG,CAAC,qDAAqD,CAAC;MAClE;IACF;IAEA,MAAM4C,gBAAgB,GAAG,IAAI,CAACjH,aAAa,CAACsE,oBAAoB,EAAE;IAClE,MAAM4C,kBAAkB,GAAG,IAAI,CAACtI,WAAW;IAE3C,IAAIqI,gBAAgB,IAAIC,kBAAkB,EAAE;MAC1C;IACF;IAEA,MAAM1E,OAAO,GAAG,IAAI,CAACxC,aAAa,CAACoH,cAAc,EAAE;IAEnD,IAAI,CAAC5E,OAAO,EACVK,OAAO,CAACD,KAAK,CAAC,wCAAwC,CAAC;IAEzD,IAAI,CAACxC,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEAgF,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACrH,aAAa,CAAC2G,WAAW,EAAE,EAAE;MACrC,IAAI,CAACxG,QAAQ,CAAC4C,aAAa,CAAC,qDAAqD,CAAC;MAClF;IACF;IAEA;IACA,IAAI,IAAI,CAAC9C,OAAO,CAAC3B,UAAU,EAAE,EAAE;MAC7B,IAAI,CAAC6B,QAAQ,CAAC4C,aAAa,CAAC,uCAAuC,CAAC;MACpE;IACF;IAEA,MAAMP,OAAO,GAAG,IAAI,CAACxC,aAAa,CAACoH,cAAc,EAAE;IACnD,IAAI,CAAC5E,OAAO,EAAE;MACZ,IAAI,CAACrC,QAAQ,CAAC4C,aAAa,CAAC,0BAA0B,CAAC;IACzD;IACA,IAAI,CAAC3C,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEAiF,aAAaA,CAAA;IACX,IAAI,CAACtH,aAAa,CAAC0D,aAAa,EAAE;IAClC,IAAI,CAACtD,GAAG,CAACiC,aAAa,EAAE;EAC1B;EAEAkF,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3I,WAAW,EAAE;MACpB,IAAI,CAAC0I,aAAa,EAAE;IACtB,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,EAAE;IACxB;EACF;EAEQrF,aAAaA,CAAA;IACnB,MAAMwF,SAAS,GAAGC,MAAM,CAACC,eAAe;IACxC,MAAMC,MAAM,GAAGH,SAAS,CAACI,SAAS,EAAE;IAEpC,IAAID,MAAM,CAAC7K,MAAM,KAAK,CAAC,EAAE;MACvB0K,SAAS,CAACK,eAAe,GAAG,MAAK;QAC/B,MAAMC,SAAS,GAAGN,SAAS,CAACI,SAAS,EAAE;QACvCE,SAAS;MACX,CAAC;MAED,MAAMC,eAAe,GAAG,IAAIC,wBAAwB,CAAC,EAAE,CAAC;MACxDD,eAAe,CAACE,MAAM,GAAG,CAAC;MAC1BT,SAAS,CAAC9B,KAAK,CAACqC,eAAe,CAAC;MAChCP,SAAS,CAAC/D,MAAM,EAAE;IACpB;EACF;EAEAyE,mBAAmBA,CAAA;IACjB,IAAI,CAACjI,OAAO,CAACyF,KAAK,CAAC,kGAAkG,EAAE,MAAK;MAC1H7C,OAAO,CAACwB,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC,CAACjB,IAAI,CAAC,MAAK;MACX,IAAI,CAACwC,iCAAiC,EAAE;IAC1C,CAAC,CAAC;EACJ;EAEAnI,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACwC,OAAO,CAAC3B,UAAU,EAAE,EAAE;MAC7B,IAAI,CAAC6B,QAAQ,CAAC4C,aAAa,CAAC,uCAAuC,CAAC;MACpE;IACF;IAEA,IAAI,IAAI,CAAC/E,SAAS,CAACqI,IAAI,EAAE,EAAE;MACzB,IAAI,CAAC3D,wBAAwB,CAAC,IAAI,CAAC1E,SAAS,CAAC;MAC7C,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACoC,GAAG,CAACiC,aAAa,EAAE;IAC1B;EACF;EAEQK,wBAAwBA,CAACoD,QAAgB;IAC/C,IAAI,IAAI,CAACjH,oBAAoB,EAAE;MAC7B,IAAI,CAACsI,mCAAmC,EAAE;IAC5C;IAEA,IAAI,CAACpG,mBAAmB,CAACgF,IAAI,CAAC,yBAAyBD,QAAQ,EAAE,CAAC;IAClE,IAAI,CAAC1F,GAAG,CAACiC,aAAa,EAAE;IACxB,IAAI,CAACyB,oBAAoB,EAAE;EAC7B;EAEcD,2BAA2BA,CAAA;IAAA,IAAAsE,MAAA;IAAA,OAAA1D,iBAAA;MACvC,IAAI;QACF,MAAM2D,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;QACzEJ,MAAM,CAACK,SAAS,EAAE,CAACtC,OAAO,CAACuC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MACnD,CAAC,CAAC,OAAO/F,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjEuF,MAAI,CAAChI,QAAQ,CAAC4C,aAAa,CAAC,oFAAoF,CAAC;MACnH;IAAC;EACH;EAEQa,aAAaA,CAAA;IACnB,MAAMgF,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAE;IAC/B,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,EAAE,GAAG,KAAK,CAAC;IAChD,OAAO,GAAGF,SAAS,IAAIE,MAAM,EAAE;EACjC;EAEA;EACQ/G,oBAAoBA,CAAA;IAC1B8E,WAAW,CAAC,MAAK;MACf,MAAMoC,iBAAiB,GAAG,IAAI,CAAClJ,OAAO,CAAC3B,UAAU,EAAE;MACnD,IAAI,IAAI,CAACA,UAAU,KAAK6K,iBAAiB,EAAE;QACzC,IAAI,CAAC7K,UAAU,GAAG6K,iBAAiB;QACnC,IAAI,CAAC/I,GAAG,CAACiC,aAAa,EAAE;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEQsD,4BAA4BA,CAAA;IAClC,IAAI,CAAChC,wBAAwB,EAAE;IAC/B,IAAI,CAAC5H,aAAa,GAAG,EAAE;IAEvB,MAAMqN,KAAK,GAAG,IAAI,CAAC5I,gBAAgB,CAAC6I,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMC,aAAa,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC/I,gBAAgB,CAAC;IACxE,MAAMgJ,YAAY,GAAGF,aAAa,GAAGF,KAAK,CAACtM,MAAM;IAEjD,IAAI2M,gBAAgB,GAAG,CAAC;IAExB,IAAI,CAAChJ,mBAAmB,GAAGsG,WAAW,CAAC,MAAK;MAC1C,IAAI0C,gBAAgB,GAAGL,KAAK,CAACtM,MAAM,EAAE;QACnC,IAAI2M,gBAAgB,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAC1N,aAAa,GAAGqN,KAAK,CAACK,gBAAgB,CAAC;QAC9C,CAAC,MAAM;UACL,IAAI,CAAC1N,aAAa,IAAI,GAAG,GAAGqN,KAAK,CAACK,gBAAgB,CAAC;QACrD;QACAA,gBAAgB,EAAE;QAClB,IAAI,CAACrJ,GAAG,CAACiC,aAAa,EAAE;MAC1B,CAAC,MAAM;QACL,IAAI,CAACsB,wBAAwB,EAAE;MACjC;IACF,CAAC,EAAE6F,YAAY,CAAC;EAClB;EAEQD,sBAAsBA,CAAC9G,IAAY;IACzC,MAAM2G,KAAK,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,GAAG,CAAC,CAACvM,MAAM;IACpC,MAAM4M,cAAc,GAAG,GAAG;IAC1B,MAAMC,iBAAiB,GAAGP,KAAK,GAAGM,cAAc;IAChD,OAAQC,iBAAiB,GAAG,EAAE,GAAG,IAAI;EACvC;EAEQhG,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAClD,mBAAmB,EAAE;MAC5BuG,aAAa,CAAC,IAAI,CAACvG,mBAAmB,CAAC;MACvC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACjC;EACF;EAEQgF,iBAAiBA,CAAA;IACvB,IAAI,CAACrF,GAAG,CAACiC,aAAa,EAAE;IACxB,IAAI,CAACuH,uBAAuB,EAAE;IAC9B,IAAI,CAACC,+BAA+B,EAAE;EACxC;EAEQD,uBAAuBA,CAAA;IAC7B,IAAI;MACF,MAAME,eAAe,GAAG;QACtB5I,IAAI,EAAE,IAAI,CAACD,cAAc,CAACC,IAAI;QAC9B6I,KAAK,EAAE,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC/I,cAAc,CAACK,cAAc,CAAC;QAC7DH,GAAG,EAAE,IAAI,CAACF,cAAc,CAACE,GAAG;QAC5BC,KAAK,EAAE,IAAI,CAACH,cAAc,CAACG,KAAK;QAChCC,QAAQ,EAAE,IAAI,CAACJ,cAAc,CAACI,QAAQ;QACtCC,cAAc,EAAE,IAAI,CAACL,cAAc,CAACK,cAAc;QAClDE,QAAQ,EAAE,IAAI,CAACP,cAAc,CAACO,QAAQ,GAAG,IAAI,CAACP,cAAc,CAACO,QAAQ,CAAC6H,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7D,IAAI,EAAE,CAAC,GAAG,EAAE;QACxG8D,cAAc,EAAE,IAAI,CAAClJ,cAAc,CAACW,WAAW,IAAI,EAAE;QACrDH,cAAc,EAAE,IAAI,CAAC2I,wBAAwB,CAAC,IAAI,CAACnJ,cAAc,CAACQ,cAAc,CAAC;QACjFC,aAAa,EAAE,IAAI,CAACT,cAAc,CAACS,aAAa;QAChDH,QAAQ,EAAE,IAAI,CAACN,cAAc,CAACM,QAAQ,IAAI,EAAE;QAC5CI,cAAc,EAAE,IAAI,CAACV,cAAc,CAACU,cAAc,IAAI,EAAE;QACxDC,WAAW,EAAE,IAAI,CAACX,cAAc,CAACW,WAAW,IAAI,EAAE;QAClDyI,iBAAiB,EAAE,IAAIxB,IAAI,EAAE,CAACyB,WAAW;OAC1C;MAEDzP,gBAAgB,CAAC0P,yBAAyB,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACX,eAAe,CAAC,CAAC;IAC1G,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACd,IAAI,CAACzC,QAAQ,CAAC4C,aAAa,CAAC,sCAAsC,CAAC;IACrE;EACF;EAEQiH,aAAaA,CAAC1I,cAAsB;IAC1C,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAE7B,IAAI;MACF,MAAMoJ,UAAU,GAAG,IAAI7B,IAAI,CAACvH,cAAc,CAAC;MAC3C,MAAMqJ,IAAI,GAAG,IAAI9B,IAAI,EAAE;MACvB,IAAIkB,KAAK,GAAGY,IAAI,CAACC,WAAW,EAAE,GAAGF,UAAU,CAACE,WAAW,EAAE;MACzD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAQ,EAAE;MAChC,MAAMC,aAAa,GAAGL,UAAU,CAACI,QAAQ,EAAE;MAE3C,IAAID,QAAQ,GAAGE,aAAa,IAAKF,QAAQ,KAAKE,aAAa,IAAIJ,IAAI,CAACK,OAAO,EAAE,GAAGN,UAAU,CAACM,OAAO,EAAG,EAAE;QACrGjB,KAAK,EAAE;MACT;MAEA,OAAOA,KAAK;IACd,CAAC,CAAC,MAAM;MACN,OAAO,CAAC;IACV;EACF;EAEQK,wBAAwBA,CAACa,WAAmB;IAClD,IAAI,CAACA,WAAW,EAAE,OAAO,CAAC;IAE1B,MAAMC,KAAK,GAAGD,WAAW,CAACC,KAAK,CAAC,KAAK,CAAC;IACtC,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EAC3C;EAEQE,0BAA0BA,CAAA;IAChC,IAAI;MACF,IAAI,CAACtL,MAAM,CAACuL,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOzI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAACzC,QAAQ,CAAC4C,aAAa,CAAC,uCAAuC,CAAC;MACpE,IAAI,CAACjD,MAAM,CAACuL,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC;EACF;EAEQxB,+BAA+BA,CAAA;IACrC,MAAMyB,SAAS,GAAG,IAAI,CAACvL,MAAM,CAACwL,IAAI,CAACjR,kCAAkC,EAAE;MACrEkR,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE;KACb,CAAC;IAEFP,SAAS,CAACQ,WAAW,EAAE,CAAC1J,SAAS,CAACG,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEwJ,MAAM,KAAK,WAAW,EAAE;QAClC,IAAI,CAAC5L,QAAQ,CAAC6L,eAAe,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAACZ,0BAA0B,EAAE;MACnC,CAAC,MAAM,IAAI7I,MAAM,EAAEwJ,MAAM,KAAK,UAAU,EAAE;QACxC,IAAI,CAAC5L,QAAQ,CAAC4C,aAAa,CAAC,wCAAwC,CAAC;QACrElI,gBAAgB,CAACoR,gCAAgC,CAAC,2BAA2B,CAAC;MAChF;MAEA,IAAI,CAAC7L,GAAG,CAACiC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAxF,mBAAmBA,CAAA;IACjB,MAAMqP,MAAM,GAA8B;MACxChL,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KACd;IAED,OAAOqE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjF,cAAc,CAAC,CACpCkL,MAAM,CAAC/F,GAAG,IAAI,IAAI,CAACnF,cAAc,CAACmF,GAAyC,CAAC,IAC3E,IAAI,CAACnF,cAAc,CAACmF,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAC9E4D,GAAG,CAAC7D,GAAG,KAAK;MACX3J,KAAK,EAAEyP,MAAM,CAAC9F,GAAG,CAAC;MAClB5J,KAAK,EAAE,IAAI,CAACyE,cAAc,CAACmF,GAAyC;KACrE,CAAC,CAAC;EACP;EAEA9I,2BAA2BA,CAAA;IACzB,MAAMgI,KAAK,GAAG,IAAI,CAACzI,mBAAmB,EAAE;IACxC,OAAOyI,KAAK,CAACxI,MAAM,GAAG,CAAC,GAAGwI,KAAK,CAACA,KAAK,CAACxI,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC1D;EAEAI,gBAAgBA,CAAA;IACd,MAAM,CAAC,6CAA6C,CAAC,CAACkG,IAAI,CAAC,CAAC;MAAEgJ;IAA2B,CAAE,KAAI;MAC7F,MAAMC,UAAU,GAAG7B,IAAI,CAAC8B,KAAK,CAAC9B,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxJ,cAAc,CAAC,CAAC;MAClE,IAAI,CAAClB,MAAM,CAACwL,IAAI,CAACa,2BAA2B,EAAE;QAC5CX,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,MAAM;QAChBa,IAAI,EAAE;UACJtL,cAAc,EAAEoL,UAAU;UAC1BlM,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,GAAG,EAAE,IAAI,CAACA;SACX;QACDyL,UAAU,EAAE,sBAAsB;QAClCW,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAzP,cAAcA,CAAA;IACZ,OAAO,EAAE;EACX;EAEAJ,qBAAqBA,CAAA;IACnB,MAAM8P,KAAK,GAAG,IAAI,CAAC1P,cAAc,EAAE;IACnC,MAAM2P,MAAM,GAAG,IAAI,CAAC7P,mBAAmB,EAAE,CAACC,MAAM;IAChD,OAAOmM,IAAI,CAAC0D,KAAK,CAAED,MAAM,GAAGD,KAAK,GAAI,GAAG,CAAC;EAC3C;EAEAG,cAAcA,CAAC1J,KAAU;IACvB,IAAI,CAACxC,UAAU,GAAGwC,KAAK,CAAC2J,MAAM,CAACrQ,KAAK;EACtC;EAEAsQ,WAAWA,CAAA;IACT,IAAI,CAACpM,UAAU,GAAG,EAAE;EACtB;EAEAqM,YAAYA,CAACZ,MAAoB;IAC/BA,MAAM,CAACrL,MAAM,GAAG,CAACqL,MAAM,CAACrL,MAAM;EAChC;EAEAkM,eAAeA,CAAA;IACb,IAAIT,IAAI,GAAG,IAAI,CAACU,2BAA2B,EAAE;IAE7C,IAAI,IAAI,CAACvM,UAAU,EAAE;MACnB,MAAMwM,WAAW,GAAG,IAAI,CAACxM,UAAU,CAACyM,WAAW,EAAE;MACjDZ,IAAI,GAAGA,IAAI,CAACJ,MAAM,CAACiB,IAAI,IACrBA,IAAI,CAAC3Q,KAAK,CAAC0Q,WAAW,EAAE,CAACrK,QAAQ,CAACoK,WAAW,CAAC,IAC9CE,IAAI,CAAC5Q,KAAK,CAAC2Q,WAAW,EAAE,CAACrK,QAAQ,CAACoK,WAAW,CAAC,CAC/C;IACH;IAEA,MAAMG,gBAAgB,GAAG,IAAI,CAAC1M,gBAAgB,CAC3CwL,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAACxM,MAAM,CAAC,CACrBmJ,GAAG,CAACqD,CAAC,IAAIA,CAAC,CAAC1M,IAAI,CAAC;IAEnB2L,IAAI,GAAGA,IAAI,CAACJ,MAAM,CAACiB,IAAI,IAAIC,gBAAgB,CAACvK,QAAQ,CAACsK,IAAI,CAACG,QAAQ,CAAC,CAAC;IAEpE,OAAOhB,IAAI;EACb;EAEAU,2BAA2BA,CAAA;IACzB,MAAMO,WAAW,GAAiF;MAChGtM,IAAI,EAAE;QAAEqM,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAE5M,IAAI,EAAE;MAAQ,CAAE;MAC/EM,GAAG,EAAE;QAAEoM,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAE5M,IAAI,EAAE;MAAO,CAAE;MAC7ES,cAAc,EAAE;QAAEiM,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAE5M,IAAI,EAAE;MAAM,CAAE;MACvFO,KAAK,EAAE;QAAEmM,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAE5M,IAAI,EAAE;MAAO,CAAE;MACvEQ,QAAQ,EAAE;QAAEkM,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAE5M,IAAI,EAAE;MAAO,CAAE;MAC1EW,QAAQ,EAAE;QAAE+L,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAE5M,IAAI,EAAE;MAAkB,CAAE;MACjGY,cAAc,EAAE;QAAE8L,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAE5M,IAAI,EAAE;MAAiB,CAAE;MACtGa,aAAa,EAAE;QAAE6L,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAE5M,IAAI,EAAE;MAAU,CAAE;MAC9FU,QAAQ,EAAE;QAAEgM,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAE5M,IAAI,EAAE;MAAqB,CAAE;MAC3Fe,WAAW,EAAE;QAAE2L,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAE5M,IAAI,EAAE;MAAa;KACrF;IAED,MAAMqL,MAAM,GAA8B;MACxChL,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KACd;IAED,OAAOqE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjF,cAAc,CAAC,CACpCkL,MAAM,CAAC/F,GAAG,IAAI,IAAI,CAACnF,cAAc,CAACmF,GAAyC,CAAC,IAC3E,IAAI,CAACnF,cAAc,CAACmF,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAC9E4D,GAAG,CAAC7D,GAAG,IAAG;MACT,MAAMsH,YAAY,GAAGF,WAAW,CAACpH,GAAG,CAAC,IAAI;QAAEmH,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,QAAQ;QAAE5M,IAAI,EAAE;MAAM,CAAE;MACxG,OAAO;QACLpE,KAAK,EAAEyP,MAAM,CAAC9F,GAAG,CAAC,IAAIA,GAAG;QACzB5J,KAAK,EAAE,IAAI,CAACyE,cAAc,CAACmF,GAAyC,CAAC;QACrEmH,QAAQ,EAAEG,YAAY,CAACH,QAAQ;QAC/BE,aAAa,EAAEC,YAAY,CAACD,aAAa;QACzC5M,IAAI,EAAE6M,YAAY,CAAC7M,IAAI;QACvBiI,SAAS,EAAE,IAAID,IAAI,EAAE;QACrB8E,gBAAgB,EAAE,IAAI,CAACC,2BAA2B,CAACxH,GAAG;OACvD;IACH,CAAC,CAAC;EACN;EAEAwH,2BAA2BA,CAACC,KAAa;IACvC,MAAMrR,KAAK,GAAG,IAAI,CAACyE,cAAc,CAAC4M,KAA2C,CAAC;IAC9E,IAAI,CAACrR,KAAK,IAAIA,KAAK,CAAC6J,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,OAAO;IAEjD,IAAIwH,KAAK,KAAK,OAAO,IAAI,CAACrR,KAAK,CAACsG,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,SAAS;IAC/D,IAAI+K,KAAK,KAAK,KAAK,IAAIrR,KAAK,CAACM,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS;IAE1D,OAAO,OAAO;EAChB;EAEAgR,SAASA,CAACC,KAAa,EAAEX,IAAsB;IAC7CW,KAAK;IACL,OAAOX,IAAI,CAAC3Q,KAAK,GAAG2Q,IAAI,CAAC5Q,KAAK;EAChC;EAEAwR,mBAAmBA,CAACvL,IAAY;IAC9B,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAAE,OAAO+B,IAAI;IAEjC,MAAMwL,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAI,IAAI,CAACxN,UAAU,GAAG,EAAE,IAAI,CAAC;IACtD,OAAO+B,IAAI,CAAC0L,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EAC/C;EAEAG,eAAeA,CAACtF,SAAe;IAC7B,OAAOA,SAAS,CAACuF,kBAAkB,CAAC,OAAO,EAAE;MAC3CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,iBAAiBA,CAACC,MAAc;IAC9B,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,MAAM;IACxB;EACF;EAEAC,kBAAkBA,CAACD,MAAc;IAC/B,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B;QAAS,OAAO,MAAM;IACxB;EACF;EAEAE,eAAeA,CAAClM,IAAY;IAC1B4F,SAAS,CAACuG,SAAS,CAACC,SAAS,CAACpM,IAAI,CAAC,CAACW,IAAI,CAAC,MAAK;MAC5C,IAAI,CAACjD,QAAQ,CAAC6L,eAAe,CAAC,4CAA4C,CAAC;IAC7E,CAAC,CAAC,CAACnG,KAAK,CAAC,MAAK;MACZ,IAAI,CAAC1F,QAAQ,CAAC4C,aAAa,CAAC,sBAAsB,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA+L,SAASA,CAAC1B,IAAsB;IAC9B,MAAM2B,QAAQ,GAAGC,MAAM,CAAC,UAAU5B,IAAI,CAAC3Q,KAAK,GAAG,EAAE2Q,IAAI,CAAC5Q,KAAK,CAAC;IAC5D,IAAIuS,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK3B,IAAI,CAAC5Q,KAAK,EAAE;MAChD,MAAMqR,KAAK,GAAG5H,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjF,cAAc,CAAC,CAACgO,IAAI,CAAC7I,GAAG,IAAG;QACxD,MAAM8F,MAAM,GAA8B;UACxChL,IAAI,EAAE,MAAM;UACZC,GAAG,EAAE,KAAK;UACVC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,aAAa,EAAE,oBAAoB;UACnCC,cAAc,EAAE,iBAAiB;UACjCC,WAAW,EAAE;SACd;QACD,OAAOsK,MAAM,CAAC9F,GAAG,CAAC,KAAKgH,IAAI,CAAC3Q,KAAK;MACnC,CAAC,CAAC;MAEF,IAAIoR,KAAK,EAAE;QACT,IAAI,CAAC5M,cAAc,CAAC4M,KAA2C,CAAC,GAAGkB,QAAQ;QAC3E,IAAI,CAAC5O,QAAQ,CAAC6L,eAAe,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAAC5L,GAAG,CAACiC,aAAa,EAAE;MAC1B;IACF;EACF;EAEA6M,YAAYA,CAAA;IACV,IAAIC,OAAO,CAAC,yDAAyD,CAAC,EAAE;MACtE,IAAI,CAAClO,cAAc,GAAG;QACpBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,EAAE;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;OACd;MACD,IAAI,CAACzB,QAAQ,CAAC6L,eAAe,CAAC,6BAA6B,CAAC;MAC5D,IAAI,CAAC5L,GAAG,CAACiC,aAAa,EAAE;IAC1B;EACF;EAEAhE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC4B,OAAO,CAAC3B,UAAU,EAAE,IAAI,IAAI,CAAChC,YAAY;EACvD;EAEA8S,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACnP,OAAO,CAAC3B,UAAU,EAAE,IAAI,IAAI,CAAChC,YAAY;EACvD;EAEAoB,WAAWA,CAAA;IACT,OAAO,CAAC,IAAI,CAACuC,OAAO,CAAC3B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAChC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC0B,SAAS,EAAEqI,IAAI,EAAE;EACrF;EAEcO,kCAAkCA,CAAA;IAAA,IAAAyI,MAAA;IAAA,OAAA5K,iBAAA;MAC9C,OAAO,IAAI6K,OAAO,CAAQC,OAAO,IAAI;QACnC,MAAMC,aAAa,GAAG,EAAE;QACxB,MAAMC,WAAW,GAAG,KAAK;QACzB,IAAIC,WAAW,GAAG,CAAC;QAEnB,MAAM5I,UAAU,GAAGC,WAAW,CAAC,MAAK;UAClC2I,WAAW,IAAIF,aAAa;UAE5B,IAAI,CAACH,MAAI,CAACpP,OAAO,CAAC3B,UAAU,EAAE,EAAE;YAC9B0I,aAAa,CAACF,UAAU,CAAC;YACzBzD,UAAU,CAAC,MAAK;cACdkM,OAAO,EAAE;YACX,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM,IAAIG,WAAW,IAAID,WAAW,EAAE;YACrCzI,aAAa,CAACF,UAAU,CAAC;YACzBjE,OAAO,CAAC8M,IAAI,CAAC,gCAAgC,CAAC;YAC9CJ,OAAO,EAAE;UACX;QACF,CAAC,EAAEC,aAAa,CAAC;MACnB,CAAC,CAAC;IAAC;EACL;EAEArT,mBAAmBA,CAAA;IACjB,IAAI,CAACyD,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACgQ,qBAAqB,CAAC,2FAA2F,CAAC;EACzH;EAEAvT,eAAeA,CAAA;IACb,IAAI,CAACuD,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACgQ,qBAAqB,CAAC,yGAAyG,CAAC;EACvI;EAEQA,qBAAqBA,CAACC,QAAgB;IAE5C,IAAI,CAAC9O,mBAAmB,CAACgF,IAAI,CAAC8J,QAAQ,CAAC;IAEvC,MAAMlL,OAAO,GAAiC;MAC5CC,eAAe,EAAE,SAAS;MAC1BC,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAAC9D,mBAAmB,CAAC;MAChD2D,cAAc,EAAEmL,QAAQ;MACxBvP,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BwE,KAAK,EAAE,IAAI,CAAC9D;KACb;IAED,IAAI,CAAC1E,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC8D,GAAG,CAACiC,aAAa,EAAE;IAExB,IAAI,CAACnC,SAAS,CAAC6E,eAAe,CAACJ,OAAO,CAAC,CACpCxC,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAAC;MACTmB,IAAI,EAAGyB,QAAuC,IAAI;QAChD,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAAC;MACpC,CAAC;MACDpC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtG,YAAY,GAAG,KAAK;QACzB,IAAI,CAAC8D,GAAG,CAACiC,aAAa,EAAE;QACxB,IAAI,CAAClC,QAAQ,CAAC4C,aAAa,CAAC,8CAA8C,CAAC;QAC3EF,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACN;;qBAl+BW/C,gCAAgC,EAAA/E,EAAA,CAAAgV,iBAAA,CAAAC,EAAA,CAAA7W,MAAA,GAAA4B,EAAA,CAAAgV,iBAAA,CAAAE,EAAA,CAAArW,SAAA,GAAAmB,EAAA,CAAAgV,iBAAA,CAAAG,EAAA,CAAAxV,oBAAA,GAAAK,EAAA,CAAAgV,iBAAA,CAAAI,EAAA,CAAAxV,cAAA,GAAAI,EAAA,CAAAgV,iBAAA,CAAAK,EAAA,CAAAxV,wBAAA,GAAAG,EAAA,CAAAgV,iBAAA,CAAAM,EAAA,CAAAxV,cAAA,GAAAE,EAAA,CAAAgV,iBAAA,CAAAhV,EAAA,CAAA7B,iBAAA;EAAA;;UAAhC4G,gCAAgC;IAAAwQ,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAzV,EAAA,CAAA0V,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC7F7ChW,EAAA,CAAAC,cAAA,aAAwC;QAgCtCD,EA9BA,CAAAqC,UAAA,IAAA6T,+CAAA,kBAAwC,IAAAC,+CAAA,mBA8BI;QAkK9CnW,EAAA,CAAAG,YAAA,EAAM;;;QAhMsBH,EAAA,CAAAe,SAAA,EAAY;QAAZf,EAAA,CAAAa,UAAA,SAAAoV,GAAA,CAAA1Q,MAAA,CAAY;QA8BTvF,EAAA,CAAAe,SAAA,EAAa;QAAbf,EAAA,CAAAa,UAAA,UAAAoV,GAAA,CAAA1Q,MAAA,CAAa;;;mBDgBxClH,YAAY,EAAA+X,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpX,mBAAmB,EAAAqX,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EACnBtX,WAAW,EAAAoX,EAAA,CAAAG,OAAA,EACX9X,kBAAkB,EAAA+X,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBvY,cAAc,EAAAwY,GAAA,CAAAC,QAAA,EACd3Y,eAAe,EAAA4Y,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfzY,aAAa,EACbF,iBAAiB,EACjBC,oBAAoB,EAAA2Y,GAAA,CAAAC,cAAA,EACpB/Y,aAAa,EAAAgZ,GAAA,CAAAC,OAAA,EACb1Y,eAAe,EACfC,gBAAgB,EAAA0Y,GAAA,CAAAC,UAAA,EAChB1Y,cAAc,EACdC,iBAAiB;IAAA0Y,MAAA;IAAAlG,IAAA;MAAAmG,SAAA,EAEP,CACVxY,OAAO,CAAC,WAAW,EAAE,CACnBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEwY,OAAO,EAAE;MAAC,CAAE,CAAC,EACrBtY,OAAO,CAAC,eAAe,EAAEF,KAAK,CAAC;QAAEwY,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAChD,CAAC,EACFvY,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;QAAEwY,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACjD,CAAC,CACH,CAAC,EACFzY,OAAO,CAAC,YAAY,EAAE,CACpBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEyY,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EACrDtY,OAAO,CAAC,wCAAwC,EAC9CF,KAAK,CAAC;QAAEyY,SAAS,EAAE,eAAe;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACrD,CAAC,EACFvY,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,eAAe,EACrBF,KAAK,CAAC;QAAEyY,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,EACFzY,OAAO,CAAC,eAAe,EAAE,CACvBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEyY,SAAS,EAAE,YAAY;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EAC9CtY,OAAO,CAAC,wCAAwC,EAC9CF,KAAK,CAAC;QAAEyY,SAAS,EAAE,UAAU;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAChD,CAAC,CACH,CAAC;IACH;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
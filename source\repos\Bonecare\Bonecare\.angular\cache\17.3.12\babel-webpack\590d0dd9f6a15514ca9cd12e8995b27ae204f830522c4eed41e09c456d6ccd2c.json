{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport { VoiceRecorderService } from './Service/voice-recorder.service';\nimport { SpeakerService } from './Service/speaker.service';\nimport { AiQuestionarioApiService } from './Service/ai-questionario-api.service';\nimport { AlertComponent } from 'src/app/alert/alert.component';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"./Service/voice-recorder.service\";\nimport * as i4 from \"./Service/speaker.service\";\nimport * as i5 from \"./Service/ai-questionario-api.service\";\nimport * as i6 from \"src/app/alert/alert.component\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/tooltip\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nfunction PreConsultaQuestionarioComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n    i0.ɵɵelement(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵelement(9, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Vou coletar suas informa\\u00E7\\u00F5es atrav\\u00E9s de uma conversa natural\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.iniciarAtendimento());\n    });\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Iniciar Atendimento\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 15);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 38);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.displayedText || ctx_r1.aiResponse);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6, \"Processando sua resposta...\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46)(3, \"mat-icon\", 47);\n    i0.ɵɵtext(4, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"A informa\\u00E7\\u00E3o est\\u00E1 correta?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.confirmarInformacao());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Confirmar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.tentarNovamente());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Tentar Novamente \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lastItem_r6 = ctx.ngIf;\n    i0.ɵɵproperty(\"matTooltip\", lastItem_r6.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r6.value);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61);\n    i0.ɵɵelement(2, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getDadosPreenchidos().length, \"/\", ctx_r1.getTotalCampos(), \" campos preenchidos\");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u00DAltima Informa\\u00E7\\u00E3o Coletada\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openHistoryModal());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"history\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, PreConsultaQuestionarioComponent_div_2_div_21_div_8_Template, 5, 3, \"div\", 55)(9, PreConsultaQuestionarioComponent_div_2_div_21_div_9_Template, 5, 4, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getUltimaVariavelPreenchida());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"send\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSendText());\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_mat_hint_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 71)(1, \"mat-icon\", 72);\n    i0.ɵɵtext(2, \"volume_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Aguarde o t\\u00E9rmino da fala... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"mat-form-field\", 66)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Sua resposta\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_div_22_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.userInput, $event) || (ctx_r1.userInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function PreConsultaQuestionarioComponent_div_2_div_22_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, PreConsultaQuestionarioComponent_div_2_div_22_button_6_Template, 3, 1, \"button\", 68)(7, PreConsultaQuestionarioComponent_div_2_div_22_mat_hint_7_Template, 4, 0, \"mat-hint\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.userInput);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isInputDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canSendText());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSpeaking);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 78);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵtemplate(2, PreConsultaQuestionarioComponent_div_2_div_23_div_2_Template, 1, 0, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 76)(4, \"mat-icon\", 77);\n    i0.ɵɵtext(5, \"mic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Gravando... \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_24_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 83);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 80)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PreConsultaQuestionarioComponent_div_2_div_24_div_4_Template, 1, 0, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"recording\", ctx_r1.isRecording)(\"processing\", ctx_r1.isProcessing)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording && !ctx_r1.isProcessing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isRecording ? \"mic\" : ctx_r1.isProcessing ? \"hourglass_empty\" : ctx_r1.isAguardandoResposta ? \"hearing\" : \"mic_off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isRecording ? \"Ouvindo...\" : ctx_r1.isProcessing ? \"Processando...\" : ctx_r1.isAguardandoResposta ? \"Carregando microfone...\" : ctx_r1.isSpeaking ? \"Falando...\" : \"Aguardando...\", \" \");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"mat-icon\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-slide-toggle\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.isTextMode, $event) || (ctx_r1.isTextMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_change_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTextMode());\n    });\n    i0.ɵɵtext(6, \" Modo Texto \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"div\", 22)(9, \"div\", 23)(10, \"div\", 24)(11, \"div\", 25);\n    i0.ɵɵelement(12, \"div\", 26)(13, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, PreConsultaQuestionarioComponent_div_2_div_15_Template, 1, 0, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵtemplate(18, PreConsultaQuestionarioComponent_div_2_div_18_Template, 4, 2, \"div\", 31)(19, PreConsultaQuestionarioComponent_div_2_div_19_Template, 7, 1, \"div\", 32)(20, PreConsultaQuestionarioComponent_div_2_div_20_Template, 16, 3, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, PreConsultaQuestionarioComponent_div_2_div_21_Template, 10, 3, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PreConsultaQuestionarioComponent_div_2_div_22_Template, 8, 4, \"div\", 35)(23, PreConsultaQuestionarioComponent_div_2_div_23_Template, 7, 3, \"div\", 36)(24, PreConsultaQuestionarioComponent_div_2_div_24_Template, 7, 10, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.isTextMode ? \"keyboard\" : \"mic\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.isTextMode);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing || ctx_r1.isSpeaking);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"processing\", ctx_r1.isProcessing)(\"listening\", ctx_r1.isRecording)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"talking\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing || ctx_r1.isRecording || ctx_r1.isAguardandoResposta);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.aiResponse);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showValidationButtons && !ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording && !ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTextMode);\n  }\n}\nexport let PreConsultaQuestionarioComponent = /*#__PURE__*/(() => {\n  class PreConsultaQuestionarioComponent {\n    router;\n    dialog;\n    voiceRecorder;\n    speaker;\n    aiService;\n    snackBar;\n    cdr;\n    isIdle = true;\n    isProcessing = false;\n    isTextMode = false;\n    isRecording = false;\n    isAguardandoResposta = false;\n    campoAtual = '';\n    isHistoryModalOpen = false;\n    showValidationButtons = false;\n    isSpeaking = false;\n    displayedText = '';\n    fullResponseText = '';\n    textDisplayInterval;\n    searchTerm = '';\n    availableFilters = [{\n      type: 'personal',\n      label: 'Dados Pessoais',\n      icon: 'person',\n      active: true\n    }, {\n      type: 'medical',\n      label: 'Informações Médicas',\n      icon: 'medical_services',\n      active: true\n    }, {\n      type: 'contact',\n      label: 'Contato',\n      icon: 'contact_phone',\n      active: true\n    }, {\n      type: 'optional',\n      label: 'Opcionais',\n      icon: 'info',\n      active: true\n    }];\n    conversationHistory = [];\n    currentToken = '';\n    aiResponse = '';\n    userInput = '';\n    dadosColetados = {\n      nome: '',\n      cpf: '',\n      email: '',\n      telefone: '',\n      dataNascimento: '',\n      alergias: '',\n      sintomas: '',\n      intensidadeDor: '',\n      tempoSintomas: '',\n      doencasPrevias: '',\n      observacoes: ''\n    };\n    destroy$ = new Subject();\n    constructor(router, dialog, voiceRecorder, speaker, aiService, snackBar, cdr) {\n      this.router = router;\n      this.dialog = dialog;\n      this.voiceRecorder = voiceRecorder;\n      this.speaker = speaker;\n      this.aiService = aiService;\n      this.snackBar = snackBar;\n      this.cdr = cdr;\n    }\n    ngOnInit() {\n      this.preloadVoices();\n      this.startSpeakingMonitor();\n      this.voiceRecorder.recording$.pipe(takeUntil(this.destroy$)).subscribe(isRecording => {\n        this.isRecording = isRecording;\n        this.cdr.detectChanges();\n      });\n      this.voiceRecorder.result$.pipe(takeUntil(this.destroy$)).subscribe(result => {\n        if (result.success && result.text) {\n          this.adicionarRespostaUsuario(result.text);\n        }\n        this.cdr.detectChanges();\n      });\n      this.voiceRecorder.error$.pipe(takeUntil(this.destroy$)).subscribe(error => {\n        console.error('Erro de reconhecimento de voz:', error);\n        this.isRecording = false;\n        this.cdr.detectChanges();\n        if (!error.includes('aborted')) {\n          this.snackBar.falhaSnackbar(error);\n          if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {\n            this.iniciarGravacaoAutomatica();\n          }\n        }\n      });\n      this.voiceRecorder.recordingEvent$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {\n          if (this.speaker.isSpeaking()) {\n            this.speaker.waitUntilFinished().then(() => {\n              if (this.isAguardandoResposta && !this.isRecording) {\n                this.iniciarGravacaoAutomatica();\n              }\n            });\n          } else {\n            setTimeout(() => {\n              if (this.isAguardandoResposta && !this.isRecording) {\n                this.iniciarGravacaoAutomatica();\n              }\n            }, 500);\n          }\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.speaker.cancel();\n      this.voiceRecorder.stopRecording();\n      this.clearTextDisplayInterval();\n    }\n    iniciarAtendimento() {\n      this.isIdle = false;\n      this.cdr.detectChanges();\n      this.currentToken = this.generateToken();\n      this.conversationHistory = ['Iniciar atendimento'];\n      this.campoAtual = 'inicio';\n      if (!this.isTextMode) {\n        this.solicitarPermissaoMicrofone();\n      }\n      this.enviarMensagemParaIA();\n    }\n    toggleTextMode() {\n      const previousMode = this.isTextMode;\n      this.cdr.detectChanges();\n      this.userInput = '';\n      // Handle microphone state management based on mode transition\n      this.handleMicrophoneStateTransition(previousMode, this.isTextMode);\n    }\n    /**\n     * Manages microphone state when switching between text and voice modes\n     * @param previousTextMode - Previous state of isTextMode\n     * @param currentTextMode - Current state of isTextMode\n     */\n    handleMicrophoneStateTransition(previousTextMode, currentTextMode) {\n      // Switching FROM voice mode TO text mode\n      if (!previousTextMode && currentTextMode) {\n        this.deactivateMicrophoneForTextMode();\n      }\n      // Switching FROM text mode TO voice mode\n      else if (previousTextMode && !currentTextMode) {\n        this.activateMicrophoneForVoiceMode();\n      }\n    }\n    /**\n     * Deactivates microphone when switching to text mode\n     * - Stops any active recording immediately\n     * - Cancels pending voice input processing\n     * - Ensures complete microphone cleanup\n     */\n    deactivateMicrophoneForTextMode() {\n      console.log('🔇 Desativando microfone - mudança para modo texto');\n      try {\n        // Stop any active recording immediately\n        if (this.voiceRecorder.isCurrentlyRecording()) {\n          console.log('⏹️ Parando gravação ativa');\n          this.voiceRecorder.stopRecording();\n        }\n        // Update recording state\n        this.isRecording = false;\n        // Clear any pending voice input processing\n        this.isAguardandoResposta = false;\n        console.log('✅ Microfone desativado com sucesso para modo texto');\n      } catch (error) {\n        console.error('❌ Erro ao desativar microfone:', error);\n      }\n      this.cdr.detectChanges();\n    }\n    /**\n     * Activates microphone when switching to voice mode\n     * - Initializes microphone if AI response reading is complete\n     * - Requests permissions if needed\n     * - Prepares for immediate voice input\n     */\n    activateMicrophoneForVoiceMode() {\n      console.log('🎤 Ativando microfone - mudança para modo voz');\n      try {\n        // Only activate if AI is not currently speaking\n        if (this.speaker.isSpeaking()) {\n          console.log('🔊 Aguardando fim da fala da IA para ativar microfone');\n          this.speaker.waitUntilFinished().then(() => {\n            this.initializeMicrophoneForVoiceMode();\n          });\n        } else {\n          this.initializeMicrophoneForVoiceMode();\n        }\n      } catch (error) {\n        console.error('❌ Erro ao ativar microfone:', error);\n        this.snackBar.falhaSnackbar('Erro ao ativar o microfone. Tente novamente.');\n      }\n    }\n    /**\n     * Initializes microphone for voice mode operation\n     */\n    initializeMicrophoneForVoiceMode() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Request microphone permissions\n          yield _this.solicitarPermissaoMicrofone();\n          // If we're waiting for user response, start recording automatically\n          if (_this.isAguardandoResposta && !_this.isProcessing) {\n            console.log('🎯 Iniciando gravação automática após ativação do microfone');\n            setTimeout(() => {\n              if (!_this.isTextMode && _this.isAguardandoResposta) {\n                _this.iniciarGravacaoAutomatica();\n              }\n            }, 500);\n          }\n          console.log('✅ Microfone ativado com sucesso para modo voz');\n        } catch (error) {\n          console.error('❌ Erro ao inicializar microfone para modo voz:', error);\n          _this.snackBar.falhaSnackbar('Erro ao inicializar o microfone. Verifique as permissões.');\n        }\n        _this.cdr.detectChanges();\n      })();\n    }\n    enviarMensagemParaIA() {\n      this.isProcessing = true;\n      this.cdr.detectChanges();\n      const ultimaMensagem = this.conversationHistory.length > 0 ? this.conversationHistory[this.conversationHistory.length - 1] : '';\n      const payload = {\n        formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',\n        historicoConversa: [...this.conversationHistory],\n        ultimaMensagem: ultimaMensagem,\n        campoAtual: this.campoAtual,\n        token: this.currentToken\n      };\n      if (this.conversationHistory.length > 1 && !this.isTextMode) {\n        if (this.voiceRecorder.isCurrentlyRecording()) {\n          this.voiceRecorder.stopRecording();\n        }\n      }\n      this.aiService.enviarMensagens(payload).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.processarRespostaIA(response);\n        },\n        error: error => {\n          this.isProcessing = false;\n          this.cdr.detectChanges();\n          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\n          console.error('Erro na comunicação com IA:', error);\n        }\n      });\n    }\n    processarRespostaIA(response) {\n      this.isProcessing = false;\n      this.aiResponse = response.textoResposta;\n      this.fullResponseText = response.textoResposta;\n      this.displayedText = '';\n      this.adicionarRespostaIA(response.textoResposta);\n      if (response.campoAtual) {\n        this.campoAtual = response.campoAtual;\n      }\n      // Handle validation confirmation\n      this.showValidationButtons = response.flgValidacaoCampoAtual || false;\n      this.cdr.detectChanges();\n      this.atualizarDadosColetados(response.dados);\n      this.cdr.detectChanges();\n      if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {\n        this.finalizarProcesso();\n      } else {\n        // Preparar texto para exibição, mas não iniciar ainda\n        this.displayedText = '';\n        this.cdr.detectChanges();\n        // Iniciar áudio com callback para sincronizar texto\n        this.speaker.speak(response.textoResposta, () => {\n          // Este callback é executado quando o áudio realmente começa a tocar\n          console.log('🎯 Iniciando sincronização de texto com áudio');\n          this.startSynchronizedTextDisplay();\n        }).then(() => {\n          // Only start waiting for response if validation buttons are not shown\n          if (!this.showValidationButtons) {\n            this.iniciarProcessoAguardandoResposta();\n          }\n        }).catch(error => {\n          console.error('Erro na reprodução da IA:', error);\n          this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro\n          this.cdr.detectChanges();\n          // Only start waiting for response if validation buttons are not shown\n          if (!this.showValidationButtons) {\n            this.iniciarProcessoAguardandoResposta();\n          }\n        });\n      }\n    }\n    adicionarRespostaIA(resposta) {\n      this.conversationHistory.push(`(resposta da ia) ${resposta}`);\n      this.cdr.detectChanges();\n    }\n    atualizarDadosColetados(novosdados) {\n      Object.keys(novosdados).forEach(key => {\n        if (novosdados[key] && novosdados[key].trim() !== '') {\n          this.dadosColetados[key] = novosdados[key];\n        }\n      });\n      this.cdr.detectChanges();\n    }\n    todosOsCamposPreenchidos() {\n      const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];\n      return camposObrigatorios.every(campo => this.dadosColetados[campo] && this.dadosColetados[campo].trim() !== '');\n    }\n    iniciarProcessoAguardandoResposta() {\n      this.isAguardandoResposta = true;\n      this.cdr.detectChanges();\n      if (!this.isTextMode) setTimeout(() => {\n        this.iniciarGravacaoContinua();\n      }, 500);\n    }\n    iniciarGravacaoContinua() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.voiceRecorder.isSupported()) {\n          console.error('Reconhecimento de voz não suportado');\n          _this2.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n          return;\n        }\n        // Redução de latência: monitorar mais frequentemente o fim da fala\n        yield _this2.waitForSpeechEndWithReducedLatency();\n        _this2.iniciarGravacaoAutomatica();\n        _this2.monitorarEstadoGravacao();\n      })();\n    }\n    monitorarEstadoGravacao() {\n      const intervalId = setInterval(() => {\n        if (!this.isAguardandoResposta) {\n          clearInterval(intervalId);\n          return;\n        }\n        if (this.isTextMode) {\n          return;\n        }\n        const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n        const componentRecording = this.isRecording;\n        if (serviceRecording !== componentRecording) {\n          this.isRecording = serviceRecording;\n          this.cdr.detectChanges();\n        }\n        if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {\n          this.iniciarGravacaoAutomatica();\n        }\n      }, 2000);\n    }\n    finalizarProcessoAguardandoResposta() {\n      this.isAguardandoResposta = false;\n      this.cdr.detectChanges();\n      if (this.isRecording) {\n        this.voiceRecorder.stopRecording();\n      }\n    }\n    iniciarGravacaoAutomatica() {\n      if (!this.voiceRecorder.isSupported()) {\n        console.error('Reconhecimento de voz não suportado');\n        this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n        return;\n      }\n      // Don't start recording if in text mode\n      if (this.isTextMode) {\n        console.log('📝 Modo texto ativo - não iniciando gravação automática');\n        return;\n      }\n      // Não iniciar gravação se o sistema estiver falando\n      if (this.speaker.isSpeaking()) {\n        console.log('🔇 Aguardando término da fala para iniciar gravação');\n        return;\n      }\n      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n      const componentRecording = this.isRecording;\n      if (serviceRecording || componentRecording) {\n        return;\n      }\n      const success = this.voiceRecorder.startRecording();\n      if (!success) console.error('❌ Falha ao iniciar gravação automática');\n      this.cdr.detectChanges();\n    }\n    iniciarGravacao() {\n      if (!this.voiceRecorder.isSupported()) {\n        this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n        return;\n      }\n      // Check if in text mode\n      if (this.isTextMode) {\n        this.snackBar.falhaSnackbar('Mude para o modo de voz para usar a gravação');\n        return;\n      }\n      // Verificar se o sistema está falando\n      if (this.speaker.isSpeaking()) {\n        this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');\n        return;\n      }\n      const success = this.voiceRecorder.startRecording();\n      if (!success) {\n        this.snackBar.falhaSnackbar('Erro ao iniciar gravação');\n      }\n      this.cdr.detectChanges();\n    }\n    pararGravacao() {\n      this.voiceRecorder.stopRecording();\n      this.cdr.detectChanges();\n    }\n    toggleRecording() {\n      if (this.isRecording) {\n        this.pararGravacao();\n      } else {\n        this.iniciarGravacao();\n      }\n    }\n    preloadVoices() {\n      const synthesis = window.speechSynthesis;\n      const voices = synthesis.getVoices();\n      if (voices.length === 0) {\n        synthesis.onvoiceschanged = () => {\n          const newVoices = synthesis.getVoices();\n          newVoices;\n        };\n        const silentUtterance = new SpeechSynthesisUtterance('');\n        silentUtterance.volume = 0;\n        synthesis.speak(silentUtterance);\n        synthesis.cancel();\n      }\n    }\n    testarFluxoCompleto() {\n      this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.', () => {\n        console.log('🎯 Teste: áudio iniciado');\n      }).then(() => {\n        this.iniciarProcessoAguardandoResposta();\n      });\n    }\n    enviarTexto() {\n      // Não permitir envio se o sistema estiver falando\n      if (this.speaker.isSpeaking()) {\n        this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');\n        return;\n      }\n      if (this.userInput.trim()) {\n        this.adicionarRespostaUsuario(this.userInput);\n        this.userInput = '';\n        this.cdr.detectChanges();\n      }\n    }\n    adicionarRespostaUsuario(resposta) {\n      if (this.isAguardandoResposta) {\n        this.finalizarProcessoAguardandoResposta();\n      }\n      this.conversationHistory.push(`(resposta do usuário) ${resposta}`);\n      this.cdr.detectChanges();\n      this.enviarMensagemParaIA();\n    }\n    solicitarPermissaoMicrofone() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const stream = yield navigator.mediaDevices.getUserMedia({\n            audio: true\n          });\n          stream.getTracks().forEach(track => track.stop());\n        } catch (error) {\n          console.error('Erro ao solicitar permissão de microfone:', error);\n          _this3.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');\n        }\n      })();\n    }\n    generateToken() {\n      const now = new Date();\n      const timestamp = now.getTime();\n      const random = Math.floor(Math.random() * 10000);\n      return `${timestamp}_${random}`;\n    }\n    // Métodos para controle de fala e exibição sincronizada\n    startSpeakingMonitor() {\n      setInterval(() => {\n        const currentlySpeaking = this.speaker.isSpeaking();\n        if (this.isSpeaking !== currentlySpeaking) {\n          this.isSpeaking = currentlySpeaking;\n          this.cdr.detectChanges();\n        }\n      }, 100); // Verificar a cada 100ms\n    }\n    startSynchronizedTextDisplay() {\n      this.clearTextDisplayInterval();\n      this.displayedText = '';\n      const words = this.fullResponseText.split(' ');\n      const totalDuration = this.estimateSpeechDuration(this.fullResponseText);\n      const intervalTime = totalDuration / words.length;\n      let currentWordIndex = 0;\n      this.textDisplayInterval = setInterval(() => {\n        if (currentWordIndex < words.length) {\n          if (currentWordIndex === 0) {\n            this.displayedText = words[currentWordIndex];\n          } else {\n            this.displayedText += ' ' + words[currentWordIndex];\n          }\n          currentWordIndex++;\n          this.cdr.detectChanges();\n        } else {\n          this.clearTextDisplayInterval();\n        }\n      }, intervalTime);\n    }\n    estimateSpeechDuration(text) {\n      const words = text.split(' ').length;\n      const wordsPerMinute = 150;\n      const durationInMinutes = words / wordsPerMinute;\n      return durationInMinutes * 60 * 1000;\n    }\n    clearTextDisplayInterval() {\n      if (this.textDisplayInterval) {\n        clearInterval(this.textDisplayInterval);\n        this.textDisplayInterval = null;\n      }\n    }\n    finalizarProcesso() {\n      this.cdr.detectChanges();\n      this.salvarDadosQuestionario();\n      this.abrirDialogColetaDadosVittalTec();\n    }\n    salvarDadosQuestionario() {\n      try {\n        const dadosParaSalvar = {\n          nome: this.dadosColetados.nome,\n          idade: this.calcularIdade(this.dadosColetados.dataNascimento),\n          cpf: this.dadosColetados.cpf,\n          email: this.dadosColetados.email,\n          telefone: this.dadosColetados.telefone,\n          dataNascimento: this.dadosColetados.dataNascimento,\n          sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],\n          sintomasOutros: this.dadosColetados.observacoes || '',\n          intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),\n          tempoSintomas: this.dadosColetados.tempoSintomas,\n          alergias: this.dadosColetados.alergias || '',\n          doencasPrevias: this.dadosColetados.doencasPrevias || '',\n          observacoes: this.dadosColetados.observacoes || '',\n          dataPreenchimento: new Date().toISOString()\n        };\n        CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));\n      } catch (error) {\n        this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');\n      }\n    }\n    calcularIdade(dataNascimento) {\n      if (!dataNascimento) return 0;\n      try {\n        const nascimento = new Date(dataNascimento);\n        const hoje = new Date();\n        let idade = hoje.getFullYear() - nascimento.getFullYear();\n        const mesAtual = hoje.getMonth();\n        const mesNascimento = nascimento.getMonth();\n        if (mesAtual < mesNascimento || mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate()) {\n          idade--;\n        }\n        return idade;\n      } catch {\n        return 0;\n      }\n    }\n    extrairNumeroIntensidade(intensidade) {\n      if (!intensidade) return 0;\n      const match = intensidade.match(/\\d+/);\n      return match ? parseInt(match[0], 10) : 0;\n    }\n    redirecionarParaFilaEspera() {\n      try {\n        this.router.navigate(['/filaespera']);\n      } catch (error) {\n        console.error('Erro ao redirecionar para fila de espera:', error);\n        this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');\n        this.router.navigate(['/filaespera']);\n      }\n    }\n    abrirDialogColetaDadosVittalTec() {\n      const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\n        disableClose: true,\n        width: '500px',\n        maxWidth: '85vw',\n        height: 'auto',\n        maxHeight: '70vh',\n        panelClass: 'vittaltec-modal-panel'\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result?.action === 'continuar') {\n          this.snackBar.sucessoSnackbar(\"Dados coletados com sucesso!\");\n          this.redirecionarParaFilaEspera();\n        } else if (result?.action === 'cancelar') {\n          this.snackBar.falhaSnackbar(\"Processo de coleta de dados cancelado.\");\n          CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');\n        }\n        this.cdr.detectChanges();\n      });\n    }\n    getDadosPreenchidos() {\n      const labels = {\n        nome: 'Nome',\n        cpf: 'CPF',\n        email: 'Email',\n        telefone: 'Telefone',\n        dataNascimento: 'Data de Nascimento',\n        alergias: 'Alergias',\n        sintomas: 'Sintomas',\n        intensidadeDor: 'Intensidade da Dor',\n        tempoSintomas: 'Tempo dos Sintomas',\n        doencasPrevias: 'Doenças Prévias',\n        observacoes: 'Observações'\n      };\n      return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => ({\n        label: labels[key],\n        value: this.dadosColetados[key]\n      }));\n    }\n    getUltimaVariavelPreenchida() {\n      const dados = this.getDadosPreenchidos();\n      return dados.length > 0 ? dados[dados.length - 1] : null;\n    }\n    openHistoryModal() {\n      import('./components/history-modal-dialog.component').then(({\n        HistoryModalDialogComponent\n      }) => {\n        const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));\n        this.dialog.open(HistoryModalDialogComponent, {\n          width: '900px',\n          maxWidth: '98vw',\n          data: {\n            dadosColetados: dadosClone,\n            snackBar: this.snackBar,\n            cdr: this.cdr\n          },\n          panelClass: 'modern-modal-overlay',\n          autoFocus: false\n        });\n      });\n    }\n    getTotalCampos() {\n      return 10;\n    }\n    getProgressPercentage() {\n      const total = this.getTotalCampos();\n      const filled = this.getDadosPreenchidos().length;\n      return Math.round(filled / total * 100);\n    }\n    onSearchChange(event) {\n      this.searchTerm = event.target.value;\n    }\n    clearSearch() {\n      this.searchTerm = '';\n    }\n    toggleFilter(filter) {\n      filter.active = !filter.active;\n    }\n    getFilteredData() {\n      let data = this.getEnhancedDadosPreenchidos();\n      if (this.searchTerm) {\n        const searchLower = this.searchTerm.toLowerCase();\n        data = data.filter(item => item.label.toLowerCase().includes(searchLower) || item.value.toLowerCase().includes(searchLower));\n      }\n      const activeCategories = this.availableFilters.filter(f => f.active).map(f => f.type);\n      data = data.filter(item => activeCategories.includes(item.category));\n      return data;\n    }\n    getEnhancedDadosPreenchidos() {\n      const categoryMap = {\n        nome: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'person'\n        },\n        cpf: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'badge'\n        },\n        dataNascimento: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'cake'\n        },\n        email: {\n          category: 'contact',\n          categoryLabel: 'Contato',\n          icon: 'email'\n        },\n        telefone: {\n          category: 'contact',\n          categoryLabel: 'Contato',\n          icon: 'phone'\n        },\n        sintomas: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'medical_services'\n        },\n        intensidadeDor: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'personal_injury'\n        },\n        tempoSintomas: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'schedule'\n        },\n        alergias: {\n          category: 'optional',\n          categoryLabel: 'Opcionais',\n          icon: 'medical_information'\n        },\n        observacoes: {\n          category: 'optional',\n          categoryLabel: 'Opcionais',\n          icon: 'description'\n        }\n      };\n      const labels = {\n        nome: 'Nome',\n        cpf: 'CPF',\n        email: 'Email',\n        telefone: 'Telefone',\n        dataNascimento: 'Data de Nascimento',\n        alergias: 'Alergias',\n        sintomas: 'Sintomas',\n        intensidadeDor: 'Intensidade da Dor',\n        tempoSintomas: 'Tempo dos Sintomas',\n        doencasPrevias: 'Doenças Prévias',\n        observacoes: 'Observações'\n      };\n      return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => {\n        const categoryInfo = categoryMap[key] || {\n          category: 'optional',\n          categoryLabel: 'Outros',\n          icon: 'info'\n        };\n        return {\n          label: labels[key] || key,\n          value: this.dadosColetados[key],\n          category: categoryInfo.category,\n          categoryLabel: categoryInfo.categoryLabel,\n          icon: categoryInfo.icon,\n          timestamp: new Date(),\n          validationStatus: this.getValidationStatusForField(key)\n        };\n      });\n    }\n    getValidationStatusForField(field) {\n      const value = this.dadosColetados[field];\n      if (!value || value.trim() === '') return 'error';\n      if (field === 'email' && !value.includes('@')) return 'warning';\n      if (field === 'cpf' && value.length < 11) return 'warning';\n      return 'valid';\n    }\n    trackByFn(index, item) {\n      index;\n      return item.label + item.value;\n    }\n    highlightSearchTerm(text) {\n      if (!this.searchTerm) return text;\n      const regex = new RegExp(`(${this.searchTerm})`, 'gi');\n      return text.replace(regex, '<mark>$1</mark>');\n    }\n    formatTimestamp(timestamp) {\n      return timestamp.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getValidationIcon(status) {\n      switch (status) {\n        case 'valid':\n          return 'check_circle';\n        case 'warning':\n          return 'warning';\n        case 'error':\n          return 'error';\n        default:\n          return 'info';\n      }\n    }\n    getValidationLabel(status) {\n      switch (status) {\n        case 'valid':\n          return 'Válido';\n        case 'warning':\n          return 'Atenção';\n        case 'error':\n          return 'Erro';\n        default:\n          return 'Info';\n      }\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\n      }).catch(() => {\n        this.snackBar.falhaSnackbar('Erro ao copiar texto');\n      });\n    }\n    editValue(item) {\n      const newValue = prompt(`Editar ${item.label}:`, item.value);\n      if (newValue !== null && newValue !== item.value) {\n        const field = Object.keys(this.dadosColetados).find(key => {\n          const labels = {\n            nome: 'Nome',\n            cpf: 'CPF',\n            email: 'Email',\n            telefone: 'Telefone',\n            dataNascimento: 'Data de Nascimento',\n            alergias: 'Alergias',\n            sintomas: 'Sintomas',\n            intensidadeDor: 'Intensidade da Dor',\n            tempoSintomas: 'Tempo dos Sintomas',\n            doencasPrevias: 'Doenças Prévias',\n            observacoes: 'Observações'\n          };\n          return labels[key] === item.label;\n        });\n        if (field) {\n          this.dadosColetados[field] = newValue;\n          this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\n          this.cdr.detectChanges();\n        }\n      }\n    }\n    clearAllData() {\n      if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\n        this.dadosColetados = {\n          nome: '',\n          cpf: '',\n          email: '',\n          telefone: '',\n          dataNascimento: '',\n          alergias: '',\n          sintomas: '',\n          intensidadeDor: '',\n          tempoSintomas: '',\n          doencasPrevias: '',\n          observacoes: ''\n        };\n        this.snackBar.sucessoSnackbar('Todos os dados foram limpos');\n        this.cdr.detectChanges();\n      }\n    }\n    isInputDisabled() {\n      return this.speaker.isSpeaking() || this.isProcessing;\n    }\n    isMicrophoneDisabled() {\n      return this.speaker.isSpeaking() || this.isProcessing;\n    }\n    canSendText() {\n      return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();\n    }\n    waitForSpeechEndWithReducedLatency() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        return new Promise(resolve => {\n          const checkInterval = 50;\n          const maxWaitTime = 30000;\n          let elapsedTime = 0;\n          const intervalId = setInterval(() => {\n            elapsedTime += checkInterval;\n            if (!_this4.speaker.isSpeaking()) {\n              clearInterval(intervalId);\n              setTimeout(() => {\n                resolve();\n              }, 100);\n            } else if (elapsedTime >= maxWaitTime) {\n              clearInterval(intervalId);\n              console.warn('Timeout aguardando fim da fala');\n              resolve();\n            }\n          }, checkInterval);\n        });\n      })();\n    }\n    confirmarInformacao() {\n      this.showValidationButtons = false;\n      this.enviarMensagemSistema('(Processamento do sistema) Usuário confirmou a informação, campo preenchido corretamente.');\n    }\n    tentarNovamente() {\n      this.showValidationButtons = false;\n      this.enviarMensagemSistema('(processamento do sistema) Campo preenchido incorretamente, o usuário terá que repreencher o campo do 0');\n    }\n    enviarMensagemSistema(mensagem) {\n      this.conversationHistory.push(mensagem);\n      const payload = {\n        formaDeResposta: 'Sistema',\n        historicoConversa: [...this.conversationHistory],\n        ultimaMensagem: mensagem,\n        campoAtual: this.campoAtual,\n        token: this.currentToken\n      };\n      this.isProcessing = true;\n      this.cdr.detectChanges();\n      this.aiService.enviarMensagens(payload).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.processarRespostaIA(response);\n        },\n        error: error => {\n          this.isProcessing = false;\n          this.cdr.detectChanges();\n          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\n          console.error('Erro na comunicação com IA:', error);\n        }\n      });\n    }\n    static ɵfac = function PreConsultaQuestionarioComponent_Factory(t) {\n      return new (t || PreConsultaQuestionarioComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.VoiceRecorderService), i0.ɵɵdirectiveInject(i4.SpeakerService), i0.ɵɵdirectiveInject(i5.AiQuestionarioApiService), i0.ɵɵdirectiveInject(i6.AlertComponent), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PreConsultaQuestionarioComponent,\n      selectors: [[\"app-pre-consulta-questionario\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"ai-questionnaire-container\"], [\"class\", \"idle-screen\", 4, \"ngIf\"], [\"class\", \"chat-interface\", 4, \"ngIf\"], [1, \"idle-screen\"], [1, \"idle-content\"], [1, \"ai-robot\", 2, \"margin-bottom\", \"9px\"], [1, \"robot-head\"], [1, \"robot-eyes\"], [1, \"eye\", \"left-eye\"], [1, \"eye\", \"right-eye\"], [1, \"robot-mouth\"], [1, \"robot-body\"], [1, \"robot-chest\"], [1, \"action-buttons\"], [1, \"start-btn\", 3, \"click\"], [1, \"btn-glow\"], [1, \"chat-interface\"], [1, \"controls-header\"], [1, \"mode-indicator\"], [1, \"mode-icon\"], [1, \"mode-toggle\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"disabled\"], [1, \"main-chat-area\"], [1, \"ai-section\"], [1, \"ai-avatar\"], [1, \"ai-face\"], [1, \"ai-eyes\"], [1, \"eye\"], [1, \"ai-mouth\"], [\"class\", \"ai-pulse\", 4, \"ngIf\"], [1, \"response-data-section\"], [1, \"response-section\"], [\"class\", \"ai-message\", 4, \"ngIf\"], [\"class\", \"processing-indicator\", 4, \"ngIf\"], [\"class\", \"validation-buttons\", 4, \"ngIf\"], [\"class\", \"data-section\", 4, \"ngIf\"], [\"class\", \"input-section\", 4, \"ngIf\"], [\"class\", \"audio-visualization\", 4, \"ngIf\"], [\"class\", \"voice-status-indicator\", 4, \"ngIf\"], [1, \"ai-pulse\"], [1, \"ai-message\"], [1, \"message-bubble\", \"scrollable-hidden\"], [1, \"processing-indicator\"], [1, \"typing-dots\"], [1, \"processing-text\"], [1, \"validation-buttons\"], [1, \"validation-container\"], [1, \"validation-message\"], [1, \"validation-icon\"], [1, \"button-group\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"confirm-btn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"retry-btn\", 3, \"click\", \"disabled\"], [1, \"data-section\"], [1, \"data-panel\"], [1, \"data-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Ver todas as vari\\u00E1veis\", 1, \"history-btn\", 3, \"click\"], [\"class\", \"data-item\", 3, \"matTooltip\", 4, \"ngIf\"], [\"class\", \"progress-info\", 4, \"ngIf\"], [1, \"data-item\", 3, \"matTooltip\"], [1, \"descInfoCategoria\"], [1, \"descInfovalue\"], [1, \"progress-info\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"input-section\"], [1, \"input-container\"], [\"appearance\", \"outline\", 1, \"user-input\"], [\"matInput\", \"\", \"placeholder\", \"Digite aqui...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"speaking-hint\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"click\", \"disabled\"], [1, \"speaking-hint\"], [1, \"hint-icon\"], [1, \"audio-visualization\"], [1, \"sound-wave\"], [\"class\", \"wave-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"recording-text\"], [1, \"recording-icon\"], [1, \"wave-bar\"], [1, \"voice-status-indicator\"], [1, \"status-icon\"], [\"class\", \"status-ripple\", 4, \"ngIf\"], [1, \"status-text\"], [1, \"status-ripple\"]],\n      template: function PreConsultaQuestionarioComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PreConsultaQuestionarioComponent_div_1_Template, 17, 0, \"div\", 1)(2, PreConsultaQuestionarioComponent_div_2_Template, 25, 19, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isIdle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isIdle);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, ReactiveFormsModule, i8.DefaultValueAccessor, i8.NgControlStatus, FormsModule, i8.NgModel, MatFormFieldModule, i9.MatFormField, i9.MatLabel, i9.MatHint, i9.MatSuffix, MatInputModule, i10.MatInput, MatButtonModule, i11.MatButton, i11.MatIconButton, MatCardModule, MatCheckboxModule, MatSlideToggleModule, i12.MatSlideToggle, MatIconModule, i13.MatIcon, MatDialogModule, MatTooltipModule, i14.MatTooltip, MatChipsModule, MatSnackBarModule],\n      styles: [\"@charset \\\"UTF-8\\\";.ai-questionnaire-container[_ngcontent-%COMP%]{height:100vh;height:100dvh;max-height:100vh;max-height:100dvh;background:linear-gradient(135deg,#667eea,#764ba2);font-family:Inter,-apple-system,BlinkMacSystemFont,sans-serif;font-size:clamp(.75rem,1.8vw,.9rem);position:relative;overflow:hidden;display:flex;flex-direction:column}.idle-screen[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:100%;padding:clamp(.25rem,1.5vw,.5rem);overflow:hidden}.idle-content[_ngcontent-%COMP%]{text-align:center;background:#fffffff2;padding:clamp(1rem,3vw,1.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(.5rem,1.5vw,1rem);box-shadow:0 .25rem .75rem #0000001a;max-width:min(80vw,20rem);width:100%;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);border:1px solid rgba(255,255,255,.2);max-height:70vh;overflow:hidden}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2d3748;font-size:clamp(1rem,2.5vw,1.125rem);font-weight:700;margin:clamp(.25rem,1vw,.5rem) 0 clamp(.25rem,.5vw,.25rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;font-size:clamp(.75rem,2vw,.875rem);margin-bottom:clamp(.5rem,2vw,1rem);line-height:1.3}.action-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.5rem,2vw,1rem);align-items:center;width:100%}.ai-robot[_ngcontent-%COMP%]{display:inline-block;animation:_ngcontent-%COMP%_float 3s ease-in-out infinite;transform:scale(1.1)}@media (max-width: 768px){.ai-robot[_ngcontent-%COMP%]{transform:scale(1)}}@media (max-width: 480px){.ai-robot[_ngcontent-%COMP%]{transform:scale(.9)}}.robot-head[_ngcontent-%COMP%]{width:clamp(3.5rem,8vw,5rem);height:clamp(3.5rem,8vw,5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:clamp(1rem,3vw,1.25rem);position:relative;margin:0 auto clamp(.5rem,2vw,1rem);box-shadow:0 .5rem 1.25rem #667eea4d}.robot-eyes[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:clamp(1rem,4vw,1.25rem) clamp(.5rem,3vw,1rem) 0}.eye[_ngcontent-%COMP%]{width:clamp(.5rem,1.5vw,.75rem);height:clamp(.5rem,1.5vw,.75rem);background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_blink 3s infinite}.eye.left-eye[_ngcontent-%COMP%]{animation-delay:.1s}.eye.right-eye[_ngcontent-%COMP%]{animation-delay:.2s}.robot-mouth[_ngcontent-%COMP%]{width:clamp(1rem,2.5vw,1.25rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:0 0 .625rem .625rem;margin:clamp(.4rem,1vw,.5rem) auto 0}.robot-body[_ngcontent-%COMP%]{width:clamp(2.5rem,6vw,3.75rem);height:clamp(1.5rem,4vw,2.5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:clamp(.75rem,2vw,1rem);margin:0 auto;position:relative}.robot-chest[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.5rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:50%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);animation:_ngcontent-%COMP%_pulse 2s infinite}.start-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#f093fb);border:none;padding:clamp(1rem,3vw,1.25rem) clamp(2rem,5vw,2.8125rem);border-radius:3.125rem;color:#fff;font-size:clamp(1.125rem,3vw,1.5rem);font-weight:700;cursor:pointer;position:relative;overflow:hidden;transition:all .3s ease;box-shadow:0 .5rem 1.5625rem #4facfe4d}.start-btn[_ngcontent-%COMP%]:hover{transform:translateY(-.125rem);box-shadow:0 .75rem 2.1875rem #4facfe66}.start-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;z-index:2}.btn-glow[_ngcontent-%COMP%]{position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);transition:left .5s}.start-btn[_ngcontent-%COMP%]:hover   .btn-glow[_ngcontent-%COMP%]{left:100%}.chat-interface[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;position:relative;overflow:hidden}.controls-header[_ngcontent-%COMP%]{flex-shrink:0;display:flex;align-items:center;justify-content:center;gap:clamp(.25rem,1vw,.5rem);background:#fffffff2;padding:clamp(.25rem,.8vw,.5rem) clamp(.5rem,1.5vw,1rem);border-radius:0 0 clamp(.5rem,1.5vw,1rem) clamp(.5rem,1.5vw,1rem);box-shadow:0 2px 8px #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);max-width:100%;box-sizing:border-box;min-height:44px}.mode-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:clamp(1.25rem,2.5vw,1.5rem);height:clamp(1.25rem,2.5vw,1.5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:50%;color:#fff;flex-shrink:0}.mode-indicator[_ngcontent-%COMP%]   .mode-icon[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.875rem);width:clamp(.75rem,1.5vw,.875rem);height:clamp(.75rem,1.5vw,.875rem)}.mode-toggle[_ngcontent-%COMP%]{font-weight:500;color:#2d3748;font-size:clamp(.75rem,1.2vw,.75rem);white-space:nowrap}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch{--mdc-switch-track-width: 28px;--mdc-switch-track-height: 16px}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle{--mdc-switch-handle-width: 12px;--mdc-switch-handle-height: 12px}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__ripple{min-width:44px;min-height:44px}.main-chat-area[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;gap:clamp(.25rem,1vw,.5rem);padding:clamp(.5rem,1.5vw,1rem) clamp(.25rem,1vw,.5rem) clamp(.25rem,1vw,.5rem);max-width:100vw;margin:0;width:100%;min-height:0;overflow:hidden;box-sizing:border-box}.response-data-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);width:100%;align-items:center;max-width:100%;flex:1;overflow:hidden;min-height:0}@media (min-width: 1024px){.response-data-section[_ngcontent-%COMP%]{flex-direction:row;align-items:flex-start;justify-content:space-between;gap:clamp(.5rem,1.5vw,1rem)}}@media (min-width: 1280px){.response-data-section[_ngcontent-%COMP%]{gap:clamp(1rem,2vw,1.5rem)}}.ai-section[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.ai-avatar[_ngcontent-%COMP%]{width:clamp(2.5rem,6vw,3.5rem);height:clamp(2.5rem,6vw,3.5rem);background:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;flex-shrink:0}.ai-avatar.processing[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_processing-pulse 2s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #667eea80}.ai-avatar.listening[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_listening-pulse 1s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #f093fb80}.ai-avatar.waiting[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_waiting-pulse 2s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #4facfe80}.ai-face[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:clamp(.15rem,.5vw,.25rem)}.ai-eyes[_ngcontent-%COMP%]{display:flex;gap:clamp(.25rem,.8vw,.4rem)}.ai-eyes[_ngcontent-%COMP%]   .eye[_ngcontent-%COMP%]{width:clamp(.2rem,.5vw,.3rem);height:clamp(.2rem,.5vw,.3rem);background:#667eea;border-radius:50%;animation:_ngcontent-%COMP%_ai-blink 4s infinite}.ai-mouth[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.6rem);height:clamp(.15rem,.4vw,.2rem);background:#667eea;border-radius:0 0 .3rem .3rem;transition:all .3s ease}.ai-mouth.talking[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_mouth-talk .5s infinite alternate}.ai-pulse[_ngcontent-%COMP%]{position:absolute;top:clamp(-.25rem,-.8vw,-.3rem);left:clamp(-.25rem,-.8vw,-.3rem);right:clamp(-.25rem,-.8vw,-.3rem);bottom:clamp(-.25rem,-.8vw,-.3rem);border:clamp(1px,.3vw,2px) solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_pulse-ring 2s infinite}.response-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:flex-start;align-items:center;width:100%;max-width:100%;flex:1;overflow:hidden;min-height:0}@media (min-width: 1024px){.response-section[_ngcontent-%COMP%]{align-items:flex-start;max-width:none}}.ai-message[_ngcontent-%COMP%]{max-width:100%;margin-bottom:clamp(.25rem,1vw,.5rem);animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out;flex-shrink:0}.message-bubble[_ngcontent-%COMP%]{background:#fff;padding:clamp(.5rem,2vw,1rem) clamp(1rem,3vw,1.5rem);border-radius:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000001a;position:relative;max-height:40vh;overflow-y:auto}.message-bubble[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:clamp(.5rem,2vw,1rem);bottom:clamp(-.25rem,-.5vw,-.3rem);width:0;height:0;border-left:clamp(.25rem,.8vw,.3rem) solid transparent;border-right:clamp(.25rem,.8vw,.3rem) solid transparent;border-top:clamp(.25rem,.8vw,.3rem) solid #ffffff}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#2d3748;font-size:clamp(.75rem,2vw,.875rem);line-height:1.4;font-weight:500}.processing-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;justify-content:center;gap:clamp(.5rem,2vw,1rem);color:#718096;padding:clamp(1rem,3vw,1.5rem)}.processing-indicator[_ngcontent-%COMP%]   .processing-text[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem);font-weight:500;color:#fff;margin-top:clamp(.25rem,1vw,.5rem)}.typing-dots[_ngcontent-%COMP%]{display:flex;gap:clamp(.2rem,.5vw,.25rem)}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.5rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}.data-section[_ngcontent-%COMP%]{width:100%;max-width:100%;margin-top:clamp(.25rem,1vw,.5rem);flex-shrink:0;overflow:hidden;max-height:30vh}@media (min-width: 1024px){.data-section[_ngcontent-%COMP%]{margin-top:0;max-width:min(35vw,18rem);width:auto;min-width:15rem}}@media (min-width: 1280px){.data-section[_ngcontent-%COMP%]{max-width:min(30vw,20rem)}}.data-panel[_ngcontent-%COMP%]{background:#ffffffe6;border-radius:clamp(.5rem,1.5vw,1rem);padding:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000000d;width:100%;animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out;border:1px solid rgba(102,126,234,.1);max-height:100%;overflow:hidden;display:flex;flex-direction:column}.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 clamp(.25rem,1vw,.5rem) 0;color:#2d3748;font-size:clamp(.875rem,2vw,1rem);font-weight:600;display:flex;align-items:center;gap:clamp(.25rem,1vw,.5rem);border-bottom:1px solid rgba(102,126,234,.1);padding-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4cb\\\";font-size:clamp(.75rem,1.5vw,.875rem)}.data-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);max-height:20vh!important;overflow-y:auto!important;flex:1;min-height:0}.data-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.data-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:44px;min-height:44px;display:flex;align-items:center;justify-content:center}.data-item[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem);background:#f8fafc;border-radius:clamp(.25rem,1vw,.5rem);border-left:clamp(2px,.3vw,3px) solid #4facfe;flex-shrink:0}.data-item[_ngcontent-%COMP%]   .descInfoCategoria[_ngcontent-%COMP%]{display:block;font-size:clamp(.75rem,1.8vw,.875rem);color:#718096;font-weight:500;margin-bottom:clamp(.1rem,.3vw,.15rem)}.data-item[_ngcontent-%COMP%]   .descInfovalue[_ngcontent-%COMP%]{display:block;color:#2d3748;font-weight:600;font-size:clamp(.75rem,2vw,.875rem);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.progress-info[_ngcontent-%COMP%]{margin-top:clamp(.25rem,1.5vw,.5rem);padding-top:clamp(.25rem,1vw,.5rem);border-top:1px solid rgba(102,126,234,.1);flex-shrink:0}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{height:clamp(3px,.8vw,4px);background:#f1f5f9;border-radius:9999px;overflow:hidden;margin-bottom:clamp(.25rem,.8vw,.25rem)}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4facfe,#667eea);border-radius:9999px;transition:width .3s cubic-bezier(.4,0,.2,1);position:relative}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.75rem);color:#64748b;font-weight:500}.history-btn[_ngcontent-%COMP%]{transition:all .2s ease;min-width:44px!important;min-height:44px!important}.history-btn[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea;transform:scale(1.05)}.input-section[_ngcontent-%COMP%]{flex-shrink:0;padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);background:#fffffff2;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);display:flex;justify-content:center;align-items:center;border-top:1px solid rgba(102,126,234,.1);box-shadow:0 -2px 8px #0000000d;min-height:60px}@media (max-width: 480px){.input-section[_ngcontent-%COMP%]{padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);min-height:56px}}.input-container[_ngcontent-%COMP%]{max-width:100%;width:100%;display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);box-sizing:border-box}.user-input[_ngcontent-%COMP%]{width:100%}.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000001a}.user-input[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border-radius:clamp(.5rem,2vw,1rem)}.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:clamp(.75rem,2vw,.875rem)!important;padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem)!important;min-height:44px}.user-input[_ngcontent-%COMP%]   button[matSuffix][_ngcontent-%COMP%]{min-width:44px;min-height:44px}.voice-display[_ngcontent-%COMP%]{background:#fff;border-radius:clamp(1.5rem,4vw,1.5625rem);box-shadow:0 .625rem 1.5625rem #0000001a;padding:clamp(1rem,3vw,1.25rem) clamp(1.5rem,4vw,1.875rem);text-align:center}.voice-input-field[_ngcontent-%COMP%]   .voice-placeholder[_ngcontent-%COMP%]{color:#718096;font-size:clamp(1rem,2.5vw,1.25rem);font-style:italic}.audio-visualization[_ngcontent-%COMP%]{position:fixed;bottom:clamp(4rem,8vw,6rem);left:50%;transform:translate(-50%);background:#fffffff2;padding:clamp(1rem,3vw,1.25rem) clamp(1.5rem,4vw,1.875rem);border-radius:clamp(1.5rem,4vw,1.5625rem);box-shadow:0 .625rem 1.5625rem #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);display:flex;align-items:center;gap:clamp(1rem,3vw,1.25rem);z-index:1000;max-width:min(90vw,25rem);border:1px solid rgba(102,126,234,.1)}.sound-wave[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.15rem,.4vw,.1875rem);height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]{width:clamp(.2rem,.5vw,.25rem);background:linear-gradient(to top,#667eea,#f093fb);border-radius:clamp(1px,.2vw,2px);animation:_ngcontent-%COMP%_wave-animation 1.5s infinite ease-in-out}.wave-bar[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s;height:clamp(1rem,2.5vw,1.25rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(2){animation-delay:.1s;height:clamp(1.5rem,3.5vw,1.875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(3){animation-delay:.2s;height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(4){animation-delay:.3s;height:clamp(1.75rem,4vw,2.1875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(5){animation-delay:.4s;height:clamp(1.25rem,3vw,1.5625rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(6){animation-delay:.5s;height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(7){animation-delay:.6s;height:clamp(1.5rem,3.5vw,1.875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(8){animation-delay:.7s;height:clamp(1rem,2.5vw,1.25rem)}.recording-text[_ngcontent-%COMP%]{color:#2d3748;font-weight:600;font-size:clamp(.875rem,2.5vw,1.125rem);display:flex;align-items:center;gap:clamp(.25rem,1vw,.5rem)}.recording-text[_ngcontent-%COMP%]   .recording-icon[_ngcontent-%COMP%]{color:#ff4757;animation:_ngcontent-%COMP%_recording-pulse 1s infinite}.voice-status-indicator[_ngcontent-%COMP%]{position:fixed;bottom:clamp(1.5rem,4vw,1.875rem);right:clamp(1.5rem,4vw,1.875rem);display:flex;flex-direction:column;align-items:center;gap:clamp(.5rem,1.5vw,1rem);z-index:1000}@media (max-width: 480px){.voice-status-indicator[_ngcontent-%COMP%]{bottom:clamp(1rem,3vw,1.5rem);right:clamp(1rem,3vw,1.5rem);scale:.9}}.status-icon[_ngcontent-%COMP%]{width:clamp(3.5rem,8vw,4.375rem);height:clamp(3.5rem,8vw,4.375rem);border-radius:50%;border:none;background:linear-gradient(135deg,#718096,#a0aec0);color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 .5rem 1.5625rem #71809666;transition:all .3s ease;position:relative;overflow:hidden}.status-icon.recording[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff4757,#ff3742);animation:_ngcontent-%COMP%_recording-pulse 1s infinite}.status-icon.processing[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);animation:_ngcontent-%COMP%_processing-pulse 2s infinite}.status-icon.waiting[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#10b981);animation:_ngcontent-%COMP%_waiting-pulse 2s infinite}.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:clamp(1.25rem,4vw,1.75rem);z-index:2;position:relative}.status-ripple[_ngcontent-%COMP%]{position:absolute;inset:0;border-radius:50%;background:#ffffff4d;animation:_ngcontent-%COMP%_ripple 1.5s infinite}.status-text[_ngcontent-%COMP%]{background:#fffffff2;padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(1rem,3vw,1.25rem);font-size:clamp(.75rem,2vw,.875rem);font-weight:600;color:#2d3748;box-shadow:0 .25rem .9375rem #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);white-space:nowrap}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}@keyframes _ngcontent-%COMP%_blink{0%,90%,to{transform:scaleY(1)}95%{transform:scaleY(.1)}}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}@keyframes _ngcontent-%COMP%_ai-blink{0%,90%,to{transform:scaleY(1)}95%{transform:scaleY(.1)}}@keyframes _ngcontent-%COMP%_mouth-talk{0%{transform:scaleY(1)}to{transform:scaleY(1.5)}}@keyframes _ngcontent-%COMP%_processing-pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_listening-pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.1);opacity:.8}}@keyframes _ngcontent-%COMP%_waiting-pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.9}}@keyframes _ngcontent-%COMP%_pulse-ring{0%{transform:scale(.8);opacity:1}to{transform:scale(1.3);opacity:0}}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0)}30%{transform:translateY(-10px)}}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:scaleY(.5)}50%{transform:scaleY(1)}}@keyframes _ngcontent-%COMP%_recording-pulse{0%,to{box-shadow:0 8px 25px #ff475766}50%{box-shadow:0 8px 35px #ff4757cc}}@keyframes _ngcontent-%COMP%_ripple{0%{transform:scale(.8);opacity:1}to{transform:scale(2);opacity:0}}@keyframes _ngcontent-%COMP%_waiting-pulse{0%,to{box-shadow:0 8px 25px #10b98166}50%{box-shadow:0 8px 35px #10b981cc}}@media (max-width: 480px){.ai-questionnaire-container[_ngcontent-%COMP%]{font-size:clamp(.7rem,1.8vw,.8rem);height:100vh;height:100dvh;overflow:hidden}.idle-content[_ngcontent-%COMP%]{padding:clamp(.5rem,3vw,1rem) clamp(.25rem,2vw,.5rem);margin:.25rem;max-width:min(85vw,18rem)}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:clamp(.875rem,3vw,1rem);margin:clamp(.25rem,1.5vw,.5rem) 0 clamp(.25rem,1vw,.25rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.75rem,2.2vw,.875rem);margin-bottom:clamp(.5rem,2.5vw,1rem)}.chat-interface[_ngcontent-%COMP%]{height:100vh;height:100dvh;overflow:hidden}.controls-header[_ngcontent-%COMP%]{flex-shrink:0;padding:clamp(.25rem,1vw,.25rem) clamp(.5rem,2vw,.5rem);gap:clamp(.25rem,1vw,.25rem);min-height:40px}.main-chat-area[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem) clamp(.25rem,1vw,.5rem);gap:clamp(.25rem,.8vw,.25rem);overflow:hidden}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:clamp(.25rem,1.5vw,.5rem);overflow:hidden;flex:1;min-height:0}.ai-avatar[_ngcontent-%COMP%]{width:clamp(2rem,5vw,2.5rem);height:clamp(2rem,5vw,2.5rem)}.message-bubble[_ngcontent-%COMP%]{padding:clamp(.25rem,1.8vw,.5rem) clamp(.5rem,2.5vw,1rem);border-radius:clamp(.25rem,1.5vw,.5rem);max-height:25vh;overflow-y:auto}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.75rem,2.2vw,.875rem);line-height:1.3}.data-section[_ngcontent-%COMP%]{max-width:100%;margin-top:clamp(.25rem,1vw,.25rem);max-height:20vh;overflow:hidden}.data-panel[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem);border-radius:clamp(.25rem,1.5vw,.5rem)}.input-section[_ngcontent-%COMP%]{padding:clamp(.25rem,1vw,.25rem) clamp(.5rem,2vw,1rem);min-height:50px}.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{border-radius:clamp(.25rem,1.5vw,.5rem)}.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.8vw,.875rem)!important;padding:clamp(.25rem,1.2vw,.25rem) clamp(.5rem,1.8vw,.5rem)!important}.audio-visualization[_ngcontent-%COMP%]{bottom:clamp(.25rem,1.5vw,.5rem);padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(.25rem,1.5vw,.5rem);max-width:75vw;min-height:40px}.voice-status-indicator[_ngcontent-%COMP%]{bottom:clamp(.25rem,1.5vw,.5rem);right:clamp(.25rem,1.5vw,.5rem);scale:.75}.status-icon[_ngcontent-%COMP%]{width:clamp(2rem,4.5vw,2.5rem);height:clamp(2rem,4.5vw,2.5rem);min-width:40px;min-height:40px}.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.8vw,1rem)}.status-text[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.75rem);padding:clamp(.25rem,.6vw,.25rem) clamp(.25rem,1.2vw,.5rem);max-width:6rem}}@media (min-width: 481px) and (max-width: 640px){.main-chat-area[_ngcontent-%COMP%]{padding:clamp(5rem,10vw,6rem) clamp(1rem,3vw,1.5rem) clamp(1.5rem,3vw,2rem)}.controls-header[_ngcontent-%COMP%]{position:absolute;top:clamp(1rem,2.5vw,1.5rem);left:clamp(1rem,2.5vw,1.5rem)}.response-data-section[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:clamp(1.5rem,4vw,2rem)}.data-section[_ngcontent-%COMP%]{grid-column:1;margin-top:clamp(1rem,3vw,2rem)}}@media (min-width: 641px) and (max-width: 768px){.main-chat-area[_ngcontent-%COMP%]{padding:clamp(6rem,10vw,7rem) clamp(1.5rem,4vw,2rem) clamp(2rem,4vw,3rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;gap:clamp(2rem,5vw,3rem);align-items:center}.data-section[_ngcontent-%COMP%]{max-width:100%;margin-top:clamp(1.5rem,4vw,2rem);width:100%}.response-section[_ngcontent-%COMP%]{align-items:center;width:100%}.controls-header[_ngcontent-%COMP%]{top:clamp(1rem,2.5vw,1.5rem);left:clamp(1rem,2.5vw,1.5rem);right:clamp(1rem,2.5vw,1.5rem);width:auto;justify-content:center}}@media (min-width: 769px) and (max-width: 1024px){.main-chat-area[_ngcontent-%COMP%]{max-width:min(90vw,75rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;gap:clamp(1.5rem,3vw,3rem);align-items:center}.data-section[_ngcontent-%COMP%]{margin-top:clamp(1.5rem,3vw,2rem);max-width:100%;width:100%}.response-section[_ngcontent-%COMP%]{width:100%}}@media (min-width: 1025px){.main-chat-area[_ngcontent-%COMP%]{max-width:min(85vw,85rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:row;align-items:flex-start;gap:clamp(2rem,2vw,4rem)}.response-section[_ngcontent-%COMP%]{flex:1;max-width:none}.data-section[_ngcontent-%COMP%]{flex-shrink:0;width:auto;min-width:20rem;margin-top:0}}@media (orientation: landscape) and (max-height: 600px){.idle-screen[_ngcontent-%COMP%]{padding:clamp(.5rem,2vh,1rem)}.idle-content[_ngcontent-%COMP%]{padding:clamp(1.5rem,4vh,2rem) clamp(1.5rem,4vw,3rem)}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:clamp(1.125rem,4vw,1.25rem);margin:clamp(.5rem,2vh,1rem) 0 clamp(.25rem,1vh,.5rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem);margin-bottom:clamp(1rem,3vh,1.5rem)}.main-chat-area[_ngcontent-%COMP%]{padding:clamp(4rem,8vh,5rem) clamp(1.5rem,4vw,2rem) clamp(1rem,2vh,1.5rem)}.ai-avatar[_ngcontent-%COMP%]{width:clamp(4rem,8vh,5rem);height:clamp(4rem,8vh,5rem)}}@media (min-resolution: 192dpi){.ai-avatar[_ngcontent-%COMP%], .robot-head[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%]{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}}@container (max-width: 480px){.message-bubble[_ngcontent-%COMP%]{padding:clamp(.5rem,2vw,1rem)}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem)}}@media (prefers-reduced-motion: reduce){.ai-robot[_ngcontent-%COMP%], .ai-avatar[_ngcontent-%COMP%], .start-btn[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%]{animation:none}.btn-glow[_ngcontent-%COMP%]{transition:none}}@media (prefers-contrast: high){.message-bubble[_ngcontent-%COMP%], .data-panel[_ngcontent-%COMP%], .controls-header[_ngcontent-%COMP%]{border:2px solid #2d3748}.start-btn[_ngcontent-%COMP%]{border:2px solid #ffffff}}.modern-modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0f172acc;-webkit-backdrop-filter:blur(8px) saturate(180%);backdrop-filter:blur(8px) saturate(180%);display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.modern-modal-overlay[_ngcontent-%COMP%]:focus-within{outline:2px solid #4facfe;outline-offset:-2px}.modern-modal-container[_ngcontent-%COMP%]{background:#fff;border-radius:1.5rem;box-shadow:0 25px 50px -12px #00000040;width:100%;max-width:56rem;max-height:90vh;display:flex;flex-direction:column;overflow:hidden;border:1px solid rgba(255,255,255,.1);background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}@media (max-width: 768px){.modern-modal-container[_ngcontent-%COMP%]{max-width:95vw;max-height:95vh;margin:.5rem}}.modal-header-modern[_ngcontent-%COMP%]{padding:2rem 2rem 1.5rem;border-bottom:1px solid #f1f5f9;background:linear-gradient(135deg,#667eea0d,#764ba20d)}.modal-header-modern[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;justify-content:space-between;gap:1.5rem;margin-bottom:1.5rem}.modal-header-modern[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;flex:1}.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:3rem;height:3rem;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:1rem;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{flex:1}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1.5rem;font-weight:700;color:#0f172a;line-height:1.2}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%]{margin:0;font-size:.875rem;color:#475569;font-weight:500}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem;border-radius:.75rem;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#f8fafc;color:#475569}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#f1f5f9;color:#0f172a;transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]{background:#ef44441a;color:#ef4444}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]:hover{background:#ef444426;transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:.875rem;color:#475569;font-weight:500}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%]{font-size:.875rem;color:#4facfe;font-weight:700}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{height:.5rem;background:#f1f5f9;border-radius:9999px;overflow:hidden}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4facfe,#667eea);border-radius:9999px;transition:width .3s cubic-bezier(.4,0,.2,1);position:relative}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.search-filter-section[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-bottom:1px solid #f1f5f9;background:#f8fafc}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{margin-bottom:1rem}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{width:100%}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:1rem;box-shadow:0 1px 2px #0000000d;border:1px solid #f1f5f9;transition:all .2s cubic-bezier(.4,0,.2,1)}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:hover{border-color:#667eea4d;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:focus-within{border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option[_ngcontent-%COMP%]{border-radius:9999px;font-weight:500;font-size:.875rem;transition:all .2s cubic-bezier(.4,0,.2,1)}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-personal[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(102, 126, 234, .1);--mdc-chip-selected-label-text-color: #667eea}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-medical[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(16, 185, 129, .1);--mdc-chip-selected-label-text-color: #10b981}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-contact[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(59, 130, 246, .1);--mdc-chip-selected-label-text-color: #3b82f6}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-optional[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(245, 158, 11, .1);--mdc-chip-selected-label-text-color: #f59e0b}.modal-body-modern[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1.5rem 2rem}.modal-body-modern[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{max-height:100%}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 1.5rem;text-align:center}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{width:4rem;height:4rem;background:#f1f5f9;border-radius:9999px;display:flex;align-items:center;justify-content:center;margin-bottom:1.5rem}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:2rem;color:#64748b}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.25rem;font-weight:600;color:#0f172a}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]{margin:0;font-size:.875rem;color:#475569;max-width:24rem}.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{display:grid;gap:1rem;grid-template-columns:1fr}@media (min-width: 1024px){.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #f1f5f9;border-radius:1rem;padding:1.5rem;transition:all .2s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,#667eea,#764ba2);opacity:0;transition:opacity .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover{border-color:#667eea33;box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;transform:translateY(-2px)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover:before{opacity:1}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-personal[_ngcontent-%COMP%]{border-left:4px solid #667eea}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-medical[_ngcontent-%COMP%]{border-left:4px solid #10b981}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-contact[_ngcontent-%COMP%]{border-left:4px solid #3b82f6}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-optional[_ngcontent-%COMP%]{border-left:4px solid #f59e0b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem;background:#f8fafc;border-radius:.75rem;display:flex;align-items:center;justify-content:center;flex-shrink:0}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.25rem;color:#475569}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]{flex:1;min-width:0}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1rem;font-weight:600;color:#0f172a;line-height:1.3}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-category[_ngcontent-%COMP%]{font-size:.75rem;color:#64748b;font-weight:500;text-transform:uppercase;letter-spacing:.05em}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{display:flex;gap:.25rem;opacity:0;transition:opacity .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]{width:2rem;height:2rem;border-radius:.5rem;background:#f8fafc;color:#475569;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;transform:scale(1.1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%]{opacity:1}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]{margin-bottom:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]{font-size:1rem;color:#0f172a;font-weight:500;line-height:1.5;word-break:break-word}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]     mark{background:#4facfe33;color:#4facfe;padding:.125rem .25rem;border-radius:.375rem;font-weight:600}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding-top:.5rem;border-top:1px solid #f1f5f9}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;color:#64748b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.875rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;font-weight:500;padding:.25rem .5rem;border-radius:9999px}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.875rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-valid[_ngcontent-%COMP%]{background:#10b9811a;color:#10b981}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-warning[_ngcontent-%COMP%]{background:#f59e0b1a;color:#f59e0b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-error[_ngcontent-%COMP%]{background:#ef44441a;color:#ef4444}.modal-footer-modern[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-top:1px solid #f1f5f9;background:#f8fafc;display:flex;align-items:center;justify-content:space-between;gap:1.5rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#475569}.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;color:#3b82f6}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{display:flex;gap:1rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]{border-radius:.75rem;font-weight:500;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]{border-radius:.75rem;font-weight:600;background:linear-gradient(135deg,#667eea,#764ba2);transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;background:linear-gradient(135deg,#506be7,#694391)}@media (max-width: 640px){.modal-footer-modern[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{justify-content:stretch}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%], .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]{flex:1}}@keyframes _ngcontent-%COMP%_shimmer{0%{transform:translate(-100%)}to{transform:translate(100%)}}@media (prefers-reduced-motion: reduce){.modern-modal-container[_ngcontent-%COMP%], .data-card[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .card-action-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%], .primary-btn[_ngcontent-%COMP%]{transition:none}.progress-fill[_ngcontent-%COMP%]:after{animation:none}}@media (prefers-contrast: high){.modern-modal-container[_ngcontent-%COMP%]{border:2px solid #0f172a}.data-card[_ngcontent-%COMP%], .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{border:2px solid #475569}}.test-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9800,#f57c00)!important;color:#fff!important;border:none!important;border-radius:25px!important;padding:12px 24px!important;font-weight:600!important;font-size:.9em!important;text-transform:uppercase!important;letter-spacing:.5px!important;box-shadow:0 4px 15px #ff98004d!important;transition:all .3s ease!important;position:relative!important;overflow:hidden!important}.test-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px)!important;box-shadow:0 6px 20px #ff980066!important;background:linear-gradient(135deg,#f57c00,#e65100)!important}.test-button[_ngcontent-%COMP%]:active{transform:translateY(0)!important}.test-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px!important;font-size:1.1em!important}.test-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.test-button[_ngcontent-%COMP%]:hover:before{left:100%}.idle-content[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%]{margin-top:10px;font-size:.85em!important;padding:10px 20px!important}.controls-header[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%]{margin-left:15px;font-size:.8em!important;padding:8px 16px!important}.scrollable-hidden[_ngcontent-%COMP%]{overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}.scrollable-hidden[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.validation-buttons[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:center;align-items:center;margin-top:1.5rem;padding:1.5rem;background:#f8fafc;border-radius:12px;border:1px solid rgba(102,126,234,.1);box-shadow:0 4px 12px #0000000d}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1.5rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#475569;font-size:1rem;font-weight:500}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]   .validation-icon[_ngcontent-%COMP%]{color:#667eea;font-size:1.25rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap;justify-content:center}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:140px;height:44px;border-radius:8px;font-weight:600;font-size:.875rem;text-transform:none;letter-spacing:.025em;transition:all .2s ease;box-shadow:0 2px 8px #0000001a}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:.25rem;font-size:1.1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active{transform:translateY(0)}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#10b981);color:#fff}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#10b981,#059669)}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f59e0b,#d97706);color:#fff}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#d97706,#b45309)}@media (max-width: 480px){.validation-buttons[_ngcontent-%COMP%]{padding:1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]{gap:1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{flex-direction:column;width:100%}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;min-width:unset}}\"],\n      data: {\n        animation: [trigger('fadeInOut', [transition(':enter', [style({\n          opacity: 0\n        }), animate('300ms ease-in', style({\n          opacity: 1\n        }))]), transition(':leave', [animate('200ms ease-out', style({\n          opacity: 0\n        }))])]), trigger('slideInOut', [transition(':enter', [style({\n          transform: 'translateY(-50px)',\n          opacity: 0\n        }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        }))]), transition(':leave', [animate('300ms ease-in', style({\n          transform: 'translateY(-30px)',\n          opacity: 0\n        }))])]), trigger('cardAnimation', [transition(':enter', [style({\n          transform: 'scale(0.8)',\n          opacity: 0\n        }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n          transform: 'scale(1)',\n          opacity: 1\n        }))])])]\n      }\n    });\n  }\n  return PreConsultaQuestionarioComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self.webpackChunkTeleMedicina=self.webpackChunkTeleMedicina||[]).push([[8],{68008:(pi,je,ie)=>{ie.r(je),ie.d(je,{AnimationDriver:()=>ds,NoopAnimationDriver:()=>ve,\u0275Animation:()=>hi,\u0275AnimationEngine:()=>qe,\u0275AnimationRenderer:()=>bt,\u0275AnimationRendererFactory:()=>di,\u0275AnimationStyleNormalizer:()=>Je,\u0275BaseAnimationRenderer:()=>Ve,\u0275NoopAnimationStyleNormalizer:()=>xe,\u0275WebAnimationsDriver:()=>wt,\u0275WebAnimationsPlayer:()=>$e,\u0275WebAnimationsStyleNormalizer:()=>at,\u0275allowPreviousPlayerStylesMerge:()=>rt,\u0275camelCaseToDashCase:()=>Es,\u0275containsElement:()=>Te,\u0275createEngine:()=>ui,\u0275getParentElement:()=>ne,\u0275invokeQuery:()=>we,\u0275normalizeKeyframes:()=>st,\u0275validateStyleProperty:()=>Ze,\u0275validateWebAnimatableStyleProperty:()=>cs});var d=ie(49969),S=ie(54438);function Ge(n){return new S.wOt(3e3,!1)}const ls=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function $(n){switch(n.length){case 0:return new d.sf;case 1:return n[0];default:return new d.ui(n)}}function He(n,e,t=new Map,s=new Map){const i=[],r=[];let a=-1,o=null;if(e.forEach(l=>{const u=l.get("offset"),h=u==a,c=h&&o||new Map;l.forEach((E,_)=>{let m=_,y=E;if("offset"!==_)switch(m=n.normalizePropertyName(m,i),y){case d.FX:y=t.get(_);break;case d.kp:y=s.get(_);break;default:y=n.normalizeStyleValue(_,m,y,i)}c.set(m,y)}),h||r.push(c),o=c,a=u}),i.length)throw function Zt(n){return new S.wOt(3502,!1)}();return r}function _e(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&Ee(t,"start",n)));break;case"done":n.onDone(()=>s(t&&Ee(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&Ee(t,"destroy",n)))}}function Ee(n,e,t){const r=Se(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,t.totalTime??n.totalTime,!!t.disabled),a=n._data;return null!=a&&(r._data=a),r}function Se(n,e,t,s,i="",r=0,a){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!a}}function O(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function Xe(n){const e=n.indexOf(":");return[n.substring(1,e),n.slice(e+1)]}const us=typeof document>"u"?null:document.documentElement;function ne(n){const e=n.parentNode||n.host||null;return e===us?null:e}let U=null,Ye=!1;function Ze(n){U||(U=function fs(){return typeof document<"u"?document.body:null}()||{},Ye=!!U.style&&"WebkitAppearance"in U.style);let e=!0;return U.style&&!function hs(n){return"ebkit"==n.substring(1,6)}(n)&&(e=n in U.style,!e&&Ye&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in U.style)),e}function cs(n){return ls.has(n)}function Te(n,e){for(;e;){if(e===n)return!0;e=ne(e)}return!1}function we(n,e,t){if(t)return Array.from(n.querySelectorAll(e));const s=n.querySelector(e);return s?[s]:[]}let ve=(()=>{class n{validateStyleProperty(t){return Ze(t)}matchesElement(t,s){return!1}containsElement(t,s){return Te(t,s)}getParentElement(t){return ne(t)}query(t,s,i){return we(t,s,i)}computeStyle(t,s,i){return i||""}animate(t,s,i,r,a,o=[],l){return new d.sf(i,r)}static{this.\u0275fac=function(s){return new(s||n)}}static{this.\u0275prov=S.jDH({token:n,factory:n.\u0275fac})}}return n})();class ds{static{this.NOOP=new ve}}class Je{}class xe{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,i){return s}}const ms=1e3,be="ng-enter",re="ng-leave",ae="ng-trigger",oe=".ng-trigger",tt="ng-animating",Ae=".ng-animating";function Q(n){if("number"==typeof n)return n;const e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:Pe(parseFloat(e[1]),e[2])}function Pe(n,e){return"s"===e?n*ms:n}function le(n,e,t){return n.hasOwnProperty("duration")?n:function gs(n,e,t){let i,r=0,a="";if("string"==typeof n){const o=n.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===o)return e.push(Ge()),{duration:0,delay:0,easing:""};i=Pe(parseFloat(o[1]),o[2]);const l=o[3];null!=l&&(r=Pe(parseFloat(l),o[4]));const u=o[5];u&&(a=u)}else i=n;if(!t){let o=!1,l=e.length;i<0&&(e.push(function Ct(){return new S.wOt(3100,!1)}()),o=!0),r<0&&(e.push(function kt(){return new S.wOt(3101,!1)}()),o=!0),o&&e.splice(l,0,Ge())}return{duration:i,delay:r,easing:a}}(n,e,t)}function st(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function it(n){return Array.isArray(n)?new Map(...n):new Map(n)}function K(n,e,t){e.forEach((s,i)=>{const r=Me(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s})}function W(n,e){e.forEach((t,s)=>{const i=Me(s);n.style[i]=""})}function x(n){return Array.isArray(n)?1==n.length?n[0]:(0,d.K2)(n):n}const Ne=new RegExp("{{\\s*(.+?)\\s*}}","g");function nt(n){let e=[];if("string"==typeof n){let t;for(;t=Ne.exec(n);)e.push(t[1]);Ne.lastIndex=0}return e}function ee(n,e,t){const s=`${n}`,i=s.replace(Ne,(r,a)=>{let o=e[a];return null==o&&(t.push(function Ot(n){return new S.wOt(3003,!1)}()),o=""),o.toString()});return i==s?n:i}const _s=/-+([a-z0-9])/g;function Me(n){return n.replace(_s,(...e)=>e[1].toUpperCase())}function Es(n){return n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function rt(n,e){return 0===n||0===e}function I(n,e,t){switch(e.type){case d.If.Trigger:return n.visitTrigger(e,t);case d.If.State:return n.visitState(e,t);case d.If.Transition:return n.visitTransition(e,t);case d.If.Sequence:return n.visitSequence(e,t);case d.If.Group:return n.visitGroup(e,t);case d.If.Animate:return n.visitAnimate(e,t);case d.If.Keyframes:return n.visitKeyframes(e,t);case d.If.Style:return n.visitStyle(e,t);case d.If.Reference:return n.visitReference(e,t);case d.If.AnimateChild:return n.visitAnimateChild(e,t);case d.If.AnimateRef:return n.visitAnimateRef(e,t);case d.If.Query:return n.visitQuery(e,t);case d.If.Stagger:return n.visitStagger(e,t);default:throw function It(n){return new S.wOt(3004,!1)}()}}function Ce(n,e){return window.getComputedStyle(n)[e]}const Ts=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]);class at extends Je{normalizePropertyName(e,t){return Me(e)}normalizeStyleValue(e,t,s,i){let r="";const a=s.toString().trim();if(Ts.has(t)&&0!==s&&"0"!==s)if("number"==typeof s)r="px";else{const o=s.match(/^[+-]?[\d\.]+([a-z]*)$/);o&&0==o[1].length&&i.push(function Rt(n,e){return new S.wOt(3005,!1)}())}return a+r}}const ue="*";const he=new Set(["true","1"]),ce=new Set(["false","0"]);function ot(n,e){const t=he.has(n)||ce.has(n),s=he.has(e)||ce.has(e);return(i,r)=>{let a=n==ue||n==i,o=e==ue||e==r;return!a&&t&&"boolean"==typeof i&&(a=i?he.has(n):ce.has(n)),!o&&s&&"boolean"==typeof r&&(o=r?he.has(e):ce.has(e)),a&&o}}const As=new RegExp("s*:selfs*,?","g");function ke(n,e,t,s){return new Ps(n).build(e,t,s)}class Ps{constructor(e){this._driver=e}build(e,t,s){const i=new Cs(t);return this._resetContextStyleTimingState(i),I(this,x(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector="",e.collectedStyles=new Map,e.collectedStyles.set("",new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0;const r=[],a=[];return"@"==e.name.charAt(0)&&t.errors.push(function Ft(){return new S.wOt(3006,!1)}()),e.definitions.forEach(o=>{if(this._resetContextStyleTimingState(t),o.type==d.If.State){const l=o,u=l.name;u.toString().split(/\s*,\s*/).forEach(h=>{l.name=h,r.push(this.visitState(l,t))}),l.name=u}else if(o.type==d.If.Transition){const l=this.visitTransition(o,t);s+=l.queryCount,i+=l.depCount,a.push(l)}else t.errors.push(function Lt(){return new S.wOt(3007,!1)}())}),{type:d.If.Trigger,name:e.name,states:r,transitions:a,queryCount:s,depCount:i,options:null}}visitState(e,t){const s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){const r=new Set,a=i||{};s.styles.forEach(o=>{o instanceof Map&&o.forEach(l=>{nt(l).forEach(u=>{a.hasOwnProperty(u)||r.add(u)})})}),r.size&&t.errors.push(function zt(n,e){return new S.wOt(3008,!1)}(0,r.values()))}return{type:d.If.State,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;const s=I(this,x(e.animation),t),i=function ws(n,e){const t=[];return"string"==typeof n?n.split(/\s*,\s*/).forEach(s=>function vs(n,e,t){if(":"==n[0]){const l=function bs(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(function Gt(n){return new S.wOt(3016,!1)}()),"* => *"}}(n,t);if("function"==typeof l)return void e.push(l);n=l}const s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==s||s.length<4)return t.push(function jt(n){return new S.wOt(3015,!1)}()),e;const i=s[1],r=s[2],a=s[3];e.push(ot(i,a)),"<"==r[0]&&(i!=ue||a!=ue)&&e.push(ot(a,i))}(s,t,e)):t.push(n),t}(e.expr,t.errors);return{type:d.If.Transition,matchers:i,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:j(e.options)}}visitSequence(e,t){return{type:d.If.Sequence,steps:e.steps.map(s=>I(this,s,t)),options:j(e.options)}}visitGroup(e,t){const s=t.currentTime;let i=0;const r=e.steps.map(a=>{t.currentTime=s;const o=I(this,a,t);return i=Math.max(i,t.currentTime),o});return t.currentTime=i,{type:d.If.Group,steps:r,options:j(e.options)}}visitAnimate(e,t){const s=function Ds(n,e){if(n.hasOwnProperty("duration"))return n;if("number"==typeof n)return De(le(n,e).duration,0,"");const t=n;if(t.split(/\s+/).some(r=>"{"==r.charAt(0)&&"{"==r.charAt(1))){const r=De(0,0,"");return r.dynamic=!0,r.strValue=t,r}const i=le(t,e);return De(i.duration,i.delay,i.easing)}(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:(0,d.iF)({});if(r.type==d.If.Keyframes)i=this.visitKeyframes(r,t);else{let a=e.styles,o=!1;if(!a){o=!0;const u={};s.easing&&(u.easing=s.easing),a=(0,d.iF)(u)}t.currentTime+=s.duration+s.delay;const l=this.visitStyle(a,t);l.isEmptyStep=o,i=l}return t.currentAnimateTimings=null,{type:d.If.Animate,timings:s,style:i,options:null}}visitStyle(e,t){const s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){const s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let o of i)"string"==typeof o?o===d.kp?s.push(o):t.errors.push(new S.wOt(3002,!1)):s.push(new Map(Object.entries(o)));let r=!1,a=null;return s.forEach(o=>{if(o instanceof Map&&(o.has("easing")&&(a=o.get("easing"),o.delete("easing")),!r))for(let l of o.values())if(l.toString().indexOf("{{")>=0){r=!0;break}}),{type:d.If.Style,styles:s,easing:a,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){const s=t.currentAnimateTimings;let i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(a=>{"string"!=typeof a&&a.forEach((o,l)=>{const u=t.collectedStyles.get(t.currentQuerySelector),h=u.get(l);let c=!0;h&&(r!=i&&r>=h.startTime&&i<=h.endTime&&(t.errors.push(function Bt(n,e,t,s,i){return new S.wOt(3010,!1)}()),c=!1),r=h.startTime),c&&u.set(l,{startTime:r,endTime:i}),t.options&&function ys(n,e,t){const s=e.params||{},i=nt(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(function Dt(n){return new S.wOt(3001,!1)}())})}(o,t.options,t.errors)})})}visitKeyframes(e,t){const s={type:d.If.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(function qt(){return new S.wOt(3011,!1)}()),s;let r=0;const a=[];let o=!1,l=!1,u=0;const h=e.steps.map(b=>{const A=this._makeStyleAst(b,t);let C=null!=A.offset?A.offset:function ks(n){if("string"==typeof n)return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){const s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){const t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}(A.styles),N=0;return null!=C&&(r++,N=A.offset=C),l=l||N<0||N>1,o=o||N<u,u=N,a.push(N),A});l&&t.errors.push(function Qt(){return new S.wOt(3012,!1)}()),o&&t.errors.push(function $t(){return new S.wOt(3200,!1)}());const c=e.steps.length;let E=0;r>0&&r<c?t.errors.push(function Vt(){return new S.wOt(3202,!1)}()):0==r&&(E=1/(c-1));const _=c-1,m=t.currentTime,y=t.currentAnimateTimings,w=y.duration;return h.forEach((b,A)=>{const C=E>0?A==_?1:E*A:a[A],N=C*w;t.currentTime=m+y.delay+N,y.duration=N,this._validateStyleAst(b,t),b.offset=C,s.styles.push(b)}),s}visitReference(e,t){return{type:d.If.Reference,animation:I(this,x(e.animation),t),options:j(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:d.If.AnimateChild,options:j(e.options)}}visitAnimateRef(e,t){return{type:d.If.AnimateRef,animation:this.visitReference(e.animation,t),options:j(e.options)}}visitQuery(e,t){const s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;const[r,a]=function Ns(n){const e=!!n.split(/\s*,\s*/).find(t=>":self"==t);return e&&(n=n.replace(As,"")),n=n.replace(/@\*/g,oe).replace(/@\w+/g,t=>oe+"-"+t.slice(1)).replace(/:animating/g,Ae),[n,e]}(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,O(t.collectedStyles,t.currentQuerySelector,new Map);const o=I(this,x(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:d.If.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:a,animation:o,originalSelector:e.selector,options:j(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(function Ut(){return new S.wOt(3013,!1)}());const s="full"===e.timings?{duration:0,delay:0,easing:"full"}:le(e.timings,t.errors,!0);return{type:d.If.Stagger,animation:I(this,x(e.animation),t),timings:s,options:null}}}class Cs{constructor(e){this.errors=e,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}}function j(n){return n?(n={...n}).params&&(n.params=function Ms(n){return n?{...n}:null}(n.params)):n={},n}function De(n,e,t){return{duration:n,delay:e,easing:t}}function Oe(n,e,t,s,i,r,a=null,o=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:a,subTimeline:o}}class fe{constructor(){this._map=new Map}get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}}const Rs=new RegExp(":enter","g"),Ls=new RegExp(":leave","g");function Ie(n,e,t,s,i,r=new Map,a=new Map,o,l,u=[]){return(new zs).buildKeyframes(n,e,t,s,i,r,a,o,l,u)}class zs{buildKeyframes(e,t,s,i,r,a,o,l,u,h=[]){u=u||new fe;const c=new Re(e,t,u,i,r,h,[]);c.options=l;const E=l.delay?Q(l.delay):0;c.currentTimeline.delayNextStep(E),c.currentTimeline.setStyles([a],null,c.errors,l),I(this,s,c);const _=c.timelines.filter(m=>m.containsAnimation());if(_.length&&o.size){let m;for(let y=_.length-1;y>=0;y--){const w=_[y];if(w.element===t){m=w;break}}m&&!m.allowOnlyTimelineStyles()&&m.setStyles([o],null,c.errors,l)}return _.length?_.map(m=>m.buildKeyframes()):[Oe(t,[],[],[],0,E,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){const s=t.subInstructions.get(t.element);if(s){const i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,a=this._visitSubInstructions(s,i,i.options);r!=a&&t.transformIntoNewTimeline(a)}t.previousNode=e}visitAnimateRef(e,t){const s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(const i of e){const r=i?.delay;if(r){const a="number"==typeof r?r:Q(ee(r,i?.params??{},t.errors));s.delayNextStep(a)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime;const a=null!=s.duration?Q(s.duration):null,o=null!=s.delay?Q(s.delay):null;return 0!==a&&e.forEach(l=>{const u=t.appendInstructionToTimeline(l,a,o);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),I(this,e.animation,t),t.previousNode=e}visitSequence(e,t){const s=t.subContextCount;let i=t;const r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),null!=r.delay)){i.previousNode.type==d.If.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=de);const a=Q(r.delay);i.delayNextStep(a)}e.steps.length&&(e.steps.forEach(a=>I(this,a,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){const s=[];let i=t.currentTimeline.currentTime;const r=e.options&&e.options.delay?Q(e.options.delay):0;e.steps.forEach(a=>{const o=t.createSubContext(e.options);r&&o.delayNextStep(r),I(this,a,o),i=Math.max(i,o.currentTimeline.currentTime),s.push(o.currentTimeline)}),s.forEach(a=>t.currentTimeline.mergeTimelineCollectedStyles(a)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){const s=e.strValue;return le(t.params?ee(s,t.params,t.errors):s,t.errors)}return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){const s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());const r=e.style;r.type==d.If.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){const s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();const r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){const s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,o=t.createSubContext().currentTimeline;o.easing=s.easing,e.styles.forEach(l=>{o.forwardTime((l.offset||0)*r),o.setStyles(l.styles,l.easing,t.errors,t.options),o.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(o),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){const s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?Q(i.delay):0;r&&(t.previousNode.type===d.If.Style||0==s&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=de);let a=s;const o=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=o.length;let l=null;o.forEach((u,h)=>{t.currentQueryIndex=h;const c=t.createSubContext(e.options,u);r&&c.delayNextStep(r),u===t.element&&(l=c.currentTimeline),I(this,e.animation,c),c.currentTimeline.applyStylesToKeyframe(),a=Math.max(a,c.currentTimeline.currentTime)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(a),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){const s=t.parentContext,i=t.currentTimeline,r=e.timings,a=Math.abs(r.duration),o=a*(t.currentQueryTotal-1);let l=a*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=o-l;break;case"full":l=s.currentStaggerTime}const h=t.currentTimeline;l&&h.delayNextStep(l);const c=h.currentTime;I(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-c+(i.startTime-s.currentTimeline.startTime)}}const de={};class Re{constructor(e,t,s,i,r,a,o,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=a,this.timelines=o,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=de,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new me(this._driver,t,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;const s=e;let i=this.options;null!=s.duration&&(i.duration=Q(s.duration)),null!=s.delay&&(i.delay=Q(s.delay));const r=s.params;if(r){let a=i.params;a||(a=this.options.params={}),Object.keys(r).forEach(o=>{(!t||!a.hasOwnProperty(o))&&(a[o]=ee(r[o],a,this.errors))})}}_copyOptions(){const e={};if(this.options){const t=this.options.params;if(t){const s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){const i=t||this.element,r=new Re(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=de,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){const i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new Ks(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,a){let o=[];if(i&&o.push(this.element),e.length>0){e=(e=e.replace(Rs,"."+this._enterClassName)).replace(Ls,"."+this._leaveClassName);let u=this._driver.query(this.element,e,1!=s);0!==s&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),o.push(...u)}return!r&&0==o.length&&a.push(function Wt(n){return new S.wOt(3014,!1)}()),o}}class me{constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){const t=1===this._keyframes.size&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new me(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||d.kp),this._currentKeyframe.set(t,d.kp);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);const r=i&&i.params||{},a=function Bs(n,e){const t=new Map;let s;return n.forEach(i=>{if("*"===i){s??=e.keys();for(let r of s)t.set(r,d.kp)}else for(let[r,a]of i)t.set(r,a)}),t}(e,this._globalTimelineStyles);for(let[o,l]of a){const u=ee(l,r,s);this._pendingStyles.set(o,u),this._localTimelineStyles.has(o)||this._backFill.set(o,this._globalTimelineStyles.get(o)??d.kp),this._updateStyle(o,u)}}applyStylesToKeyframe(){0!=this._pendingStyles.size&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{const i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();const e=new Set,t=new Set,s=1===this._keyframes.size&&0===this.duration;let i=[];this._keyframes.forEach((o,l)=>{const u=new Map([...this._backFill,...o]);u.forEach((h,c)=>{h===d.FX?e.add(c):h===d.kp&&t.add(c)}),s||u.set("offset",l/this.duration),i.push(u)});const r=[...e.values()],a=[...t.values()];if(s){const o=i[0],l=new Map(o);o.set("offset",0),l.set("offset",1),i=[o,l]}return Oe(this.element,i,r,a,this.duration,this.startTime,this.easing,!1)}}class Ks extends me{constructor(e,t,s,i,r,a,o=!1){super(e,t,a.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=o,this.timings={duration:a.duration,delay:a.delay,easing:a.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){const r=[],a=s+t,o=t/a,l=new Map(e[0]);l.set("offset",0),r.push(l);const u=new Map(e[0]);u.set("offset",ht(o)),r.push(u);const h=e.length-1;for(let c=1;c<=h;c++){let E=new Map(e[c]);const _=E.get("offset");E.set("offset",ht((t+_*s)/a)),r.push(E)}s=a,t=0,i="",e=r}return Oe(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}}function ht(n,e=3){const t=Math.pow(10,e-1);return Math.round(n*t)/t}function ct(n,e,t,s,i,r,a,o,l,u,h,c,E){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:a,timelines:o,queriedElements:l,preStyleProps:u,postStyleProps:h,totalTime:c,errors:E}}const Fe={};class ft{constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return function qs(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return void 0!==e&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,a,o,l,u,h){const c=[],E=this.ast.options&&this.ast.options.params||Fe,m=this.buildStyles(s,o&&o.params||Fe,c),y=l&&l.params||Fe,w=this.buildStyles(i,y,c),b=new Set,A=new Map,C=new Map,N="void"===i,Z={params:dt(y,E),delay:this.ast.options?.delay},B=h?[]:Ie(e,t,this.ast.animation,r,a,m,w,Z,u,c);let k=0;return B.forEach(D=>{k=Math.max(D.duration+D.delay,k)}),c.length?ct(t,this._triggerName,s,i,N,m,w,[],[],A,C,k,c):(B.forEach(D=>{const G=D.element,J=O(A,G,new Set);D.preStyleProps.forEach(H=>J.add(H));const At=O(C,G,new Set);D.postStyleProps.forEach(H=>At.add(H)),G!==t&&b.add(G)}),ct(t,this._triggerName,s,i,N,m,w,B,[...b.values()],A,C,k))}}function dt(n,e){const t={...e};return Object.entries(n).forEach(([s,i])=>{null!=i&&(t[s]=i)}),t}class Qs{constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){const s=new Map,i=dt(e,this.defaultParams);return this.styles.styles.forEach(r=>{"string"!=typeof r&&r.forEach((a,o)=>{a&&(a=ee(a,i,t));const l=this.normalizer.normalizePropertyName(o,t);a=this.normalizer.normalizeStyleValue(o,l,a,t),s.set(o,a)})}),s}}class Vs{constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,this.transitionFactories=[],this.states=new Map,t.states.forEach(i=>{this.states.set(i.name,new Qs(i.style,i.options&&i.options.params||{},s))}),mt(this.states,"true","1"),mt(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new ft(e,i,this.states))}),this.fallbackTransition=function Us(n,e,t){return new ft(n,{type:d.If.Transition,animation:{type:d.If.Sequence,steps:[],options:null},matchers:[(a,o)=>!0],options:null,queryCount:0,depCount:0},e)}(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(a=>a.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}}function mt(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}const Ws=new fe;class js{constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s,this._animations=new Map,this._playersById=new Map,this.players=[]}register(e,t){const s=[],r=ke(this._driver,t,s,[]);if(s.length)throw function Jt(n){return new S.wOt(3503,!1)}();this._animations.set(e,r)}_buildPlayer(e,t,s){const i=e.element,r=He(this._normalizer,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){const i=[],r=this._animations.get(e);let a;const o=new Map;if(r?(a=Ie(this._driver,t,r,be,re,new Map,new Map,s,Ws,i),a.forEach(h=>{const c=O(o,h.element,new Map);h.postStyleProps.forEach(E=>c.set(E,null))})):(i.push(function xt(){return new S.wOt(3300,!1)}()),a=[]),i.length)throw function es(n){return new S.wOt(3504,!1)}();o.forEach((h,c)=>{h.forEach((E,_)=>{h.set(_,this._driver.computeStyle(c,_,d.kp))})});const u=$(a.map(h=>{const c=o.get(h.element);return this._buildPlayer(h,new Map,c)}));return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){const t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);const s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){const t=this._playersById.get(e);if(!t)throw function ts(n){return new S.wOt(3301,!1)}();return t}listen(e,t,s,i){const r=Se(t,"","","");return _e(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if("register"==s)return void this.register(e,i[0]);if("create"==s)return void this.create(e,t,i[0]||{});const r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e)}}}const pt="ng-animate-queued",Le="ng-animate-disabled",Zs=[],gt={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},Js={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},z="__ng_removed";class ze{get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;const s=e&&e.hasOwnProperty("value");if(this.value=function si(n){return n??null}(s?e.value:e),s){const{value:r,...a}=e;this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){const t=e.params;if(t){const s=this.options.params;Object.keys(t).forEach(i=>{null==s[i]&&(s[i]=t[i])})}}}const te="void",Ke=new ze(te);class xs{constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+e,F(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw function ss(n,e){return new S.wOt(3302,!1)}();if(null==s||0==s.length)throw function is(n){return new S.wOt(3303,!1)}();if(!function ii(n){return"start"==n||"done"==n}(s))throw function ns(n,e){return new S.wOt(3400,!1)}();const r=O(this._elementListeners,e,[]),a={name:t,phase:s,callback:i};r.push(a);const o=O(this._engine.statesByElement,e,new Map);return o.has(t)||(F(e,ae),F(e,ae+"-"+t),o.set(t,Ke)),()=>{this._engine.afterFlush(()=>{const l=r.indexOf(a);l>=0&&r.splice(l,1),this._triggers.has(t)||o.delete(t)})}}register(e,t){return!this._triggers.has(e)&&(this._triggers.set(e,t),!0)}_getTrigger(e){const t=this._triggers.get(e);if(!t)throw function rs(n){return new S.wOt(3401,!1)}();return t}trigger(e,t,s,i=!0){const r=this._getTrigger(t),a=new Be(this.id,t,e);let o=this._engine.statesByElement.get(e);o||(F(e,ae),F(e,ae+"-"+t),this._engine.statesByElement.set(e,o=new Map));let l=o.get(t);const u=new ze(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),o.set(t,u),l||(l=Ke),u.value!==te&&l.value===u.value){if(!function ai(n,e){const t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){const r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}(l.params,u.params)){const y=[],w=r.matchStyles(l.value,l.params,y),b=r.matchStyles(u.value,u.params,y);y.length?this._engine.reportError(y):this._engine.afterFlush(()=>{W(e,w),K(e,b)})}return}const E=O(this._engine.playersByElement,e,[]);E.forEach(y=>{y.namespaceId==this.id&&y.triggerName==t&&y.queued&&y.destroy()});let _=r.matchTransition(l.value,u.value,e,u.params),m=!1;if(!_){if(!i)return;_=r.fallbackTransition,m=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:_,fromState:l,toState:u,player:a,isFallbackTransition:m}),m||(F(e,pt),a.onStart(()=>{Y(e,pt)})),a.onDone(()=>{let y=this.players.indexOf(a);y>=0&&this.players.splice(y,1);const w=this._engine.playersByElement.get(e);if(w){let b=w.indexOf(a);b>=0&&w.splice(b,1)}}),this.players.push(a),E.push(a),a}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);const t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){const s=this._engine.driver.query(e,oe,!0);s.forEach(i=>{if(i[z])return;const r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(a=>a.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){const r=this._engine.statesByElement.get(e),a=new Map;if(r){const o=[];if(r.forEach((l,u)=>{if(a.set(u,l.value),this._triggers.has(u)){const h=this.trigger(e,u,te,i);h&&o.push(h)}}),o.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,a),s&&$(o).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){const t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){const i=new Set;t.forEach(r=>{const a=r.name;if(i.has(a))return;i.add(a);const l=this._triggers.get(a).fallbackTransition,u=s.get(a)||Ke,h=new ze(te),c=new Be(this.id,a,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:a,transition:l,fromState:u,toState:h,player:c,isFallbackTransition:!0})})}}removeNode(e,t){const s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){const r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let a=e;for(;a=a.parentNode;)if(s.statesByElement.get(a)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{const r=e[z];(!r||r===gt)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){F(e,this._hostClassName)}drainQueuedTransitions(e){const t=[];return this._queue.forEach(s=>{const i=s.player;if(i.destroyed)return;const r=s.element,a=this._elementListeners.get(r);a&&a.forEach(o=>{if(o.name==s.triggerName){const l=Se(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,_e(s.player,o.phase,l,o.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{const r=s.transition.ast.depCount,a=i.transition.ast.depCount;return 0==r||0==a?r-a:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}}class ei{_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s,i){this.bodyNode=e,this.driver=t,this._normalizer=s,this.scheduler=i,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(r,a)=>{}}get queuedPlayers(){const e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){const s=new xs(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){const s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let a=!1,o=this.driver.getParentElement(t);for(;o;){const l=i.get(o);if(l){const u=s.indexOf(l);s.splice(u+1,0,e),a=!0;break}o=this.driver.getParentElement(o)}a||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{const s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);const i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){const t=new Set,s=this.statesByElement.get(e);if(s)for(let i of s.values())if(i.namespaceId){const r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}return t}trigger(e,t,s,i){if(pe(t)){const r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!pe(t))return;const r=t[z];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;const a=this.collectedLeaveElements.indexOf(t);a>=0&&this.collectedLeaveElements.splice(a,1)}if(e){const a=this._fetchNamespace(e);a&&a.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),F(e,Le)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),Y(e,Le))}removeNode(e,t,s){if(pe(t)){this.scheduler?.notify();const i=e?this._fetchNamespace(e):null;i?i.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);const r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[z]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return pe(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,oe,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),0!=this.playersByQueriedElement.size&&(t=this.driver.query(e,Ae,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){const t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){const t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return $(this.players).onDone(()=>e());e()})}processLeaveNode(e){const t=e[z];if(t&&t.setForRemoval){if(e[z]=gt,t.namespaceId){this.destroyInnerAnimations(e);const s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Le)&&this.markElementAsDisabled(e,!1),this.driver.query(e,".ng-animate-disabled",!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++)F(this.collectedEnterElements[s],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++)this.processLeaveNode(this.collectedLeaveElements[s]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){const s=this._whenQuietFns;this._whenQuietFns=[],t.length?$(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw function as(n){return new S.wOt(3402,!1)}()}_flushAnimations(e,t){const s=new fe,i=[],r=new Map,a=[],o=new Map,l=new Map,u=new Map,h=new Set;this.disabledNodes.forEach(f=>{h.add(f);const p=this.driver.query(f,".ng-animate-queued",!0);for(let g=0;g<p.length;g++)h.add(p[g])});const c=this.bodyNode,E=Array.from(this.statesByElement.keys()),_=Et(E,this.collectedEnterElements),m=new Map;let y=0;_.forEach((f,p)=>{const g=be+y++;m.set(p,g),f.forEach(T=>F(T,g))});const w=[],b=new Set,A=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){const p=this.collectedLeaveElements[f],g=p[z];g&&g.setForRemoval&&(w.push(p),b.add(p),g.hasAnimation?this.driver.query(p,".ng-star-inserted",!0).forEach(T=>b.add(T)):A.add(p))}const C=new Map,N=Et(E,Array.from(b));N.forEach((f,p)=>{const g=re+y++;C.set(p,g),f.forEach(T=>F(T,g))}),e.push(()=>{_.forEach((f,p)=>{const g=m.get(p);f.forEach(T=>Y(T,g))}),N.forEach((f,p)=>{const g=C.get(p);f.forEach(T=>Y(T,g))}),w.forEach(f=>{this.processLeaveNode(f)})});const Z=[],B=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(g=>{const T=g.player,P=g.element;if(Z.push(T),this.collectedEnterElements.length){const M=P[z];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(g.triggerName)){const X=M.previousTriggersValues.get(g.triggerName),L=this.statesByElement.get(g.element);if(L&&L.has(g.triggerName)){const ye=L.get(g.triggerName);ye.value=X,L.set(g.triggerName,ye)}}return void T.destroy()}}const q=!c||!this.driver.containsElement(c,P),R=C.get(P),V=m.get(P),v=this._buildInstruction(g,s,V,R,q);if(v.errors&&v.errors.length)return void B.push(v);if(q)return T.onStart(()=>W(P,v.fromStyles)),T.onDestroy(()=>K(P,v.toStyles)),void i.push(T);if(g.isFallbackTransition)return T.onStart(()=>W(P,v.fromStyles)),T.onDestroy(()=>K(P,v.toStyles)),void i.push(T);const Mt=[];v.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||Mt.push(M)}),v.timelines=Mt,s.append(P,v.timelines),a.push({instruction:v,player:T,element:P}),v.queriedElements.forEach(M=>O(o,M,[]).push(T)),v.preStyleProps.forEach((M,X)=>{if(M.size){let L=l.get(X);L||l.set(X,L=new Set),M.forEach((ye,We)=>L.add(We))}}),v.postStyleProps.forEach((M,X)=>{let L=u.get(X);L||u.set(X,L=new Set),M.forEach((ye,We)=>L.add(We))})});if(B.length){const f=[];B.forEach(p=>{f.push(function os(n,e){return new S.wOt(3505,!1)}())}),Z.forEach(p=>p.destroy()),this.reportError(f)}const k=new Map,D=new Map;a.forEach(f=>{const p=f.element;s.has(p)&&(D.set(p,p),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,k))}),i.forEach(f=>{const p=f.element;this._getPreviousPlayers(p,!1,f.namespaceId,f.triggerName,null).forEach(T=>{O(k,p,[]).push(T),T.destroy()})});const G=w.filter(f=>Tt(f,l,u)),J=new Map;_t(J,this.driver,A,u,d.kp).forEach(f=>{Tt(f,l,u)&&G.push(f)});const H=new Map;_.forEach((f,p)=>{_t(H,this.driver,new Set(f),l,d.FX)}),G.forEach(f=>{const p=J.get(f),g=H.get(f);J.set(f,new Map([...p?.entries()??[],...g?.entries()??[]]))});const Ue=[],Pt=[],Nt={};a.forEach(f=>{const{element:p,player:g,instruction:T}=f;if(s.has(p)){if(h.has(p))return g.onDestroy(()=>K(p,T.toStyles)),g.disabled=!0,g.overrideTotalTime(T.totalTime),void i.push(g);let P=Nt;if(D.size>1){let R=p;const V=[];for(;R=R.parentNode;){const v=D.get(R);if(v){P=v;break}V.push(R)}V.forEach(v=>D.set(v,P))}const q=this._buildAnimation(g.namespaceId,T,k,r,H,J);if(g.setRealPlayer(q),P===Nt)Ue.push(g);else{const R=this.playersByElement.get(P);R&&R.length&&(g.parentPlayer=$(R)),i.push(g)}}else W(p,T.fromStyles),g.onDestroy(()=>K(p,T.toStyles)),Pt.push(g),h.has(p)&&i.push(g)}),Pt.forEach(f=>{const p=r.get(f.element);if(p&&p.length){const g=$(p);f.setRealPlayer(g)}}),i.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<w.length;f++){const p=w[f],g=p[z];if(Y(p,re),g&&g.hasAnimation)continue;let T=[];if(o.size){let q=o.get(p);q&&q.length&&T.push(...q);let R=this.driver.query(p,Ae,!0);for(let V=0;V<R.length;V++){let v=o.get(R[V]);v&&v.length&&T.push(...v)}}const P=T.filter(q=>!q.destroyed);P.length?ni(this,p,P):this.processLeaveNode(p)}return w.length=0,Ue.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();const p=this.players.indexOf(f);this.players.splice(p,1)}),f.play()}),Ue}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let a=[];if(t){const o=this.playersByQueriedElement.get(e);o&&(a=o)}else{const o=this.playersByElement.get(e);if(o){const l=!r||r==te;o.forEach(u=>{u.queued||!l&&u.triggerName!=i||a.push(u)})}}return(s||i)&&(a=a.filter(o=>!(s&&s!=o.namespaceId||i&&i!=o.triggerName))),a}_beforeAnimationBuild(e,t,s){const r=t.element,a=t.isRemovalTransition?void 0:e,o=t.isRemovalTransition?void 0:t.triggerName;for(const l of t.timelines){const u=l.element,h=u!==r,c=O(s,u,[]);this._getPreviousPlayers(u,h,a,o,t.toState).forEach(_=>{const m=_.getRealPlayer();m.beforeDestroy&&m.beforeDestroy(),_.destroy(),c.push(_)})}W(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,a){const o=t.triggerName,l=t.element,u=[],h=new Set,c=new Set,E=t.timelines.map(m=>{const y=m.element;h.add(y);const w=y[z];if(w&&w.removedBeforeQueried)return new d.sf(m.duration,m.delay);const b=y!==l,A=function ri(n){const e=[];return St(n,e),e}((s.get(y)||Zs).map(k=>k.getRealPlayer())).filter(k=>!!k.element&&k.element===y),C=r.get(y),N=a.get(y),Z=He(this._normalizer,m.keyframes,C,N),B=this._buildPlayer(m,Z,A);if(m.subTimeline&&i&&c.add(y),b){const k=new Be(e,o,y);k.setRealPlayer(B),u.push(k)}return B});u.forEach(m=>{O(this.playersByQueriedElement,m.element,[]).push(m),m.onDone(()=>function ti(n,e,t){let s=n.get(e);if(s){if(s.length){const i=s.indexOf(t);s.splice(i,1)}0==s.length&&n.delete(e)}return s}(this.playersByQueriedElement,m.element,m))}),h.forEach(m=>F(m,tt));const _=$(E);return _.onDestroy(()=>{h.forEach(m=>Y(m,tt)),K(l,t.toStyles)}),c.forEach(m=>{O(i,m,[]).push(_)}),_}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new d.sf(e.duration,e.delay)}}class Be{constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s,this._player=new d.sf,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>_e(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){const t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){O(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){const t=this._player;t.triggerCallback&&t.triggerCallback(e)}}function pe(n){return n&&1===n.nodeType}function yt(n,e){const t=n.style.display;return n.style.display=e??"none",t}function _t(n,e,t,s,i){const r=[];t.forEach(l=>r.push(yt(l)));const a=[];s.forEach((l,u)=>{const h=new Map;l.forEach(c=>{const E=e.computeStyle(u,c,i);h.set(c,E),(!E||0==E.length)&&(u[z]=Js,a.push(u))}),n.set(u,h)});let o=0;return t.forEach(l=>yt(l,r[o++])),a}function Et(n,e){const t=new Map;if(n.forEach(o=>t.set(o,[])),0==e.length)return t;const i=new Set(e),r=new Map;function a(o){if(!o)return 1;let l=r.get(o);if(l)return l;const u=o.parentNode;return l=t.has(u)?u:i.has(u)?1:a(u),r.set(o,l),l}return e.forEach(o=>{const l=a(o);1!==l&&t.get(l).push(o)}),t}function F(n,e){n.classList?.add(e)}function Y(n,e){n.classList?.remove(e)}function ni(n,e,t){$(t).onDone(()=>n.processLeaveNode(e))}function St(n,e){for(let t=0;t<n.length;t++){const s=n[t];s instanceof d.ui?St(s.players,e):e.push(s)}}function Tt(n,e,t){const s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}class qe{constructor(e,t,s,i){this._driver=t,this._normalizer=s,this._triggerCache={},this.onRemovalComplete=(r,a)=>{},this._transitionEngine=new ei(e.body,t,s,i),this._timelineEngine=new js(e.body,t,s),this._transitionEngine.onRemovalComplete=(r,a)=>this.onRemovalComplete(r,a)}registerTrigger(e,t,s,i,r){const a=e+"-"+i;let o=this._triggerCache[a];if(!o){const l=[],h=ke(this._driver,r,l,[]);if(l.length)throw function Yt(n,e){return new S.wOt(3404,!1)}();o=function $s(n,e,t){return new Vs(n,e,t)}(i,h,this._normalizer),this._triggerCache[a]=o}this._transitionEngine.registerTrigger(t,i,o)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if("@"==s.charAt(0)){const[r,a]=Xe(s);this._timelineEngine.command(r,t,a,i)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if("@"==s.charAt(0)){const[a,o]=Xe(s);return this._timelineEngine.listen(a,t,o,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}}class se{static{this.initialStylesByElement=new WeakMap}constructor(e,t,s){this._element=e,this._startStyles=t,this._endStyles=s,this._state=0;let i=se.initialStylesByElement.get(e);i||se.initialStylesByElement.set(e,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&K(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(K(this._element,this._initialStyles),this._endStyles&&(K(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(se.initialStylesByElement.delete(this._element),this._startStyles&&(W(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(W(this._element,this._endStyles),this._endStyles=null),K(this._element,this._initialStyles),this._state=3)}}function Qe(n){let e=null;return n.forEach((t,s)=>{(function li(n){return"display"===n||"position"===n})(s)&&(e=e||new Map,e.set(s,t))}),e}class $e{constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;const t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){const t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{"offset"!==i&&e.set(i,this._finished?s:Ce(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){const t="start"===e?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}}class wt{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}matchesElement(e,t){return!1}containsElement(e,t){return Te(e,t)}getParentElement(e){return ne(e)}query(e,t,s){return we(e,t,s)}computeStyle(e,t,s){return Ce(e,t)}animate(e,t,s,i,r,a=[]){const l={duration:s,delay:i,fill:0==i?"both":"forwards"};r&&(l.easing=r);const u=new Map,h=a.filter(_=>_ instanceof $e);rt(s,i)&&h.forEach(_=>{_.currentSnapshot.forEach((m,y)=>u.set(y,m))});let c=st(t).map(_=>new Map(_));c=function Ss(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,a)=>{s.has(a)||i.push(a),s.set(a,r)}),i.length)for(let r=1;r<e.length;r++){let a=e[r];i.forEach(o=>a.set(o,Ce(n,o)))}}return e}(e,c,u);const E=function oi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=Qe(e[0]),e.length>1&&(s=Qe(e[e.length-1]))):e instanceof Map&&(t=Qe(e)),t||s?new se(n,t,s):null}(e,c);return new $e(e,c,l,E)}}function ui(n,e,t){return"noop"===n?new qe(e,new ve,new xe,t):new qe(e,new wt,new at,t)}class hi{constructor(e,t){this._driver=e;const s=[],r=ke(e,t,s,[]);if(s.length)throw function Ht(n){return new S.wOt(3500,!1)}();this._animationAst=r}buildTimelines(e,t,s,i,r){const a=Array.isArray(t)?it(t):t,o=Array.isArray(s)?it(s):s,l=[];r=r||new fe;const u=Ie(this._driver,e,this._animationAst,be,re,a,o,i,r,l);if(l.length)throw function Xt(n){return new S.wOt(3501,!1)}();return u}}const vt="@.disabled";class Ve{constructor(e,t,s,i){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=i,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,i=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,s){this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,i){this.delegate.setAttribute(e,t,s,i)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,i){this.delegate.setStyle(e,t,s,i)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){"@"==t.charAt(0)&&t==vt?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s){return this.delegate.listen(e,t,s)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}}class bt extends Ve{constructor(e,t,s,i,r){super(t,s,i,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){"@"==t.charAt(0)?"."==t.charAt(1)&&t==vt?this.disableAnimations(e,s=void 0===s||!!s):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s){if("@"==t.charAt(0)){const i=function ci(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}(e);let r=t.slice(1),a="";return"@"!=r.charAt(0)&&([r,a]=function fi(n){const e=n.indexOf(".");return[n.substring(0,e),n.slice(e+1)]}(r)),this.engine.listen(this.namespaceId,i,r,a,o=>{this.factory.scheduleListenerCallback(o._data||-1,s,o)})}return this.delegate.listen(e,t,s)}}class di{constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,t.onRemovalComplete=(i,r)=>{const a=r?.parentNode(i);a&&r.removeChild(a,i)}}createRenderer(e,t){const i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){const u=this._rendererCache;let h=u.get(i);return h||(h=new Ve("",i,this.engine,()=>u.delete(i)),u.set(i,h)),h}const r=t.id,a=t.id+"-"+this._currentId;this._currentId++,this.engine.register(a,e);const o=u=>{Array.isArray(u)?u.forEach(o):this.engine.registerTrigger(r,a,e,u.name,u)};return t.data.animation.forEach(o),new bt(this,a,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId)return void this._zone.run(()=>t(s));const i=this._animationCallbacksBuffer;0==i.length&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{const[a,o]=r;a(o)}),this._animationCallbacksBuffer=[]})}),i.push([t,s])}end(){this._cdRecurDepth--,0==this._cdRecurDepth&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}}}}]);
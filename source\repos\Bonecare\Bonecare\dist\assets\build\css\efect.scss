
.basic-pulses{
  -webkit-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  -moz-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  -ms-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
}

.basic-pulses-red{
  -webkit-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  -moz-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  -ms-animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  animation: pulse 1.25s infinite cubic-bezier(0.66, 0, 0, 1);
  
}

.basic-pulses:hover{
  -webkit-animation: none;-moz-animation: none;-ms-animation: none;animation: none;
  box-shadow:0 4px 10px 0 rgba(0,0,0,0.2), 0 4px 20px 0 rgba(0,0,0,0.19);
  transition: all 3s;
  
}


@-webkit-keyframes pulse {to {box-shadow: 0 0 0 45px rgb(255,  204, 204)}}
@-moz-keyframes pulse {to {box-shadow: 0 0 0 45px rgb(255, 204, 204)}}
// @-ms-keyframes pulse {to {box-shadow: 0 0 0 45px red}}
@keyframes pulse {to {box-shadow: 0 0 0 45px rgb(255, 204, 204)}}
 
.shake:hover{
    animation: shake 0.8s;  
    cursor:pointer;
    animation-iteration-count: infinite; 
}

@keyframes shake {
  0% { transform: translate(1px, 1px) rotate(0deg); }
  10% { transform: translate(-1px, -2px) rotate(-1deg); }
  20% { transform: translate(-3px, 0px) rotate(1deg); }
  30% { transform: translate(3px, 2px) rotate(0deg); }
  40% { transform: translate(1px, -1px) rotate(1deg); }
  50% { transform: translate(-1px, 2px) rotate(-1deg); }
  60% { transform: translate(-3px, 1px) rotate(0deg); }
  70% { transform: translate(3px, 1px) rotate(-1deg); }
  80% { transform: translate(-1px, -1px) rotate(1deg); }
  90% { transform: translate(1px, 2px) rotate(0deg); }
  100% { transform: translate(1px, -2px) rotate(-1deg); }
}

* {
    box-sizing:border-box;
  }
  
  html,body {
  
    &.modal-active {
      overflow: hidden;
    }
  }
  
  #modal-container {
    position:fixed;
    display:table;
    height:100%;
    width:100%;
    top:0;
    left:0;
    transform:scale(0);
    z-index:1;
   
    &.one {
      transform:scale(1);
      .modal-background {
        background:rgba(0,0,0,.0);
        animation: fadeIn .5s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
        .modal {
          transform:translateX(-1500px);
          animation: roadRunnerIn .3s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
        }
      }
      &.out {
        animation: quickScaleDown 0s .5s linear forwards;
        .modal-background {
          animation: fadeOut .5s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
          .modal {
            animation: roadRunnerOut .5s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
          }
        }
      }
    }
    
    .modal-background {
      display:table-cell;
      background:rgba(0,0,0,.8);
      text-align:center;
      vertical-align:middle;
      .modal {
        background:white;
        padding:50px;
        display:inline-block;
        border-radius:3px;
        font-weight:300;
        position:relative;
        h2 {
          font-size:25px;
          line-height:25px;
          margin-bottom:15px;
        }
        p {
          font-size:18px;
          line-height:22px;
        }
        .modal-svg {
          position:absolute;
          top:0;
          left:0;
          height:100%;
          width:100%;
          border-radius:3px;
          rect {
            stroke: #fff;
            stroke-width: 2px;
            stroke-dasharray: 778;
            stroke-dashoffset: 778;
          }
        }
      }
    }
  }
  
  .content {
    position:relative;
    z-index:0;
    h1 {
      padding:75px 0 30px 0;
      text-align:center;
      font-size:30px;
      line-height:30px;
    }
    .buttons {
      max-width:800px;
      margin:0 auto;
      padding:0;
      text-align:center;
      .button {
        display:inline-block;
        text-align:center;
        padding:10px 15px;
        margin:10px;
        background:red;
        font-size:18px;
        background-color:#efefef;
        border-radius:3px;
        box-shadow:0 1px 2px rgba(0,0,0,.3);
        cursor:pointer;
        &:hover {
          color:white;
          background:#009bd5;
        }
      }
    } 
  }
  
  @keyframes unfoldIn {
    0% {
      transform:scaleY(.005) scaleX(0);
    }
    50% {
      transform:scaleY(.005) scaleX(1);
    }
    100% {
      transform:scaleY(1) scaleX(1);
    }
  }
  
  @keyframes unfoldOut {
    0% {
      transform:scaleY(1) scaleX(1);
    }
    50% {
      transform:scaleY(.005) scaleX(1);
    }
    100% {
      transform:scaleY(.005) scaleX(0);
    }
  }
  
  @keyframes zoomIn {
    0% {
      transform:scale(0);
    }
    100% {
      transform:scale(1);
    }
  }
  
  @keyframes zoomOut {
    0% {
      transform:scale(1);
    }
    100% {
      transform:scale(0);
    }
  }
  
  @keyframes fadeIn {
    0% {
      background:rgba(0,0,0,.0);
    }
    100% {
      background:rgba(0,0,0,.7);
    }
  }
  
  @keyframes fadeOut {
    0% {
      background:rgba(0,0,0,.7);
    }
    100% {
      background:rgba(0,0,0,.0);
    }
  }
  
  @keyframes scaleUp {
    0% {
      transform:scale(.8) translateY(1000px);
      opacity:0;
    }
    100% {
      transform:scale(1) translateY(0px);
      opacity:1;
    }
  }
  
  @keyframes scaleDown {
    0% {
      transform:scale(1) translateY(0px);
      opacity:1;
    }
    100% {
      transform:scale(.8) translateY(1000px);
      opacity:0;
    }
  }
  
  @keyframes scaleBack {
    0% {
      transform:scale(1);
    }
    100% {
      transform:scale(.85);
    }
  }
  
  @keyframes scaleForward {
    0% {
      transform:scale(.85);
    }
    100% {
      transform:scale(1);
    }
  }
  
  @keyframes quickScaleDown {
    0% {
      transform:scale(1);
    }
    99.9% {
      transform:scale(1);
    }
    100% {
      transform:scale(0);
    }
  }
  
  @keyframes slideUpLarge {
    0% {
      transform:translateY(0%);
    }
    100% {
      transform:translateY(-100%);
    }
  }
  
  @keyframes slideDownLarge {
    0% {
      transform:translateY(-100%);
    }
    100% {
      transform:translateY(0%);
    }
  }
  
  @keyframes moveUp {
    0% {
      transform:translateY(150px);
    }
    100% {
      transform:translateY(0);
    }
  }
  
  @keyframes moveDown {
    0% {
      transform:translateY(0px);
    }
    100% {
      transform:translateY(150px);
    }
  }
  
  @keyframes blowUpContent {
    0% {
      transform:scale(1);
      opacity:1;
    }
    99.9% {
      transform:scale(2);
      opacity:0;
    }
    100% {
      transform:scale(0);
    }
  }
  
  @keyframes blowUpContentTwo {
    0% {
      transform:scale(2);
      opacity:0;
    }
    100% {
      transform:scale(1);
      opacity:1;
    }
  }
  
  @keyframes blowUpModal {
    0% {
      transform:scale(0);
    }
    100% {
      transform:scale(1);
    }
  }
  
  @keyframes blowUpModalTwo {
    0% {
      transform:scale(1);
      opacity:1;
    }
    100% {
      transform:scale(0);
      opacity:0;
    }
  }
  
  @keyframes roadRunnerIn {
    0% {
      transform:translateX(-1500px) skewX(30deg) scaleX(1.3);
    }
    70% {
      transform:translateX(30px) skewX(0deg) scaleX(.9);
    }
    100% {
      transform:translateX(0px) skewX(0deg) scaleX(1);
    }
  }
  
  @keyframes roadRunnerOut {
    0% {
      transform:translateX(0px) skewX(0deg) scaleX(1);
    }
    30% {
      transform:translateX(-30px) skewX(-5deg) scaleX(.9);
    }
    100% {
      transform:translateX(1500px) skewX(30deg) scaleX(1.3);
    }
  }
  
  @keyframes sketchIn {
      0% {
          stroke-dashoffset: 778;
      }
      100% {
          stroke-dashoffset: 0;
      }
  }
  
  @keyframes sketchOut {
      0% {
          stroke-dashoffset: 0;
      }
      100% {
          stroke-dashoffset: 778;
      }
  }
  
  @keyframes modalFadeIn {
    0% {
      background-color:transparent;
    }
    100% {
      background-color:white;
    }
  }
  
  @keyframes modalFadeOut {
    0% {
      background-color:white;
    }
    100% {
      background-color:transparent;
    }
  }
  
  @keyframes modalContentFadeIn {
    0% {
      opacity:0;
      top:-20px;
    }
    100% {
      opacity:1;
      top:0;
    }
  }
  
  @keyframes modalContentFadeOut {
    0% {
      opacity:1;
      top:0px;
    }
    100% {
      opacity:0;
      top:-20px;
    }
  }
  
  @keyframes bondJamesBond {
    0% {
      transform:translateX(1000px);
    }
    80% {
      transform:translateX(0px);
      border-radius:75px;
      height:75px;
      width:75px;
    }
    90% {
      border-radius:3px;
      height:182px;
      width:247px;
    }
    100% {
      border-radius:3px;
      height:162px;
      width:227px;
    }
  }
  
  @keyframes killShot {
    0% {
      transform:translateY(0) rotate(0deg);
      opacity:1;
    }
    100% {
      transform:translateY(300px) rotate(45deg);
      opacity:0;
    }
  }
  
  @keyframes fadeToRed {
    0% {
      box-shadow:inset 0 0 0 rgba(201,24,24,.8);
    }
    100% {
      box-shadow:inset 0 2000px 0 rgba(201,24,24,.8);
    }
  }
  
  @keyframes slowFade {
    0% {
      opacity:1;
    }
    99.9% {
      opacity:0;
      transform:scale(1);
    }
    100% {
      transform:scale(0);
    }
  }
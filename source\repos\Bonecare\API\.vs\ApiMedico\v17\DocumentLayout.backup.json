{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4F061063-1795-42EA-91DC-8C5EA2BDF7E5}|ApiMedico\\ApiMedico.csproj|c:\\users\\<USER>\\source\\repos\\bonecare\\api\\apimedico\\controllers\\aiquestionarioapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4F061063-1795-42EA-91DC-8C5EA2BDF7E5}|ApiMedico\\ApiMedico.csproj|solutionrelative:apimedico\\controllers\\aiquestionarioapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BE50153F-2E32-4332-8B51-B6324EFE6338}|MedicoRepository\\MedicoRepository.csproj|c:\\users\\<USER>\\source\\repos\\bonecare\\api\\medicorepository\\repository\\relatorioexameapirepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BE50153F-2E32-4332-8B51-B6324EFE6338}|MedicoRepository\\MedicoRepository.csproj|solutionrelative:medicorepository\\repository\\relatorioexameapirepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4F061063-1795-42EA-91DC-8C5EA2BDF7E5}|ApiMedico\\ApiMedico.csproj|c:\\users\\<USER>\\source\\repos\\bonecare\\api\\apimedico\\controllers\\relatorioexameapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4F061063-1795-42EA-91DC-8C5EA2BDF7E5}|ApiMedico\\ApiMedico.csproj|solutionrelative:apimedico\\controllers\\relatorioexameapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F78318E3-F29C-45A6-AD20-5F758D3F24F8}|MedicoService\\MedicoService.csproj|c:\\users\\<USER>\\source\\repos\\bonecare\\api\\medicoservice\\service\\relatorioexameapiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F78318E3-F29C-45A6-AD20-5F758D3F24F8}|MedicoService\\MedicoService.csproj|solutionrelative:medicoservice\\service\\relatorioexameapiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:129:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:130:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AiQuestionarioApiController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\ApiMedico\\Controllers\\AiQuestionarioApiController.cs", "RelativeDocumentMoniker": "ApiMedico\\Controllers\\AiQuestionarioApiController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\ApiMedico\\Controllers\\AiQuestionarioApiController.cs", "RelativeToolTip": "ApiMedico\\Controllers\\AiQuestionarioApiController.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAAACAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T19:51:59.173Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RelatorioExameApiRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\MedicoRepository\\Repository\\RelatorioExameApiRepository.cs", "RelativeDocumentMoniker": "MedicoRepository\\Repository\\RelatorioExameApiRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\MedicoRepository\\Repository\\RelatorioExameApiRepository.cs", "RelativeToolTip": "MedicoRepository\\Repository\\RelatorioExameApiRepository.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAAFUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T11:57:09.181Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "RelatorioExameApiController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\ApiMedico\\Controllers\\RelatorioExameApiController.cs", "RelativeDocumentMoniker": "ApiMedico\\Controllers\\RelatorioExameApiController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\ApiMedico\\Controllers\\RelatorioExameApiController.cs", "RelativeToolTip": "ApiMedico\\Controllers\\RelatorioExameApiController.cs", "ViewState": "AgIAAE4AAAAAAAAAAAApwHMAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T11:57:09.167Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "RelatorioExameApiService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\MedicoService\\Service\\RelatorioExameApiService.cs", "RelativeDocumentMoniker": "MedicoService\\Service\\RelatorioExameApiService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Bonecare\\API\\MedicoService\\Service\\RelatorioExameApiService.cs", "RelativeToolTip": "MedicoService\\Service\\RelatorioExameApiService.cs", "ViewState": "AgIAABgAAAAAAAAAAAASwDMAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T11:57:09.153Z", "EditorCaption": ""}]}]}]}
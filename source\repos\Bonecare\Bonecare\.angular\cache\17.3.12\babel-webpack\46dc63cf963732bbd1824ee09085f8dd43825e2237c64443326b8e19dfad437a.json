{"ast": null, "code": "import { ChangeDetectorRef } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/forms\";\nfunction HistoryModalDialogComponent_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HistoryModalDialogComponent_mat_chip_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 39);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_mat_chip_option_39_Template_mat_chip_option_click_0_listener() {\n      const filter_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleFilter(filter_r4));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r4 = ctx.$implicit;\n    i0.ɵɵclassMap(\"filter-chip-\" + filter_r4.type);\n    i0.ɵɵproperty(\"selected\", filter_r4.active);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r4.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r4.label, \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"search_off\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"h3\", 42);\n    i0.ɵɵtext(5, \"Nenhuma informa\\u00E7\\u00E3o encontrada\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 43);\n    i0.ɵɵtext(7, \" Tente ajustar os filtros ou termo de busca \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HistoryModalDialogComponent_div_43_div_1_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatTimestamp(item_r6.timestamp), \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 49)(6, \"h4\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 52)(11, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_div_43_div_1_Template_button_click_11_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.copyToClipboard(item_r6.value));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"content_copy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_div_43_div_1_Template_button_click_14_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editValue(item_r6));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"edit\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 55)(18, \"div\", 56);\n    i0.ɵɵelement(19, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 58);\n    i0.ɵɵtemplate(21, HistoryModalDialogComponent_div_43_div_1_span_21_Template, 4, 1, \"span\", 59);\n    i0.ɵɵelementStart(22, \"span\", 60)(23, \"mat-icon\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"card-\" + item_r6.category);\n    i0.ɵɵproperty(\"@cardAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r6.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.categoryLabel);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.highlightSearchTerm(item_r6.value), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r6.timestamp);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + item_r6.validationStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getValidationIcon(item_r6.validationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getValidationLabel(item_r6.validationStatus), \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, HistoryModalDialogComponent_div_43_div_1_Template, 26, 12, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getFilteredData())(\"ngForTrackBy\", ctx_r1.trackByFn);\n  }\n}\nexport class HistoryModalDialogComponent {\n  dialogRef;\n  data;\n  searchTerm = '';\n  availableFilters = [{\n    type: 'personal',\n    label: 'Dados Pessoais',\n    icon: 'person',\n    active: true\n  }, {\n    type: 'medical',\n    label: 'Informações Médicas',\n    icon: 'medical_services',\n    active: true\n  }, {\n    type: 'contact',\n    label: 'Contato',\n    icon: 'contact_phone',\n    active: true\n  }, {\n    type: 'optional',\n    label: 'Opcionais',\n    icon: 'info',\n    active: true\n  }];\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onClose() {\n    this.dialogRef.close();\n  }\n  getTotalCampos() {\n    return 10;\n  }\n  getDadosPreenchidos() {\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.data.dadosColetados).filter(key => {\n      const value = this.data.dadosColetados[key];\n      return value && value.trim() !== '';\n    }).map(key => ({\n      label: labels[key],\n      value: this.data.dadosColetados[key]\n    }));\n  }\n  getProgressPercentage() {\n    const total = this.getTotalCampos();\n    const filled = this.getDadosPreenchidos().length;\n    return Math.round(filled / total * 100);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n  }\n  clearSearch() {\n    this.searchTerm = '';\n  }\n  toggleFilter(filter) {\n    filter.active = !filter.active;\n  }\n  getFilteredData() {\n    let data = this.getEnhancedDadosPreenchidos();\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      data = data.filter(item => item.label.toLowerCase().includes(searchLower) || item.value.toLowerCase().includes(searchLower));\n    }\n    const activeCategories = this.availableFilters.filter(f => f.active).map(f => f.type);\n    data = data.filter(item => activeCategories.includes(item.category));\n    return data;\n  }\n  getEnhancedDadosPreenchidos() {\n    const categoryMap = {\n      nome: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'person'\n      },\n      cpf: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'badge'\n      },\n      dataNascimento: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'cake'\n      },\n      email: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'email'\n      },\n      telefone: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'phone'\n      },\n      sintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'medical_services'\n      },\n      intensidadeDor: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'personal_injury'\n      },\n      tempoSintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'schedule'\n      },\n      alergias: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'medical_information'\n      },\n      observacoes: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'description'\n      }\n    };\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.data.dadosColetados).filter(key => {\n      const value = this.data.dadosColetados[key];\n      return value && value.trim() !== '';\n    }).map(key => {\n      const categoryInfo = categoryMap[key] || {\n        category: 'optional',\n        categoryLabel: 'Outros',\n        icon: 'info'\n      };\n      return {\n        label: labels[key] || key,\n        value: this.data.dadosColetados[key],\n        category: categoryInfo.category,\n        categoryLabel: categoryInfo.categoryLabel,\n        icon: categoryInfo.icon,\n        timestamp: new Date(),\n        validationStatus: this.getValidationStatusForField(key)\n      };\n    });\n  }\n  getValidationStatusForField(field) {\n    const value = this.data.dadosColetados[field];\n    if (!value || value.trim() === '') return 'error';\n    if (field === 'email' && !value.includes('@')) return 'warning';\n    if (field === 'cpf' && value.length < 11) return 'warning';\n    return 'valid';\n  }\n  trackByFn(index, item) {\n    console.log('index', index);\n    return item.label + item.value;\n  }\n  highlightSearchTerm(text) {\n    if (!this.searchTerm) return text;\n    const regex = new RegExp(`(${this.searchTerm})`, 'gi');\n    return text.replace(regex, '<mark>$1</mark>');\n  }\n  formatTimestamp(timestamp) {\n    return timestamp.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getValidationIcon(status) {\n    switch (status) {\n      case 'valid':\n        return 'check_circle';\n      case 'warning':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'info';\n    }\n  }\n  getValidationLabel(status) {\n    switch (status) {\n      case 'valid':\n        return 'Válido';\n      case 'warning':\n        return 'Atenção';\n      case 'error':\n        return 'Erro';\n      default:\n        return 'Info';\n    }\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\n    }).catch(() => {\n      if (this.data.snackBar) this.data.snackBar.falhaSnackbar('Erro ao copiar texto');\n    });\n  }\n  editValue(item) {\n    const newValue = prompt(`Editar ${item.label}:`, item.value);\n    if (newValue !== null && newValue !== item.value) {\n      const field = Object.keys(this.data.dadosColetados).find(key => {\n        const labels = {\n          nome: 'Nome',\n          cpf: 'CPF',\n          email: 'Email',\n          telefone: 'Telefone',\n          dataNascimento: 'Data de Nascimento',\n          alergias: 'Alergias',\n          sintomas: 'Sintomas',\n          intensidadeDor: 'Intensidade da Dor',\n          tempoSintomas: 'Tempo dos Sintomas',\n          doencasPrevias: 'Doenças Prévias',\n          observacoes: 'Observações'\n        };\n        return labels[key] === item.label;\n      });\n      if (field) {\n        this.data.dadosColetados[field] = newValue;\n        if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\n        if (this.data.cdr) this.data.cdr.detectChanges();\n      }\n    }\n  }\n  clearAllData() {\n    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\n      Object.keys(this.data.dadosColetados).forEach(key => {\n        this.data.dadosColetados[key] = '';\n      });\n      if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Todos os dados foram limpos');\n      if (this.data.cdr) this.data.cdr.detectChanges();\n    }\n  }\n  exportData(event) {\n    event.stopPropagation();\n    const dataStr = JSON.stringify(this.data.dadosColetados, null, 2);\n    const blob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'dados-preenchidos.json';\n    a.click();\n    window.URL.revokeObjectURL(url);\n  }\n  static ɵfac = function HistoryModalDialogComponent_Factory(t) {\n    return new (t || HistoryModalDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HistoryModalDialogComponent,\n    selectors: [[\"app-history-modal-dialog\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 59,\n    vars: 13,\n    consts: [[\"role\", \"dialog\", \"aria-modal\", \"true\", \"aria-labelledby\", \"modal-title\", \"aria-describedby\", \"modal-description\", 1, \"modern-modal-overlay\", 3, \"click\"], [1, \"modern-modal-container\", 3, \"click\"], [1, \"modal-header-modern\"], [1, \"header-content\"], [1, \"title-section\"], [1, \"icon-wrapper\"], [1, \"header-icon\"], [1, \"title-text\"], [\"id\", \"modal-title\", 1, \"modal-title\"], [\"id\", \"modal-description\", 1, \"modal-subtitle\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Exportar dados\", \"aria-label\", \"Exportar dados coletados\", 1, \"action-btn\", \"secondary\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Fechar modal\", \"aria-label\", \"Fechar modal\", 1, \"action-btn\", \"close-btn\", 3, \"click\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"progress-text\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"search-filter-section\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Digite para buscar...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matPrefix\", \"\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"aria-label\", \"Limpar busca\", 3, \"click\", 4, \"ngIf\"], [1, \"filter-chips\"], [1, \"filter-chip-list\"], [3, \"selected\", \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-body-modern\"], [1, \"content-wrapper\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"data-grid\", 4, \"ngIf\"], [1, \"modal-footer-modern\"], [1, \"footer-info\"], [1, \"info-text\"], [1, \"footer-actions\"], [\"mat-stroked-button\", \"\", 1, \"secondary-btn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"primary-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"aria-label\", \"Limpar busca\", 3, \"click\"], [3, \"click\", \"selected\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"data-grid\"], [\"class\", \"data-card\", 3, \"class\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"data-card\"], [1, \"card-header\"], [1, \"card-icon\"], [1, \"card-title-section\"], [1, \"card-title\"], [1, \"card-category\"], [1, \"card-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copiar valor\", \"aria-label\", \"Copiar valor\", 1, \"card-action-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Editar valor\", \"aria-label\", \"Editar valor\", 1, \"card-action-btn\", 3, \"click\"], [1, \"card-content\"], [1, \"value-container\"], [1, \"card-value\", 3, \"innerHTML\"], [1, \"card-metadata\"], [\"class\", \"timestamp\", 4, \"ngIf\"], [1, \"validation-status\"], [1, \"timestamp\"]],\n    template: function HistoryModalDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_div_click_0_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_div_click_1_listener($event) {\n          return $event.stopPropagation();\n        });\n        i0.ɵɵelementStart(2, \"header\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"mat-icon\", 6);\n        i0.ɵɵtext(7, \"history\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"h2\", 8);\n        i0.ɵɵtext(10, \"Hist\\u00F3rico de Informa\\u00E7\\u00F5es\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\", 9);\n        i0.ɵɵtext(12, \" Dados coletados durante a conversa \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_14_listener($event) {\n          return ctx.exportData($event);\n        });\n        i0.ɵɵelementStart(15, \"mat-icon\");\n        i0.ɵɵtext(16, \"download\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_17_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(18, \"mat-icon\");\n        i0.ɵɵtext(19, \"close\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"div\", 13)(21, \"div\", 14)(22, \"span\", 15);\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"span\", 16);\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 17);\n        i0.ɵɵelement(27, \"div\", 18);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"mat-form-field\", 21)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Buscar informa\\u00E7\\u00E3o\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"input\", 22);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function HistoryModalDialogComponent_Template_input_ngModelChange_33_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n          return $event;\n        });\n        i0.ɵɵlistener(\"input\", function HistoryModalDialogComponent_Template_input_input_33_listener($event) {\n          return ctx.onSearchChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"mat-icon\", 23);\n        i0.ɵɵtext(35, \"search\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(36, HistoryModalDialogComponent_button_36_Template, 3, 0, \"button\", 24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"div\", 25)(38, \"mat-chip-listbox\", 26);\n        i0.ɵɵtemplate(39, HistoryModalDialogComponent_mat_chip_option_39_Template, 4, 5, \"mat-chip-option\", 27);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(40, \"main\", 28)(41, \"div\", 29);\n        i0.ɵɵtemplate(42, HistoryModalDialogComponent_div_42_Template, 8, 0, \"div\", 30)(43, HistoryModalDialogComponent_div_43_Template, 2, 2, \"div\", 31);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"footer\", 32)(45, \"div\", 33)(46, \"span\", 34)(47, \"mat-icon\");\n        i0.ɵɵtext(48, \"info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(49, \" Dados salvos automaticamente \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(50, \"div\", 35)(51, \"button\", 36);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_51_listener() {\n          return ctx.clearAllData();\n        });\n        i0.ɵɵelementStart(52, \"mat-icon\");\n        i0.ɵɵtext(53, \"delete_sweep\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(54, \" Limpar Tudo \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"button\", 37);\n        i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_55_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(56, \"mat-icon\");\n        i0.ɵɵtext(57, \"check\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(58, \" Conclu\\u00EDdo \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"@fadeInOut\", undefined);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"@slideInOut\", undefined);\n        i0.ɵɵadvance(22);\n        i0.ɵɵtextInterpolate2(\" \", ctx.getDadosPreenchidos().length, \" de \", ctx.getTotalCampos(), \" campos preenchidos \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.getProgressPercentage(), \"% \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"width\", ctx.getProgressPercentage(), \"%\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.searchTerm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.availableFilters);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.getFilteredData().length === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.getFilteredData().length > 0);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"disabled\", ctx.getDadosPreenchidos().length === 0);\n      }\n    },\n    dependencies: [CommonModule, i2.NgForOf, i2.NgIf, MatButtonModule, i3.MatButton, i3.MatIconButton, MatIconModule, i4.MatIcon, MatInputModule, i5.MatInput, i6.MatFormField, i6.MatLabel, i6.MatPrefix, i6.MatSuffix, MatFormFieldModule, MatTooltipModule, i7.MatTooltip, MatChipsModule, i8.MatChipListbox, i8.MatChipOption, MatDialogModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, ReactiveFormsModule],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.ai-questionnaire-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  height: 100dvh;\\n  max-height: 100vh;\\n  max-height: 100dvh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n  font-size: clamp(0.75rem, 1.8vw, 0.9rem);\\n  position: relative;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.idle-screen[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n  overflow: hidden;\\n}\\n\\n.idle-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\\n  max-width: min(80vw, 20rem);\\n  width: 100%;\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  max-height: 70vh;\\n  overflow: hidden;\\n}\\n.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: clamp(1rem, 2.5vw, 1.125rem);\\n  font-weight: 700;\\n  margin: clamp(0.25rem, 1vw, 0.5rem) 0 clamp(0.25rem, 0.5vw, 0.25rem) 0;\\n}\\n.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  margin-bottom: clamp(0.5rem, 2vw, 1rem);\\n  line-height: 1.3;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  align-items: center;\\n  width: 100%;\\n}\\n\\n\\n\\n.ai-robot[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(0.9);\\n  }\\n}\\n\\n.robot-head[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 5rem);\\n  height: clamp(3.5rem, 8vw, 5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  position: relative;\\n  margin: 0 auto clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 0.5rem 1.25rem rgba(102, 126, 234, 0.3);\\n}\\n\\n.robot-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: clamp(1rem, 4vw, 1.25rem) clamp(0.5rem, 3vw, 1rem) 0;\\n}\\n\\n.eye[_ngcontent-%COMP%] {\\n  width: clamp(0.5rem, 1.5vw, 0.75rem);\\n  height: clamp(0.5rem, 1.5vw, 0.75rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_blink 3s infinite;\\n}\\n.eye.left-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.1s;\\n}\\n.eye.right-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.2s;\\n}\\n\\n.robot-mouth[_ngcontent-%COMP%] {\\n  width: clamp(1rem, 2.5vw, 1.25rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 0 0 0.625rem 0.625rem;\\n  margin: clamp(0.4rem, 1vw, 0.5rem) auto 0;\\n}\\n\\n.robot-body[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.75rem);\\n  height: clamp(1.5rem, 4vw, 2.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(0.75rem, 2vw, 1rem);\\n  margin: 0 auto;\\n  position: relative;\\n}\\n\\n.robot-chest[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.start-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #f093fb);\\n  border: none;\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(2rem, 5vw, 2.8125rem);\\n  border-radius: 3.125rem;\\n  color: #ffffff;\\n  font-size: clamp(1.125rem, 3vw, 1.5rem);\\n  font-weight: 700;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(79, 172, 254, 0.3);\\n}\\n.start-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-0.125rem);\\n  box-shadow: 0 0.75rem 2.1875rem rgba(79, 172, 254, 0.4);\\n}\\n.start-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.btn-glow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.start-btn[_ngcontent-%COMP%]:hover   .btn-glow[_ngcontent-%COMP%] {\\n  left: 100%;\\n}\\n\\n\\n\\n\\n\\n.chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.controls-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 0.8vw, 0.5rem) clamp(0.5rem, 1.5vw, 1rem);\\n  border-radius: 0 0 clamp(0.5rem, 1.5vw, 1rem) clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  min-height: 44px;\\n}\\n\\n.mode-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: clamp(1.25rem, 2.5vw, 1.5rem);\\n  height: clamp(1.25rem, 2.5vw, 1.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  color: #ffffff;\\n  flex-shrink: 0;\\n}\\n.mode-indicator[_ngcontent-%COMP%]   .mode-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n  width: clamp(0.75rem, 1.5vw, 0.875rem);\\n  height: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.mode-toggle[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 1.2vw, 0.75rem);\\n  white-space: nowrap;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch {\\n  --mdc-switch-track-width: 28px;\\n  --mdc-switch-track-height: 16px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle {\\n  --mdc-switch-handle-width: 12px;\\n  --mdc-switch-handle-height: 12px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__ripple {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n\\n\\n.main-chat-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  padding: clamp(0.5rem, 1.5vw, 1rem) clamp(0.25rem, 1vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n  max-width: 100vw;\\n  margin: 0;\\n  width: 100%;\\n  min-height: 0;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.response-data-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  width: 100%;\\n  align-items: center;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    justify-content: space-between;\\n    gap: clamp(0.5rem, 1.5vw, 1rem);\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    gap: clamp(1rem, 2vw, 1.5rem);\\n  }\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n\\n.ai-avatar[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.5rem);\\n  height: clamp(2.5rem, 6vw, 3.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.ai-avatar.processing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(102, 126, 234, 0.5);\\n}\\n.ai-avatar.listening[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_listening-pulse 1s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(240, 147, 251, 0.5);\\n}\\n.ai-avatar.waiting[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(79, 172, 254, 0.5);\\n}\\n\\n.ai-face[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.5vw, 0.25rem);\\n}\\n\\n.ai-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.25rem, 0.8vw, 0.4rem);\\n}\\n.ai-eyes[_ngcontent-%COMP%]   .eye[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.3rem);\\n  height: clamp(0.2rem, 0.5vw, 0.3rem);\\n  background: #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_ai-blink 4s infinite;\\n}\\n\\n.ai-mouth[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.6rem);\\n  height: clamp(0.15rem, 0.4vw, 0.2rem);\\n  background: #667eea;\\n  border-radius: 0 0 0.3rem 0.3rem;\\n  transition: all 0.3s ease;\\n}\\n.ai-mouth.talking[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_mouth-talk 0.5s infinite alternate;\\n}\\n\\n.ai-pulse[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  left: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  right: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  bottom: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  border: clamp(1px, 0.3vw, 2px) solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse-ring 2s infinite;\\n}\\n\\n\\n\\n.response-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: flex-start;\\n    max-width: none;\\n  }\\n}\\n\\n.ai-message[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  flex-shrink: 0;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem);\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  max-height: 40vh;\\n  overflow-y: auto;\\n}\\n.message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: clamp(0.5rem, 2vw, 1rem);\\n  bottom: clamp(-0.25rem, -0.5vw, -0.3rem);\\n  width: 0;\\n  height: 0;\\n  border-left: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-right: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-top: clamp(0.25rem, 0.8vw, 0.3rem) solid #ffffff;\\n}\\n.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n\\n.processing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 100%;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  color: #718096;\\n  padding: clamp(1rem, 3vw, 1.5rem);\\n}\\n.processing-indicator[_ngcontent-%COMP%]   .processing-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  font-weight: 500;\\n  color: #fff;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.2rem, 0.5vw, 0.25rem);\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #fff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n\\n\\n.data-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n  overflow: hidden;\\n  max-height: 30vh;\\n}\\n@media (min-width: 1024px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: 0;\\n    max-width: min(35vw, 18rem);\\n    width: auto;\\n    min-width: 15rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: min(30vw, 20rem);\\n  }\\n}\\n\\n.data-panel[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  padding: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  max-height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 clamp(0.25rem, 1vw, 0.5rem) 0;\\n  color: #2d3748;\\n  font-size: clamp(0.875rem, 2vw, 1rem);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  border-bottom: 1px solid rgba(102, 126, 234, 0.1);\\n  padding-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.data-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  max-height: 20vh !important;\\n  overflow-y: auto !important;\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n.data-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: #f8fafc;\\n  border-radius: clamp(0.25rem, 1vw, 0.5rem);\\n  border-left: clamp(2px, 0.3vw, 3px) solid #4facfe;\\n  flex-shrink: 0;\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfoCategoria[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: clamp(0.75rem, 1.8vw, 0.875rem);\\n  color: #718096;\\n  font-weight: 500;\\n  margin-bottom: clamp(0.1rem, 0.3vw, 0.15rem);\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfovalue[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  margin-top: clamp(0.25rem, 1.5vw, 0.5rem);\\n  padding-top: clamp(0.25rem, 1vw, 0.5rem);\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  flex-shrink: 0;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: clamp(3px, 0.8vw, 4px);\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n  margin-bottom: clamp(0.25rem, 0.8vw, 0.25rem);\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  min-width: 44px !important;\\n  min-height: 44px !important;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  color: #667eea;\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.input-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\\n  min-height: 60px;\\n}\\n@media (max-width: 480px) {\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 56px;\\n  }\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  box-sizing: border-box;\\n}\\n\\n.user-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n}\\n.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 2vw, 0.875rem) !important;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem) !important;\\n  min-height: 44px;\\n}\\n.user-input[_ngcontent-%COMP%]   button[matSuffix][_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n.voice-display[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  text-align: center;\\n}\\n\\n.voice-input-field[_ngcontent-%COMP%]   .voice-placeholder[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\\n  font-style: italic;\\n}\\n\\n\\n\\n.audio-visualization[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(4rem, 8vw, 6rem);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(1rem, 3vw, 1.25rem);\\n  z-index: 1000;\\n  max-width: min(90vw, 25rem);\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n}\\n\\n.sound-wave[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.4vw, 0.1875rem);\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n\\n.wave-bar[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.25rem);\\n  background: linear-gradient(to top, #667eea, #f093fb);\\n  border-radius: clamp(1px, 0.2vw, 2px);\\n  animation: _ngcontent-%COMP%_wave-animation 1.5s infinite ease-in-out;\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.1s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.2s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(4) {\\n  animation-delay: 0.3s;\\n  height: clamp(1.75rem, 4vw, 2.1875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(5) {\\n  animation-delay: 0.4s;\\n  height: clamp(1.25rem, 3vw, 1.5625rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(6) {\\n  animation-delay: 0.5s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(7) {\\n  animation-delay: 0.6s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(8) {\\n  animation-delay: 0.7s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n\\n.recording-text[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.875rem, 2.5vw, 1.125rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n.recording-text[_ngcontent-%COMP%]   .recording-icon[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n\\n\\n\\n.voice-status-indicator[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(1.5rem, 4vw, 1.875rem);\\n  right: clamp(1.5rem, 4vw, 1.875rem);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.5rem, 1.5vw, 1rem);\\n  z-index: 1000;\\n}\\n@media (max-width: 480px) {\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(1rem, 3vw, 1.5rem);\\n    right: clamp(1rem, 3vw, 1.5rem);\\n    scale: 0.9;\\n  }\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 4.375rem);\\n  height: clamp(3.5rem, 8vw, 4.375rem);\\n  border-radius: 50%;\\n  border: none;\\n  background: linear-gradient(135deg, #718096, #a0aec0);\\n  color: #ffffff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(113, 128, 150, 0.4);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.status-icon.recording[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757, #ff3742);\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n.status-icon.processing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n}\\n.status-icon.waiting[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #10b981);\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n}\\n.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(1.25rem, 4vw, 1.75rem);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.status-ripple[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.3);\\n  animation: _ngcontent-%COMP%_ripple 1.5s infinite;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  font-weight: 600;\\n  color: #2d3748;\\n  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  white-space: nowrap;\\n}\\n\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ai-blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_mouth-talk {\\n  0% {\\n    transform: scaleY(1);\\n  }\\n  100% {\\n    transform: scaleY(1.5);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_processing-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_listening-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.8;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.9;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse-ring {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1.3);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_wave-animation {\\n  0%, 100% {\\n    transform: scaleY(0.5);\\n  }\\n  50% {\\n    transform: scaleY(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_recording-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(255, 71, 87, 0.8);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(2);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(16, 185, 129, 0.8);\\n  }\\n}\\n\\n\\n\\n\\n@media (max-width: 480px) {\\n  .ai-questionnaire-container[_ngcontent-%COMP%] {\\n    font-size: clamp(0.7rem, 1.8vw, 0.8rem);\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 3vw, 1rem) clamp(0.25rem, 2vw, 0.5rem);\\n    margin: 0.25rem;\\n    max-width: min(85vw, 18rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 3vw, 1rem);\\n    margin: clamp(0.25rem, 1.5vw, 0.5rem) 0 clamp(0.25rem, 1vw, 0.25rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    margin-bottom: clamp(0.5rem, 2.5vw, 1rem);\\n  }\\n  .chat-interface[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 0.5rem);\\n    gap: clamp(0.25rem, 1vw, 0.25rem);\\n    min-height: 40px;\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n    gap: clamp(0.25rem, 0.8vw, 0.25rem);\\n    overflow: hidden;\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: clamp(0.25rem, 1.5vw, 0.5rem);\\n    overflow: hidden;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 5vw, 2.5rem);\\n    height: clamp(2rem, 5vw, 2.5rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.8vw, 0.5rem) clamp(0.5rem, 2.5vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-height: 25vh;\\n    overflow-y: auto;\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    line-height: 1.3;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(0.25rem, 1vw, 0.25rem);\\n    max-height: 20vh;\\n    overflow: hidden;\\n  }\\n  .data-panel[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 50px;\\n  }\\n  .user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.8vw, 0.875rem) !important;\\n    padding: clamp(0.25rem, 1.2vw, 0.25rem) clamp(0.5rem, 1.8vw, 0.5rem) !important;\\n  }\\n  .audio-visualization[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-width: 75vw;\\n    min-height: 40px;\\n  }\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    right: clamp(0.25rem, 1.5vw, 0.5rem);\\n    scale: 0.75;\\n  }\\n  .status-icon[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 4.5vw, 2.5rem);\\n    height: clamp(2rem, 4.5vw, 2.5rem);\\n    min-width: 40px;\\n    min-height: 40px;\\n  }\\n  .status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.8vw, 1rem);\\n  }\\n  .status-text[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n    padding: clamp(0.25rem, 0.6vw, 0.25rem) clamp(0.25rem, 1.2vw, 0.5rem);\\n    max-width: 6rem;\\n  }\\n}\\n@media (min-width: 481px) and (max-width: 640px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(5rem, 10vw, 6rem) clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 3vw, 2rem);\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: clamp(1.5rem, 4vw, 2rem);\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    margin-top: clamp(1rem, 3vw, 2rem);\\n  }\\n}\\n@media (min-width: 641px) and (max-width: 768px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(6rem, 10vw, 7rem) clamp(1.5rem, 4vw, 2rem) clamp(2rem, 4vw, 3rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(2rem, 5vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(1.5rem, 4vw, 2rem);\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: center;\\n    width: 100%;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n    right: clamp(1rem, 2.5vw, 1.5rem);\\n    width: auto;\\n    justify-content: center;\\n  }\\n}\\n@media (min-width: 769px) and (max-width: 1024px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(90vw, 75rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(1.5rem, 3vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: clamp(1.5rem, 3vw, 2rem);\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (min-width: 1025px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(85vw, 85rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    gap: clamp(2rem, 2vw, 4rem);\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    max-width: none;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    width: auto;\\n    min-width: 20rem;\\n    margin-top: 0;\\n  }\\n}\\n@media (orientation: landscape) and (max-height: 600px) {\\n  .idle-screen[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vh, 1rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(1.5rem, 4vh, 2rem) clamp(1.5rem, 4vw, 3rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(1.125rem, 4vw, 1.25rem);\\n    margin: clamp(0.5rem, 2vh, 1rem) 0 clamp(0.25rem, 1vh, 0.5rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n    margin-bottom: clamp(1rem, 3vh, 1.5rem);\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(4rem, 8vh, 5rem) clamp(1.5rem, 4vw, 2rem) clamp(1rem, 2vh, 1.5rem);\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(4rem, 8vh, 5rem);\\n    height: clamp(4rem, 8vh, 5rem);\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .ai-avatar[_ngcontent-%COMP%], .robot-head[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@container (max-width: 480px) {\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vw, 1rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .ai-robot[_ngcontent-%COMP%], .ai-avatar[_ngcontent-%COMP%], .start-btn[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  .btn-glow[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .message-bubble[_ngcontent-%COMP%], .data-panel[_ngcontent-%COMP%], .controls-header[_ngcontent-%COMP%] {\\n    border: 2px solid #2d3748;\\n  }\\n  .start-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #ffffff;\\n  }\\n}\\n\\n\\n\\n\\n.modern-modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(15, 23, 42, 0.8);\\n  -webkit-backdrop-filter: blur(8px) saturate(180%);\\n          backdrop-filter: blur(8px) saturate(180%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n.modern-modal-overlay[_ngcontent-%COMP%]:focus-within {\\n  outline: 2px solid #4facfe;\\n  outline-offset: -2px;\\n}\\n\\n.modern-modal-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1.5rem;\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  width: 100%;\\n  max-width: 56rem;\\n  max-height: 90vh;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n@media (max-width: 768px) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    max-height: 95vh;\\n    margin: 0.5rem;\\n  }\\n}\\n\\n.modal-header-modern[_ngcontent-%COMP%] {\\n  padding: 2rem 2rem 1.5rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 3rem;\\n  height: 3rem;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 1rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  font-size: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #0f172a;\\n  line-height: 1.2;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: 0.75rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  color: #475569;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  color: #0f172a;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(239, 68, 68, 0.15);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #4facfe;\\n  font-weight: 700;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 0.5rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.search-filter-section[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  border: 1px solid #f1f5f9;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.3);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-personal[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(102, 126, 234, 0.1);\\n  --mdc-chip-selected-label-text-color: #667eea;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-medical[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(16, 185, 129, 0.1);\\n  --mdc-chip-selected-label-text-color: #10b981;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-contact[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(59, 130, 246, 0.1);\\n  --mdc-chip-selected-label-text-color: #3b82f6;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-optional[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(245, 158, 11, 0.1);\\n  --mdc-chip-selected-label-text-color: #f59e0b;\\n}\\n\\n.modal-body-modern[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem 2rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 1.5rem;\\n  text-align: center;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  width: 4rem;\\n  height: 4rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  max-width: 24rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n  grid-template-columns: 1fr;\\n}\\n@media (min-width: 1024px) {\\n  .modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #f1f5f9;\\n  border-radius: 1rem;\\n  padding: 1.5rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 3px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.2);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  transform: translateY(-2px);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-personal[_ngcontent-%COMP%] {\\n  border-left: 4px solid #667eea;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-medical[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-contact[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3b82f6;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-optional[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  background: #f8fafc;\\n  border-radius: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: #475569;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n  line-height: 1.3;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-category[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 0.5rem;\\n  background: #f8fafc;\\n  color: #475569;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: #ffffff;\\n  transform: scale(1.1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #0f172a;\\n  font-weight: 500;\\n  line-height: 1.5;\\n  word-break: break-word;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]     mark {\\n  background: rgba(79, 172, 254, 0.2);\\n  color: #4facfe;\\n  padding: 0.125rem 0.25rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 0.5rem;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f1f5f9;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-valid[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-warning[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-error[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n\\n.modal-footer-modern[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-top: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  color: #475569;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3b82f6;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 500;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  background: linear-gradient(135deg, #506be7, #694391);\\n}\\n@media (max-width: 640px) {\\n  .modal-footer-modern[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n    justify-content: stretch;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%], .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .modern-modal-container[_ngcontent-%COMP%], .data-card[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .card-action-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%], .primary-btn[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .progress-fill[_ngcontent-%COMP%]::after {\\n    animation: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    border: 2px solid #0f172a;\\n  }\\n  .data-card[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n  .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n}\\n\\n\\n\\n\\n.test-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;\\n  color: white !important;\\n  border: none !important;\\n  border-radius: 25px !important;\\n  padding: 12px 24px !important;\\n  font-weight: 600 !important;\\n  font-size: 0.9em !important;\\n  text-transform: uppercase !important;\\n  letter-spacing: 0.5px !important;\\n  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;\\n  transition: all 0.3s ease !important;\\n  position: relative !important;\\n  overflow: hidden !important;\\n}\\n.test-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) !important;\\n  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;\\n  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%) !important;\\n}\\n.test-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) !important;\\n}\\n.test-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px !important;\\n  font-size: 1.1em !important;\\n}\\n.test-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.test-button[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.idle-content[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  font-size: 0.85em !important;\\n  padding: 10px 20px !important;\\n}\\n\\n.controls-header[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n  font-size: 0.8em !important;\\n  padding: 8px 16px !important;\\n}\\n\\n\\n\\n.scrollable-hidden[_ngcontent-%COMP%] {\\n  overflow-y: scroll;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.scrollable-hidden[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n\\n\\n.validation-buttons[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 1.5rem;\\n  padding: 1.5rem;\\n  background: #f8fafc;\\n  border-radius: 12px;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #475569;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]   .validation-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 1.25rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  text-transform: none;\\n  letter-spacing: 0.025em;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  font-size: 1.1rem;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #10b981 100%);\\n  color: white;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\\n  color: white;\\n}\\n.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\\n}\\n@media (max-width: 480px) {\\n  .validation-buttons[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: unset;\\n  }\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [trigger('fadeInOut', [transition(':enter', [style({\n        opacity: 0\n      }), animate('300ms ease-in', style({\n        opacity: 1\n      }))]), transition(':leave', [animate('200ms ease-out', style({\n        opacity: 0\n      }))])]), trigger('slideInOut', [transition(':enter', [style({\n        transform: 'translateY(-50px)',\n        opacity: 0\n      }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      }))]), transition(':leave', [animate('300ms ease-in', style({\n        transform: 'translateY(-30px)',\n        opacity: 0\n      }))])]), trigger('cardAnimation', [transition(':enter', [style({\n        transform: 'scale(0.8)',\n        opacity: 0\n      }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'scale(1)',\n        opacity: 1\n      }))])])]\n    }\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "MAT_DIALOG_DATA", "MatDialogRef", "MatDialogModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatTooltipModule", "MatChipsModule", "FormsModule", "ReactiveFormsModule", "CommonModule", "trigger", "style", "transition", "animate", "i0", "ɵɵelementStart", "ɵɵlistener", "HistoryModalDialogComponent_button_36_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵtext", "ɵɵelementEnd", "HistoryModalDialogComponent_mat_chip_option_39_Template_mat_chip_option_click_0_listener", "filter_r4", "_r3", "$implicit", "toggleFilter", "ɵɵclassMap", "type", "ɵɵproperty", "active", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "ɵɵtextInterpolate1", "label", "formatTimestamp", "item_r6", "timestamp", "HistoryModalDialogComponent_div_43_div_1_Template_button_click_11_listener", "_r5", "copyToClipboard", "value", "HistoryModalDialogComponent_div_43_div_1_Template_button_click_14_listener", "editValue", "ɵɵelement", "ɵɵtemplate", "HistoryModalDialogComponent_div_43_div_1_span_21_Template", "category", "undefined", "categoryLabel", "highlightSearchTerm", "ɵɵsanitizeHtml", "validationStatus", "getValidationIcon", "getValidationLabel", "HistoryModalDialogComponent_div_43_div_1_Template", "getFilteredData", "trackByFn", "HistoryModalDialogComponent", "dialogRef", "data", "searchTerm", "availableFilters", "constructor", "onClose", "close", "getTotalCampos", "getDadosPreenchidos", "labels", "nome", "cpf", "email", "telefone", "dataNascimento", "<PERSON><PERSON><PERSON>", "sintomas", "intensidadeDor", "tempoSintomas", "doencasPrevia<PERSON>", "observacoes", "Object", "keys", "dadosColetados", "filter", "key", "trim", "map", "getProgressPercentage", "total", "filled", "length", "Math", "round", "onSearchChange", "event", "target", "getEnhancedDadosPreenchidos", "searchLower", "toLowerCase", "item", "includes", "activeCategories", "f", "categoryMap", "categoryInfo", "Date", "getValidationStatusForField", "field", "index", "console", "log", "text", "regex", "RegExp", "replace", "toLocaleTimeString", "hour", "minute", "status", "navigator", "clipboard", "writeText", "then", "snackBar", "sucessoSnackbar", "catch", "falhaSnackbar", "newValue", "prompt", "find", "cdr", "detectChanges", "clearAllData", "confirm", "for<PERSON>ach", "exportData", "stopPropagation", "dataStr", "JSON", "stringify", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "ɵɵdirectiveInject", "i1", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HistoryModalDialogComponent_Template", "rf", "ctx", "HistoryModalDialogComponent_Template_div_click_0_listener", "HistoryModalDialogComponent_Template_div_click_1_listener", "$event", "HistoryModalDialogComponent_Template_button_click_14_listener", "HistoryModalDialogComponent_Template_button_click_17_listener", "ɵɵtwoWayListener", "HistoryModalDialogComponent_Template_input_ngModelChange_33_listener", "ɵɵtwoWayBindingSet", "HistoryModalDialogComponent_Template_input_input_33_listener", "HistoryModalDialogComponent_button_36_Template", "HistoryModalDialogComponent_mat_chip_option_39_Template", "HistoryModalDialogComponent_div_42_Template", "HistoryModalDialogComponent_div_43_Template", "HistoryModalDialogComponent_Template_button_click_51_listener", "HistoryModalDialogComponent_Template_button_click_55_listener", "ɵɵtextInterpolate2", "ɵɵstyleProp", "ɵɵtwoWayProperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "MatButton", "MatIconButton", "i4", "MatIcon", "i5", "MatInput", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatPrefix", "MatSuffix", "i7", "MatTooltip", "i8", "MatChipListbox", "MatChipOption", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "animation", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\components\\history-modal-dialog.component.ts", "C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\components\\history-modal-dialog.component.html"], "sourcesContent": ["import { Component, Inject, ChangeDetectorRef } from '@angular/core';\r\nimport { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { trigger, style, transition, animate } from '@angular/animations';\r\nimport { QuestionarioPreConsultaDados } from '../MapPalavrasModel';\r\n\r\ninterface FilterOption {\r\n    type: string;\r\n    label: string;\r\n    icon: string;\r\n    active: boolean;\r\n}\r\n\r\ninterface EnhancedDataItem {\r\n    label: string;\r\n    value: string;\r\n    category: string;\r\n    categoryLabel: string;\r\n    icon: string;\r\n    timestamp?: Date;\r\n    validationStatus: 'valid' | 'warning' | 'error';\r\n}\r\n\r\n@Component({\r\n    selector: 'app-history-modal-dialog',\r\n    templateUrl: './history-modal-dialog.component.html',\r\n    styleUrls: ['./history-modal-dialog.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        MatButtonModule,\r\n        MatIconModule,\r\n        MatInputModule,\r\n        MatFormFieldModule,\r\n        MatTooltipModule,\r\n        MatChipsModule,\r\n        MatDialogModule,\r\n        FormsModule,\r\n        ReactiveFormsModule\r\n    ],\r\n    animations: [\r\n        trigger('fadeInOut', [\r\n            transition(':enter', [\r\n                style({ opacity: 0 }),\r\n                animate('300ms ease-in', style({ opacity: 1 }))\r\n            ]),\r\n            transition(':leave', [\r\n                animate('200ms ease-out', style({ opacity: 0 }))\r\n            ])\r\n        ]),\r\n        trigger('slideInOut', [\r\n            transition(':enter', [\r\n                style({ transform: 'translateY(-50px)', opacity: 0 }),\r\n                animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n                    style({ transform: 'translateY(0)', opacity: 1 }))\r\n            ]),\r\n            transition(':leave', [\r\n                animate('300ms ease-in',\r\n                    style({ transform: 'translateY(-30px)', opacity: 0 }))\r\n            ])\r\n        ]),\r\n        trigger('cardAnimation', [\r\n            transition(':enter', [\r\n                style({ transform: 'scale(0.8)', opacity: 0 }),\r\n                animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n                    style({ transform: 'scale(1)', opacity: 1 }))\r\n            ])\r\n        ])\r\n    ]\r\n})\r\nexport class HistoryModalDialogComponent {\r\n    searchTerm = '';\r\n    availableFilters: FilterOption[] = [\r\n        { type: 'personal', label: 'Dados Pessoais', icon: 'person', active: true },\r\n        { type: 'medical', label: 'Informações Médicas', icon: 'medical_services', active: true },\r\n        { type: 'contact', label: 'Contato', icon: 'contact_phone', active: true },\r\n        { type: 'optional', label: 'Opcionais', icon: 'info', active: true }\r\n    ];\r\n\r\n    constructor(\r\n        public dialogRef: MatDialogRef<HistoryModalDialogComponent>,\r\n        @Inject(MAT_DIALOG_DATA) public data: {\r\n            dadosColetados: QuestionarioPreConsultaDados,\r\n            snackBar?: any,\r\n            cdr?: ChangeDetectorRef\r\n        }\r\n    ) { }\r\n\r\n    onClose(): void {\r\n        this.dialogRef.close();\r\n    }\r\n\r\n    getTotalCampos(): number {\r\n        return 10;\r\n    }\r\n\r\n    getDadosPreenchidos(): Array<{ label: string, value: string }> {\r\n        const labels: { [key: string]: string } = {\r\n            nome: 'Nome',\r\n            cpf: 'CPF',\r\n            email: 'Email',\r\n            telefone: 'Telefone',\r\n            dataNascimento: 'Data de Nascimento',\r\n            alergias: 'Alergias',\r\n            sintomas: 'Sintomas',\r\n            intensidadeDor: 'Intensidade da Dor',\r\n            tempoSintomas: 'Tempo dos Sintomas',\r\n            doencasPrevias: 'Doenças Prévias',\r\n            observacoes: 'Observações'\r\n        };\r\n        return Object.keys(this.data.dadosColetados)\r\n            .filter(key => {\r\n                const value = this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados];\r\n                return value && value.trim() !== '';\r\n            })\r\n            .map(key => ({\r\n                label: labels[key],\r\n                value: this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados]\r\n            }));\r\n    }\r\n\r\n    getProgressPercentage(): number {\r\n        const total = this.getTotalCampos();\r\n        const filled = this.getDadosPreenchidos().length;\r\n        return Math.round((filled / total) * 100);\r\n    }\r\n\r\n    onSearchChange(event: any): void {\r\n        this.searchTerm = event.target.value;\r\n    }\r\n\r\n    clearSearch(): void {\r\n        this.searchTerm = '';\r\n    }\r\n\r\n    toggleFilter(filter: FilterOption): void {\r\n        filter.active = !filter.active;\r\n    }\r\n\r\n    getFilteredData(): EnhancedDataItem[] {\r\n        let data = this.getEnhancedDadosPreenchidos();\r\n        if (this.searchTerm) {\r\n            const searchLower = this.searchTerm.toLowerCase();\r\n            data = data.filter(item =>\r\n                item.label.toLowerCase().includes(searchLower) ||\r\n                item.value.toLowerCase().includes(searchLower)\r\n            );\r\n        }\r\n        const activeCategories = this.availableFilters\r\n            .filter(f => f.active)\r\n            .map(f => f.type);\r\n        data = data.filter(item => activeCategories.includes(item.category));\r\n        return data;\r\n    }\r\n\r\n    getEnhancedDadosPreenchidos(): EnhancedDataItem[] {\r\n        const categoryMap: { [key: string]: { category: string, categoryLabel: string, icon: string } } = {\r\n            nome: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'person' },\r\n            cpf: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'badge' },\r\n            dataNascimento: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'cake' },\r\n            email: { category: 'contact', categoryLabel: 'Contato', icon: 'email' },\r\n            telefone: { category: 'contact', categoryLabel: 'Contato', icon: 'phone' },\r\n            sintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'medical_services' },\r\n            intensidadeDor: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'personal_injury' },\r\n            tempoSintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'schedule' },\r\n            alergias: { category: 'optional', categoryLabel: 'Opcionais', icon: 'medical_information' },\r\n            observacoes: { category: 'optional', categoryLabel: 'Opcionais', icon: 'description' }\r\n        };\r\n        const labels: { [key: string]: string } = {\r\n            nome: 'Nome',\r\n            cpf: 'CPF',\r\n            email: 'Email',\r\n            telefone: 'Telefone',\r\n            dataNascimento: 'Data de Nascimento',\r\n            alergias: 'Alergias',\r\n            sintomas: 'Sintomas',\r\n            intensidadeDor: 'Intensidade da Dor',\r\n            tempoSintomas: 'Tempo dos Sintomas',\r\n            doencasPrevias: 'Doenças Prévias',\r\n            observacoes: 'Observações'\r\n        };\r\n        return Object.keys(this.data.dadosColetados)\r\n            .filter(key => {\r\n                const value = this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados];\r\n                return value && value.trim() !== '';\r\n            })\r\n            .map(key => {\r\n                const categoryInfo = categoryMap[key] || { category: 'optional', categoryLabel: 'Outros', icon: 'info' };\r\n                return {\r\n                    label: labels[key] || key,\r\n                    value: this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados],\r\n                    category: categoryInfo.category,\r\n                    categoryLabel: categoryInfo.categoryLabel,\r\n                    icon: categoryInfo.icon,\r\n                    timestamp: new Date(),\r\n                    validationStatus: this.getValidationStatusForField(key) as 'valid' | 'warning' | 'error'\r\n                };\r\n            });\r\n    }\r\n\r\n    getValidationStatusForField(field: string): string {\r\n        const value = this.data.dadosColetados[field as keyof QuestionarioPreConsultaDados];\r\n        if (!value || value.trim() === '') return 'error';\r\n        if (field === 'email' && !value.includes('@')) return 'warning';\r\n        if (field === 'cpf' && value.length < 11) return 'warning';\r\n        return 'valid';\r\n    }\r\n\r\n    trackByFn(index: number, item: EnhancedDataItem): string {\r\n        console.log('index', index);\r\n        return item.label + item.value;\r\n    }\r\n\r\n    highlightSearchTerm(text: string): string {\r\n        if (!this.searchTerm) return text;\r\n        const regex = new RegExp(`(${this.searchTerm})`, 'gi');\r\n        return text.replace(regex, '<mark>$1</mark>');\r\n    }\r\n\r\n    formatTimestamp(timestamp: Date): string {\r\n        return timestamp.toLocaleTimeString('pt-BR', {\r\n            hour: '2-digit',\r\n            minute: '2-digit'\r\n        });\r\n    }\r\n\r\n    getValidationIcon(status: string): string {\r\n        switch (status) {\r\n            case 'valid': return 'check_circle';\r\n            case 'warning': return 'warning';\r\n            case 'error': return 'error';\r\n            default: return 'info';\r\n        }\r\n    }\r\n\r\n    getValidationLabel(status: string): string {\r\n        switch (status) {\r\n            case 'valid': return 'Válido';\r\n            case 'warning': return 'Atenção';\r\n            case 'error': return 'Erro';\r\n            default: return 'Info';\r\n        }\r\n    }\r\n\r\n    copyToClipboard(text: string): void {\r\n        navigator.clipboard.writeText(text).then(() => {\r\n            if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\r\n        }).catch(() => {\r\n            if (this.data.snackBar) this.data.snackBar.falhaSnackbar('Erro ao copiar texto');\r\n        });\r\n    }\r\n\r\n    editValue(item: EnhancedDataItem): void {\r\n        const newValue = prompt(`Editar ${item.label}:`, item.value);\r\n        if (newValue !== null && newValue !== item.value) {\r\n            const field = Object.keys(this.data.dadosColetados).find(key => {\r\n                const labels: { [key: string]: string } = {\r\n                    nome: 'Nome',\r\n                    cpf: 'CPF',\r\n                    email: 'Email',\r\n                    telefone: 'Telefone',\r\n                    dataNascimento: 'Data de Nascimento',\r\n                    alergias: 'Alergias',\r\n                    sintomas: 'Sintomas',\r\n                    intensidadeDor: 'Intensidade da Dor',\r\n                    tempoSintomas: 'Tempo dos Sintomas',\r\n                    doencasPrevias: 'Doenças Prévias',\r\n                    observacoes: 'Observações'\r\n                };\r\n                return labels[key] === item.label;\r\n            });\r\n            if (field) {\r\n                this.data.dadosColetados[field as keyof QuestionarioPreConsultaDados] = newValue;\r\n                if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\r\n                if (this.data.cdr) this.data.cdr.detectChanges();\r\n            }\r\n        }\r\n    }\r\n\r\n    clearAllData(): void {\r\n        if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\r\n            Object.keys(this.data.dadosColetados).forEach(key => {\r\n                this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados] = '';\r\n            });\r\n            if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Todos os dados foram limpos');\r\n            if (this.data.cdr) this.data.cdr.detectChanges();\r\n        }\r\n    }\r\n\r\n    exportData(event: Event): void {\r\n        event.stopPropagation();\r\n        const dataStr = JSON.stringify(this.data.dadosColetados, null, 2);\r\n        const blob = new Blob([dataStr], { type: 'application/json' });\r\n        const url = window.URL.createObjectURL(blob);\r\n        const a = document.createElement('a');\r\n        a.href = url;\r\n        a.download = 'dados-preenchidos.json';\r\n        a.click();\r\n        window.URL.revokeObjectURL(url);\r\n    }\r\n}", "<!-- Modern History Modal Dialog -->\r\n<div class=\"modern-modal-overlay\" (click)=\"onClose()\" [@fadeInOut] role=\"dialog\" aria-modal=\"true\"\r\n    aria-labelledby=\"modal-title\" aria-describedby=\"modal-description\">\r\n    <div class=\"modern-modal-container\" (click)=\"$event.stopPropagation()\" [@slideInOut]>\r\n        <!-- Modal Header -->\r\n        <header class=\"modal-header-modern\">\r\n            <div class=\"header-content\">\r\n                <div class=\"title-section\">\r\n                    <div class=\"icon-wrapper\">\r\n                        <mat-icon class=\"header-icon\">history</mat-icon>\r\n                    </div>\r\n                    <div class=\"title-text\">\r\n                        <h2 id=\"modal-title\" class=\"modal-title\">Histórico de Informações</h2>\r\n                        <p id=\"modal-description\" class=\"modal-subtitle\">\r\n                            Dados coletados durante a conversa\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n                <div class=\"header-actions\">\r\n                    <button mat-icon-button class=\"action-btn secondary\" matTooltip=\"Exportar dados\"\r\n                        aria-label=\"Exportar dados coletados\" (click)=\"exportData($event)\">\r\n                        <mat-icon>download</mat-icon>\r\n                    </button>\r\n                    <button mat-icon-button class=\"action-btn close-btn\" (click)=\"onClose()\" matTooltip=\"Fechar modal\"\r\n                        aria-label=\"Fechar modal\">\r\n                        <mat-icon>close</mat-icon>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <!-- Progress Indicator -->\r\n            <div class=\"progress-section\">\r\n                <div class=\"progress-info\">\r\n                    <span class=\"progress-text\">\r\n                        {{ getDadosPreenchidos().length }} de {{ getTotalCampos() }} campos preenchidos\r\n                    </span>\r\n                    <span class=\"progress-percentage\">\r\n                        {{ getProgressPercentage() }}%\r\n                    </span>\r\n                </div>\r\n                <div class=\"progress-bar\">\r\n                    <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage()\">\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <!-- Search and Filter Section -->\r\n        <div class=\"search-filter-section\">\r\n            <div class=\"search-container\">\r\n                <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n                    <mat-label>Buscar informação</mat-label>\r\n                    <input matInput [(ngModel)]=\"searchTerm\" placeholder=\"Digite para buscar...\"\r\n                        (input)=\"onSearchChange($event)\">\r\n                    <mat-icon matPrefix>search</mat-icon>\r\n                    <button mat-icon-button matSuffix *ngIf=\"searchTerm\" (click)=\"clearSearch()\"\r\n                        aria-label=\"Limpar busca\">\r\n                        <mat-icon>clear</mat-icon>\r\n                    </button>\r\n                </mat-form-field>\r\n            </div>\r\n            <div class=\"filter-chips\">\r\n                <mat-chip-listbox class=\"filter-chip-list\">\r\n                    <mat-chip-option *ngFor=\"let filter of availableFilters\" [selected]=\"filter.active\"\r\n                        (click)=\"toggleFilter(filter)\" [class]=\"'filter-chip-' + filter.type\">\r\n                        <mat-icon>{{ filter.icon }}</mat-icon>\r\n                        {{ filter.label }}\r\n                    </mat-chip-option>\r\n                </mat-chip-listbox>\r\n            </div>\r\n        </div>\r\n        <!-- Modal Body -->\r\n        <main class=\"modal-body-modern\">\r\n            <div class=\"content-wrapper\">\r\n                <!-- Empty State -->\r\n                <div class=\"empty-state\" *ngIf=\"getFilteredData().length === 0\">\r\n                    <div class=\"empty-icon\">\r\n                        <mat-icon>search_off</mat-icon>\r\n                    </div>\r\n                    <h3 class=\"empty-title\">Nenhuma informação encontrada</h3>\r\n                    <p class=\"empty-description\">\r\n                        Tente ajustar os filtros ou termo de busca\r\n                    </p>\r\n                </div>\r\n                <!-- Data Grid -->\r\n                <div class=\"data-grid\" *ngIf=\"getFilteredData().length > 0\">\r\n                    <div class=\"data-card\" *ngFor=\"let item of getFilteredData(); trackBy: trackByFn\" [@cardAnimation]\r\n                        [class]=\"'card-' + item.category\">\r\n                        <div class=\"card-header\">\r\n                            <div class=\"card-icon\">\r\n                                <mat-icon>{{ item.icon }}</mat-icon>\r\n                            </div>\r\n                            <div class=\"card-title-section\">\r\n                                <h4 class=\"card-title\">{{ item.label }}</h4>\r\n                                <span class=\"card-category\">{{ item.categoryLabel }}</span>\r\n                            </div>\r\n                            <div class=\"card-actions\">\r\n                                <button mat-icon-button class=\"card-action-btn\" matTooltip=\"Copiar valor\"\r\n                                    (click)=\"copyToClipboard(item.value)\" aria-label=\"Copiar valor\">\r\n                                    <mat-icon>content_copy</mat-icon>\r\n                                </button>\r\n                                <button mat-icon-button class=\"card-action-btn\" matTooltip=\"Editar valor\"\r\n                                    (click)=\"editValue(item)\" aria-label=\"Editar valor\">\r\n                                    <mat-icon>edit</mat-icon>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"card-content\">\r\n                            <div class=\"value-container\">\r\n                                <span class=\"card-value\" [innerHTML]=\"highlightSearchTerm(item.value)\">\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"card-metadata\">\r\n                                <span class=\"timestamp\" *ngIf=\"item.timestamp\">\r\n                                    <mat-icon>schedule</mat-icon>\r\n                                    {{ formatTimestamp(item.timestamp) }}\r\n                                </span>\r\n                                <span class=\"validation-status\" [class]=\"'status-' + item.validationStatus\">\r\n                                    <mat-icon>{{ getValidationIcon(item.validationStatus) }}</mat-icon>\r\n                                    {{ getValidationLabel(item.validationStatus) }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </main>\r\n        <!-- Modal Footer -->\r\n        <footer class=\"modal-footer-modern\">\r\n            <div class=\"footer-info\">\r\n                <span class=\"info-text\">\r\n                    <mat-icon>info</mat-icon>\r\n                    Dados salvos automaticamente\r\n                </span>\r\n            </div>\r\n            <div class=\"footer-actions\">\r\n                <button mat-stroked-button class=\"secondary-btn\" (click)=\"clearAllData()\"\r\n                    [disabled]=\"getDadosPreenchidos().length === 0\">\r\n                    <mat-icon>delete_sweep</mat-icon>\r\n                    Limpar Tudo\r\n                </button>\r\n                <button mat-raised-button color=\"primary\" class=\"primary-btn\" (click)=\"onClose()\">\r\n                    <mat-icon>check</mat-icon>\r\n                    Concluído\r\n                </button>\r\n            </div>\r\n        </footer>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAA4BA,iBAAiB,QAAQ,eAAe;AACpE,SAASC,eAAe,EAAEC,YAAY,EAAEC,eAAe,QAAQ,0BAA0B;AACzF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;;;;;;;;IC2CrDC,EAAA,CAAAC,cAAA,iBAC8B;IADuBD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAExET,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IACnBV,EADmB,CAAAW,YAAA,EAAW,EACrB;;;;;;IAKTX,EAAA,CAAAC,cAAA,0BAC0E;IAAtED,EAAA,CAAAE,UAAA,mBAAAU,yFAAA;MAAA,MAAAC,SAAA,GAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,CAAAH,SAAA,CAAoB;IAAA,EAAC;IAC9Bb,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACtCX,EAAA,CAAAU,MAAA,GACJ;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;IAHiBX,EAAA,CAAAiB,UAAA,kBAAAJ,SAAA,CAAAK,IAAA,CAAsC;IADhBlB,EAAA,CAAAmB,UAAA,aAAAN,SAAA,CAAAO,MAAA,CAA0B;IAErEpB,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAsB,iBAAA,CAAAT,SAAA,CAAAU,IAAA,CAAiB;IAC3BvB,EAAA,CAAAqB,SAAA,EACJ;IADIrB,EAAA,CAAAwB,kBAAA,MAAAX,SAAA,CAAAY,KAAA,MACJ;;;;;IAUIzB,EAFR,CAAAC,cAAA,cAAgE,cACpC,eACV;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IACxBV,EADwB,CAAAW,YAAA,EAAW,EAC7B;IACNX,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,8CAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC1DX,EAAA,CAAAC,cAAA,YAA6B;IACzBD,EAAA,CAAAU,MAAA,mDACJ;IACJV,EADI,CAAAW,YAAA,EAAI,EACF;;;;;IA+BcX,EADJ,CAAAC,cAAA,eAA+C,eACjC;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC7BX,EAAA,CAAAU,MAAA,GACJ;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IADHX,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAoB,eAAA,CAAAC,OAAA,CAAAC,SAAA,OACJ;;;;;;IA1BA5B,EAJZ,CAAAC,cAAA,cACsC,cACT,cACE,eACT;IAAAD,EAAA,CAAAU,MAAA,GAAe;IAC7BV,EAD6B,CAAAW,YAAA,EAAW,EAClC;IAEFX,EADJ,CAAAC,cAAA,cAAgC,aACL;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC5CX,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAwB;IACxDV,EADwD,CAAAW,YAAA,EAAO,EACzD;IAEFX,EADJ,CAAAC,cAAA,eAA0B,kBAE8C;IAAhED,EAAA,CAAAE,UAAA,mBAAA2B,2EAAA;MAAA,MAAAF,OAAA,GAAA3B,EAAA,CAAAI,aAAA,CAAA0B,GAAA,EAAAf,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,eAAA,CAAAJ,OAAA,CAAAK,KAAA,CAA2B;IAAA,EAAC;IACrChC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAC1BV,EAD0B,CAAAW,YAAA,EAAW,EAC5B;IACTX,EAAA,CAAAC,cAAA,kBACwD;IAApDD,EAAA,CAAAE,UAAA,mBAAA+B,2EAAA;MAAA,MAAAN,OAAA,GAAA3B,EAAA,CAAAI,aAAA,CAAA0B,GAAA,EAAAf,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4B,SAAA,CAAAP,OAAA,CAAe;IAAA,EAAC;IACzB3B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAU,MAAA,YAAI;IAG1BV,EAH0B,CAAAW,YAAA,EAAW,EACpB,EACP,EACJ;IAEFX,EADJ,CAAAC,cAAA,eAA0B,eACO;IACzBD,EAAA,CAAAmC,SAAA,gBACO;IACXnC,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACvBD,EAAA,CAAAoC,UAAA,KAAAC,yDAAA,mBAA+C;IAK3CrC,EADJ,CAAAC,cAAA,gBAA4E,gBAC9D;IAAAD,EAAA,CAAAU,MAAA,IAA8C;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACnEX,EAAA,CAAAU,MAAA,IACJ;IAGZV,EAHY,CAAAW,YAAA,EAAO,EACL,EACJ,EACJ;;;;;IApCFX,EAAA,CAAAiB,UAAA,WAAAU,OAAA,CAAAW,QAAA,CAAiC;IAD6CtC,EAAA,CAAAmB,UAAA,mBAAAoB,SAAA,CAAgB;IAI5EvC,EAAA,CAAAqB,SAAA,GAAe;IAAfrB,EAAA,CAAAsB,iBAAA,CAAAK,OAAA,CAAAJ,IAAA,CAAe;IAGFvB,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAAsB,iBAAA,CAAAK,OAAA,CAAAF,KAAA,CAAgB;IACXzB,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAAsB,iBAAA,CAAAK,OAAA,CAAAa,aAAA,CAAwB;IAe3BxC,EAAA,CAAAqB,SAAA,IAA6C;IAA7CrB,EAAA,CAAAmB,UAAA,cAAAb,MAAA,CAAAmC,mBAAA,CAAAd,OAAA,CAAAK,KAAA,GAAAhC,EAAA,CAAA0C,cAAA,CAA6C;IAI7C1C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAmB,UAAA,SAAAQ,OAAA,CAAAC,SAAA,CAAoB;IAIb5B,EAAA,CAAAqB,SAAA,EAA2C;IAA3CrB,EAAA,CAAAiB,UAAA,aAAAU,OAAA,CAAAgB,gBAAA,CAA2C;IAC7D3C,EAAA,CAAAqB,SAAA,GAA8C;IAA9CrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAsC,iBAAA,CAAAjB,OAAA,CAAAgB,gBAAA,EAA8C;IACxD3C,EAAA,CAAAqB,SAAA,EACJ;IADIrB,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAuC,kBAAA,CAAAlB,OAAA,CAAAgB,gBAAA,OACJ;;;;;IAnChB3C,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAoC,UAAA,IAAAU,iDAAA,oBACsC;IAqC1C9C,EAAA,CAAAW,YAAA,EAAM;;;;IAtCsCX,EAAA,CAAAqB,SAAA,EAAsB;IAAArB,EAAtB,CAAAmB,UAAA,YAAAb,MAAA,CAAAyC,eAAA,GAAsB,iBAAAzC,MAAA,CAAA0C,SAAA,CAAkB;;;ADPpG,OAAM,MAAOC,2BAA2B;EAUzBC,SAAA;EACyBC,IAAA;EAVpCC,UAAU,GAAG,EAAE;EACfC,gBAAgB,GAAmB,CAC/B;IAAEnC,IAAI,EAAE,UAAU;IAAEO,KAAK,EAAE,gBAAgB;IAAEF,IAAI,EAAE,QAAQ;IAAEH,MAAM,EAAE;EAAI,CAAE,EAC3E;IAAEF,IAAI,EAAE,SAAS;IAAEO,KAAK,EAAE,qBAAqB;IAAEF,IAAI,EAAE,kBAAkB;IAAEH,MAAM,EAAE;EAAI,CAAE,EACzF;IAAEF,IAAI,EAAE,SAAS;IAAEO,KAAK,EAAE,SAAS;IAAEF,IAAI,EAAE,eAAe;IAAEH,MAAM,EAAE;EAAI,CAAE,EAC1E;IAAEF,IAAI,EAAE,UAAU;IAAEO,KAAK,EAAE,WAAW;IAAEF,IAAI,EAAE,MAAM;IAAEH,MAAM,EAAE;EAAI,CAAE,CACvE;EAEDkC,YACWJ,SAAoD,EAC3BC,IAI/B;IALM,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EAKpC;EAEJI,OAAOA,CAAA;IACH,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACV,OAAO,EAAE;EACb;EAEAC,mBAAmBA,CAAA;IACf,MAAMC,MAAM,GAA8B;MACtCC,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KAChB;IACD,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,IAAI,CAACsB,cAAc,CAAC,CACvCC,MAAM,CAACC,GAAG,IAAG;MACV,MAAM3C,KAAK,GAAG,IAAI,CAACmB,IAAI,CAACsB,cAAc,CAACE,GAAyC,CAAC;MACjF,OAAO3C,KAAK,IAAIA,KAAK,CAAC4C,IAAI,EAAE,KAAK,EAAE;IACvC,CAAC,CAAC,CACDC,GAAG,CAACF,GAAG,KAAK;MACTlD,KAAK,EAAEkC,MAAM,CAACgB,GAAG,CAAC;MAClB3C,KAAK,EAAE,IAAI,CAACmB,IAAI,CAACsB,cAAc,CAACE,GAAyC;KAC5E,CAAC,CAAC;EACX;EAEAG,qBAAqBA,CAAA;IACjB,MAAMC,KAAK,GAAG,IAAI,CAACtB,cAAc,EAAE;IACnC,MAAMuB,MAAM,GAAG,IAAI,CAACtB,mBAAmB,EAAE,CAACuB,MAAM;IAChD,OAAOC,IAAI,CAACC,KAAK,CAAEH,MAAM,GAAGD,KAAK,GAAI,GAAG,CAAC;EAC7C;EAEAK,cAAcA,CAACC,KAAU;IACrB,IAAI,CAACjC,UAAU,GAAGiC,KAAK,CAACC,MAAM,CAACtD,KAAK;EACxC;EAEAvB,WAAWA,CAAA;IACP,IAAI,CAAC2C,UAAU,GAAG,EAAE;EACxB;EAEApC,YAAYA,CAAC0D,MAAoB;IAC7BA,MAAM,CAACtD,MAAM,GAAG,CAACsD,MAAM,CAACtD,MAAM;EAClC;EAEA2B,eAAeA,CAAA;IACX,IAAII,IAAI,GAAG,IAAI,CAACoC,2BAA2B,EAAE;IAC7C,IAAI,IAAI,CAACnC,UAAU,EAAE;MACjB,MAAMoC,WAAW,GAAG,IAAI,CAACpC,UAAU,CAACqC,WAAW,EAAE;MACjDtC,IAAI,GAAGA,IAAI,CAACuB,MAAM,CAACgB,IAAI,IACnBA,IAAI,CAACjE,KAAK,CAACgE,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC9CE,IAAI,CAAC1D,KAAK,CAACyD,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACjD;IACL;IACA,MAAMI,gBAAgB,GAAG,IAAI,CAACvC,gBAAgB,CACzCqB,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAACzE,MAAM,CAAC,CACrByD,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAAC3E,IAAI,CAAC;IACrBiC,IAAI,GAAGA,IAAI,CAACuB,MAAM,CAACgB,IAAI,IAAIE,gBAAgB,CAACD,QAAQ,CAACD,IAAI,CAACpD,QAAQ,CAAC,CAAC;IACpE,OAAOa,IAAI;EACf;EAEAoC,2BAA2BA,CAAA;IACvB,MAAMO,WAAW,GAAiF;MAC9FlC,IAAI,EAAE;QAAEtB,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEjB,IAAI,EAAE;MAAQ,CAAE;MAC/EsC,GAAG,EAAE;QAAEvB,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEjB,IAAI,EAAE;MAAO,CAAE;MAC7EyC,cAAc,EAAE;QAAE1B,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEjB,IAAI,EAAE;MAAM,CAAE;MACvFuC,KAAK,EAAE;QAAExB,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAEjB,IAAI,EAAE;MAAO,CAAE;MACvEwC,QAAQ,EAAE;QAAEzB,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAEjB,IAAI,EAAE;MAAO,CAAE;MAC1E2C,QAAQ,EAAE;QAAE5B,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEjB,IAAI,EAAE;MAAkB,CAAE;MACjG4C,cAAc,EAAE;QAAE7B,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEjB,IAAI,EAAE;MAAiB,CAAE;MACtG6C,aAAa,EAAE;QAAE9B,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEjB,IAAI,EAAE;MAAU,CAAE;MAC9F0C,QAAQ,EAAE;QAAE3B,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAEjB,IAAI,EAAE;MAAqB,CAAE;MAC3F+C,WAAW,EAAE;QAAEhC,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAEjB,IAAI,EAAE;MAAa;KACvF;IACD,MAAMoC,MAAM,GAA8B;MACtCC,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KAChB;IACD,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,IAAI,CAACsB,cAAc,CAAC,CACvCC,MAAM,CAACC,GAAG,IAAG;MACV,MAAM3C,KAAK,GAAG,IAAI,CAACmB,IAAI,CAACsB,cAAc,CAACE,GAAyC,CAAC;MACjF,OAAO3C,KAAK,IAAIA,KAAK,CAAC4C,IAAI,EAAE,KAAK,EAAE;IACvC,CAAC,CAAC,CACDC,GAAG,CAACF,GAAG,IAAG;MACP,MAAMoB,YAAY,GAAGD,WAAW,CAACnB,GAAG,CAAC,IAAI;QAAErC,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,QAAQ;QAAEjB,IAAI,EAAE;MAAM,CAAE;MACxG,OAAO;QACHE,KAAK,EAAEkC,MAAM,CAACgB,GAAG,CAAC,IAAIA,GAAG;QACzB3C,KAAK,EAAE,IAAI,CAACmB,IAAI,CAACsB,cAAc,CAACE,GAAyC,CAAC;QAC1ErC,QAAQ,EAAEyD,YAAY,CAACzD,QAAQ;QAC/BE,aAAa,EAAEuD,YAAY,CAACvD,aAAa;QACzCjB,IAAI,EAAEwE,YAAY,CAACxE,IAAI;QACvBK,SAAS,EAAE,IAAIoE,IAAI,EAAE;QACrBrD,gBAAgB,EAAE,IAAI,CAACsD,2BAA2B,CAACtB,GAAG;OACzD;IACL,CAAC,CAAC;EACV;EAEAsB,2BAA2BA,CAACC,KAAa;IACrC,MAAMlE,KAAK,GAAG,IAAI,CAACmB,IAAI,CAACsB,cAAc,CAACyB,KAA2C,CAAC;IACnF,IAAI,CAAClE,KAAK,IAAIA,KAAK,CAAC4C,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,OAAO;IACjD,IAAIsB,KAAK,KAAK,OAAO,IAAI,CAAClE,KAAK,CAAC2D,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,SAAS;IAC/D,IAAIO,KAAK,KAAK,KAAK,IAAIlE,KAAK,CAACiD,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS;IAC1D,OAAO,OAAO;EAClB;EAEAjC,SAASA,CAACmD,KAAa,EAAET,IAAsB;IAC3CU,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,KAAK,CAAC;IAC3B,OAAOT,IAAI,CAACjE,KAAK,GAAGiE,IAAI,CAAC1D,KAAK;EAClC;EAEAS,mBAAmBA,CAAC6D,IAAY;IAC5B,IAAI,CAAC,IAAI,CAAClD,UAAU,EAAE,OAAOkD,IAAI;IACjC,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAI,IAAI,CAACpD,UAAU,GAAG,EAAE,IAAI,CAAC;IACtD,OAAOkD,IAAI,CAACG,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EACjD;EAEA7E,eAAeA,CAACE,SAAe;IAC3B,OAAOA,SAAS,CAAC8E,kBAAkB,CAAC,OAAO,EAAE;MACzCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACX,CAAC;EACN;EAEAhE,iBAAiBA,CAACiE,MAAc;IAC5B,QAAQA,MAAM;MACV,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,MAAM;IAC1B;EACJ;EAEAhE,kBAAkBA,CAACgE,MAAc;IAC7B,QAAQA,MAAM;MACV,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B;QAAS,OAAO,MAAM;IAC1B;EACJ;EAEA9E,eAAeA,CAACuE,IAAY;IACxBQ,SAAS,CAACC,SAAS,CAACC,SAAS,CAACV,IAAI,CAAC,CAACW,IAAI,CAAC,MAAK;MAC1C,IAAI,IAAI,CAAC9D,IAAI,CAAC+D,QAAQ,EAAE,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ,CAACC,eAAe,CAAC,4CAA4C,CAAC;IAC5G,CAAC,CAAC,CAACC,KAAK,CAAC,MAAK;MACV,IAAI,IAAI,CAACjE,IAAI,CAAC+D,QAAQ,EAAE,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ,CAACG,aAAa,CAAC,sBAAsB,CAAC;IACpF,CAAC,CAAC;EACN;EAEAnF,SAASA,CAACwD,IAAsB;IAC5B,MAAM4B,QAAQ,GAAGC,MAAM,CAAC,UAAU7B,IAAI,CAACjE,KAAK,GAAG,EAAEiE,IAAI,CAAC1D,KAAK,CAAC;IAC5D,IAAIsF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK5B,IAAI,CAAC1D,KAAK,EAAE;MAC9C,MAAMkE,KAAK,GAAG3B,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,IAAI,CAACsB,cAAc,CAAC,CAAC+C,IAAI,CAAC7C,GAAG,IAAG;QAC3D,MAAMhB,MAAM,GAA8B;UACtCC,IAAI,EAAE,MAAM;UACZC,GAAG,EAAE,KAAK;UACVC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,aAAa,EAAE,oBAAoB;UACnCC,cAAc,EAAE,iBAAiB;UACjCC,WAAW,EAAE;SAChB;QACD,OAAOX,MAAM,CAACgB,GAAG,CAAC,KAAKe,IAAI,CAACjE,KAAK;MACrC,CAAC,CAAC;MACF,IAAIyE,KAAK,EAAE;QACP,IAAI,CAAC/C,IAAI,CAACsB,cAAc,CAACyB,KAA2C,CAAC,GAAGoB,QAAQ;QAChF,IAAI,IAAI,CAACnE,IAAI,CAAC+D,QAAQ,EAAE,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ,CAACC,eAAe,CAAC,8BAA8B,CAAC;QAC1F,IAAI,IAAI,CAAChE,IAAI,CAACsE,GAAG,EAAE,IAAI,CAACtE,IAAI,CAACsE,GAAG,CAACC,aAAa,EAAE;MACpD;IACJ;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAIC,OAAO,CAAC,yDAAyD,CAAC,EAAE;MACpErD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,IAAI,CAACsB,cAAc,CAAC,CAACoD,OAAO,CAAClD,GAAG,IAAG;QAChD,IAAI,CAACxB,IAAI,CAACsB,cAAc,CAACE,GAAyC,CAAC,GAAG,EAAE;MAC5E,CAAC,CAAC;MACF,IAAI,IAAI,CAACxB,IAAI,CAAC+D,QAAQ,EAAE,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ,CAACC,eAAe,CAAC,6BAA6B,CAAC;MACzF,IAAI,IAAI,CAAChE,IAAI,CAACsE,GAAG,EAAE,IAAI,CAACtE,IAAI,CAACsE,GAAG,CAACC,aAAa,EAAE;IACpD;EACJ;EAEAI,UAAUA,CAACzC,KAAY;IACnBA,KAAK,CAAC0C,eAAe,EAAE;IACvB,MAAMC,OAAO,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/E,IAAI,CAACsB,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;IACjE,MAAM0D,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAE9G,IAAI,EAAE;IAAkB,CAAE,CAAC;IAC9D,MAAMmH,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,wBAAwB;IACrCJ,CAAC,CAACK,KAAK,EAAE;IACTR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;EACnC;;qBArOSpF,2BAA2B,EAAAjD,EAAA,CAAAgJ,iBAAA,CAAAC,EAAA,CAAAhK,YAAA,GAAAe,EAAA,CAAAgJ,iBAAA,CAWxBhK,eAAe;EAAA;;UAXlBiE,2BAA2B;IAAAiG,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAApJ,EAAA,CAAAqJ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC5ExC3J,EAAA,CAAAC,cAAA,aACuE;QADrCD,EAAA,CAAAE,UAAA,mBAAA2J,0DAAA;UAAA,OAASD,GAAA,CAAArG,OAAA,EAAS;QAAA,EAAC;QAEjDvD,EAAA,CAAAC,cAAA,aAAqF;QAAjDD,EAAA,CAAAE,UAAA,mBAAA4J,0DAAAC,MAAA;UAAA,OAASA,MAAA,CAAAhC,eAAA,EAAwB;QAAA,EAAC;QAMlD/H,EAJhB,CAAAC,cAAA,gBAAoC,aACJ,aACG,aACG,kBACQ;QAAAD,EAAA,CAAAU,MAAA,cAAO;QACzCV,EADyC,CAAAW,YAAA,EAAW,EAC9C;QAEFX,EADJ,CAAAC,cAAA,aAAwB,YACqB;QAAAD,EAAA,CAAAU,MAAA,+CAAwB;QAAAV,EAAA,CAAAW,YAAA,EAAK;QACtEX,EAAA,CAAAC,cAAA,YAAiD;QAC7CD,EAAA,CAAAU,MAAA,4CACJ;QAERV,EAFQ,CAAAW,YAAA,EAAI,EACF,EACJ;QAEFX,EADJ,CAAAC,cAAA,eAA4B,kBAE+C;QAA7BD,EAAA,CAAAE,UAAA,mBAAA8J,8DAAAD,MAAA;UAAA,OAASH,GAAA,CAAA9B,UAAA,CAAAiC,MAAA,CAAkB;QAAA,EAAC;QAClE/J,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,gBAAQ;QACtBV,EADsB,CAAAW,YAAA,EAAW,EACxB;QACTX,EAAA,CAAAC,cAAA,kBAC8B;QADuBD,EAAA,CAAAE,UAAA,mBAAA+J,8DAAA;UAAA,OAASL,GAAA,CAAArG,OAAA,EAAS;QAAA,EAAC;QAEpEvD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,aAAK;QAG3BV,EAH2B,CAAAW,YAAA,EAAW,EACrB,EACP,EACJ;QAIEX,EAFR,CAAAC,cAAA,eAA8B,eACC,gBACK;QACxBD,EAAA,CAAAU,MAAA,IACJ;QAAAV,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAC,cAAA,gBAAkC;QAC9BD,EAAA,CAAAU,MAAA,IACJ;QACJV,EADI,CAAAW,YAAA,EAAO,EACL;QACNX,EAAA,CAAAC,cAAA,eAA0B;QACtBD,EAAA,CAAAmC,SAAA,eACM;QAGlBnC,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACD;QAKGX,EAHZ,CAAAC,cAAA,eAAmC,eACD,0BACgC,iBAC3C;QAAAD,EAAA,CAAAU,MAAA,mCAAiB;QAAAV,EAAA,CAAAW,YAAA,EAAY;QACxCX,EAAA,CAAAC,cAAA,iBACqC;QADrBD,EAAA,CAAAkK,gBAAA,2BAAAC,qEAAAJ,MAAA;UAAA/J,EAAA,CAAAoK,kBAAA,CAAAR,GAAA,CAAAxG,UAAA,EAAA2G,MAAA,MAAAH,GAAA,CAAAxG,UAAA,GAAA2G,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAwB;QACpC/J,EAAA,CAAAE,UAAA,mBAAAmK,6DAAAN,MAAA;UAAA,OAASH,GAAA,CAAAxE,cAAA,CAAA2E,MAAA,CAAsB;QAAA,EAAC;QADpC/J,EAAA,CAAAW,YAAA,EACqC;QACrCX,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAU,MAAA,cAAM;QAAAV,EAAA,CAAAW,YAAA,EAAW;QACrCX,EAAA,CAAAoC,UAAA,KAAAkI,8CAAA,qBAC8B;QAItCtK,EADI,CAAAW,YAAA,EAAiB,EACf;QAEFX,EADJ,CAAAC,cAAA,eAA0B,4BACqB;QACvCD,EAAA,CAAAoC,UAAA,KAAAmI,uDAAA,8BAC0E;QAMtFvK,EAFQ,CAAAW,YAAA,EAAmB,EACjB,EACJ;QAGFX,EADJ,CAAAC,cAAA,gBAAgC,eACC;QAYzBD,EAVA,CAAAoC,UAAA,KAAAoI,2CAAA,kBAAgE,KAAAC,2CAAA,kBAUJ;QAyCpEzK,EADI,CAAAW,YAAA,EAAM,EACH;QAKKX,EAHZ,CAAAC,cAAA,kBAAoC,eACP,gBACG,gBACV;QAAAD,EAAA,CAAAU,MAAA,YAAI;QAAAV,EAAA,CAAAW,YAAA,EAAW;QACzBX,EAAA,CAAAU,MAAA,sCACJ;QACJV,EADI,CAAAW,YAAA,EAAO,EACL;QAEFX,EADJ,CAAAC,cAAA,eAA4B,kBAE4B;QADHD,EAAA,CAAAE,UAAA,mBAAAwK,8DAAA;UAAA,OAASd,GAAA,CAAAjC,YAAA,EAAc;QAAA,EAAC;QAErE3H,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,oBAAY;QAAAV,EAAA,CAAAW,YAAA,EAAW;QACjCX,EAAA,CAAAU,MAAA,qBACJ;QAAAV,EAAA,CAAAW,YAAA,EAAS;QACTX,EAAA,CAAAC,cAAA,kBAAkF;QAApBD,EAAA,CAAAE,UAAA,mBAAAyK,8DAAA;UAAA,OAASf,GAAA,CAAArG,OAAA,EAAS;QAAA,EAAC;QAC7EvD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,aAAK;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAC1BX,EAAA,CAAAU,MAAA,wBACJ;QAIhBV,EAJgB,CAAAW,YAAA,EAAS,EACP,EACD,EACP,EACJ;;;QAjJgDX,EAAA,CAAAmB,UAAA,eAAAoB,SAAA,CAAY;QAESvC,EAAA,CAAAqB,SAAA,EAAa;QAAbrB,EAAA,CAAAmB,UAAA,gBAAAoB,SAAA,CAAa;QA8BhEvC,EAAA,CAAAqB,SAAA,IACJ;QADIrB,EAAA,CAAA4K,kBAAA,MAAAhB,GAAA,CAAAlG,mBAAA,GAAAuB,MAAA,UAAA2E,GAAA,CAAAnG,cAAA,2BACJ;QAEIzD,EAAA,CAAAqB,SAAA,GACJ;QADIrB,EAAA,CAAAwB,kBAAA,MAAAoI,GAAA,CAAA9E,qBAAA,SACJ;QAG2B9E,EAAA,CAAAqB,SAAA,GAAyC;QAAzCrB,EAAA,CAAA6K,WAAA,UAAAjB,GAAA,CAAA9E,qBAAA,QAAyC;QAUpD9E,EAAA,CAAAqB,SAAA,GAAwB;QAAxBrB,EAAA,CAAA8K,gBAAA,YAAAlB,GAAA,CAAAxG,UAAA,CAAwB;QAGLpD,EAAA,CAAAqB,SAAA,GAAgB;QAAhBrB,EAAA,CAAAmB,UAAA,SAAAyI,GAAA,CAAAxG,UAAA,CAAgB;QAQfpD,EAAA,CAAAqB,SAAA,GAAmB;QAAnBrB,EAAA,CAAAmB,UAAA,YAAAyI,GAAA,CAAAvG,gBAAA,CAAmB;QAYjCrD,EAAA,CAAAqB,SAAA,GAAoC;QAApCrB,EAAA,CAAAmB,UAAA,SAAAyI,GAAA,CAAA7G,eAAA,GAAAkC,MAAA,OAAoC;QAUtCjF,EAAA,CAAAqB,SAAA,EAAkC;QAAlCrB,EAAA,CAAAmB,UAAA,SAAAyI,GAAA,CAAA7G,eAAA,GAAAkC,MAAA,KAAkC;QAoDtDjF,EAAA,CAAAqB,SAAA,GAA+C;QAA/CrB,EAAA,CAAAmB,UAAA,aAAAyI,GAAA,CAAAlG,mBAAA,GAAAuB,MAAA,OAA+C;;;mBDnG3DtF,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9L,eAAe,EAAA+L,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfhM,aAAa,EAAAiM,EAAA,CAAAC,OAAA,EACbjM,cAAc,EAAAkM,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAH,EAAA,CAAAI,SAAA,EACdvM,kBAAkB,EAClBC,gBAAgB,EAAAuM,EAAA,CAAAC,UAAA,EAChBvM,cAAc,EAAAwM,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,aAAA,EACdhN,eAAe,EACfO,WAAW,EAAA0M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX5M,mBAAmB;IAAA6M,MAAA;IAAApJ,IAAA;MAAAqJ,SAAA,EAEX,CACR5M,OAAO,CAAC,WAAW,EAAE,CACjBE,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;QAAE4M,OAAO,EAAE;MAAC,CAAE,CAAC,EACrB1M,OAAO,CAAC,eAAe,EAAEF,KAAK,CAAC;QAAE4M,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAClD,CAAC,EACF3M,UAAU,CAAC,QAAQ,EAAE,CACjBC,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;QAAE4M,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACnD,CAAC,CACL,CAAC,EACF7M,OAAO,CAAC,YAAY,EAAE,CAClBE,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;QAAE6M,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EACrD1M,OAAO,CAAC,wCAAwC,EAC5CF,KAAK,CAAC;QAAE6M,SAAS,EAAE,eAAe;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACzD,CAAC,EACF3M,UAAU,CAAC,QAAQ,EAAE,CACjBC,OAAO,CAAC,eAAe,EACnBF,KAAK,CAAC;QAAE6M,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAC7D,CAAC,CACL,CAAC,EACF7M,OAAO,CAAC,eAAe,EAAE,CACrBE,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;QAAE6M,SAAS,EAAE,YAAY;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EAC9C1M,OAAO,CAAC,wCAAwC,EAC5CF,KAAK,CAAC;QAAE6M,SAAS,EAAE,UAAU;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACpD,CAAC,CACL,CAAC;IACL;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
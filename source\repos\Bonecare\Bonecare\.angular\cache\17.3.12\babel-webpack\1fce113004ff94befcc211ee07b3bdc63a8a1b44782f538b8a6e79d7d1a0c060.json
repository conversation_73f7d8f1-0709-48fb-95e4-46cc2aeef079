{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ChangeDetectorRef } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { finalize, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { IntegrationService } from '../../pre-consulta-questionario/Service/integracao-vittal-tec.service';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../pre-consulta-questionario/Service/integracao-vittal-tec.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-bar\";\nimport * as i7 from \"@angular/material/divider\";\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"sync\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_2_Template, 2, 0, \"mat-icon\", 25)(3, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_3_Template, 2, 0, \"mat-icon\", 25)(4, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_4_Template, 2, 0, \"mat-icon\", 26)(5, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_5_Template, 2, 0, \"mat-icon\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"span\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.currentStep === i_r2)(\"completed\", step_r1.completed)(\"error\", step_r1.error);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r1.completed && !step_r1.error);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r1.error);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !step_r1.completed && !step_r1.error && ctx_r2.currentStep === i_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !step_r1.completed && !step_r1.error && ctx_r2.currentStep !== i_r2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r1.label);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, ModalColetaDadosVittaltecComponent_div_15_div_1_Template, 9, 11, \"div\", 21)(2, ModalColetaDadosVittaltecComponent_div_15_div_2_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"div\", 34)(3, \"mat-icon\", 35);\n    i0.ɵɵtext(4, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 36);\n    i0.ɵɵtext(6, \"Ops! Algo deu errado\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 37);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38)(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_16_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startProcess());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Tentar Novamente \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 40);\n    i0.ɵɵtext(15, \" Cancelar \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"mat-icon\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + ctx_r2.capturedData.status.toLowerCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.status === \"aprovado\" ? \"check_circle\" : \"info\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Status: \", i0.ɵɵpipeBind1(6, 3, ctx_r2.capturedData.status), \"\");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" ID do Paciente: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.IdPaciente);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"favorite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Press\\u00E3o Arterial: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"span\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 68);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 69);\n    i0.ɵɵtext(13, \"mmHg\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.pressaoSistolica || \"--\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.pressaoDiastolica || \"--\");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"device_thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Temperatura: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 1, ctx_r2.capturedData.data.temperatura, \"1.1-1\"), \"\\u00B0C \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"air\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Satura\\u00E7\\u00E3o de O\\u2082: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.data.oxigenacao, \"% \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"monitor_heart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Freq. Card\\u00EDaca: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8, \"bpm\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.data.batimento, \" \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"h4\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dados do Paciente \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵtemplate(6, ModalColetaDadosVittaltecComponent_div_17_div_9_div_6_Template, 7, 1, \"div\", 58)(7, ModalColetaDadosVittaltecComponent_div_17_div_9_div_7_Template, 14, 2, \"div\", 59)(8, ModalColetaDadosVittaltecComponent_div_17_div_9_div_8_Template, 8, 4, \"div\", 59)(9, ModalColetaDadosVittaltecComponent_div_17_div_9_div_9_Template, 7, 1, \"div\", 59)(10, ModalColetaDadosVittaltecComponent_div_17_div_9_div_10_Template, 9, 1, \"div\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.IdPaciente);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.pressaoSistolica || ctx_r2.capturedData.data.pressaoDiastolica);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.temperatura);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.oxigenacao);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.batimento);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"mat-icon\", 72);\n    i0.ɵɵtext(3, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 73);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Coletado em: \", i0.ɵɵpipeBind2(6, 1, ctx_r2.capturedData.timestamp, \"dd/MM/yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"h4\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"data_object\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dados Coletados \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 75)(6, \"pre\", 76);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"json\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 1, ctx_r2.capturedData));\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\", 78);\n    i0.ɵɵtext(2, \"info_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 79);\n    i0.ɵɵtext(4, \"Nenhum dado dispon\\u00EDvel para visualiza\\u00E7\\u00E3o.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"mat-icon\", 43);\n    i0.ɵɵtext(3, \"assignment_turned_in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 44);\n    i0.ɵɵtext(5, \"Dados Coletados com Sucesso\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-divider\");\n    i0.ɵɵelementStart(7, \"div\", 45);\n    i0.ɵɵtemplate(8, ModalColetaDadosVittaltecComponent_div_17_div_8_Template, 7, 5, \"div\", 46)(9, ModalColetaDadosVittaltecComponent_div_17_div_9_Template, 11, 5, \"div\", 47)(10, ModalColetaDadosVittaltecComponent_div_17_div_10_Template, 7, 4, \"div\", 48)(11, ModalColetaDadosVittaltecComponent_div_17_div_11_Template, 9, 3, \"div\", 49)(12, ModalColetaDadosVittaltecComponent_div_17_div_12_Template, 5, 0, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.data);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.timestamp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.capturedData == null ? null : ctx_r2.capturedData.data) && ctx_r2.capturedData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.capturedData);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81)(2, \"mat-icon\", 82);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 83);\n    i0.ɵɵtext(5, \"Processo Conclu\\u00EDdo!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 84);\n    i0.ɵɵtext(7, \" Todos os dados foram coletados e processados com sucesso. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Cancelar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_20_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.retry());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Tentar Novamente \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Cancelar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.continueProcess());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Confirmar e Continuar \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onContinue());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Finalizar \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 90)(2, \"mat-icon\", 91);\n    i0.ɵɵtext(3, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processando... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ModalColetaDadosVittaltecComponent = /*#__PURE__*/(() => {\n  class ModalColetaDadosVittaltecComponent {\n    dialogRef;\n    data;\n    integrationService;\n    cdr;\n    isLoading = false;\n    currentStep = 0;\n    errorMessage = '';\n    capturedData = null;\n    showDataPreview = false;\n    steps = [{\n      label: 'Verificando conectividade...',\n      completed: false,\n      error: false\n    }, {\n      label: 'Capturando dados...',\n      completed: false,\n      error: false\n    }, {\n      label: 'Lendo informações...',\n      completed: false,\n      error: false\n    }, {\n      label: 'Alternando janela do navegador...',\n      completed: false,\n      error: false\n    }];\n    constructor(dialogRef, data, integrationService, cdr) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.integrationService = integrationService;\n      this.cdr = cdr;\n    }\n    ngOnInit() {\n      console.clear();\n      this.startIntegrationProcess();\n    }\n    startIntegrationProcess() {\n      this.isLoading = true;\n      this.currentStep = 0;\n      this.errorMessage = '';\n      this.capturedData = null;\n      this.showDataPreview = false;\n      this.steps.forEach(step => {\n        step.completed = false;\n        step.error = false;\n      });\n      this.executeHealthCheck();\n    }\n    executeHealthCheck() {\n      this.currentStep = 0;\n      this.cdr.detectChanges();\n      this.integrationService.healthCheck().pipe(catchError(error => {\n        console.error('Health check error caught:', error);\n        this.steps[0].error = true;\n        this.errorMessage = 'Falha na verificação de conectividade. Verifique se o serviço VittalTec está rodando na porta 8080.';\n        this.isLoading = false;\n        this.registrarLog('healthCheck', 'ERRO', JSON.stringify(error));\n        this.cdr.detectChanges();\n        return of(null);\n      }), finalize(() => {\n        setTimeout(() => {\n          if (!this.steps[0].error) {\n            this.steps[0].completed = true;\n            this.cdr.detectChanges();\n            this.executeCapture();\n          }\n        }, 500);\n      })).subscribe({\n        next: response => {\n          if (response) {\n            this.registrarLog('healthCheck', 'SUCESSO', JSON.stringify(response));\n          }\n        },\n        error: error => {\n          console.error('Health check subscription error:', error);\n          this.steps[0].error = true;\n          this.errorMessage = 'Erro inesperado na verificação de conectividade.';\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    executeCapture() {\n      this.currentStep = 1;\n      this.cdr.detectChanges();\n      this.integrationService.capture().pipe(catchError(error => {\n        console.error('Capture error caught:', error);\n        this.steps[1].error = true;\n        this.errorMessage = 'Falha na captura dos dados. Verifique se o dispositivo VittalTec está conectado e funcionando.';\n        this.isLoading = false;\n        this.registrarLog('capture', 'ERRO', JSON.stringify(error));\n        this.cdr.detectChanges();\n        return of(null);\n      }), finalize(() => {\n        setTimeout(() => {\n          if (!this.steps[1].error) {\n            this.steps[1].completed = true;\n            this.cdr.detectChanges();\n            this.executeRead();\n          }\n        }, 500);\n      })).subscribe({\n        next: response => {\n          if (response) {\n            this.registrarLog('capture', 'SUCESSO', JSON.stringify(response));\n          }\n        },\n        error: error => {\n          console.error('Capture subscription error:', error);\n          this.steps[1].error = true;\n          this.errorMessage = 'Erro inesperado na captura de dados.';\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    executeRead() {\n      this.currentStep = 2;\n      this.cdr.detectChanges();\n      this.integrationService.read().pipe(catchError(error => {\n        console.error('Read error caught:', error);\n        this.steps[2].error = true;\n        this.errorMessage = 'Falha na leitura dos dados. Verifique se os dados foram capturados corretamente.';\n        this.isLoading = false;\n        this.registrarLog('read', 'ERRO', JSON.stringify(error));\n        this.cdr.detectChanges();\n        return of(null);\n      }), finalize(() => {\n        setTimeout(() => {\n          if (!this.steps[2].error) {\n            this.steps[2].completed = true;\n            this.cdr.detectChanges();\n            if (this.capturedData) {\n              this.showDataPreview = true;\n              this.isLoading = false;\n            } else {\n              this.executeSwitchWindow();\n            }\n          }\n        }, 500);\n      })).subscribe({\n        next: response => {\n          if (response) {\n            this.registrarLog('read', 'SUCESSO', JSON.stringify(response));\n            console.log(\"response\", response);\n            try {\n              // Parse and deserialize the JSON response\n              const vitalSignsResponse = this.parseVitalSignsResponse(response);\n              if (vitalSignsResponse && vitalSignsResponse.data) {\n                // Store the original response for component use\n                this.capturedData = response;\n                // Prepare vital signs data for localStorage\n                const vitalSignsData = {\n                  ...vitalSignsResponse.data,\n                  timestamp: new Date().toISOString()\n                };\n                // Store the deserialized data in localStorage\n                CriptografarUtil.localStorageCriptografado(\"VittalTecDados\", JSON.stringify(vitalSignsData));\n                console.log('✅ Dados vitais armazenados no localStorage:', vitalSignsData);\n                // Check if the process is completed\n                if (vitalSignsResponse.status === 'Finalizado') {\n                  console.log('🎯 Processo de leitura finalizado com sucesso');\n                }\n              } else {\n                this.steps[2].error = true;\n                this.errorMessage = 'Dados vitais não encontrados na resposta.';\n                this.isLoading = false;\n              }\n            } catch (error) {\n              console.error('❌ Erro ao processar resposta dos dados vitais:', error);\n              this.steps[2].error = true;\n              this.errorMessage = 'Erro ao processar os dados recebidos.';\n              this.isLoading = false;\n            }\n          }\n        },\n        error: error => {\n          console.error('Read subscription error:', error);\n          this.steps[2].error = true;\n          this.errorMessage = 'Erro inesperado na leitura de dados.';\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    executeSwitchWindow() {\n      this.currentStep = 3;\n      this.integrationService.switchWindowBrowser().pipe(finalize(() => {\n        setTimeout(() => {\n          if (!this.steps[3].error) {\n            this.steps[3].completed = true;\n            this.isLoading = false;\n            setTimeout(() => {\n              this.onContinue();\n            }, 1000);\n          }\n        }, 500);\n      })).subscribe({\n        next: response => {\n          this.registrarLog('switchWindowBrowser', '', JSON.stringify(response));\n        },\n        error: error => {\n          console.error('Switch window failed:', error);\n          this.steps[3].error = true;\n          this.errorMessage = 'Falha ao alternar a janela do navegador.';\n          this.isLoading = false;\n          this.registrarLog('switchWindowBrowser', '', JSON.stringify(error));\n        }\n      });\n    }\n    registrarLog(endpoint, requisicao, resposta) {\n      const logData = {\n        Id: null,\n        Requisicao: `${endpoint}: ${requisicao}`,\n        Resposta: resposta,\n        DtCadastro: new Date()\n      };\n      this.integrationService.RegistraLogRequisicao(logData).subscribe({\n        next: response => {\n          response;\n        },\n        error: error => {\n          console.error('Erro ao registrar log:', error);\n        }\n      });\n    }\n    continueProcess() {\n      this.showDataPreview = false;\n      this.isLoading = true;\n      this.executeSwitchWindow();\n    }\n    areAllStepsCompleted() {\n      return this.steps.every(step => step.completed);\n    }\n    retry() {\n      this.startIntegrationProcess();\n    }\n    startProcess() {\n      this.startIntegrationProcess();\n    }\n    onContinue() {\n      this.dialogRef.close({\n        action: 'continuar',\n        data: this.capturedData\n      });\n    }\n    onCancel() {\n      this.dialogRef.close({\n        action: 'cancelar'\n      });\n    }\n    onClose() {\n      this.dialogRef.close();\n    }\n    /**\n     * Parse and validate the vital signs response from the integration service\n     * @param response - Raw response from the integration service\n     * @returns Parsed VitalSignsResponse or null if invalid\n     */\n    parseVitalSignsResponse(response) {\n      try {\n        // If response is already an object, use it directly\n        let parsedResponse = response;\n        // If response is a string, try to parse it as JSON\n        if (typeof response === 'string') {\n          parsedResponse = JSON.parse(response);\n        }\n        // Validate the response structure\n        if (!parsedResponse || typeof parsedResponse !== 'object') {\n          console.warn('⚠️ Resposta inválida: não é um objeto válido');\n          return null;\n        }\n        // Check if it has the expected structure\n        if (!parsedResponse.hasOwnProperty('status') || !parsedResponse.hasOwnProperty('data')) {\n          console.warn('⚠️ Resposta inválida: estrutura não contém \"status\" ou \"data\"');\n          return null;\n        }\n        const data = parsedResponse.data;\n        // Validate vital signs data structure\n        const requiredFields = ['pressaoSistolica', 'pressaoDiastolica', 'temperatura', 'oxigenacao', 'batimento'];\n        const missingFields = requiredFields.filter(field => !data.hasOwnProperty(field));\n        if (missingFields.length > 0) {\n          console.warn('⚠️ Campos obrigatórios ausentes nos dados vitais:', missingFields);\n          return null;\n        }\n        // Validate data types\n        const numericFields = ['pressaoSistolica', 'pressaoDiastolica', 'temperatura', 'oxigenacao', 'batimento'];\n        for (const field of numericFields) {\n          if (typeof data[field] !== 'number' || isNaN(data[field])) {\n            console.warn(`⚠️ Campo \"${field}\" deve ser um número válido, recebido:`, data[field]);\n            return null;\n          }\n        }\n        console.log('✅ Resposta dos dados vitais validada com sucesso');\n        return parsedResponse;\n      } catch (error) {\n        console.error('❌ Erro ao fazer parse da resposta dos dados vitais:', error);\n        return null;\n      }\n    }\n    static ɵfac = function ModalColetaDadosVittaltecComponent_Factory(t) {\n      return new (t || ModalColetaDadosVittaltecComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.IntegrationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModalColetaDadosVittaltecComponent,\n      selectors: [[\"app-modal-coleta-dados-vittaltec\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 8,\n      consts: [[1, \"modal-overlay\"], [1, \"modal-container\"], [1, \"modal-card\"], [1, \"modal-header\"], [1, \"header-left\"], [1, \"icon-wrapper\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z\", \"stroke\", \"#00ff80\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"fill\", \"none\"], [\"d\", \"M9 12L11 14L15 10\", \"stroke\", \"#00ff80\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"header-text\"], [1, \"modal-title\"], [1, \"modal-subtitle\"], [1, \"modal-content\"], [\"class\", \"steps-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"data-preview-container\", 4, \"ngIf\"], [\"class\", \"success-container\", 4, \"ngIf\"], [1, \"modal-actions\"], [\"class\", \"action-group\", 4, \"ngIf\"], [\"class\", \"action-group loading-actions\", 4, \"ngIf\"], [1, \"steps-container\"], [\"class\", \"step-item\", 3, \"active\", \"completed\", \"error\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"progress-container\", 4, \"ngIf\"], [1, \"step-item\"], [1, \"step-icon\"], [4, \"ngIf\"], [\"class\", \"rotating\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-label\"], [1, \"rotating\"], [1, \"progress-container\"], [\"mode\", \"indeterminate\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-title\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"mat-dialog-close\", \"\", 1, \"cancel-button\"], [1, \"data-preview-container\"], [1, \"preview-header\"], [1, \"preview-icon\"], [1, \"preview-title\"], [1, \"preview-content\"], [\"class\", \"status-section\", 4, \"ngIf\"], [\"class\", \"patient-data-section\", 4, \"ngIf\"], [\"class\", \"timestamp-section\", 4, \"ngIf\"], [\"class\", \"raw-data-section\", 4, \"ngIf\"], [\"class\", \"no-data-section\", 4, \"ngIf\"], [1, \"status-section\"], [1, \"status-item\", 3, \"ngClass\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"patient-data-section\"], [1, \"section-title\"], [1, \"data-grid\"], [\"class\", \"data-item\", 4, \"ngIf\"], [\"class\", \"data-item vital-sign\", 4, \"ngIf\"], [1, \"data-item\"], [1, \"data-label\"], [1, \"data-icon\"], [1, \"data-value\"], [1, \"data-item\", \"vital-sign\"], [1, \"data-value\", \"pressure-value\"], [1, \"systolic\"], [1, \"separator\"], [1, \"diastolic\"], [1, \"unit\"], [1, \"timestamp-section\"], [1, \"timestamp-item\"], [1, \"timestamp-icon\"], [1, \"timestamp-text\"], [1, \"raw-data-section\"], [1, \"raw-data-container\"], [1, \"raw-data\"], [1, \"no-data-section\"], [1, \"no-data-icon\"], [1, \"no-data-text\"], [1, \"success-container\"], [1, \"success-card\"], [1, \"success-icon\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"action-group\"], [\"mat-stroked-button\", \"\", 1, \"btn-secondary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-primary\", \"full-width\", 3, \"click\"], [1, \"action-group\", \"loading-actions\"], [1, \"loading-text\"], [1, \"loading-icon\"]],\n      template: function ModalColetaDadosVittaltecComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7)(8, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"h2\", 10);\n          i0.ɵɵtext(11, \"Coleta de Dados VittalTec\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 11);\n          i0.ɵɵtext(13, \"Integra\\u00E7\\u00E3o com dispositivo de an\\u00E1lise\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 12);\n          i0.ɵɵtemplate(15, ModalColetaDadosVittaltecComponent_div_15_Template, 3, 2, \"div\", 13)(16, ModalColetaDadosVittaltecComponent_div_16_Template, 16, 1, \"div\", 14)(17, ModalColetaDadosVittaltecComponent_div_17_Template, 13, 5, \"div\", 15)(18, ModalColetaDadosVittaltecComponent_div_18_Template, 8, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 17);\n          i0.ɵɵtemplate(20, ModalColetaDadosVittaltecComponent_div_20_Template, 9, 0, \"div\", 18)(21, ModalColetaDadosVittaltecComponent_div_21_Template, 9, 0, \"div\", 18)(22, ModalColetaDadosVittaltecComponent_div_22_Template, 5, 0, \"div\", 18)(23, ModalColetaDadosVittaltecComponent_div_23_Template, 5, 0, \"div\", 19);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading || !ctx.showDataPreview);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataPreview && ctx.capturedData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.areAllStepsCompleted() && !ctx.showDataPreview && !ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataPreview);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.areAllStepsCompleted() && !ctx.showDataPreview && !ctx.isLoading && !ctx.errorMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.JsonPipe, i3.DecimalPipe, i3.TitleCasePipe, i3.DatePipe, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatButtonModule, i4.MatButton, MatCardModule, MatSelectModule, MatCheckboxModule, MatIconModule, i5.MatIcon, MatDialogModule, i1.MatDialogClose, MatProgressBarModule, i6.MatProgressBar, MatDividerModule, i7.MatDivider],\n      styles: [\".modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#0009;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);display:flex;justify-content:center;align-items:center;z-index:1000;padding:1rem}.modal-container[_ngcontent-%COMP%]{width:100%;max-width:500px;max-height:70vh;overflow-y:auto;display:flex;justify-content:center;align-items:center}.modal-card[_ngcontent-%COMP%]{background:#fff;border-radius:24px;box-shadow:0 8px 30px #0000001f;overflow:hidden;width:100%;position:relative;animation:_ngcontent-%COMP%_modalEnter .4s ease-out}.modal-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f0fff4,#e6ffed);padding:1.5rem;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid rgba(0,255,128,.1)}.modal-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1.5rem}.modal-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{color:#666;transition:all .2s ease;width:40px;height:40px}.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover{color:#333;background-color:#0000000a;transform:scale(1.1)}.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px}.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;width:60px;height:60px;background:#00ff801a;border-radius:50%;transition:all .3s cubic-bezier(.4,0,.2,1);flex-shrink:0}.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]:hover{transform:scale(1.05);background:#00ff8026}.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:36px;height:36px}.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#343a40;margin:0 0 .25rem;line-height:1.2}@media (max-width: 768px){.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:1.3rem}}.modal-header[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%]{font-size:1rem;color:#6c757d;line-height:1.6;max-width:400px;margin:0 auto}.modal-content[_ngcontent-%COMP%]{padding:1.5rem;min-height:150px;max-height:calc(70vh - 200px);overflow-y:auto}@media (max-width: 768px){.modal-content[_ngcontent-%COMP%]{padding:1rem}}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]{display:flex;align-items:center;border-left:3px solid transparent;padding:.75rem 0 .75rem .75rem;margin-left:.75rem;transition:all .3s cubic-bezier(.4,0,.2,1)}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid rgba(0,0,0,.05)}.steps-container[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]{border-left-color:#00ff80;background:#00ff800d;border-radius:0 12px 12px 0}.steps-container[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{color:#000!important}.steps-container[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]{border-left-color:#28a745}.steps-container[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#28a745}.steps-container[_ngcontent-%COMP%]   .step-item.error[_ngcontent-%COMP%]{border-left-color:#dc3545;background:#dc35450d;border-radius:0 12px 12px 0}.steps-container[_ngcontent-%COMP%]   .step-item.error[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#dc3545}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{margin-right:.75rem;display:flex;align-items:center;justify-content:center;width:28px;height:28px}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon.rotating[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_rotate 2s linear infinite;color:#00ff80}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]{flex:1}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%]{font-weight:500;color:#343a40;font-size:.9rem}.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]{margin-top:1.5rem}.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]     .mat-progress-bar-fill:after{background-color:#00ff80}.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]     .mat-progress-bar-buffer{background:#00ff8033}.error-container[_ngcontent-%COMP%]{text-align:center;padding:2rem 0}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]{background:#dc35450d;border:1px solid rgba(220,53,69,.2);border-radius:12px;padding:2rem;max-width:450px;margin:0 auto}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.75rem;margin-bottom:1rem}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:2rem;color:#dc3545}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#dc3545;margin:0}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#343a40;line-height:1.6;margin:0 0 1.5rem;font-size:.95rem}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center;flex-wrap:wrap}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%]{background:#dc3545;color:#fff}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%]:hover{background:#bd2130}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:.5rem}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]{color:#6c757d;border-color:#ddd}.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]:hover{background:#0000000a}.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-icon[_ngcontent-%COMP%]{color:#28a745;margin-bottom:1rem}.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-title[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:600;color:#343a40;margin:0 0 .5rem}.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-subtitle[_ngcontent-%COMP%]{color:#6c757d;margin:0}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]{margin-top:1.5rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.75rem 1rem;border-radius:12px;margin-bottom:.5rem;background:#f8f9fa;transition:all .3s cubic-bezier(.4,0,.2,1)}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]:hover{background:#00ff800d}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]{font-weight:600;color:#343a40;flex:1}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]{font-weight:500;color:#00ff80;text-align:right;font-size:1.1rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .array-display[_ngcontent-%COMP%]   .array-items[_ngcontent-%COMP%]   .array-item[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:1rem;margin-bottom:1rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .array-display[_ngcontent-%COMP%]   .array-items[_ngcontent-%COMP%]   .array-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#343a40;display:block;margin-bottom:.5rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .json-container[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:1rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .json-display[_ngcontent-%COMP%]{background:transparent;border:none;font-family:Courier New,monospace;font-size:.9rem;color:#343a40;white-space:pre-wrap;word-break:break-word;margin:0;line-height:1.4}.success-container[_ngcontent-%COMP%]{text-align:center;padding:2rem 0}.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]{background:#28a7450d;border:1px solid rgba(40,167,69,.2);border-radius:12px;padding:2rem}.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{color:#28a745;margin-bottom:1rem}.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:600;color:#28a745;margin:0 0 1rem}.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%]{color:#343a40;line-height:1.6;margin:0}.modal-actions[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-top:1px solid rgba(0,0,0,.1);background:#f8f9fa80}@media (max-width: 768px){.modal-actions[_ngcontent-%COMP%]{padding:1rem}}.modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;gap:1rem}@media (max-width: 768px){.modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]{flex-direction:column}.modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%]{justify-content:center}.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%]{display:flex;align-items:center;color:#6c757d;font-weight:500;margin:0}.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%]   .loading-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_rotate 2s linear infinite}.btn-secondary[_ngcontent-%COMP%]{color:#6c757d;border-color:#6c757d;padding:.75rem 1.5rem;font-weight:600;border-radius:12px;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;color:#fff;transform:translateY(-1px)}.btn-primary[_ngcontent-%COMP%]{background-color:#00ff80;color:#343a40;padding:.75rem 2rem;font-weight:600;border-radius:12px;box-shadow:0 4px 12px #00ff804d;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0c6;box-shadow:0 6px 20px #00ff8066;transform:translateY(-2px)}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-primary.full-width[_ngcontent-%COMP%]{width:100%;justify-content:center}.close-button[_ngcontent-%COMP%]{position:absolute;top:1rem;right:1rem;transition:all .3s cubic-bezier(.4,0,.2,1)}.close-button[_ngcontent-%COMP%]:hover:not(:disabled){background:#fff;transform:scale(1.1)}.close-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.close-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#6c757d}@keyframes _ngcontent-%COMP%_modalEnter{0%{opacity:0;transform:scale(.9) translateY(-20px)}to{opacity:1;transform:scale(1) translateY(0)}}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}  .mat-progress-bar{height:6px;border-radius:3px;overflow:hidden}  .mat-divider{border-top-color:#00ff8033;margin:1.5rem 0}  .vittaltec-modal-panel .mat-dialog-container{padding:0!important;border-radius:16px!important;overflow:hidden!important;box-shadow:0 8px 32px #0000001f!important}  .mat-raised-button{border-radius:8px!important;font-weight:500!important;text-transform:none!important;box-shadow:0 2px 8px #0000001a!important}  .mat-raised-button:hover{box-shadow:0 4px 12px #00000026!important}  .mat-stroked-button{border-radius:8px!important;font-weight:500!important;text-transform:none!important}  .mat-icon-button{border-radius:8px!important}@media (max-width: 768px){.modal-overlay[_ngcontent-%COMP%]{padding:.5rem}.modal-container[_ngcontent-%COMP%]{max-width:95vw;max-height:85vh}.modal-card[_ngcontent-%COMP%]{margin:0;max-height:85vh}.modal-header[_ngcontent-%COMP%]{padding:1rem}.modal-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{gap:1rem}.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{width:50px;height:50px}.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:30px;height:30px}.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:1.2rem}.modal-header[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%]{font-size:.8rem}.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]{margin-left:.5rem;padding-left:.75rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]{text-align:left}}@media (max-width: 480px){.modal-header[_ngcontent-%COMP%]{padding:1.5rem 1rem}.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:1.2rem}.modal-content[_ngcontent-%COMP%], .modal-actions[_ngcontent-%COMP%]{padding:1rem}}.preview-content[_ngcontent-%COMP%]{padding:16px 0}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]{margin-bottom:20px}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px;border-radius:8px;background-color:#f5f5f5}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item.status-aprovado[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item.status-aprovado[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#4caf50}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:20px}.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-weight:500;font-size:14px}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{display:flex;align-items:center;margin:0 0 16px;font-size:16px;font-weight:500;color:#333}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px;color:#666}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:12px}@media (min-width: 600px){.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:12px;border:1px solid #e0e0e0;border-radius:8px;background-color:#fafafa;transition:all .2s ease}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]:hover{background-color:#f0f0f0;border-color:#d0d0d0}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item.vital-sign[_ngcontent-%COMP%]{border-left:4px solid #2196f3}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:12px;font-weight:500;color:#666;margin-bottom:4px;text-transform:uppercase;letter-spacing:.5px}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]   .data-icon[_ngcontent-%COMP%]{margin-right:6px;font-size:16px;color:#2196f3}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#666;margin-left:4px}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]{display:flex;align-items:baseline}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .systolic[_ngcontent-%COMP%]{color:#f44336}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .diastolic[_ngcontent-%COMP%]{color:#ff9800}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]{margin:0 4px;color:#666;font-weight:400}.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%]{margin-top:20px;padding-top:16px;border-top:1px solid #e0e0e0}.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%]   .timestamp-item[_ngcontent-%COMP%]{display:flex;align-items:center;color:#666;font-size:12px}.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%]   .timestamp-item[_ngcontent-%COMP%]   .timestamp-icon[_ngcontent-%COMP%]{margin-right:6px;font-size:16px}.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{display:flex;align-items:center;margin:0 0 12px;font-size:16px;font-weight:500;color:#333}.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px;color:#666}.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .raw-data-container[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:8px;padding:16px;overflow-x:auto}.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .raw-data-container[_ngcontent-%COMP%]   .raw-data[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:12px;color:#333;margin:0;white-space:pre-wrap;word-break:break-word}.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:32px 16px;text-align:center;color:#666}.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:12px;opacity:.5}.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-text[_ngcontent-%COMP%]{margin:0;font-size:14px}@media (max-width: 599px){.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]{font-size:16px}}\"]\n    });\n  }\n  return ModalColetaDadosVittaltecComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport { VoiceRecorderService } from './Service/voice-recorder.service';\nimport { SpeakerService } from './Service/speaker.service';\nimport { AiQuestionarioApiService } from './Service/ai-questionario-api.service';\nimport { AlertComponent } from 'src/app/alert/alert.component';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"./Service/voice-recorder.service\";\nimport * as i4 from \"./Service/speaker.service\";\nimport * as i5 from \"./Service/ai-questionario-api.service\";\nimport * as i6 from \"src/app/alert/alert.component\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/tooltip\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nfunction PreConsultaQuestionarioComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n    i0.ɵɵelement(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵelement(9, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Vou coletar suas informa\\u00E7\\u00F5es atrav\\u00E9s de uma conversa natural\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.iniciarAtendimento());\n    });\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Iniciar Atendimento\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 15);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 37);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.displayedText || ctx_r1.aiResponse);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 42);\n    i0.ɵɵtext(6, \"Processando sua resposta...\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lastItem_r5 = ctx.ngIf;\n    i0.ɵɵproperty(\"matTooltip\", lastItem_r5.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(lastItem_r5.value);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_20_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getDadosPreenchidos().length, \"/\", ctx_r1.getTotalCampos(), \" campos preenchidos\");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u00DAltima Informa\\u00E7\\u00E3o Coletada\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openHistoryModal());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"history\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, PreConsultaQuestionarioComponent_div_2_div_20_div_8_Template, 5, 3, \"div\", 47)(9, PreConsultaQuestionarioComponent_div_2_div_20_div_9_Template, 5, 4, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getUltimaVariavelPreenchida());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function PreConsultaQuestionarioComponent_div_2_div_21_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"send\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSendText());\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_mat_hint_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 63)(1, \"mat-icon\", 64);\n    i0.ɵɵtext(2, \"volume_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Aguarde o t\\u00E9rmino da fala... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"mat-form-field\", 58)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Sua resposta\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_div_21_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.userInput, $event) || (ctx_r1.userInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function PreConsultaQuestionarioComponent_div_2_div_21_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.enviarTexto());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, PreConsultaQuestionarioComponent_div_2_div_21_button_6_Template, 3, 1, \"button\", 60)(7, PreConsultaQuestionarioComponent_div_2_div_21_mat_hint_7_Template, 4, 0, \"mat-hint\", 61);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.userInput);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isInputDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canSendText());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSpeaking);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 70);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtemplate(2, PreConsultaQuestionarioComponent_div_2_div_22_div_2_Template, 1, 0, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 68)(4, \"mat-icon\", 69);\n    i0.ɵɵtext(5, \"mic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Gravando... \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 75);\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PreConsultaQuestionarioComponent_div_2_div_23_div_4_Template, 1, 0, \"div\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"recording\", ctx_r1.isRecording)(\"processing\", ctx_r1.isProcessing)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording && !ctx_r1.isProcessing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isRecording ? \"mic\" : ctx_r1.isProcessing ? \"hourglass_empty\" : ctx_r1.isAguardandoResposta ? \"hearing\" : \"mic_off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isRecording ? \"Ouvindo...\" : ctx_r1.isProcessing ? \"Processando...\" : ctx_r1.isAguardandoResposta ? \"Carregando microfone...\" : ctx_r1.isSpeaking ? \"Falando...\" : \"Aguardando...\", \" \");\n  }\n}\nfunction PreConsultaQuestionarioComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"mat-icon\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-slide-toggle\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.isTextMode, $event) || (ctx_r1.isTextMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_change_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTextMode());\n    });\n    i0.ɵɵtext(6, \" Modo Texto \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"div\", 22)(9, \"div\", 23)(10, \"div\", 24)(11, \"div\", 25);\n    i0.ɵɵelement(12, \"div\", 26)(13, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, PreConsultaQuestionarioComponent_div_2_div_15_Template, 1, 0, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵtemplate(18, PreConsultaQuestionarioComponent_div_2_div_18_Template, 4, 2, \"div\", 31)(19, PreConsultaQuestionarioComponent_div_2_div_19_Template, 7, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, PreConsultaQuestionarioComponent_div_2_div_20_Template, 10, 3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, PreConsultaQuestionarioComponent_div_2_div_21_Template, 8, 4, \"div\", 34)(22, PreConsultaQuestionarioComponent_div_2_div_22_Template, 7, 3, \"div\", 35)(23, PreConsultaQuestionarioComponent_div_2_div_23_Template, 7, 10, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.isTextMode ? \"keyboard\" : \"mic\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.isTextMode);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing || ctx_r1.isSpeaking);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"processing\", ctx_r1.isProcessing)(\"listening\", ctx_r1.isRecording)(\"waiting\", ctx_r1.isAguardandoResposta && !ctx_r1.isRecording);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"talking\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing || ctx_r1.isRecording || ctx_r1.isAguardandoResposta);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.aiResponse);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDadosPreenchidos().length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRecording && !ctx_r1.isTextMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTextMode);\n  }\n}\nexport class PreConsultaQuestionarioComponent {\n  router;\n  dialog;\n  voiceRecorder;\n  speaker;\n  aiService;\n  snackBar;\n  cdr;\n  isIdle = true;\n  isProcessing = false;\n  isTextMode = false;\n  isRecording = false;\n  isAguardandoResposta = false;\n  campoAtual = '';\n  isHistoryModalOpen = false;\n  // Validation confirmation feature\n  showValidationButtons = false;\n  // Novos estados para controle de fala e UI aprimorada\n  isSpeaking = false;\n  displayedText = '';\n  fullResponseText = '';\n  textDisplayInterval;\n  searchTerm = '';\n  availableFilters = [{\n    type: 'personal',\n    label: 'Dados Pessoais',\n    icon: 'person',\n    active: true\n  }, {\n    type: 'medical',\n    label: 'Informações Médicas',\n    icon: 'medical_services',\n    active: true\n  }, {\n    type: 'contact',\n    label: 'Contato',\n    icon: 'contact_phone',\n    active: true\n  }, {\n    type: 'optional',\n    label: 'Opcionais',\n    icon: 'info',\n    active: true\n  }];\n  conversationHistory = [];\n  currentToken = '';\n  aiResponse = '';\n  userInput = '';\n  dadosColetados = {\n    nome: '',\n    cpf: '',\n    email: '',\n    telefone: '',\n    dataNascimento: '',\n    alergias: '',\n    sintomas: '',\n    intensidadeDor: '',\n    tempoSintomas: '',\n    doencasPrevias: '',\n    observacoes: ''\n  };\n  destroy$ = new Subject();\n  constructor(router, dialog, voiceRecorder, speaker, aiService, snackBar, cdr) {\n    this.router = router;\n    this.dialog = dialog;\n    this.voiceRecorder = voiceRecorder;\n    this.speaker = speaker;\n    this.aiService = aiService;\n    this.snackBar = snackBar;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.preloadVoices();\n    this.startSpeakingMonitor();\n    this.voiceRecorder.recording$.pipe(takeUntil(this.destroy$)).subscribe(isRecording => {\n      this.isRecording = isRecording;\n      this.cdr.detectChanges();\n    });\n    this.voiceRecorder.result$.pipe(takeUntil(this.destroy$)).subscribe(result => {\n      if (result.success && result.text) {\n        this.adicionarRespostaUsuario(result.text);\n      }\n      this.cdr.detectChanges();\n    });\n    this.voiceRecorder.error$.pipe(takeUntil(this.destroy$)).subscribe(error => {\n      console.error('Erro de reconhecimento de voz:', error);\n      this.isRecording = false;\n      this.cdr.detectChanges();\n      if (!error.includes('aborted')) {\n        this.snackBar.falhaSnackbar(error);\n        if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {\n          this.iniciarGravacaoAutomatica();\n        }\n      }\n    });\n    this.voiceRecorder.recordingEvent$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {\n        if (this.speaker.isSpeaking()) {\n          this.speaker.waitUntilFinished().then(() => {\n            if (this.isAguardandoResposta && !this.isRecording) {\n              this.iniciarGravacaoAutomatica();\n            }\n          });\n        } else {\n          setTimeout(() => {\n            if (this.isAguardandoResposta && !this.isRecording) {\n              this.iniciarGravacaoAutomatica();\n            }\n          }, 500);\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.speaker.cancel();\n    this.voiceRecorder.stopRecording();\n    this.clearTextDisplayInterval();\n  }\n  iniciarAtendimento() {\n    this.isIdle = false;\n    this.cdr.detectChanges();\n    this.currentToken = this.generateToken();\n    this.conversationHistory = ['Iniciar atendimento'];\n    this.campoAtual = 'inicio';\n    if (!this.isTextMode) {\n      this.solicitarPermissaoMicrofone();\n    }\n    this.enviarMensagemParaIA();\n  }\n  toggleTextMode() {\n    this.cdr.detectChanges();\n    this.userInput = '';\n  }\n  enviarMensagemParaIA() {\n    this.isProcessing = true;\n    this.cdr.detectChanges();\n    const ultimaMensagem = this.conversationHistory.length > 0 ? this.conversationHistory[this.conversationHistory.length - 1] : '';\n    const payload = {\n      formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',\n      historicoConversa: [...this.conversationHistory],\n      ultimaMensagem: ultimaMensagem,\n      campoAtual: this.campoAtual,\n      token: this.currentToken\n    };\n    if (this.conversationHistory.length > 1 && !this.isTextMode) {\n      if (this.voiceRecorder.isCurrentlyRecording()) {\n        this.voiceRecorder.stopRecording();\n      }\n    }\n    this.aiService.enviarMensagens(payload).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.processarRespostaIA(response);\n      },\n      error: error => {\n        this.isProcessing = false;\n        this.cdr.detectChanges();\n        this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\n        console.error('Erro na comunicação com IA:', error);\n      }\n    });\n  }\n  processarRespostaIA(response) {\n    this.isProcessing = false;\n    this.aiResponse = response.textoResposta;\n    this.fullResponseText = response.textoResposta;\n    this.displayedText = '';\n    this.adicionarRespostaIA(response.textoResposta);\n    if (response.campoAtual) {\n      this.campoAtual = response.campoAtual;\n    }\n    // Handle validation confirmation\n    this.showValidationButtons = response.flgValidacaoCampoAtual || false;\n    this.cdr.detectChanges();\n    this.atualizarDadosColetados(response.dados);\n    this.cdr.detectChanges();\n    if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {\n      this.finalizarProcesso();\n    } else {\n      // Preparar texto para exibição, mas não iniciar ainda\n      this.displayedText = '';\n      this.cdr.detectChanges();\n      // Iniciar áudio com callback para sincronizar texto\n      this.speaker.speak(response.textoResposta, () => {\n        // Este callback é executado quando o áudio realmente começa a tocar\n        console.log('🎯 Iniciando sincronização de texto com áudio');\n        this.startSynchronizedTextDisplay();\n      }).then(() => {\n        // Only start waiting for response if validation buttons are not shown\n        if (!this.showValidationButtons) {\n          this.iniciarProcessoAguardandoResposta();\n        }\n      }).catch(error => {\n        console.error('Erro na reprodução da IA:', error);\n        this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro\n        this.cdr.detectChanges();\n        // Only start waiting for response if validation buttons are not shown\n        if (!this.showValidationButtons) {\n          this.iniciarProcessoAguardandoResposta();\n        }\n      });\n    }\n  }\n  adicionarRespostaIA(resposta) {\n    this.conversationHistory.push(`(resposta da ia) ${resposta}`);\n    this.cdr.detectChanges();\n  }\n  atualizarDadosColetados(novosdados) {\n    Object.keys(novosdados).forEach(key => {\n      if (novosdados[key] && novosdados[key].trim() !== '') {\n        this.dadosColetados[key] = novosdados[key];\n      }\n    });\n    this.cdr.detectChanges();\n  }\n  todosOsCamposPreenchidos() {\n    const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];\n    return camposObrigatorios.every(campo => this.dadosColetados[campo] && this.dadosColetados[campo].trim() !== '');\n  }\n  iniciarProcessoAguardandoResposta() {\n    this.isAguardandoResposta = true;\n    this.cdr.detectChanges();\n    if (!this.isTextMode) setTimeout(() => {\n      this.iniciarGravacaoContinua();\n    }, 500);\n  }\n  iniciarGravacaoContinua() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.voiceRecorder.isSupported()) {\n        console.error('Reconhecimento de voz não suportado');\n        _this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n        return;\n      }\n      // Redução de latência: monitorar mais frequentemente o fim da fala\n      yield _this.waitForSpeechEndWithReducedLatency();\n      _this.iniciarGravacaoAutomatica();\n      _this.monitorarEstadoGravacao();\n    })();\n  }\n  monitorarEstadoGravacao() {\n    const intervalId = setInterval(() => {\n      if (!this.isAguardandoResposta) {\n        clearInterval(intervalId);\n        return;\n      }\n      if (this.isTextMode) {\n        return;\n      }\n      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n      const componentRecording = this.isRecording;\n      if (serviceRecording !== componentRecording) {\n        this.isRecording = serviceRecording;\n        this.cdr.detectChanges();\n      }\n      if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {\n        this.iniciarGravacaoAutomatica();\n      }\n    }, 2000);\n  }\n  finalizarProcessoAguardandoResposta() {\n    this.isAguardandoResposta = false;\n    this.cdr.detectChanges();\n    if (this.isRecording) {\n      this.voiceRecorder.stopRecording();\n    }\n  }\n  iniciarGravacaoAutomatica() {\n    if (!this.voiceRecorder.isSupported()) {\n      console.error('Reconhecimento de voz não suportado');\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n      return;\n    }\n    // Não iniciar gravação se o sistema estiver falando\n    if (this.speaker.isSpeaking()) {\n      console.log('🔇 Aguardando término da fala para iniciar gravação');\n      return;\n    }\n    const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\n    const componentRecording = this.isRecording;\n    if (serviceRecording || componentRecording) {\n      return;\n    }\n    const success = this.voiceRecorder.startRecording();\n    if (!success) console.error('❌ Falha ao iniciar gravação automática');\n    this.cdr.detectChanges();\n  }\n  iniciarGravacao() {\n    if (!this.voiceRecorder.isSupported()) {\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\n      return;\n    }\n    // Verificar se o sistema está falando\n    if (this.speaker.isSpeaking()) {\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');\n      return;\n    }\n    const success = this.voiceRecorder.startRecording();\n    if (!success) {\n      this.snackBar.falhaSnackbar('Erro ao iniciar gravação');\n    }\n    this.cdr.detectChanges();\n  }\n  pararGravacao() {\n    this.voiceRecorder.stopRecording();\n    this.cdr.detectChanges();\n  }\n  toggleRecording() {\n    if (this.isRecording) {\n      this.pararGravacao();\n    } else {\n      this.iniciarGravacao();\n    }\n  }\n  preloadVoices() {\n    const synthesis = window.speechSynthesis;\n    const voices = synthesis.getVoices();\n    if (voices.length === 0) {\n      synthesis.onvoiceschanged = () => {\n        const newVoices = synthesis.getVoices();\n        newVoices;\n      };\n      const silentUtterance = new SpeechSynthesisUtterance('');\n      silentUtterance.volume = 0;\n      synthesis.speak(silentUtterance);\n      synthesis.cancel();\n    }\n  }\n  testarFluxoCompleto() {\n    this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.', () => {\n      console.log('🎯 Teste: áudio iniciado');\n    }).then(() => {\n      this.iniciarProcessoAguardandoResposta();\n    });\n  }\n  enviarTexto() {\n    // Não permitir envio se o sistema estiver falando\n    if (this.speaker.isSpeaking()) {\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');\n      return;\n    }\n    if (this.userInput.trim()) {\n      this.adicionarRespostaUsuario(this.userInput);\n      this.userInput = '';\n      this.cdr.detectChanges();\n    }\n  }\n  adicionarRespostaUsuario(resposta) {\n    if (this.isAguardandoResposta) {\n      this.finalizarProcessoAguardandoResposta();\n    }\n    this.conversationHistory.push(`(resposta do usuário) ${resposta}`);\n    this.cdr.detectChanges();\n    this.enviarMensagemParaIA();\n  }\n  solicitarPermissaoMicrofone() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: true\n        });\n        stream.getTracks().forEach(track => track.stop());\n      } catch (error) {\n        console.error('Erro ao solicitar permissão de microfone:', error);\n        _this2.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');\n      }\n    })();\n  }\n  generateToken() {\n    const now = new Date();\n    const timestamp = now.getTime();\n    const random = Math.floor(Math.random() * 10000);\n    return `${timestamp}_${random}`;\n  }\n  // Métodos para controle de fala e exibição sincronizada\n  startSpeakingMonitor() {\n    setInterval(() => {\n      const currentlySpeaking = this.speaker.isSpeaking();\n      if (this.isSpeaking !== currentlySpeaking) {\n        this.isSpeaking = currentlySpeaking;\n        this.cdr.detectChanges();\n      }\n    }, 100); // Verificar a cada 100ms\n  }\n  startSynchronizedTextDisplay() {\n    this.clearTextDisplayInterval();\n    this.displayedText = '';\n    const words = this.fullResponseText.split(' ');\n    const totalDuration = this.estimateSpeechDuration(this.fullResponseText);\n    const intervalTime = totalDuration / words.length;\n    let currentWordIndex = 0;\n    this.textDisplayInterval = setInterval(() => {\n      if (currentWordIndex < words.length) {\n        if (currentWordIndex === 0) {\n          this.displayedText = words[currentWordIndex];\n        } else {\n          this.displayedText += ' ' + words[currentWordIndex];\n        }\n        currentWordIndex++;\n        this.cdr.detectChanges();\n      } else {\n        this.clearTextDisplayInterval();\n      }\n    }, intervalTime);\n  }\n  estimateSpeechDuration(text) {\n    // Estimar duração baseada em ~150 palavras por minuto (velocidade média de fala)\n    const words = text.split(' ').length;\n    const wordsPerMinute = 150;\n    const durationInMinutes = words / wordsPerMinute;\n    return durationInMinutes * 60 * 1000; // Converter para milissegundos\n  }\n  clearTextDisplayInterval() {\n    if (this.textDisplayInterval) {\n      clearInterval(this.textDisplayInterval);\n      this.textDisplayInterval = null;\n    }\n  }\n  finalizarProcesso() {\n    this.cdr.detectChanges();\n    this.salvarDadosQuestionario();\n    this.abrirDialogColetaDadosVittalTec();\n  }\n  salvarDadosQuestionario() {\n    try {\n      const dadosParaSalvar = {\n        nome: this.dadosColetados.nome,\n        idade: this.calcularIdade(this.dadosColetados.dataNascimento),\n        cpf: this.dadosColetados.cpf,\n        email: this.dadosColetados.email,\n        telefone: this.dadosColetados.telefone,\n        dataNascimento: this.dadosColetados.dataNascimento,\n        sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],\n        sintomasOutros: this.dadosColetados.observacoes || '',\n        intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),\n        tempoSintomas: this.dadosColetados.tempoSintomas,\n        alergias: this.dadosColetados.alergias || '',\n        doencasPrevias: this.dadosColetados.doencasPrevias || '',\n        observacoes: this.dadosColetados.observacoes || '',\n        dataPreenchimento: new Date().toISOString()\n      };\n      CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));\n      console.log('Dados do questionário salvos no localStorage:', dadosParaSalvar);\n    } catch (error) {\n      console.error('Erro ao salvar dados do questionário:', error);\n      this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');\n    }\n  }\n  calcularIdade(dataNascimento) {\n    if (!dataNascimento) return 0;\n    try {\n      const nascimento = new Date(dataNascimento);\n      const hoje = new Date();\n      let idade = hoje.getFullYear() - nascimento.getFullYear();\n      const mesAtual = hoje.getMonth();\n      const mesNascimento = nascimento.getMonth();\n      if (mesAtual < mesNascimento || mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate()) {\n        idade--;\n      }\n      return idade;\n    } catch {\n      return 0;\n    }\n  }\n  extrairNumeroIntensidade(intensidade) {\n    if (!intensidade) return 0;\n    const match = intensidade.match(/\\d+/);\n    return match ? parseInt(match[0], 10) : 0;\n  }\n  redirecionarParaFilaEspera() {\n    try {\n      this.router.navigate(['/filaespera']);\n    } catch (error) {\n      console.error('Erro ao redirecionar para fila de espera:', error);\n      this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');\n      this.router.navigate(['/filaespera']);\n    }\n  }\n  abrirDialogColetaDadosVittalTec() {\n    const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\n      disableClose: true,\n      width: '500px',\n      maxWidth: '85vw',\n      height: 'auto',\n      maxHeight: '70vh',\n      panelClass: 'vittaltec-modal-panel'\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.action === 'continuar') {\n        this.snackBar.sucessoSnackbar(\"Dados coletados com sucesso!\");\n        this.redirecionarParaFilaEspera();\n      } else if (result?.action === 'cancelar') {\n        this.snackBar.falhaSnackbar(\"Processo de coleta de dados cancelado.\");\n        CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');\n      }\n      this.cdr.detectChanges();\n    });\n  }\n  getDadosPreenchidos() {\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => ({\n      label: labels[key],\n      value: this.dadosColetados[key]\n    }));\n  }\n  getUltimaVariavelPreenchida() {\n    const dados = this.getDadosPreenchidos();\n    return dados.length > 0 ? dados[dados.length - 1] : null;\n  }\n  openHistoryModal() {\n    import('./components/history-modal-dialog.component').then(({\n      HistoryModalDialogComponent\n    }) => {\n      const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));\n      this.dialog.open(HistoryModalDialogComponent, {\n        width: '900px',\n        maxWidth: '98vw',\n        data: {\n          dadosColetados: dadosClone,\n          snackBar: this.snackBar,\n          cdr: this.cdr\n        },\n        panelClass: 'modern-modal-overlay',\n        autoFocus: false\n      });\n    });\n  }\n  getTotalCampos() {\n    return 10;\n  }\n  getProgressPercentage() {\n    const total = this.getTotalCampos();\n    const filled = this.getDadosPreenchidos().length;\n    return Math.round(filled / total * 100);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n  }\n  clearSearch() {\n    this.searchTerm = '';\n  }\n  toggleFilter(filter) {\n    filter.active = !filter.active;\n  }\n  getFilteredData() {\n    let data = this.getEnhancedDadosPreenchidos();\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      data = data.filter(item => item.label.toLowerCase().includes(searchLower) || item.value.toLowerCase().includes(searchLower));\n    }\n    const activeCategories = this.availableFilters.filter(f => f.active).map(f => f.type);\n    data = data.filter(item => activeCategories.includes(item.category));\n    return data;\n  }\n  getEnhancedDadosPreenchidos() {\n    const categoryMap = {\n      nome: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'person'\n      },\n      cpf: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'badge'\n      },\n      dataNascimento: {\n        category: 'personal',\n        categoryLabel: 'Dados Pessoais',\n        icon: 'cake'\n      },\n      email: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'email'\n      },\n      telefone: {\n        category: 'contact',\n        categoryLabel: 'Contato',\n        icon: 'phone'\n      },\n      sintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'medical_services'\n      },\n      intensidadeDor: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'personal_injury'\n      },\n      tempoSintomas: {\n        category: 'medical',\n        categoryLabel: 'Informações Médicas',\n        icon: 'schedule'\n      },\n      alergias: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'medical_information'\n      },\n      observacoes: {\n        category: 'optional',\n        categoryLabel: 'Opcionais',\n        icon: 'description'\n      }\n    };\n    const labels = {\n      nome: 'Nome',\n      cpf: 'CPF',\n      email: 'Email',\n      telefone: 'Telefone',\n      dataNascimento: 'Data de Nascimento',\n      alergias: 'Alergias',\n      sintomas: 'Sintomas',\n      intensidadeDor: 'Intensidade da Dor',\n      tempoSintomas: 'Tempo dos Sintomas',\n      doencasPrevias: 'Doenças Prévias',\n      observacoes: 'Observações'\n    };\n    return Object.keys(this.dadosColetados).filter(key => this.dadosColetados[key] && this.dadosColetados[key].trim() !== '').map(key => {\n      const categoryInfo = categoryMap[key] || {\n        category: 'optional',\n        categoryLabel: 'Outros',\n        icon: 'info'\n      };\n      return {\n        label: labels[key] || key,\n        value: this.dadosColetados[key],\n        category: categoryInfo.category,\n        categoryLabel: categoryInfo.categoryLabel,\n        icon: categoryInfo.icon,\n        timestamp: new Date(),\n        validationStatus: this.getValidationStatusForField(key)\n      };\n    });\n  }\n  getValidationStatusForField(field) {\n    const value = this.dadosColetados[field];\n    if (!value || value.trim() === '') return 'error';\n    if (field === 'email' && !value.includes('@')) return 'warning';\n    if (field === 'cpf' && value.length < 11) return 'warning';\n    return 'valid';\n  }\n  trackByFn(index, item) {\n    index;\n    return item.label + item.value;\n  }\n  highlightSearchTerm(text) {\n    if (!this.searchTerm) return text;\n    const regex = new RegExp(`(${this.searchTerm})`, 'gi');\n    return text.replace(regex, '<mark>$1</mark>');\n  }\n  formatTimestamp(timestamp) {\n    return timestamp.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getValidationIcon(status) {\n    switch (status) {\n      case 'valid':\n        return 'check_circle';\n      case 'warning':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'info';\n    }\n  }\n  getValidationLabel(status) {\n    switch (status) {\n      case 'valid':\n        return 'Válido';\n      case 'warning':\n        return 'Atenção';\n      case 'error':\n        return 'Erro';\n      default:\n        return 'Info';\n    }\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\n    }).catch(() => {\n      this.snackBar.falhaSnackbar('Erro ao copiar texto');\n    });\n  }\n  editValue(item) {\n    const newValue = prompt(`Editar ${item.label}:`, item.value);\n    if (newValue !== null && newValue !== item.value) {\n      const field = Object.keys(this.dadosColetados).find(key => {\n        const labels = {\n          nome: 'Nome',\n          cpf: 'CPF',\n          email: 'Email',\n          telefone: 'Telefone',\n          dataNascimento: 'Data de Nascimento',\n          alergias: 'Alergias',\n          sintomas: 'Sintomas',\n          intensidadeDor: 'Intensidade da Dor',\n          tempoSintomas: 'Tempo dos Sintomas',\n          doencasPrevias: 'Doenças Prévias',\n          observacoes: 'Observações'\n        };\n        return labels[key] === item.label;\n      });\n      if (field) {\n        this.dadosColetados[field] = newValue;\n        this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\n        this.cdr.detectChanges();\n      }\n    }\n  }\n  clearAllData() {\n    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\n      this.dadosColetados = {\n        nome: '',\n        cpf: '',\n        email: '',\n        telefone: '',\n        dataNascimento: '',\n        alergias: '',\n        sintomas: '',\n        intensidadeDor: '',\n        tempoSintomas: '',\n        doencasPrevias: '',\n        observacoes: ''\n      };\n      this.snackBar.sucessoSnackbar('Todos os dados foram limpos');\n      this.cdr.detectChanges();\n    }\n  }\n  // Métodos auxiliares para controle de UI\n  isInputDisabled() {\n    return this.speaker.isSpeaking() || this.isProcessing;\n  }\n  isMicrophoneDisabled() {\n    return this.speaker.isSpeaking() || this.isProcessing;\n  }\n  canSendText() {\n    return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();\n  }\n  // Método para reduzir latência na transição de áudio\n  waitForSpeechEndWithReducedLatency() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        const checkInterval = 50; // Verificar a cada 50ms para reduzir latência\n        const maxWaitTime = 30000; // Máximo 30 segundos de espera\n        let elapsedTime = 0;\n        const intervalId = setInterval(() => {\n          elapsedTime += checkInterval;\n          if (!_this3.speaker.isSpeaking()) {\n            clearInterval(intervalId);\n            // Pequena pausa adicional para garantir que o áudio terminou completamente\n            setTimeout(() => {\n              resolve();\n            }, 100);\n          } else if (elapsedTime >= maxWaitTime) {\n            clearInterval(intervalId);\n            console.warn('Timeout aguardando fim da fala');\n            resolve();\n          }\n        }, checkInterval);\n      });\n    })();\n  }\n  static ɵfac = function PreConsultaQuestionarioComponent_Factory(t) {\n    return new (t || PreConsultaQuestionarioComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.VoiceRecorderService), i0.ɵɵdirectiveInject(i4.SpeakerService), i0.ɵɵdirectiveInject(i5.AiQuestionarioApiService), i0.ɵɵdirectiveInject(i6.AlertComponent), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PreConsultaQuestionarioComponent,\n    selectors: [[\"app-pre-consulta-questionario\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"ai-questionnaire-container\"], [\"class\", \"idle-screen\", 4, \"ngIf\"], [\"class\", \"chat-interface\", 4, \"ngIf\"], [1, \"idle-screen\"], [1, \"idle-content\"], [1, \"ai-robot\", 2, \"margin-bottom\", \"9px\"], [1, \"robot-head\"], [1, \"robot-eyes\"], [1, \"eye\", \"left-eye\"], [1, \"eye\", \"right-eye\"], [1, \"robot-mouth\"], [1, \"robot-body\"], [1, \"robot-chest\"], [1, \"action-buttons\"], [1, \"start-btn\", 3, \"click\"], [1, \"btn-glow\"], [1, \"chat-interface\"], [1, \"controls-header\"], [1, \"mode-indicator\"], [1, \"mode-icon\"], [1, \"mode-toggle\", 3, \"ngModelChange\", \"change\", \"ngModel\", \"disabled\"], [1, \"main-chat-area\"], [1, \"ai-section\"], [1, \"ai-avatar\"], [1, \"ai-face\"], [1, \"ai-eyes\"], [1, \"eye\"], [1, \"ai-mouth\"], [\"class\", \"ai-pulse\", 4, \"ngIf\"], [1, \"response-data-section\"], [1, \"response-section\"], [\"class\", \"ai-message\", 4, \"ngIf\"], [\"class\", \"processing-indicator\", 4, \"ngIf\"], [\"class\", \"data-section\", 4, \"ngIf\"], [\"class\", \"input-section\", 4, \"ngIf\"], [\"class\", \"audio-visualization\", 4, \"ngIf\"], [\"class\", \"voice-status-indicator\", 4, \"ngIf\"], [1, \"ai-pulse\"], [1, \"ai-message\"], [1, \"message-bubble\", \"scrollable-hidden\"], [1, \"processing-indicator\"], [1, \"typing-dots\"], [1, \"processing-text\"], [1, \"data-section\"], [1, \"data-panel\"], [1, \"data-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Ver todas as vari\\u00E1veis\", 1, \"history-btn\", 3, \"click\"], [\"class\", \"data-item\", 3, \"matTooltip\", 4, \"ngIf\"], [\"class\", \"progress-info\", 4, \"ngIf\"], [1, \"data-item\", 3, \"matTooltip\"], [1, \"descInfoCategoria\"], [1, \"descInfovalue\"], [1, \"progress-info\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"input-section\"], [1, \"input-container\"], [\"appearance\", \"outline\", 1, \"user-input\"], [\"matInput\", \"\", \"placeholder\", \"Digite aqui...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"speaking-hint\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"click\", \"disabled\"], [1, \"speaking-hint\"], [1, \"hint-icon\"], [1, \"audio-visualization\"], [1, \"sound-wave\"], [\"class\", \"wave-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"recording-text\"], [1, \"recording-icon\"], [1, \"wave-bar\"], [1, \"voice-status-indicator\"], [1, \"status-icon\"], [\"class\", \"status-ripple\", 4, \"ngIf\"], [1, \"status-text\"], [1, \"status-ripple\"]],\n    template: function PreConsultaQuestionarioComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, PreConsultaQuestionarioComponent_div_1_Template, 17, 0, \"div\", 1)(2, PreConsultaQuestionarioComponent_div_2_Template, 24, 18, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isIdle);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isIdle);\n      }\n    },\n    dependencies: [CommonModule, i7.NgForOf, i7.NgIf, ReactiveFormsModule, i8.DefaultValueAccessor, i8.NgControlStatus, FormsModule, i8.NgModel, MatFormFieldModule, i9.MatFormField, i9.MatLabel, i9.MatHint, i9.MatSuffix, MatInputModule, i10.MatInput, MatButtonModule, i11.MatIconButton, MatCardModule, MatCheckboxModule, MatSlideToggleModule, i12.MatSlideToggle, MatIconModule, i13.MatIcon, MatDialogModule, MatTooltipModule, i14.MatTooltip, MatChipsModule, MatSnackBarModule],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.ai-questionnaire-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  height: 100dvh;\\n  max-height: 100vh;\\n  max-height: 100dvh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n  font-size: clamp(0.75rem, 1.8vw, 0.9rem);\\n  position: relative;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.idle-screen[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n  overflow: hidden;\\n}\\n\\n.idle-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\\n  max-width: min(80vw, 20rem);\\n  width: 100%;\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  max-height: 70vh;\\n  overflow: hidden;\\n}\\n.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: clamp(1rem, 2.5vw, 1.125rem);\\n  font-weight: 700;\\n  margin: clamp(0.25rem, 1vw, 0.5rem) 0 clamp(0.25rem, 0.5vw, 0.25rem) 0;\\n}\\n.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  margin-bottom: clamp(0.5rem, 2vw, 1rem);\\n  line-height: 1.3;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  align-items: center;\\n  width: 100%;\\n}\\n\\n\\n\\n.ai-robot[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ai-robot[_ngcontent-%COMP%] {\\n    transform: scale(0.9);\\n  }\\n}\\n\\n.robot-head[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 5rem);\\n  height: clamp(3.5rem, 8vw, 5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  position: relative;\\n  margin: 0 auto clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 0.5rem 1.25rem rgba(102, 126, 234, 0.3);\\n}\\n\\n.robot-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: clamp(1rem, 4vw, 1.25rem) clamp(0.5rem, 3vw, 1rem) 0;\\n}\\n\\n.eye[_ngcontent-%COMP%] {\\n  width: clamp(0.5rem, 1.5vw, 0.75rem);\\n  height: clamp(0.5rem, 1.5vw, 0.75rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_blink 3s infinite;\\n}\\n.eye.left-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.1s;\\n}\\n.eye.right-eye[_ngcontent-%COMP%] {\\n  animation-delay: 0.2s;\\n}\\n\\n.robot-mouth[_ngcontent-%COMP%] {\\n  width: clamp(1rem, 2.5vw, 1.25rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 0 0 0.625rem 0.625rem;\\n  margin: clamp(0.4rem, 1vw, 0.5rem) auto 0;\\n}\\n\\n.robot-body[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.75rem);\\n  height: clamp(1.5rem, 4vw, 2.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: clamp(0.75rem, 2vw, 1rem);\\n  margin: 0 auto;\\n  position: relative;\\n}\\n\\n.robot-chest[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.start-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #f093fb);\\n  border: none;\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(2rem, 5vw, 2.8125rem);\\n  border-radius: 3.125rem;\\n  color: #ffffff;\\n  font-size: clamp(1.125rem, 3vw, 1.5rem);\\n  font-weight: 700;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(79, 172, 254, 0.3);\\n}\\n.start-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-0.125rem);\\n  box-shadow: 0 0.75rem 2.1875rem rgba(79, 172, 254, 0.4);\\n}\\n.start-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.btn-glow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.start-btn[_ngcontent-%COMP%]:hover   .btn-glow[_ngcontent-%COMP%] {\\n  left: 100%;\\n}\\n\\n\\n\\n\\n\\n.chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.controls-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 0.8vw, 0.5rem) clamp(0.5rem, 1.5vw, 1rem);\\n  border-radius: 0 0 clamp(0.5rem, 1.5vw, 1rem) clamp(0.5rem, 1.5vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  min-height: 44px;\\n}\\n\\n.mode-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: clamp(1.25rem, 2.5vw, 1.5rem);\\n  height: clamp(1.25rem, 2.5vw, 1.5rem);\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  color: #ffffff;\\n  flex-shrink: 0;\\n}\\n.mode-indicator[_ngcontent-%COMP%]   .mode-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n  width: clamp(0.75rem, 1.5vw, 0.875rem);\\n  height: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.mode-toggle[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 1.2vw, 0.75rem);\\n  white-space: nowrap;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch {\\n  --mdc-switch-track-width: 28px;\\n  --mdc-switch-track-height: 16px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle {\\n  --mdc-switch-handle-width: 12px;\\n  --mdc-switch-handle-height: 12px;\\n}\\n.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__ripple {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n\\n\\n.main-chat-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  padding: clamp(0.5rem, 1.5vw, 1rem) clamp(0.25rem, 1vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n  max-width: 100vw;\\n  margin: 0;\\n  width: 100%;\\n  min-height: 0;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.response-data-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  width: 100%;\\n  align-items: center;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    justify-content: space-between;\\n    gap: clamp(0.5rem, 1.5vw, 1rem);\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .response-data-section[_ngcontent-%COMP%] {\\n    gap: clamp(1rem, 2vw, 1.5rem);\\n  }\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n\\n.ai-avatar[_ngcontent-%COMP%] {\\n  width: clamp(2.5rem, 6vw, 3.5rem);\\n  height: clamp(2.5rem, 6vw, 3.5rem);\\n  background: #ffffff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.ai-avatar.processing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(102, 126, 234, 0.5);\\n}\\n.ai-avatar.listening[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_listening-pulse 1s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(240, 147, 251, 0.5);\\n}\\n.ai-avatar.waiting[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n  box-shadow: 0 0 clamp(0.5rem, 1.5vw, 0.75rem) rgba(79, 172, 254, 0.5);\\n}\\n\\n.ai-face[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.5vw, 0.25rem);\\n}\\n\\n.ai-eyes[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.25rem, 0.8vw, 0.4rem);\\n}\\n.ai-eyes[_ngcontent-%COMP%]   .eye[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.3rem);\\n  height: clamp(0.2rem, 0.5vw, 0.3rem);\\n  background: #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_ai-blink 4s infinite;\\n}\\n\\n.ai-mouth[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.6rem);\\n  height: clamp(0.15rem, 0.4vw, 0.2rem);\\n  background: #667eea;\\n  border-radius: 0 0 0.3rem 0.3rem;\\n  transition: all 0.3s ease;\\n}\\n.ai-mouth.talking[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_mouth-talk 0.5s infinite alternate;\\n}\\n\\n.ai-pulse[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  left: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  right: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  bottom: clamp(-0.25rem, -0.8vw, -0.3rem);\\n  border: clamp(1px, 0.3vw, 2px) solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse-ring 2s infinite;\\n}\\n\\n\\n\\n.response-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 100%;\\n  flex: 1;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n@media (min-width: 1024px) {\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: flex-start;\\n    max-width: none;\\n  }\\n}\\n\\n.ai-message[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  flex-shrink: 0;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem);\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  max-height: 40vh;\\n  overflow-y: auto;\\n}\\n.message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: clamp(0.5rem, 2vw, 1rem);\\n  bottom: clamp(-0.25rem, -0.5vw, -0.3rem);\\n  width: 0;\\n  height: 0;\\n  border-left: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-right: clamp(0.25rem, 0.8vw, 0.3rem) solid transparent;\\n  border-top: clamp(0.25rem, 0.8vw, 0.3rem) solid #ffffff;\\n}\\n.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2d3748;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n\\n.processing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 100%;\\n  align-items: center;\\n  justify-content: center;\\n  gap: clamp(0.5rem, 2vw, 1rem);\\n  color: #718096;\\n  padding: clamp(1rem, 3vw, 1.5rem);\\n}\\n.processing-indicator[_ngcontent-%COMP%]   .processing-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  font-weight: 500;\\n  color: #fff;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: clamp(0.2rem, 0.5vw, 0.25rem);\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: clamp(0.4rem, 1vw, 0.5rem);\\n  height: clamp(0.4rem, 1vw, 0.5rem);\\n  background: #fff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n\\n\\n.data-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n  margin-top: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n  overflow: hidden;\\n  max-height: 30vh;\\n}\\n@media (min-width: 1024px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: 0;\\n    max-width: min(35vw, 18rem);\\n    width: auto;\\n    min-width: 15rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: min(30vw, 20rem);\\n  }\\n}\\n\\n.data-panel[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: clamp(0.5rem, 1.5vw, 1rem);\\n  padding: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in-out;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  max-height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 clamp(0.25rem, 1vw, 0.5rem) 0;\\n  color: #2d3748;\\n  font-size: clamp(0.875rem, 2vw, 1rem);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  border-bottom: 1px solid rgba(102, 126, 234, 0.1);\\n  padding-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: clamp(0.75rem, 1.5vw, 0.875rem);\\n}\\n\\n.data-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  max-height: 20vh !important;\\n  overflow-y: auto !important;\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n.data-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: clamp(0.25rem, 1vw, 0.5rem);\\n  flex-shrink: 0;\\n}\\n.data-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: #f8fafc;\\n  border-radius: clamp(0.25rem, 1vw, 0.5rem);\\n  border-left: clamp(2px, 0.3vw, 3px) solid #4facfe;\\n  flex-shrink: 0;\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfoCategoria[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: clamp(0.75rem, 1.8vw, 0.875rem);\\n  color: #718096;\\n  font-weight: 500;\\n  margin-bottom: clamp(0.1rem, 0.3vw, 0.15rem);\\n}\\n.data-item[_ngcontent-%COMP%]   .descInfovalue[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  margin-top: clamp(0.25rem, 1.5vw, 0.5rem);\\n  padding-top: clamp(0.25rem, 1vw, 0.5rem);\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  flex-shrink: 0;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: clamp(3px, 0.8vw, 4px);\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n  margin-bottom: clamp(0.25rem, 0.8vw, 0.25rem);\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  min-width: 44px !important;\\n  min-height: 44px !important;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  color: #667eea;\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.input-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-top: 1px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\\n  min-height: 60px;\\n}\\n@media (max-width: 480px) {\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 56px;\\n  }\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n  box-sizing: border-box;\\n}\\n\\n.user-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.user-input[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: clamp(0.5rem, 2vw, 1rem);\\n}\\n.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 2vw, 0.875rem) !important;\\n  padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem) !important;\\n  min-height: 44px;\\n}\\n.user-input[_ngcontent-%COMP%]   button[matSuffix][_ngcontent-%COMP%] {\\n  min-width: 44px;\\n  min-height: 44px;\\n}\\n\\n.voice-display[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  text-align: center;\\n}\\n\\n.voice-input-field[_ngcontent-%COMP%]   .voice-placeholder[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\\n  font-style: italic;\\n}\\n\\n\\n\\n.audio-visualization[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(4rem, 8vw, 6rem);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(1rem, 3vw, 1.25rem) clamp(1.5rem, 4vw, 1.875rem);\\n  border-radius: clamp(1.5rem, 4vw, 1.5625rem);\\n  box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(1rem, 3vw, 1.25rem);\\n  z-index: 1000;\\n  max-width: min(90vw, 25rem);\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n}\\n\\n.sound-wave[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.15rem, 0.4vw, 0.1875rem);\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n\\n.wave-bar[_ngcontent-%COMP%] {\\n  width: clamp(0.2rem, 0.5vw, 0.25rem);\\n  background: linear-gradient(to top, #667eea, #f093fb);\\n  border-radius: clamp(1px, 0.2vw, 2px);\\n  animation: _ngcontent-%COMP%_wave-animation 1.5s infinite ease-in-out;\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.1s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.2s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(4) {\\n  animation-delay: 0.3s;\\n  height: clamp(1.75rem, 4vw, 2.1875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(5) {\\n  animation-delay: 0.4s;\\n  height: clamp(1.25rem, 3vw, 1.5625rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(6) {\\n  animation-delay: 0.5s;\\n  height: clamp(2rem, 5vw, 2.5rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(7) {\\n  animation-delay: 0.6s;\\n  height: clamp(1.5rem, 3.5vw, 1.875rem);\\n}\\n.wave-bar[_ngcontent-%COMP%]:nth-child(8) {\\n  animation-delay: 0.7s;\\n  height: clamp(1rem, 2.5vw, 1.25rem);\\n}\\n\\n.recording-text[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: clamp(0.875rem, 2.5vw, 1.125rem);\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.25rem, 1vw, 0.5rem);\\n}\\n.recording-text[_ngcontent-%COMP%]   .recording-icon[_ngcontent-%COMP%] {\\n  color: #ff4757;\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n\\n\\n\\n.voice-status-indicator[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: clamp(1.5rem, 4vw, 1.875rem);\\n  right: clamp(1.5rem, 4vw, 1.875rem);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: clamp(0.5rem, 1.5vw, 1rem);\\n  z-index: 1000;\\n}\\n@media (max-width: 480px) {\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(1rem, 3vw, 1.5rem);\\n    right: clamp(1rem, 3vw, 1.5rem);\\n    scale: 0.9;\\n  }\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  width: clamp(3.5rem, 8vw, 4.375rem);\\n  height: clamp(3.5rem, 8vw, 4.375rem);\\n  border-radius: 50%;\\n  border: none;\\n  background: linear-gradient(135deg, #718096, #a0aec0);\\n  color: #ffffff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 0.5rem 1.5625rem rgba(113, 128, 150, 0.4);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.status-icon.recording[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff4757, #ff3742);\\n  animation: _ngcontent-%COMP%_recording-pulse 1s infinite;\\n}\\n.status-icon.processing[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  animation: _ngcontent-%COMP%_processing-pulse 2s infinite;\\n}\\n.status-icon.waiting[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe, #10b981);\\n  animation: _ngcontent-%COMP%_waiting-pulse 2s infinite;\\n}\\n.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: clamp(1.25rem, 4vw, 1.75rem);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.status-ripple[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.3);\\n  animation: _ngcontent-%COMP%_ripple 1.5s infinite;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n  border-radius: clamp(1rem, 3vw, 1.25rem);\\n  font-size: clamp(0.75rem, 2vw, 0.875rem);\\n  font-weight: 600;\\n  color: #2d3748;\\n  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(0.625rem);\\n          backdrop-filter: blur(0.625rem);\\n  white-space: nowrap;\\n}\\n\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ai-blink {\\n  0%, 90%, 100% {\\n    transform: scaleY(1);\\n  }\\n  95% {\\n    transform: scaleY(0.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_mouth-talk {\\n  0% {\\n    transform: scaleY(1);\\n  }\\n  100% {\\n    transform: scaleY(1.5);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_processing-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_listening-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.8;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.9;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse-ring {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1.3);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_wave-animation {\\n  0%, 100% {\\n    transform: scaleY(0.5);\\n  }\\n  50% {\\n    transform: scaleY(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_recording-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(255, 71, 87, 0.8);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(2);\\n    opacity: 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_waiting-pulse {\\n  0%, 100% {\\n    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\\n  }\\n  50% {\\n    box-shadow: 0 8px 35px rgba(16, 185, 129, 0.8);\\n  }\\n}\\n\\n\\n\\n\\n@media (max-width: 480px) {\\n  .ai-questionnaire-container[_ngcontent-%COMP%] {\\n    font-size: clamp(0.7rem, 1.8vw, 0.8rem);\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 3vw, 1rem) clamp(0.25rem, 2vw, 0.5rem);\\n    margin: 0.25rem;\\n    max-width: min(85vw, 18rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 3vw, 1rem);\\n    margin: clamp(0.25rem, 1.5vw, 0.5rem) 0 clamp(0.25rem, 1vw, 0.25rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    margin-bottom: clamp(0.5rem, 2.5vw, 1rem);\\n  }\\n  .chat-interface[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    height: 100dvh;\\n    overflow: hidden;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 0.5rem);\\n    gap: clamp(0.25rem, 1vw, 0.25rem);\\n    min-height: 40px;\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.25rem, 1vw, 0.5rem);\\n    gap: clamp(0.25rem, 0.8vw, 0.25rem);\\n    overflow: hidden;\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: clamp(0.25rem, 1.5vw, 0.5rem);\\n    overflow: hidden;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 5vw, 2.5rem);\\n    height: clamp(2rem, 5vw, 2.5rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.8vw, 0.5rem) clamp(0.5rem, 2.5vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-height: 25vh;\\n    overflow-y: auto;\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 2.2vw, 0.875rem);\\n    line-height: 1.3;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(0.25rem, 1vw, 0.25rem);\\n    max-height: 20vh;\\n    overflow: hidden;\\n  }\\n  .data-panel[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .input-section[_ngcontent-%COMP%] {\\n    padding: clamp(0.25rem, 1vw, 0.25rem) clamp(0.5rem, 2vw, 1rem);\\n    min-height: 50px;\\n  }\\n  .user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n  }\\n  .user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.8vw, 0.875rem) !important;\\n    padding: clamp(0.25rem, 1.2vw, 0.25rem) clamp(0.5rem, 1.8vw, 0.5rem) !important;\\n  }\\n  .audio-visualization[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    padding: clamp(0.25rem, 1.5vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);\\n    border-radius: clamp(0.25rem, 1.5vw, 0.5rem);\\n    max-width: 75vw;\\n    min-height: 40px;\\n  }\\n  .voice-status-indicator[_ngcontent-%COMP%] {\\n    bottom: clamp(0.25rem, 1.5vw, 0.5rem);\\n    right: clamp(0.25rem, 1.5vw, 0.5rem);\\n    scale: 0.75;\\n  }\\n  .status-icon[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 4.5vw, 2.5rem);\\n    height: clamp(2rem, 4.5vw, 2.5rem);\\n    min-width: 40px;\\n    min-height: 40px;\\n  }\\n  .status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.8vw, 1rem);\\n  }\\n  .status-text[_ngcontent-%COMP%] {\\n    font-size: clamp(0.75rem, 1.5vw, 0.75rem);\\n    padding: clamp(0.25rem, 0.6vw, 0.25rem) clamp(0.25rem, 1.2vw, 0.5rem);\\n    max-width: 6rem;\\n  }\\n}\\n@media (min-width: 481px) and (max-width: 640px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(5rem, 10vw, 6rem) clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 3vw, 2rem);\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: clamp(1.5rem, 4vw, 2rem);\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    margin-top: clamp(1rem, 3vw, 2rem);\\n  }\\n}\\n@media (min-width: 641px) and (max-width: 768px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(6rem, 10vw, 7rem) clamp(1.5rem, 4vw, 2rem) clamp(2rem, 4vw, 3rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(2rem, 5vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    margin-top: clamp(1.5rem, 4vw, 2rem);\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    align-items: center;\\n    width: 100%;\\n  }\\n  .controls-header[_ngcontent-%COMP%] {\\n    top: clamp(1rem, 2.5vw, 1.5rem);\\n    left: clamp(1rem, 2.5vw, 1.5rem);\\n    right: clamp(1rem, 2.5vw, 1.5rem);\\n    width: auto;\\n    justify-content: center;\\n  }\\n}\\n@media (min-width: 769px) and (max-width: 1024px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(90vw, 75rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: clamp(1.5rem, 3vw, 3rem);\\n    align-items: center;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    margin-top: clamp(1.5rem, 3vw, 2rem);\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (min-width: 1025px) {\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    max-width: min(85vw, 85rem);\\n  }\\n  .response-data-section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    gap: clamp(2rem, 2vw, 4rem);\\n  }\\n  .response-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    max-width: none;\\n  }\\n  .data-section[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    width: auto;\\n    min-width: 20rem;\\n    margin-top: 0;\\n  }\\n}\\n@media (orientation: landscape) and (max-height: 600px) {\\n  .idle-screen[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vh, 1rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%] {\\n    padding: clamp(1.5rem, 4vh, 2rem) clamp(1.5rem, 4vw, 3rem);\\n  }\\n  .idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: clamp(1.125rem, 4vw, 1.25rem);\\n    margin: clamp(0.5rem, 2vh, 1rem) 0 clamp(0.25rem, 1vh, 0.5rem) 0;\\n  }\\n  .idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n    margin-bottom: clamp(1rem, 3vh, 1.5rem);\\n  }\\n  .main-chat-area[_ngcontent-%COMP%] {\\n    padding: clamp(4rem, 8vh, 5rem) clamp(1.5rem, 4vw, 2rem) clamp(1rem, 2vh, 1.5rem);\\n  }\\n  .ai-avatar[_ngcontent-%COMP%] {\\n    width: clamp(4rem, 8vh, 5rem);\\n    height: clamp(4rem, 8vh, 5rem);\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .ai-avatar[_ngcontent-%COMP%], .robot-head[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@container (max-width: 480px) {\\n  .message-bubble[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 2vw, 1rem);\\n  }\\n  .message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: clamp(0.875rem, 2.5vw, 1rem);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .ai-robot[_ngcontent-%COMP%], .ai-avatar[_ngcontent-%COMP%], .start-btn[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  .btn-glow[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .message-bubble[_ngcontent-%COMP%], .data-panel[_ngcontent-%COMP%], .controls-header[_ngcontent-%COMP%] {\\n    border: 2px solid #2d3748;\\n  }\\n  .start-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #ffffff;\\n  }\\n}\\n\\n\\n\\n\\n.modern-modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(15, 23, 42, 0.8);\\n  -webkit-backdrop-filter: blur(8px) saturate(180%);\\n          backdrop-filter: blur(8px) saturate(180%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n.modern-modal-overlay[_ngcontent-%COMP%]:focus-within {\\n  outline: 2px solid #4facfe;\\n  outline-offset: -2px;\\n}\\n\\n.modern-modal-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1.5rem;\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  width: 100%;\\n  max-width: 56rem;\\n  max-height: 90vh;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n@media (max-width: 768px) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    max-height: 95vh;\\n    margin: 0.5rem;\\n  }\\n}\\n\\n.modal-header-modern[_ngcontent-%COMP%] {\\n  padding: 2rem 2rem 1.5rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 3rem;\\n  height: 3rem;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 1rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  font-size: 1.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #0f172a;\\n  line-height: 1.2;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: 0.75rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  color: #475569;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  color: #0f172a;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(239, 68, 68, 0.15);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #475569;\\n  font-weight: 500;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #4facfe;\\n  font-weight: 700;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 0.5rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  overflow: hidden;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4facfe, #667eea);\\n  border-radius: 9999px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.search-filter-section[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-bottom: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 1rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  border: 1px solid #f1f5f9;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.3);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-personal[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(102, 126, 234, 0.1);\\n  --mdc-chip-selected-label-text-color: #667eea;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-medical[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(16, 185, 129, 0.1);\\n  --mdc-chip-selected-label-text-color: #10b981;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-contact[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(59, 130, 246, 0.1);\\n  --mdc-chip-selected-label-text-color: #3b82f6;\\n}\\n.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-optional[_ngcontent-%COMP%] {\\n  --mdc-chip-selected-container-color: rgba(245, 158, 11, 0.1);\\n  --mdc-chip-selected-label-text-color: #f59e0b;\\n}\\n\\n.modal-body-modern[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem 2rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4rem 1.5rem;\\n  text-align: center;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  width: 4rem;\\n  height: 4rem;\\n  background: #f1f5f9;\\n  border-radius: 9999px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1.5rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  color: #475569;\\n  max-width: 24rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n  grid-template-columns: 1fr;\\n}\\n@media (min-width: 1024px) {\\n  .modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #f1f5f9;\\n  border-radius: 1rem;\\n  padding: 1.5rem;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 3px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(102, 126, 234, 0.2);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  transform: translateY(-2px);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-personal[_ngcontent-%COMP%] {\\n  border-left: 4px solid #667eea;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-medical[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-contact[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3b82f6;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-optional[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  background: #f8fafc;\\n  border-radius: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: #475569;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #0f172a;\\n  line-height: 1.3;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-category[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  opacity: 0;\\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 0.5rem;\\n  background: #f8fafc;\\n  color: #475569;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: #ffffff;\\n  transform: scale(1.1);\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #0f172a;\\n  font-weight: 500;\\n  line-height: 1.5;\\n  word-break: break-word;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]     mark {\\n  background: rgba(79, 172, 254, 0.2);\\n  color: #4facfe;\\n  padding: 0.125rem 0.25rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 0.5rem;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f1f5f9;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-valid[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-warning[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n}\\n.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-error[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n\\n.modal-footer-modern[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n  border-top: 1px solid #f1f5f9;\\n  background: #f8fafc;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 1.5rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  color: #475569;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3b82f6;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 500;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n  border-radius: 0.75rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  background: linear-gradient(135deg, #506be7, #694391);\\n}\\n@media (max-width: 640px) {\\n  .modal-footer-modern[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n    justify-content: stretch;\\n  }\\n  .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%], .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .modern-modal-container[_ngcontent-%COMP%], .data-card[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .card-action-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%], .primary-btn[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .progress-fill[_ngcontent-%COMP%]::after {\\n    animation: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .modern-modal-container[_ngcontent-%COMP%] {\\n    border: 2px solid #0f172a;\\n  }\\n  .data-card[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n  .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n    border: 2px solid #475569;\\n  }\\n}\\n\\n\\n\\n\\n.test-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;\\n  color: white !important;\\n  border: none !important;\\n  border-radius: 25px !important;\\n  padding: 12px 24px !important;\\n  font-weight: 600 !important;\\n  font-size: 0.9em !important;\\n  text-transform: uppercase !important;\\n  letter-spacing: 0.5px !important;\\n  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;\\n  transition: all 0.3s ease !important;\\n  position: relative !important;\\n  overflow: hidden !important;\\n}\\n.test-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) !important;\\n  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;\\n  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%) !important;\\n}\\n.test-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) !important;\\n}\\n.test-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px !important;\\n  font-size: 1.1em !important;\\n}\\n.test-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.test-button[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.idle-content[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  font-size: 0.85em !important;\\n  padding: 10px 20px !important;\\n}\\n\\n.controls-header[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n  font-size: 0.8em !important;\\n  padding: 8px 16px !important;\\n}\\n\\n\\n\\n.scrollable-hidden[_ngcontent-%COMP%] {\\n  overflow-y: scroll;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.scrollable-hidden[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [trigger('fadeInOut', [transition(':enter', [style({\n        opacity: 0\n      }), animate('300ms ease-in', style({\n        opacity: 1\n      }))]), transition(':leave', [animate('200ms ease-out', style({\n        opacity: 0\n      }))])]), trigger('slideInOut', [transition(':enter', [style({\n        transform: 'translateY(-50px)',\n        opacity: 0\n      }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      }))]), transition(':leave', [animate('300ms ease-in', style({\n        transform: 'translateY(-30px)',\n        opacity: 0\n      }))])]), trigger('cardAnimation', [transition(':enter', [style({\n        transform: 'scale(0.8)',\n        opacity: 0\n      }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n        transform: 'scale(1)',\n        opacity: 1\n      }))])])]\n    }\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "Router", "CommonModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatCheckboxModule", "MatSlideToggleModule", "MatCardModule", "MatFormFieldModule", "MatDialog", "MatDialogModule", "MatTooltipModule", "MatChipsModule", "MatSnackBarModule", "ReactiveFormsModule", "FormsModule", "trigger", "style", "transition", "animate", "ModalColetaDadosVittaltecComponent", "Subject", "takeUntil", "VoiceRecorderService", "SpeakerService", "AiQuestionarioApiService", "AlertComponent", "CriptografarUtil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "PreConsultaQuestionarioComponent_div_1_Template_button_click_13_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "iniciarAtendimento", "ɵɵproperty", "undefined", "ɵɵadvance", "ɵɵtextInterpolate", "displayedText", "aiResponse", "lastItem_r5", "value", "label", "ɵɵstyleProp", "getProgressPercentage", "ɵɵtextInterpolate2", "getDadosPreenchidos", "length", "getTotalCampos", "PreConsultaQuestionarioComponent_div_2_div_20_Template_button_click_5_listener", "_r4", "openHistoryModal", "ɵɵtemplate", "PreConsultaQuestionarioComponent_div_2_div_20_div_8_Template", "PreConsultaQuestionarioComponent_div_2_div_20_div_9_Template", "getUltimaVariavelPreenchida", "PreConsultaQuestionarioComponent_div_2_div_21_button_6_Template_button_click_0_listener", "_r7", "enviarTexto", "canSendText", "ɵɵtwoWayListener", "PreConsultaQuestionarioComponent_div_2_div_21_Template_input_ngModelChange_5_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "userInput", "PreConsultaQuestionarioComponent_div_2_div_21_Template_input_keyup_enter_5_listener", "PreConsultaQuestionarioComponent_div_2_div_21_button_6_Template", "PreConsultaQuestionarioComponent_div_2_div_21_mat_hint_7_Template", "ɵɵtwoWayProperty", "isInputDisabled", "isSpeaking", "PreConsultaQuestionarioComponent_div_2_div_22_div_2_Template", "ɵɵpureFunction0", "_c0", "PreConsultaQuestionarioComponent_div_2_div_23_div_4_Template", "ɵɵclassProp", "isRecording", "isProcessing", "isAguardandoResposta", "ɵɵtextInterpolate1", "PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_ngModelChange_5_listener", "_r3", "isTextMode", "PreConsultaQuestionarioComponent_div_2_Template_mat_slide_toggle_change_5_listener", "toggleTextMode", "PreConsultaQuestionarioComponent_div_2_div_15_Template", "PreConsultaQuestionarioComponent_div_2_div_18_Template", "PreConsultaQuestionarioComponent_div_2_div_19_Template", "PreConsultaQuestionarioComponent_div_2_div_20_Template", "PreConsultaQuestionarioComponent_div_2_div_21_Template", "PreConsultaQuestionarioComponent_div_2_div_22_Template", "PreConsultaQuestionarioComponent_div_2_div_23_Template", "PreConsultaQuestionarioComponent", "router", "dialog", "voiceRecorder", "speaker", "aiService", "snackBar", "cdr", "isIdle", "campoAtual", "isHistoryModalOpen", "showValidationButtons", "fullResponseText", "textDisplayInterval", "searchTerm", "availableFilters", "type", "icon", "active", "conversationHistory", "currentToken", "dadosColetados", "nome", "cpf", "email", "telefone", "dataNascimento", "<PERSON><PERSON><PERSON>", "sintomas", "intensidadeDor", "tempoSintomas", "doencasPrevia<PERSON>", "observacoes", "destroy$", "constructor", "ngOnInit", "preloadVoices", "startSpeakingMonitor", "recording$", "pipe", "subscribe", "detectChanges", "result$", "result", "success", "text", "adicionarRespostaUsuario", "error$", "error", "console", "includes", "falhaSnackbar", "iniciarGravacaoAutomatica", "recordingEvent$", "event", "waitUntilFinished", "then", "setTimeout", "ngOnDestroy", "next", "complete", "cancel", "stopRecording", "clearTextDisplayInterval", "generateToken", "solicitarPermissaoMicrofone", "enviarMensagemParaIA", "ultimaMensagem", "payload", "formaDeResposta", "historicoConversa", "token", "isCurrentlyRecording", "enviarMensagens", "response", "processarRespostaIA", "textoResposta", "adicionarRespostaIA", "flgValidacaoCampoAtual", "atualizarDadosColetados", "dados", "flgFinalizar", "todosOsCamposPreenchidos", "finalizarProcesso", "speak", "log", "startSynchronizedTextDisplay", "iniciarProcessoAguardandoResposta", "catch", "resposta", "push", "novosdados", "Object", "keys", "for<PERSON>ach", "key", "trim", "camposObrigatorios", "every", "campo", "iniciarGravacaoContinua", "_this", "_asyncToGenerator", "isSupported", "waitForSpeechEndWithReducedLatency", "monitorarEstadoGravacao", "intervalId", "setInterval", "clearInterval", "serviceRecording", "componentRecording", "finalizarProcessoAguardandoResposta", "startRecording", "iniciarGravacao", "pararGravacao", "toggleRecording", "synthesis", "window", "speechSynthesis", "voices", "getVoices", "onvoiceschanged", "newVoices", "silentUtterance", "SpeechSynthesisUtterance", "volume", "testarFluxoCompleto", "_this2", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "getTracks", "track", "stop", "now", "Date", "timestamp", "getTime", "random", "Math", "floor", "currentlySpeaking", "words", "split", "totalDuration", "estimateSpeechDuration", "intervalTime", "currentWordIndex", "wordsPerMinute", "durationInMinutes", "salvarDadosQuestionario", "abrirDialogColetaDadosVittalTec", "dadosParaSalvar", "idade", "calcularIdade", "map", "s", "sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "extrairNumeroIntensidade", "dataPreenchimento", "toISOString", "localStorageCriptografado", "JSON", "stringify", "nascimento", "hoje", "getFullYear", "mesAtual", "getMonth", "mesNascimento", "getDate", "intensidade", "match", "parseInt", "redirecionarParaFilaEspera", "navigate", "dialogRef", "open", "disableClose", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "panelClass", "afterClosed", "action", "sucessoSnackbar", "removerLocalStorageCriptografado", "labels", "filter", "HistoryModalDialogComponent", "dadosClone", "parse", "data", "autoFocus", "total", "filled", "round", "onSearchChange", "target", "clearSearch", "toggleFilter", "getFilteredData", "getEnhancedDadosPreenchidos", "searchLower", "toLowerCase", "item", "activeCategories", "f", "category", "categoryMap", "categoryLabel", "categoryInfo", "validationStatus", "getValidationStatusForField", "field", "trackByFn", "index", "highlightSearchTerm", "regex", "RegExp", "replace", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "getValidationIcon", "status", "getValidationLabel", "copyToClipboard", "clipboard", "writeText", "editValue", "newValue", "prompt", "find", "clearAllData", "confirm", "isMicrophoneDisabled", "_this3", "Promise", "resolve", "checkInterval", "maxWaitTime", "elapsedTime", "warn", "ɵɵdirectiveInject", "i1", "i2", "i3", "i4", "i5", "i6", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PreConsultaQuestionarioComponent_Template", "rf", "ctx", "PreConsultaQuestionarioComponent_div_1_Template", "PreConsultaQuestionarioComponent_div_2_Template", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i9", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "MatSuffix", "i10", "MatInput", "i11", "MatIconButton", "i12", "MatSlideToggle", "i13", "MatIcon", "i14", "MatTooltip", "styles", "animation", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\pre-consulta-questionario.component.ts", "C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\pre-consulta-questionario\\pre-consulta-questionario.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { trigger, style, transition, animate } from '@angular/animations';\r\nimport { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\r\nimport { QuestionarioPreConsultaDados, WebhookAiQuestionarioPayload, WebhookAiQuestionarioResponse } from './MapPalavrasModel';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { VoiceRecorderService } from './Service/voice-recorder.service';\r\nimport { SpeakerService } from './Service/speaker.service';\r\nimport { AiQuestionarioApiService } from './Service/ai-questionario-api.service';\r\nimport { AlertComponent } from 'src/app/alert/alert.component';\r\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\r\n\r\ninterface FilterOption {\r\n  type: string;\r\n  label: string;\r\n  icon: string;\r\n  active: boolean;\r\n}\r\n\r\ninterface EnhancedDataItem {\r\n  label: string;\r\n  value: string;\r\n  category: string;\r\n  categoryLabel: string;\r\n  icon: string;\r\n  timestamp?: Date;\r\n  validationStatus: 'valid' | 'warning' | 'error';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-pre-consulta-questionario',\r\n  templateUrl: './pre-consulta-questionario.component.html',\r\n  styleUrls: ['./pre-consulta-questionario.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatCheckboxModule,\r\n    MatSlideToggleModule,\r\n    MatIconModule,\r\n    MatDialogModule,\r\n    MatTooltipModule,\r\n    MatChipsModule,\r\n    MatSnackBarModule\r\n  ],\r\n  animations: [\r\n    trigger('fadeInOut', [\r\n      transition(':enter', [\r\n        style({ opacity: 0 }),\r\n        animate('300ms ease-in', style({ opacity: 1 }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('200ms ease-out', style({ opacity: 0 }))\r\n      ])\r\n    ]),\r\n    trigger('slideInOut', [\r\n      transition(':enter', [\r\n        style({ transform: 'translateY(-50px)', opacity: 0 }),\r\n        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n          style({ transform: 'translateY(0)', opacity: 1 }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('300ms ease-in',\r\n          style({ transform: 'translateY(-30px)', opacity: 0 }))\r\n      ])\r\n    ]),\r\n    trigger('cardAnimation', [\r\n      transition(':enter', [\r\n        style({ transform: 'scale(0.8)', opacity: 0 }),\r\n        animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\r\n          style({ transform: 'scale(1)', opacity: 1 }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\nexport class PreConsultaQuestionarioComponent implements OnInit, OnDestroy {\r\n\r\n  isIdle = true;\r\n  isProcessing = false;\r\n  isTextMode = false;\r\n  isRecording = false;\r\n  isAguardandoResposta = false;\r\n  campoAtual = '';\r\n  isHistoryModalOpen = false;\r\n\r\n  // Validation confirmation feature\r\n  showValidationButtons = false;\r\n\r\n  // Novos estados para controle de fala e UI aprimorada\r\n  isSpeaking = false;\r\n  displayedText = '';\r\n  fullResponseText = '';\r\n  textDisplayInterval: any;\r\n\r\n  searchTerm = '';\r\n  availableFilters: FilterOption[] = [\r\n    { type: 'personal', label: 'Dados Pessoais', icon: 'person', active: true },\r\n    { type: 'medical', label: 'Informações Médicas', icon: 'medical_services', active: true },\r\n    { type: 'contact', label: 'Contato', icon: 'contact_phone', active: true },\r\n    { type: 'optional', label: 'Opcionais', icon: 'info', active: true }\r\n  ];\r\n\r\n  conversationHistory: string[] = [];\r\n  currentToken = '';\r\n  aiResponse = '';\r\n  userInput = '';\r\n\r\n  dadosColetados: QuestionarioPreConsultaDados = {\r\n    nome: '',\r\n    cpf: '',\r\n    email: '',\r\n    telefone: '',\r\n    dataNascimento: '',\r\n    alergias: '',\r\n    sintomas: '',\r\n    intensidadeDor: '',\r\n    tempoSintomas: '',\r\n    doencasPrevias: '',\r\n    observacoes: ''\r\n  };\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private dialog: MatDialog,\r\n    private voiceRecorder: VoiceRecorderService,\r\n    private speaker: SpeakerService,\r\n    private aiService: AiQuestionarioApiService,\r\n    private snackBar: AlertComponent,\r\n    private cdr: ChangeDetectorRef\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.preloadVoices();\r\n    this.startSpeakingMonitor();\r\n\r\n    this.voiceRecorder.recording$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(isRecording => {\r\n        this.isRecording = isRecording;\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    this.voiceRecorder.result$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(result => {\r\n        if (result.success && result.text) {\r\n          this.adicionarRespostaUsuario(result.text);\r\n        }\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    this.voiceRecorder.error$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(error => {\r\n        console.error('Erro de reconhecimento de voz:', error);\r\n        this.isRecording = false;\r\n        this.cdr.detectChanges();\r\n\r\n        if (!error.includes('aborted')) {\r\n          this.snackBar.falhaSnackbar(error);\r\n\r\n          if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {\r\n            this.iniciarGravacaoAutomatica();\r\n          }\r\n        }\r\n      });\r\n\r\n    this.voiceRecorder.recordingEvent$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(event => {\r\n        if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {\r\n          if (this.speaker.isSpeaking()) {\r\n            this.speaker.waitUntilFinished().then(() => {\r\n              if (this.isAguardandoResposta && !this.isRecording) {\r\n                this.iniciarGravacaoAutomatica();\r\n              }\r\n            });\r\n          } else {\r\n            setTimeout(() => {\r\n              if (this.isAguardandoResposta && !this.isRecording) {\r\n                this.iniciarGravacaoAutomatica();\r\n              }\r\n            }, 500);\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.speaker.cancel();\r\n    this.voiceRecorder.stopRecording();\r\n    this.clearTextDisplayInterval();\r\n  }\r\n\r\n  iniciarAtendimento(): void {\r\n    this.isIdle = false;\r\n    this.cdr.detectChanges();\r\n    this.currentToken = this.generateToken();\r\n    this.conversationHistory = ['Iniciar atendimento'];\r\n    this.campoAtual = 'inicio';\r\n\r\n    if (!this.isTextMode) {\r\n      this.solicitarPermissaoMicrofone();\r\n    }\r\n\r\n    this.enviarMensagemParaIA();\r\n  }\r\n\r\n  toggleTextMode(): void {\r\n    this.cdr.detectChanges();\r\n    this.userInput = '';\r\n  }\r\n\r\n  private enviarMensagemParaIA(): void {\r\n    this.isProcessing = true;\r\n    this.cdr.detectChanges();\r\n\r\n    const ultimaMensagem = this.conversationHistory.length > 0\r\n      ? this.conversationHistory[this.conversationHistory.length - 1]\r\n      : '';\r\n\r\n    const payload: WebhookAiQuestionarioPayload = {\r\n      formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',\r\n      historicoConversa: [...this.conversationHistory],\r\n      ultimaMensagem: ultimaMensagem,\r\n      campoAtual: this.campoAtual,\r\n      token: this.currentToken\r\n    };\r\n\r\n    if (this.conversationHistory.length > 1 && !this.isTextMode) {\r\n      if (this.voiceRecorder.isCurrentlyRecording()) {\r\n        this.voiceRecorder.stopRecording();\r\n      }\r\n    }\r\n\r\n    this.aiService.enviarMensagens(payload)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response: WebhookAiQuestionarioResponse) => {\r\n          this.processarRespostaIA(response);\r\n        },\r\n        error: (error) => {\r\n          this.isProcessing = false;\r\n          this.cdr.detectChanges();\r\n          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');\r\n          console.error('Erro na comunicação com IA:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  private processarRespostaIA(response: WebhookAiQuestionarioResponse): void {\r\n    this.isProcessing = false;\r\n    this.aiResponse = response.textoResposta;\r\n    this.fullResponseText = response.textoResposta;\r\n    this.displayedText = '';\r\n\r\n    this.adicionarRespostaIA(response.textoResposta);\r\n\r\n    if (response.campoAtual) {\r\n      this.campoAtual = response.campoAtual;\r\n    }\r\n\r\n    // Handle validation confirmation\r\n    this.showValidationButtons = response.flgValidacaoCampoAtual || false;\r\n\r\n    this.cdr.detectChanges();\r\n\r\n    this.atualizarDadosColetados(response.dados);\r\n    this.cdr.detectChanges();\r\n\r\n    if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {\r\n      this.finalizarProcesso();\r\n    } else {\r\n      // Preparar texto para exibição, mas não iniciar ainda\r\n      this.displayedText = '';\r\n      this.cdr.detectChanges();\r\n\r\n      // Iniciar áudio com callback para sincronizar texto\r\n      this.speaker.speak(response.textoResposta, () => {\r\n        // Este callback é executado quando o áudio realmente começa a tocar\r\n        console.log('🎯 Iniciando sincronização de texto com áudio');\r\n        this.startSynchronizedTextDisplay();\r\n      }).then(() => {\r\n        // Only start waiting for response if validation buttons are not shown\r\n        if (!this.showValidationButtons) {\r\n          this.iniciarProcessoAguardandoResposta();\r\n        }\r\n      }).catch(error => {\r\n        console.error('Erro na reprodução da IA:', error);\r\n        this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro\r\n        this.cdr.detectChanges();\r\n        // Only start waiting for response if validation buttons are not shown\r\n        if (!this.showValidationButtons) {\r\n          this.iniciarProcessoAguardandoResposta();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private adicionarRespostaIA(resposta: string): void {\r\n    this.conversationHistory.push(`(resposta da ia) ${resposta}`);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private atualizarDadosColetados(novosdados: QuestionarioPreConsultaDados): void {\r\n    Object.keys(novosdados).forEach(key => {\r\n      if (novosdados[key as keyof QuestionarioPreConsultaDados] &&\r\n        novosdados[key as keyof QuestionarioPreConsultaDados].trim() !== '') {\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados] =\r\n          novosdados[key as keyof QuestionarioPreConsultaDados];\r\n      }\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private todosOsCamposPreenchidos(): boolean {\r\n    const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];\r\n    return camposObrigatorios.every(campo =>\r\n      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados] &&\r\n      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados].trim() !== ''\r\n    );\r\n  }\r\n\r\n  private iniciarProcessoAguardandoResposta(): void {\r\n    this.isAguardandoResposta = true;\r\n    this.cdr.detectChanges();\r\n\r\n    if (!this.isTextMode)\r\n      setTimeout(() => {\r\n        this.iniciarGravacaoContinua();\r\n      }, 500);\r\n  }\r\n\r\n  private async iniciarGravacaoContinua(): Promise<void> {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      console.error('Reconhecimento de voz não suportado');\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Redução de latência: monitorar mais frequentemente o fim da fala\r\n    await this.waitForSpeechEndWithReducedLatency();\r\n    this.iniciarGravacaoAutomatica();\r\n    this.monitorarEstadoGravacao();\r\n  }\r\n\r\n  private monitorarEstadoGravacao(): void {\r\n    const intervalId = setInterval(() => {\r\n      if (!this.isAguardandoResposta) {\r\n        clearInterval(intervalId);\r\n        return;\r\n      }\r\n\r\n      if (this.isTextMode) {\r\n        return;\r\n      }\r\n\r\n      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\r\n      const componentRecording = this.isRecording;\r\n\r\n      if (serviceRecording !== componentRecording) {\r\n        this.isRecording = serviceRecording;\r\n        this.cdr.detectChanges();\r\n      }\r\n\r\n      if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {\r\n        this.iniciarGravacaoAutomatica();\r\n      }\r\n    }, 2000);\r\n  }\r\n\r\n  private finalizarProcessoAguardandoResposta(): void {\r\n    this.isAguardandoResposta = false;\r\n    this.cdr.detectChanges();\r\n\r\n    if (this.isRecording) {\r\n      this.voiceRecorder.stopRecording();\r\n    }\r\n  }\r\n\r\n  private iniciarGravacaoAutomatica(): void {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      console.error('Reconhecimento de voz não suportado');\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Não iniciar gravação se o sistema estiver falando\r\n    if (this.speaker.isSpeaking()) {\r\n      console.log('🔇 Aguardando término da fala para iniciar gravação');\r\n      return;\r\n    }\r\n\r\n    const serviceRecording = this.voiceRecorder.isCurrentlyRecording();\r\n    const componentRecording = this.isRecording;\r\n\r\n    if (serviceRecording || componentRecording) {\r\n      return;\r\n    }\r\n\r\n    const success = this.voiceRecorder.startRecording();\r\n\r\n    if (!success)\r\n      console.error('❌ Falha ao iniciar gravação automática');\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  iniciarGravacao(): void {\r\n    if (!this.voiceRecorder.isSupported()) {\r\n      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');\r\n      return;\r\n    }\r\n\r\n    // Verificar se o sistema está falando\r\n    if (this.speaker.isSpeaking()) {\r\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');\r\n      return;\r\n    }\r\n\r\n    const success = this.voiceRecorder.startRecording();\r\n    if (!success) {\r\n      this.snackBar.falhaSnackbar('Erro ao iniciar gravação');\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  pararGravacao(): void {\r\n    this.voiceRecorder.stopRecording();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  toggleRecording(): void {\r\n    if (this.isRecording) {\r\n      this.pararGravacao();\r\n    } else {\r\n      this.iniciarGravacao();\r\n    }\r\n  }\r\n\r\n  private preloadVoices(): void {\r\n    const synthesis = window.speechSynthesis;\r\n    const voices = synthesis.getVoices();\r\n\r\n    if (voices.length === 0) {\r\n      synthesis.onvoiceschanged = () => {\r\n        const newVoices = synthesis.getVoices();\r\n        newVoices;\r\n      };\r\n\r\n      const silentUtterance = new SpeechSynthesisUtterance('');\r\n      silentUtterance.volume = 0;\r\n      synthesis.speak(silentUtterance);\r\n      synthesis.cancel();\r\n    }\r\n  }\r\n\r\n  testarFluxoCompleto(): void {\r\n    this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.', () => {\r\n      console.log('🎯 Teste: áudio iniciado');\r\n    }).then(() => {\r\n      this.iniciarProcessoAguardandoResposta();\r\n    });\r\n  }\r\n\r\n  enviarTexto(): void {\r\n    // Não permitir envio se o sistema estiver falando\r\n    if (this.speaker.isSpeaking()) {\r\n      this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');\r\n      return;\r\n    }\r\n\r\n    if (this.userInput.trim()) {\r\n      this.adicionarRespostaUsuario(this.userInput);\r\n      this.userInput = '';\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private adicionarRespostaUsuario(resposta: string): void {\r\n    if (this.isAguardandoResposta) {\r\n      this.finalizarProcessoAguardandoResposta();\r\n    }\r\n\r\n    this.conversationHistory.push(`(resposta do usuário) ${resposta}`);\r\n    this.cdr.detectChanges();\r\n    this.enviarMensagemParaIA();\r\n  }\r\n\r\n  private async solicitarPermissaoMicrofone(): Promise<void> {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      stream.getTracks().forEach(track => track.stop());\r\n    } catch (error) {\r\n      console.error('Erro ao solicitar permissão de microfone:', error);\r\n      this.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');\r\n    }\r\n  }\r\n\r\n  private generateToken(): string {\r\n    const now = new Date();\r\n    const timestamp = now.getTime();\r\n    const random = Math.floor(Math.random() * 10000);\r\n    return `${timestamp}_${random}`;\r\n  }\r\n\r\n  // Métodos para controle de fala e exibição sincronizada\r\n  private startSpeakingMonitor(): void {\r\n    setInterval(() => {\r\n      const currentlySpeaking = this.speaker.isSpeaking();\r\n      if (this.isSpeaking !== currentlySpeaking) {\r\n        this.isSpeaking = currentlySpeaking;\r\n        this.cdr.detectChanges();\r\n      }\r\n    }, 100); // Verificar a cada 100ms\r\n  }\r\n\r\n  private startSynchronizedTextDisplay(): void {\r\n    this.clearTextDisplayInterval();\r\n    this.displayedText = '';\r\n\r\n    const words = this.fullResponseText.split(' ');\r\n    const totalDuration = this.estimateSpeechDuration(this.fullResponseText);\r\n    const intervalTime = totalDuration / words.length;\r\n\r\n    let currentWordIndex = 0;\r\n\r\n    this.textDisplayInterval = setInterval(() => {\r\n      if (currentWordIndex < words.length) {\r\n        if (currentWordIndex === 0) {\r\n          this.displayedText = words[currentWordIndex];\r\n        } else {\r\n          this.displayedText += ' ' + words[currentWordIndex];\r\n        }\r\n        currentWordIndex++;\r\n        this.cdr.detectChanges();\r\n      } else {\r\n        this.clearTextDisplayInterval();\r\n      }\r\n    }, intervalTime);\r\n  }\r\n\r\n  private estimateSpeechDuration(text: string): number {\r\n    // Estimar duração baseada em ~150 palavras por minuto (velocidade média de fala)\r\n    const words = text.split(' ').length;\r\n    const wordsPerMinute = 150;\r\n    const durationInMinutes = words / wordsPerMinute;\r\n    return (durationInMinutes * 60 * 1000); // Converter para milissegundos\r\n  }\r\n\r\n  private clearTextDisplayInterval(): void {\r\n    if (this.textDisplayInterval) {\r\n      clearInterval(this.textDisplayInterval);\r\n      this.textDisplayInterval = null;\r\n    }\r\n  }\r\n\r\n  private finalizarProcesso(): void {\r\n    this.cdr.detectChanges();\r\n    this.salvarDadosQuestionario();\r\n    this.abrirDialogColetaDadosVittalTec();\r\n  }\r\n\r\n  private salvarDadosQuestionario(): void {\r\n    try {\r\n      const dadosParaSalvar = {\r\n        nome: this.dadosColetados.nome,\r\n        idade: this.calcularIdade(this.dadosColetados.dataNascimento),\r\n        cpf: this.dadosColetados.cpf,\r\n        email: this.dadosColetados.email,\r\n        telefone: this.dadosColetados.telefone,\r\n        dataNascimento: this.dadosColetados.dataNascimento,\r\n        sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],\r\n        sintomasOutros: this.dadosColetados.observacoes || '',\r\n        intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),\r\n        tempoSintomas: this.dadosColetados.tempoSintomas,\r\n        alergias: this.dadosColetados.alergias || '',\r\n        doencasPrevias: this.dadosColetados.doencasPrevias || '',\r\n        observacoes: this.dadosColetados.observacoes || '',\r\n        dataPreenchimento: new Date().toISOString()\r\n      };\r\n\r\n      CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));\r\n      console.log('Dados do questionário salvos no localStorage:', dadosParaSalvar);\r\n    } catch (error) {\r\n      console.error('Erro ao salvar dados do questionário:', error);\r\n      this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');\r\n    }\r\n  }\r\n\r\n  private calcularIdade(dataNascimento: string): number {\r\n    if (!dataNascimento) return 0;\r\n\r\n    try {\r\n      const nascimento = new Date(dataNascimento);\r\n      const hoje = new Date();\r\n      let idade = hoje.getFullYear() - nascimento.getFullYear();\r\n      const mesAtual = hoje.getMonth();\r\n      const mesNascimento = nascimento.getMonth();\r\n\r\n      if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {\r\n        idade--;\r\n      }\r\n\r\n      return idade;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  private extrairNumeroIntensidade(intensidade: string): number {\r\n    if (!intensidade) return 0;\r\n\r\n    const match = intensidade.match(/\\d+/);\r\n    return match ? parseInt(match[0], 10) : 0;\r\n  }\r\n\r\n  private redirecionarParaFilaEspera(): void {\r\n    try {\r\n      this.router.navigate(['/filaespera']);\r\n    } catch (error) {\r\n      console.error('Erro ao redirecionar para fila de espera:', error);\r\n      this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');\r\n      this.router.navigate(['/filaespera']);\r\n    }\r\n  }\r\n\r\n  private abrirDialogColetaDadosVittalTec(): void {\r\n    const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\r\n      disableClose: true,\r\n      width: '500px',\r\n      maxWidth: '85vw',\r\n      height: 'auto',\r\n      maxHeight: '70vh',\r\n      panelClass: 'vittaltec-modal-panel',\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result?.action === 'continuar') {\r\n        this.snackBar.sucessoSnackbar(\"Dados coletados com sucesso!\");\r\n        this.redirecionarParaFilaEspera();\r\n      } else if (result?.action === 'cancelar') {\r\n        this.snackBar.falhaSnackbar(\"Processo de coleta de dados cancelado.\");\r\n        CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');\r\n      }\r\n\r\n      this.cdr.detectChanges();\r\n    });\r\n  }\r\n\r\n  getDadosPreenchidos(): Array<{ label: string, value: string }> {\r\n    const labels: { [key: string]: string } = {\r\n      nome: 'Nome',\r\n      cpf: 'CPF',\r\n      email: 'Email',\r\n      telefone: 'Telefone',\r\n      dataNascimento: 'Data de Nascimento',\r\n      alergias: 'Alergias',\r\n      sintomas: 'Sintomas',\r\n      intensidadeDor: 'Intensidade da Dor',\r\n      tempoSintomas: 'Tempo dos Sintomas',\r\n      doencasPrevias: 'Doenças Prévias',\r\n      observacoes: 'Observações'\r\n    };\r\n\r\n    return Object.keys(this.dadosColetados)\r\n      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')\r\n      .map(key => ({\r\n        label: labels[key],\r\n        value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados]\r\n      }));\r\n  }\r\n\r\n  getUltimaVariavelPreenchida(): { label: string, value: string } | null {\r\n    const dados = this.getDadosPreenchidos();\r\n    return dados.length > 0 ? dados[dados.length - 1] : null;\r\n  }\r\n\r\n  openHistoryModal(): void {\r\n    import('./components/history-modal-dialog.component').then(({ HistoryModalDialogComponent }) => {\r\n      const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));\r\n      this.dialog.open(HistoryModalDialogComponent, {\r\n        width: '900px',\r\n        maxWidth: '98vw',\r\n        data: {\r\n          dadosColetados: dadosClone,\r\n          snackBar: this.snackBar,\r\n          cdr: this.cdr\r\n        },\r\n        panelClass: 'modern-modal-overlay',\r\n        autoFocus: false\r\n      });\r\n    });\r\n  }\r\n\r\n  getTotalCampos(): number {\r\n    return 10;\r\n  }\r\n\r\n  getProgressPercentage(): number {\r\n    const total = this.getTotalCampos();\r\n    const filled = this.getDadosPreenchidos().length;\r\n    return Math.round((filled / total) * 100);\r\n  }\r\n\r\n  onSearchChange(event: any): void {\r\n    this.searchTerm = event.target.value;\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchTerm = '';\r\n  }\r\n\r\n  toggleFilter(filter: FilterOption): void {\r\n    filter.active = !filter.active;\r\n  }\r\n\r\n  getFilteredData(): EnhancedDataItem[] {\r\n    let data = this.getEnhancedDadosPreenchidos();\r\n\r\n    if (this.searchTerm) {\r\n      const searchLower = this.searchTerm.toLowerCase();\r\n      data = data.filter(item =>\r\n        item.label.toLowerCase().includes(searchLower) ||\r\n        item.value.toLowerCase().includes(searchLower)\r\n      );\r\n    }\r\n\r\n    const activeCategories = this.availableFilters\r\n      .filter(f => f.active)\r\n      .map(f => f.type);\r\n\r\n    data = data.filter(item => activeCategories.includes(item.category));\r\n\r\n    return data;\r\n  }\r\n\r\n  getEnhancedDadosPreenchidos(): EnhancedDataItem[] {\r\n    const categoryMap: { [key: string]: { category: string, categoryLabel: string, icon: string } } = {\r\n      nome: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'person' },\r\n      cpf: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'badge' },\r\n      dataNascimento: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'cake' },\r\n      email: { category: 'contact', categoryLabel: 'Contato', icon: 'email' },\r\n      telefone: { category: 'contact', categoryLabel: 'Contato', icon: 'phone' },\r\n      sintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'medical_services' },\r\n      intensidadeDor: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'personal_injury' },\r\n      tempoSintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'schedule' },\r\n      alergias: { category: 'optional', categoryLabel: 'Opcionais', icon: 'medical_information' },\r\n      observacoes: { category: 'optional', categoryLabel: 'Opcionais', icon: 'description' }\r\n    };\r\n\r\n    const labels: { [key: string]: string } = {\r\n      nome: 'Nome',\r\n      cpf: 'CPF',\r\n      email: 'Email',\r\n      telefone: 'Telefone',\r\n      dataNascimento: 'Data de Nascimento',\r\n      alergias: 'Alergias',\r\n      sintomas: 'Sintomas',\r\n      intensidadeDor: 'Intensidade da Dor',\r\n      tempoSintomas: 'Tempo dos Sintomas',\r\n      doencasPrevias: 'Doenças Prévias',\r\n      observacoes: 'Observações'\r\n    };\r\n\r\n    return Object.keys(this.dadosColetados)\r\n      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&\r\n        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')\r\n      .map(key => {\r\n        const categoryInfo = categoryMap[key] || { category: 'optional', categoryLabel: 'Outros', icon: 'info' };\r\n        return {\r\n          label: labels[key] || key,\r\n          value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados],\r\n          category: categoryInfo.category,\r\n          categoryLabel: categoryInfo.categoryLabel,\r\n          icon: categoryInfo.icon,\r\n          timestamp: new Date(),\r\n          validationStatus: this.getValidationStatusForField(key) as 'valid' | 'warning' | 'error'\r\n        };\r\n      });\r\n  }\r\n\r\n  getValidationStatusForField(field: string): string {\r\n    const value = this.dadosColetados[field as keyof QuestionarioPreConsultaDados];\r\n    if (!value || value.trim() === '') return 'error';\r\n\r\n    if (field === 'email' && !value.includes('@')) return 'warning';\r\n    if (field === 'cpf' && value.length < 11) return 'warning';\r\n\r\n    return 'valid';\r\n  }\r\n\r\n  trackByFn(index: number, item: EnhancedDataItem): string {\r\n    index;\r\n    return item.label + item.value;\r\n  }\r\n\r\n  highlightSearchTerm(text: string): string {\r\n    if (!this.searchTerm) return text;\r\n\r\n    const regex = new RegExp(`(${this.searchTerm})`, 'gi');\r\n    return text.replace(regex, '<mark>$1</mark>');\r\n  }\r\n\r\n  formatTimestamp(timestamp: Date): string {\r\n    return timestamp.toLocaleTimeString('pt-BR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  }\r\n\r\n  getValidationIcon(status: string): string {\r\n    switch (status) {\r\n      case 'valid': return 'check_circle';\r\n      case 'warning': return 'warning';\r\n      case 'error': return 'error';\r\n      default: return 'info';\r\n    }\r\n  }\r\n\r\n  getValidationLabel(status: string): string {\r\n    switch (status) {\r\n      case 'valid': return 'Válido';\r\n      case 'warning': return 'Atenção';\r\n      case 'error': return 'Erro';\r\n      default: return 'Info';\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text).then(() => {\r\n      this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\r\n    }).catch(() => {\r\n      this.snackBar.falhaSnackbar('Erro ao copiar texto');\r\n    });\r\n  }\r\n\r\n  editValue(item: EnhancedDataItem): void {\r\n    const newValue = prompt(`Editar ${item.label}:`, item.value);\r\n    if (newValue !== null && newValue !== item.value) {\r\n      const field = Object.keys(this.dadosColetados).find(key => {\r\n        const labels: { [key: string]: string } = {\r\n          nome: 'Nome',\r\n          cpf: 'CPF',\r\n          email: 'Email',\r\n          telefone: 'Telefone',\r\n          dataNascimento: 'Data de Nascimento',\r\n          alergias: 'Alergias',\r\n          sintomas: 'Sintomas',\r\n          intensidadeDor: 'Intensidade da Dor',\r\n          tempoSintomas: 'Tempo dos Sintomas',\r\n          doencasPrevias: 'Doenças Prévias',\r\n          observacoes: 'Observações'\r\n        };\r\n        return labels[key] === item.label;\r\n      });\r\n\r\n      if (field) {\r\n        this.dadosColetados[field as keyof QuestionarioPreConsultaDados] = newValue;\r\n        this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\r\n        this.cdr.detectChanges();\r\n      }\r\n    }\r\n  }\r\n\r\n  clearAllData(): void {\r\n    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\r\n      this.dadosColetados = {\r\n        nome: '',\r\n        cpf: '',\r\n        email: '',\r\n        telefone: '',\r\n        dataNascimento: '',\r\n        alergias: '',\r\n        sintomas: '',\r\n        intensidadeDor: '',\r\n        tempoSintomas: '',\r\n        doencasPrevias: '',\r\n        observacoes: ''\r\n      };\r\n      this.snackBar.sucessoSnackbar('Todos os dados foram limpos');\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  // Métodos auxiliares para controle de UI\r\n  isInputDisabled(): boolean {\r\n    return this.speaker.isSpeaking() || this.isProcessing;\r\n  }\r\n\r\n  isMicrophoneDisabled(): boolean {\r\n    return this.speaker.isSpeaking() || this.isProcessing;\r\n  }\r\n\r\n  canSendText(): boolean {\r\n    return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();\r\n  }\r\n\r\n  // Método para reduzir latência na transição de áudio\r\n  private async waitForSpeechEndWithReducedLatency(): Promise<void> {\r\n    return new Promise<void>((resolve) => {\r\n      const checkInterval = 50; // Verificar a cada 50ms para reduzir latência\r\n      const maxWaitTime = 30000; // Máximo 30 segundos de espera\r\n      let elapsedTime = 0;\r\n\r\n      const intervalId = setInterval(() => {\r\n        elapsedTime += checkInterval;\r\n\r\n        if (!this.speaker.isSpeaking()) {\r\n          clearInterval(intervalId);\r\n          // Pequena pausa adicional para garantir que o áudio terminou completamente\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 100);\r\n        } else if (elapsedTime >= maxWaitTime) {\r\n          clearInterval(intervalId);\r\n          console.warn('Timeout aguardando fim da fala');\r\n          resolve();\r\n        }\r\n      }, checkInterval);\r\n    });\r\n  }\r\n}", "<div class=\"ai-questionnaire-container\">\r\n  <!-- <PERSON><PERSON> -->\r\n  <div class=\"idle-screen\" *ngIf=\"isIdle\">\r\n    <div class=\"idle-content\">\r\n      <!-- IA Robot Custom CSS -->\r\n      <div class=\"ai-robot\" style=\"margin-bottom: 9px;\">\r\n        <div class=\"robot-head\">\r\n          <div class=\"robot-eyes\">\r\n            <div class=\"eye left-eye\"></div>\r\n            <div class=\"eye right-eye\"></div>\r\n          </div>\r\n          <div class=\"robot-mouth\"></div>\r\n        </div>\r\n        <div class=\"robot-body\">\r\n          <div class=\"robot-chest\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <p>Vou coletar suas informações através de uma conversa natural</p>\r\n\r\n      <div class=\"action-buttons\">\r\n        <button class=\"start-btn\" (click)=\"iniciarAtendimento()\">\r\n          <span>Iniciar Atendimento</span>\r\n          <div class=\"btn-glow\"></div>\r\n        </button>\r\n\r\n        <!-- <PERSON><PERSON><PERSON> de teste na tela inicial -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Tela de Atendimento -->\r\n  <div class=\"chat-interface\" *ngIf=\"!isIdle\">\r\n    <!-- Controls Header - Position Absolute Top Left -->\r\n    <div class=\"controls-header\">\r\n      <div class=\"mode-indicator\">\r\n        <mat-icon class=\"mode-icon\">{{ isTextMode ? 'keyboard' : 'mic' }}</mat-icon>\r\n      </div>\r\n      <mat-slide-toggle\r\n        [(ngModel)]=\"isTextMode\"\r\n        (change)=\"toggleTextMode()\"\r\n        class=\"mode-toggle\"\r\n        [disabled]=\"isProcessing || isSpeaking\">\r\n        Modo Texto\r\n      </mat-slide-toggle>\r\n    </div>\r\n\r\n    <!-- Main Chat Area -->\r\n    <div class=\"main-chat-area\">\r\n      <!-- IA Section -->\r\n      <div class=\"ai-section\">\r\n        <div class=\"ai-avatar\" [class.processing]=\"isProcessing\" [class.listening]=\"isRecording\"\r\n          [class.waiting]=\"isAguardandoResposta && !isRecording\">\r\n          <div class=\"ai-face\">\r\n            <div class=\"ai-eyes\">\r\n              <div class=\"eye\"></div>\r\n              <div class=\"eye\"></div>\r\n            </div>\r\n            <div class=\"ai-mouth\" [class.talking]=\"isProcessing\"></div>\r\n          </div>\r\n          <div class=\"ai-pulse\" *ngIf=\"isProcessing || isRecording || isAguardandoResposta\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Response and Data Section -->\r\n      <div class=\"response-data-section\">\r\n        <div class=\"response-section\">\r\n          <div class=\"ai-message\" *ngIf=\"aiResponse\" [@fadeInOut]>\r\n            <div class=\"message-bubble scrollable-hidden\">\r\n              <p>{{ displayedText || aiResponse }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Processing Indicator -->\r\n          <div class=\"processing-indicator\" *ngIf=\"isProcessing\" [@fadeInOut]>\r\n            <div class=\"typing-dots\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n            <span class=\"processing-text\">Processando sua resposta...</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Dados Preenchidos -->\r\n        <div class=\"data-section\" *ngIf=\"getDadosPreenchidos().length > 0\" [@slideInOut]>\r\n          <div class=\"data-panel\">\r\n            <div class=\"data-header\">\r\n              <h3>Última Informação Coletada</h3>\r\n              <button\r\n                mat-icon-button\r\n                (click)=\"openHistoryModal()\"\r\n                matTooltip=\"Ver todas as variáveis\"\r\n                class=\"history-btn\">\r\n                <mat-icon>history</mat-icon>\r\n              </button>\r\n            </div>\r\n            <div class=\"data-item\" *ngIf=\"getUltimaVariavelPreenchida() as lastItem\" [matTooltip]=\"lastItem.value\">\r\n              <span class=\"descInfoCategoria\">{{ lastItem.label }}</span>\r\n              <span class=\"descInfovalue\">{{ lastItem.value }}</span>\r\n            </div>\r\n            \r\n            <!-- Progress indicator -->\r\n            <div class=\"progress-info\" *ngIf=\"getDadosPreenchidos().length > 0\">\r\n              <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage()\"></div>\r\n              </div>\r\n              <span class=\"progress-text\">{{ getDadosPreenchidos().length }}/{{ getTotalCampos() }} campos preenchidos</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Input Area - Only show when in text mode -->\r\n    <div class=\"input-section\" *ngIf=\"isTextMode\">\r\n      <div class=\"input-container\">\r\n        <!-- Text Input -->\r\n        <mat-form-field appearance=\"outline\" class=\"user-input\">\r\n          <mat-label>Sua resposta</mat-label>\r\n          <input matInput [(ngModel)]=\"userInput\" placeholder=\"Digite aqui...\" (keyup.enter)=\"enviarTexto()\"\r\n            [disabled]=\"isInputDisabled()\">\r\n          <button mat-icon-button matSuffix *ngIf=\"canSendText()\" (click)=\"enviarTexto()\" [disabled]=\"!canSendText()\">\r\n            <mat-icon>send</mat-icon>\r\n          </button>\r\n          <!-- Indicador de que está aguardando fala terminar -->\r\n          <mat-hint *ngIf=\"isSpeaking\" class=\"speaking-hint\">\r\n            <mat-icon class=\"hint-icon\">volume_up</mat-icon>\r\n            Aguarde o término da fala...\r\n          </mat-hint>\r\n        </mat-form-field>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Audio Visualization -->\r\n    <div class=\"audio-visualization\" *ngIf=\"isRecording && !isTextMode\" [@fadeInOut]>\r\n      <div class=\"sound-wave\">\r\n        <div class=\"wave-bar\" *ngFor=\"let bar of [1,2,3,4,5,6,7,8]\"></div>\r\n      </div>\r\n      <span class=\"recording-text\">\r\n        <mat-icon class=\"recording-icon\">mic</mat-icon>\r\n        Gravando...\r\n      </span>\r\n    </div>\r\n\r\n    <!-- Voice Status Indicator (Floating) -->\r\n    <div class=\"voice-status-indicator\" *ngIf=\"!isTextMode\" [@fadeInOut]>\r\n      <div class=\"status-icon\"\r\n           [class.recording]=\"isRecording\"\r\n           [class.processing]=\"isProcessing\"\r\n           [class.waiting]=\"isAguardandoResposta && !isRecording && !isProcessing\">\r\n        <mat-icon>{{ isRecording ? 'mic' : isProcessing ? 'hourglass_empty' : isAguardandoResposta ? 'hearing' : 'mic_off' }}</mat-icon>\r\n        <div class=\"status-ripple\" *ngIf=\"isRecording\"></div>\r\n      </div>\r\n      <span class=\"status-text\">\r\n        {{ isRecording ? 'Ouvindo...' :\r\n        isProcessing ? 'Processando...' :\r\n        isAguardandoResposta ? 'Carregando microfone...' :\r\n        isSpeaking ? 'Falando...' :\r\n        'Aguardando...' }}\r\n      </span>\r\n    </div>\r\n  </div>\r\n\r\n</div>"], "mappings": ";AAAA,SAAuCA,iBAAiB,QAAQ,eAAe;AAC/E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,SAASC,kCAAkC,QAAQ,oFAAoF;AAEvI,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;IChBvDC,EALR,CAAAC,cAAA,aAAwC,aACZ,aAE0B,aACxB,aACE;IAEtBD,EADA,CAAAE,SAAA,aAAgC,aACC;IACnCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAI,MAAA,mFAA4D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGjEH,EADF,CAAAC,cAAA,eAA4B,kBAC+B;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,yEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACtDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAE,SAAA,eAA4B;IAMpCF,EALM,CAAAG,YAAA,EAAS,EAGL,EACF,EACF;;;;;IA+BEH,EAAA,CAAAE,SAAA,cAAwF;;;;;IASpFF,EAFJ,CAAAC,cAAA,cAAwD,cACR,QACzC;IAAAD,EAAA,CAAAI,MAAA,GAAiC;IAExCJ,EAFwC,CAAAG,YAAA,EAAI,EACpC,EACF;;;;IAJqCH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAEhDd,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAAQ,aAAA,IAAAR,MAAA,CAAAS,UAAA,CAAiC;;;;;IAMtClB,EADF,CAAAC,cAAA,cAAoE,cACzC;IAGrBD,EAFA,CAAAE,SAAA,WAAa,WACA,WACA;IACfF,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAI,MAAA,kCAA2B;IAC3DJ,EAD2D,CAAAG,YAAA,EAAO,EAC5D;;;IAPiDH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;;;;;IAwB/Dd,EADF,CAAAC,cAAA,cAAuG,eACrE;IAAAD,EAAA,CAAAI,MAAA,GAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAoB;IAClDJ,EADkD,CAAAG,YAAA,EAAO,EACnD;;;;IAHmEH,EAAA,CAAAa,UAAA,eAAAM,WAAA,CAAAC,KAAA,CAA6B;IACpEpB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAgB,iBAAA,CAAAG,WAAA,CAAAE,KAAA,CAAoB;IACxBrB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAgB,iBAAA,CAAAG,WAAA,CAAAC,KAAA,CAAoB;;;;;IAKhDpB,EADF,CAAAC,cAAA,cAAoE,cACxC;IACxBD,EAAA,CAAAE,SAAA,cAA2E;IAC7EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAA4E;IAC1GJ,EAD0G,CAAAG,YAAA,EAAO,EAC3G;;;;IAHyBH,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAsB,WAAA,UAAAb,MAAA,CAAAc,qBAAA,QAAyC;IAE1CvB,EAAA,CAAAe,SAAA,GAA4E;IAA5Ef,EAAA,CAAAwB,kBAAA,KAAAf,MAAA,CAAAgB,mBAAA,GAAAC,MAAA,OAAAjB,MAAA,CAAAkB,cAAA,0BAA4E;;;;;;IAnBxG3B,EAHN,CAAAC,cAAA,cAAiF,cACvD,cACG,SACnB;IAAAD,EAAA,CAAAI,MAAA,gDAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,iBAIsB;IAFpBD,EAAA,CAAAK,UAAA,mBAAAuB,+EAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqB,gBAAA,EAAkB;IAAA,EAAC;IAG5B9B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAErBJ,EAFqB,CAAAG,YAAA,EAAW,EACrB,EACL;IAONH,EANA,CAAA+B,UAAA,IAAAC,4DAAA,kBAAuG,IAAAC,4DAAA,kBAMnC;IAOxEjC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzB6DH,EAAA,CAAAa,UAAA,gBAAAC,SAAA,CAAa;IAYpDd,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAyB,2BAAA,GAAoC;IAMhClC,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAgB,mBAAA,GAAAC,MAAA,KAAsC;;;;;;IAmBpE1B,EAAA,CAAAC,cAAA,iBAA4G;IAApDD,EAAA,CAAAK,UAAA,mBAAA8B,wFAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4B,WAAA,EAAa;IAAA,EAAC;IAC7ErC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAChBJ,EADgB,CAAAG,YAAA,EAAW,EAClB;;;;IAFuEH,EAAA,CAAAa,UAAA,cAAAJ,MAAA,CAAA6B,WAAA,GAA2B;;;;;IAKzGtC,EADF,CAAAC,cAAA,mBAAmD,mBACrB;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAI,MAAA,0CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAW;;;;;;IAVXH,EAJN,CAAAC,cAAA,cAA8C,cACf,yBAE6B,gBAC3C;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,gBACiC;IADjBD,EAAA,CAAAuC,gBAAA,2BAAAC,sFAAAC,MAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA2C,kBAAA,CAAAlC,MAAA,CAAAmC,SAAA,EAAAH,MAAA,MAAAhC,MAAA,CAAAmC,SAAA,GAAAH,MAAA;MAAA,OAAAzC,EAAA,CAAAW,WAAA,CAAA8B,MAAA;IAAA,EAAuB;IAA8BzC,EAAA,CAAAK,UAAA,yBAAAwC,oFAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAeF,MAAA,CAAA4B,WAAA,EAAa;IAAA,EAAC;IAAlGrC,EAAA,CAAAG,YAAA,EACiC;IAKjCH,EAJA,CAAA+B,UAAA,IAAAe,+DAAA,qBAA4G,IAAAC,iEAAA,uBAIzD;IAMzD/C,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;;;;IAZgBH,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgD,gBAAA,YAAAvC,MAAA,CAAAmC,SAAA,CAAuB;IACrC5C,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAAwC,eAAA,GAA8B;IACGjD,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA6B,WAAA,GAAmB;IAI3CtC,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAyC,UAAA,CAAgB;;;;;IAW7BlD,EAAA,CAAAE,SAAA,cAAkE;;;;;IADpEF,EADF,CAAAC,cAAA,cAAiF,cACvD;IACtBD,EAAA,CAAA+B,UAAA,IAAAoB,4DAAA,kBAA4D;IAC9DnD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA6B,mBACM;IAAAD,EAAA,CAAAI,MAAA,UAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAI,MAAA,oBACF;IACFJ,EADE,CAAAG,YAAA,EAAO,EACH;;;IAR8DH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAEtCd,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAoD,eAAA,IAAAC,GAAA,EAAoB;;;;;IAe1DrD,EAAA,CAAAE,SAAA,cAAqD;;;;;IADrDF,EALJ,CAAAC,cAAA,cAAqE,cAIU,eACjE;IAAAD,EAAA,CAAAI,MAAA,GAA2G;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAChIH,EAAA,CAAA+B,UAAA,IAAAuB,4DAAA,kBAA+C;IACjDtD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAI,MAAA,GAKF;IACFJ,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAfkDH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAY;IAE7Dd,EAAA,CAAAe,SAAA,EAA+B;IAE/Bf,EAFA,CAAAuD,WAAA,cAAA9C,MAAA,CAAA+C,WAAA,CAA+B,eAAA/C,MAAA,CAAAgD,YAAA,CACE,YAAAhD,MAAA,CAAAiD,oBAAA,KAAAjD,MAAA,CAAA+C,WAAA,KAAA/C,MAAA,CAAAgD,YAAA,CACsC;IAChEzD,EAAA,CAAAe,SAAA,GAA2G;IAA3Gf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAA+C,WAAA,WAAA/C,MAAA,CAAAgD,YAAA,uBAAAhD,MAAA,CAAAiD,oBAAA,yBAA2G;IACzF1D,EAAA,CAAAe,SAAA,EAAiB;IAAjBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA+C,WAAA,CAAiB;IAG7CxD,EAAA,CAAAe,SAAA,GAKF;IALEf,EAAA,CAAA2D,kBAAA,MAAAlD,MAAA,CAAA+C,WAAA,kBAAA/C,MAAA,CAAAgD,YAAA,sBAAAhD,MAAA,CAAAiD,oBAAA,+BAAAjD,MAAA,CAAAyC,UAAA,uCAKF;;;;;;IA5HElD,EAJN,CAAAC,cAAA,cAA4C,cAEb,cACC,mBACE;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IACnEJ,EADmE,CAAAG,YAAA,EAAW,EACxE;IACNH,EAAA,CAAAC,cAAA,2BAI0C;IAHxCD,EAAA,CAAAuC,gBAAA,2BAAAqB,0FAAAnB,MAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA2C,kBAAA,CAAAlC,MAAA,CAAAqD,UAAA,EAAArB,MAAA,MAAAhC,MAAA,CAAAqD,UAAA,GAAArB,MAAA;MAAA,OAAAzC,EAAA,CAAAW,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IACxBzC,EAAA,CAAAK,UAAA,oBAAA0D,mFAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuD,cAAA,EAAgB;IAAA,EAAC;IAG3BhE,EAAA,CAAAI,MAAA,mBACF;IACFJ,EADE,CAAAG,YAAA,EAAmB,EACf;IASEH,EANR,CAAAC,cAAA,cAA4B,cAEF,cAEmC,eAClC,eACE;IAEnBD,EADA,CAAAE,SAAA,eAAuB,eACA;IACzBF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,eAA2D;IAC7DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+B,UAAA,KAAAkC,sDAAA,kBAAkF;IAEtFjE,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACH;IAQ5BD,EAPA,CAAA+B,UAAA,KAAAmC,sDAAA,kBAAwD,KAAAC,sDAAA,kBAOY;IAQtEnE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA+B,UAAA,KAAAqC,sDAAA,mBAAiF;IA2BrFpE,EADE,CAAAG,YAAA,EAAM,EACF;IAkCNH,EA/BA,CAAA+B,UAAA,KAAAsC,sDAAA,kBAA8C,KAAAC,sDAAA,kBAoBmC,KAAAC,sDAAA,mBAWZ;IAgBvEvE,EAAA,CAAAG,YAAA,EAAM;;;;IA9H4BH,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,iBAAA,CAAAP,MAAA,CAAAqD,UAAA,sBAAqC;IAGjE9D,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAAgD,gBAAA,YAAAvC,MAAA,CAAAqD,UAAA,CAAwB;IAGxB9D,EAAA,CAAAa,UAAA,aAAAJ,MAAA,CAAAgD,YAAA,IAAAhD,MAAA,CAAAyC,UAAA,CAAuC;IAShBlD,EAAA,CAAAe,SAAA,GAAiC;IACtDf,EADqB,CAAAuD,WAAA,eAAA9C,MAAA,CAAAgD,YAAA,CAAiC,cAAAhD,MAAA,CAAA+C,WAAA,CAAgC,YAAA/C,MAAA,CAAAiD,oBAAA,KAAAjD,MAAA,CAAA+C,WAAA,CAChC;IAM9BxD,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAuD,WAAA,YAAA9C,MAAA,CAAAgD,YAAA,CAA8B;IAE/BzD,EAAA,CAAAe,SAAA,EAAyD;IAAzDf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAgD,YAAA,IAAAhD,MAAA,CAAA+C,WAAA,IAAA/C,MAAA,CAAAiD,oBAAA,CAAyD;IAOvD1D,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAS,UAAA,CAAgB;IAONlB,EAAA,CAAAe,SAAA,EAAkB;IAAlBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAgD,YAAA,CAAkB;IAW5BzD,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAgB,mBAAA,GAAAC,MAAA,KAAsC;IA8BzC1B,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAqD,UAAA,CAAgB;IAoBV9D,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA+C,WAAA,KAAA/C,MAAA,CAAAqD,UAAA,CAAgC;IAW7B9D,EAAA,CAAAe,SAAA,EAAiB;IAAjBf,EAAA,CAAAa,UAAA,UAAAJ,MAAA,CAAAqD,UAAA,CAAiB;;;ADrD1D,OAAM,MAAOU,gCAAgC;EAiDjCC,MAAA;EACAC,MAAA;EACAC,aAAA;EACAC,OAAA;EACAC,SAAA;EACAC,QAAA;EACAC,GAAA;EArDVC,MAAM,GAAG,IAAI;EACbvB,YAAY,GAAG,KAAK;EACpBK,UAAU,GAAG,KAAK;EAClBN,WAAW,GAAG,KAAK;EACnBE,oBAAoB,GAAG,KAAK;EAC5BuB,UAAU,GAAG,EAAE;EACfC,kBAAkB,GAAG,KAAK;EAE1B;EACAC,qBAAqB,GAAG,KAAK;EAE7B;EACAjC,UAAU,GAAG,KAAK;EAClBjC,aAAa,GAAG,EAAE;EAClBmE,gBAAgB,GAAG,EAAE;EACrBC,mBAAmB;EAEnBC,UAAU,GAAG,EAAE;EACfC,gBAAgB,GAAmB,CACjC;IAAEC,IAAI,EAAE,UAAU;IAAEnE,KAAK,EAAE,gBAAgB;IAAEoE,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAI,CAAE,EAC3E;IAAEF,IAAI,EAAE,SAAS;IAAEnE,KAAK,EAAE,qBAAqB;IAAEoE,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE;EAAI,CAAE,EACzF;IAAEF,IAAI,EAAE,SAAS;IAAEnE,KAAK,EAAE,SAAS;IAAEoE,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE;EAAI,CAAE,EAC1E;IAAEF,IAAI,EAAE,UAAU;IAAEnE,KAAK,EAAE,WAAW;IAAEoE,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAI,CAAE,CACrE;EAEDC,mBAAmB,GAAa,EAAE;EAClCC,YAAY,GAAG,EAAE;EACjB1E,UAAU,GAAG,EAAE;EACf0B,SAAS,GAAG,EAAE;EAEdiD,cAAc,GAAiC;IAC7CC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;GACd;EAEOC,QAAQ,GAAG,IAAIhH,OAAO,EAAQ;EAEtCiH,YACUjC,MAAc,EACdC,MAAiB,EACjBC,aAAmC,EACnCC,OAAuB,EACvBC,SAAmC,EACnCC,QAAwB,EACxBC,GAAsB;IANtB,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,GAAG,GAAHA,GAAG;EACT;EAEJ4B,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,CAAClC,aAAa,CAACmC,UAAU,CAC1BC,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC+G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACxD,WAAW,IAAG;MACvB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACuB,GAAG,CAACkC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACtC,aAAa,CAACuC,OAAO,CACvBH,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC+G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACG,MAAM,IAAG;MAClB,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,EAAE;QACjC,IAAI,CAACC,wBAAwB,CAACH,MAAM,CAACE,IAAI,CAAC;MAC5C;MACA,IAAI,CAACtC,GAAG,CAACkC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACtC,aAAa,CAAC4C,MAAM,CACtBR,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC+G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACQ,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAChE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACuB,GAAG,CAACkC,aAAa,EAAE;MAExB,IAAI,CAACO,KAAK,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC9B,IAAI,CAAC5C,QAAQ,CAAC6C,aAAa,CAACH,KAAK,CAAC;QAElC,IAAI,CAACA,KAAK,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC5D,UAAU,IAAI,CAAC,IAAI,CAACL,YAAY,EAAE;UAC5E,IAAI,CAACmE,yBAAyB,EAAE;QAClC;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAACjD,aAAa,CAACkD,eAAe,CAC/Bd,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC+G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACc,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACtC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC9B,oBAAoB,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE;QACzF,IAAI,IAAI,CAACc,OAAO,CAAC1B,UAAU,EAAE,EAAE;UAC7B,IAAI,CAAC0B,OAAO,CAACmD,iBAAiB,EAAE,CAACC,IAAI,CAAC,MAAK;YACzC,IAAI,IAAI,CAACtE,oBAAoB,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;cAClD,IAAI,CAACoE,yBAAyB,EAAE;YAClC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLK,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAACvE,oBAAoB,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;cAClD,IAAI,CAACoE,yBAAyB,EAAE;YAClC;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,CAAC;EACN;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,EAAE;IACpB,IAAI,CAAC1B,QAAQ,CAAC2B,QAAQ,EAAE;IACxB,IAAI,CAACxD,OAAO,CAACyD,MAAM,EAAE;IACrB,IAAI,CAAC1D,aAAa,CAAC2D,aAAa,EAAE;IAClC,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEA3H,kBAAkBA,CAAA;IAChB,IAAI,CAACoE,MAAM,GAAG,KAAK;IACnB,IAAI,CAACD,GAAG,CAACkC,aAAa,EAAE;IACxB,IAAI,CAACrB,YAAY,GAAG,IAAI,CAAC4C,aAAa,EAAE;IACxC,IAAI,CAAC7C,mBAAmB,GAAG,CAAC,qBAAqB,CAAC;IAClD,IAAI,CAACV,UAAU,GAAG,QAAQ;IAE1B,IAAI,CAAC,IAAI,CAACnB,UAAU,EAAE;MACpB,IAAI,CAAC2E,2BAA2B,EAAE;IACpC;IAEA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA1E,cAAcA,CAAA;IACZ,IAAI,CAACe,GAAG,CAACkC,aAAa,EAAE;IACxB,IAAI,CAACrE,SAAS,GAAG,EAAE;EACrB;EAEQ8F,oBAAoBA,CAAA;IAC1B,IAAI,CAACjF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACsB,GAAG,CAACkC,aAAa,EAAE;IAExB,MAAM0B,cAAc,GAAG,IAAI,CAAChD,mBAAmB,CAACjE,MAAM,GAAG,CAAC,GACtD,IAAI,CAACiE,mBAAmB,CAAC,IAAI,CAACA,mBAAmB,CAACjE,MAAM,GAAG,CAAC,CAAC,GAC7D,EAAE;IAEN,MAAMkH,OAAO,GAAiC;MAC5CC,eAAe,EAAE,IAAI,CAAC/E,UAAU,GAAG,gBAAgB,GAAG,6BAA6B;MACnFgF,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAACnD,mBAAmB,CAAC;MAChDgD,cAAc,EAAEA,cAAc;MAC9B1D,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B8D,KAAK,EAAE,IAAI,CAACnD;KACb;IAED,IAAI,IAAI,CAACD,mBAAmB,CAACjE,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACoC,UAAU,EAAE;MAC3D,IAAI,IAAI,CAACa,aAAa,CAACqE,oBAAoB,EAAE,EAAE;QAC7C,IAAI,CAACrE,aAAa,CAAC2D,aAAa,EAAE;MACpC;IACF;IAEA,IAAI,CAACzD,SAAS,CAACoE,eAAe,CAACL,OAAO,CAAC,CACpC7B,IAAI,CAACrH,SAAS,CAAC,IAAI,CAAC+G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAAC;MACTmB,IAAI,EAAGe,QAAuC,IAAI;QAChD,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAAC;MACpC,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsB,GAAG,CAACkC,aAAa,EAAE;QACxB,IAAI,CAACnC,QAAQ,CAAC6C,aAAa,CAAC,8CAA8C,CAAC;QAC3EF,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACN;EAEQ2B,mBAAmBA,CAACD,QAAuC;IACjE,IAAI,CAACzF,YAAY,GAAG,KAAK;IACzB,IAAI,CAACvC,UAAU,GAAGgI,QAAQ,CAACE,aAAa;IACxC,IAAI,CAAChE,gBAAgB,GAAG8D,QAAQ,CAACE,aAAa;IAC9C,IAAI,CAACnI,aAAa,GAAG,EAAE;IAEvB,IAAI,CAACoI,mBAAmB,CAACH,QAAQ,CAACE,aAAa,CAAC;IAEhD,IAAIF,QAAQ,CAACjE,UAAU,EAAE;MACvB,IAAI,CAACA,UAAU,GAAGiE,QAAQ,CAACjE,UAAU;IACvC;IAEA;IACA,IAAI,CAACE,qBAAqB,GAAG+D,QAAQ,CAACI,sBAAsB,IAAI,KAAK;IAErE,IAAI,CAACvE,GAAG,CAACkC,aAAa,EAAE;IAExB,IAAI,CAACsC,uBAAuB,CAACL,QAAQ,CAACM,KAAK,CAAC;IAC5C,IAAI,CAACzE,GAAG,CAACkC,aAAa,EAAE;IAExB,IAAIiC,QAAQ,CAACO,YAAY,IAAI,IAAI,CAACC,wBAAwB,EAAE,EAAE;MAC5D,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,MAAM;MACL;MACA,IAAI,CAAC1I,aAAa,GAAG,EAAE;MACvB,IAAI,CAAC8D,GAAG,CAACkC,aAAa,EAAE;MAExB;MACA,IAAI,CAACrC,OAAO,CAACgF,KAAK,CAACV,QAAQ,CAACE,aAAa,EAAE,MAAK;QAC9C;QACA3B,OAAO,CAACoC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,IAAI,CAACC,4BAA4B,EAAE;MACrC,CAAC,CAAC,CAAC9B,IAAI,CAAC,MAAK;QACX;QACA,IAAI,CAAC,IAAI,CAAC7C,qBAAqB,EAAE;UAC/B,IAAI,CAAC4E,iCAAiC,EAAE;QAC1C;MACF,CAAC,CAAC,CAACC,KAAK,CAACxC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACvG,aAAa,GAAG,IAAI,CAACmE,gBAAgB,CAAC,CAAC;QAC5C,IAAI,CAACL,GAAG,CAACkC,aAAa,EAAE;QACxB;QACA,IAAI,CAAC,IAAI,CAAC9B,qBAAqB,EAAE;UAC/B,IAAI,CAAC4E,iCAAiC,EAAE;QAC1C;MACF,CAAC,CAAC;IACJ;EACF;EAEQV,mBAAmBA,CAACY,QAAgB;IAC1C,IAAI,CAACtE,mBAAmB,CAACuE,IAAI,CAAC,oBAAoBD,QAAQ,EAAE,CAAC;IAC7D,IAAI,CAAClF,GAAG,CAACkC,aAAa,EAAE;EAC1B;EAEQsC,uBAAuBA,CAACY,UAAwC;IACtEC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;MACpC,IAAIJ,UAAU,CAACI,GAAyC,CAAC,IACvDJ,UAAU,CAACI,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACrE,IAAI,CAAC3E,cAAc,CAAC0E,GAAyC,CAAC,GAC5DJ,UAAU,CAACI,GAAyC,CAAC;MACzD;IACF,CAAC,CAAC;IACF,IAAI,CAACxF,GAAG,CAACkC,aAAa,EAAE;EAC1B;EAEQyC,wBAAwBA,CAAA;IAC9B,MAAMe,kBAAkB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EAAE,eAAe,CAAC;IAChI,OAAOA,kBAAkB,CAACC,KAAK,CAACC,KAAK,IACnC,IAAI,CAAC9E,cAAc,CAAC8E,KAA2C,CAAC,IAChE,IAAI,CAAC9E,cAAc,CAAC8E,KAA2C,CAAC,CAACH,IAAI,EAAE,KAAK,EAAE,CAC/E;EACH;EAEQT,iCAAiCA,CAAA;IACvC,IAAI,CAACrG,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACqB,GAAG,CAACkC,aAAa,EAAE;IAExB,IAAI,CAAC,IAAI,CAACnD,UAAU,EAClBmE,UAAU,CAAC,MAAK;MACd,IAAI,CAAC2C,uBAAuB,EAAE;IAChC,CAAC,EAAE,GAAG,CAAC;EACX;EAEcA,uBAAuBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnC,IAAI,CAACD,KAAI,CAAClG,aAAa,CAACoG,WAAW,EAAE,EAAE;QACrCtD,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;QACpDqD,KAAI,CAAC/F,QAAQ,CAAC6C,aAAa,CAAC,qDAAqD,CAAC;QAClF;MACF;MAEA;MACA,MAAMkD,KAAI,CAACG,kCAAkC,EAAE;MAC/CH,KAAI,CAACjD,yBAAyB,EAAE;MAChCiD,KAAI,CAACI,uBAAuB,EAAE;IAAC;EACjC;EAEQA,uBAAuBA,CAAA;IAC7B,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAAC,IAAI,CAACzH,oBAAoB,EAAE;QAC9B0H,aAAa,CAACF,UAAU,CAAC;QACzB;MACF;MAEA,IAAI,IAAI,CAACpH,UAAU,EAAE;QACnB;MACF;MAEA,MAAMuH,gBAAgB,GAAG,IAAI,CAAC1G,aAAa,CAACqE,oBAAoB,EAAE;MAClE,MAAMsC,kBAAkB,GAAG,IAAI,CAAC9H,WAAW;MAE3C,IAAI6H,gBAAgB,KAAKC,kBAAkB,EAAE;QAC3C,IAAI,CAAC9H,WAAW,GAAG6H,gBAAgB;QACnC,IAAI,CAACtG,GAAG,CAACkC,aAAa,EAAE;MAC1B;MAEA,IAAI,IAAI,CAACvD,oBAAoB,IAAI,CAAC2H,gBAAgB,IAAI,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAAC1G,OAAO,CAAC1B,UAAU,EAAE,EAAE;QACvG,IAAI,CAAC0E,yBAAyB,EAAE;MAClC;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ2D,mCAAmCA,CAAA;IACzC,IAAI,CAAC7H,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACqB,GAAG,CAACkC,aAAa,EAAE;IAExB,IAAI,IAAI,CAACzD,WAAW,EAAE;MACpB,IAAI,CAACmB,aAAa,CAAC2D,aAAa,EAAE;IACpC;EACF;EAEQV,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACjD,aAAa,CAACoG,WAAW,EAAE,EAAE;MACrCtD,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;MACpD,IAAI,CAAC1C,QAAQ,CAAC6C,aAAa,CAAC,qDAAqD,CAAC;MAClF;IACF;IAEA;IACA,IAAI,IAAI,CAAC/C,OAAO,CAAC1B,UAAU,EAAE,EAAE;MAC7BuE,OAAO,CAACoC,GAAG,CAAC,qDAAqD,CAAC;MAClE;IACF;IAEA,MAAMwB,gBAAgB,GAAG,IAAI,CAAC1G,aAAa,CAACqE,oBAAoB,EAAE;IAClE,MAAMsC,kBAAkB,GAAG,IAAI,CAAC9H,WAAW;IAE3C,IAAI6H,gBAAgB,IAAIC,kBAAkB,EAAE;MAC1C;IACF;IAEA,MAAMlE,OAAO,GAAG,IAAI,CAACzC,aAAa,CAAC6G,cAAc,EAAE;IAEnD,IAAI,CAACpE,OAAO,EACVK,OAAO,CAACD,KAAK,CAAC,wCAAwC,CAAC;IAEzD,IAAI,CAACzC,GAAG,CAACkC,aAAa,EAAE;EAC1B;EAEAwE,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC9G,aAAa,CAACoG,WAAW,EAAE,EAAE;MACrC,IAAI,CAACjG,QAAQ,CAAC6C,aAAa,CAAC,qDAAqD,CAAC;MAClF;IACF;IAEA;IACA,IAAI,IAAI,CAAC/C,OAAO,CAAC1B,UAAU,EAAE,EAAE;MAC7B,IAAI,CAAC4B,QAAQ,CAAC6C,aAAa,CAAC,uCAAuC,CAAC;MACpE;IACF;IAEA,MAAMP,OAAO,GAAG,IAAI,CAACzC,aAAa,CAAC6G,cAAc,EAAE;IACnD,IAAI,CAACpE,OAAO,EAAE;MACZ,IAAI,CAACtC,QAAQ,CAAC6C,aAAa,CAAC,0BAA0B,CAAC;IACzD;IACA,IAAI,CAAC5C,GAAG,CAACkC,aAAa,EAAE;EAC1B;EAEAyE,aAAaA,CAAA;IACX,IAAI,CAAC/G,aAAa,CAAC2D,aAAa,EAAE;IAClC,IAAI,CAACvD,GAAG,CAACkC,aAAa,EAAE;EAC1B;EAEA0E,eAAeA,CAAA;IACb,IAAI,IAAI,CAACnI,WAAW,EAAE;MACpB,IAAI,CAACkI,aAAa,EAAE;IACtB,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,EAAE;IACxB;EACF;EAEQ7E,aAAaA,CAAA;IACnB,MAAMgF,SAAS,GAAGC,MAAM,CAACC,eAAe;IACxC,MAAMC,MAAM,GAAGH,SAAS,CAACI,SAAS,EAAE;IAEpC,IAAID,MAAM,CAACrK,MAAM,KAAK,CAAC,EAAE;MACvBkK,SAAS,CAACK,eAAe,GAAG,MAAK;QAC/B,MAAMC,SAAS,GAAGN,SAAS,CAACI,SAAS,EAAE;QACvCE,SAAS;MACX,CAAC;MAED,MAAMC,eAAe,GAAG,IAAIC,wBAAwB,CAAC,EAAE,CAAC;MACxDD,eAAe,CAACE,MAAM,GAAG,CAAC;MAC1BT,SAAS,CAAChC,KAAK,CAACuC,eAAe,CAAC;MAChCP,SAAS,CAACvD,MAAM,EAAE;IACpB;EACF;EAEAiE,mBAAmBA,CAAA;IACjB,IAAI,CAAC1H,OAAO,CAACgF,KAAK,CAAC,kGAAkG,EAAE,MAAK;MAC1HnC,OAAO,CAACoC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC,CAAC7B,IAAI,CAAC,MAAK;MACX,IAAI,CAAC+B,iCAAiC,EAAE;IAC1C,CAAC,CAAC;EACJ;EAEA1H,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACuC,OAAO,CAAC1B,UAAU,EAAE,EAAE;MAC7B,IAAI,CAAC4B,QAAQ,CAAC6C,aAAa,CAAC,uCAAuC,CAAC;MACpE;IACF;IAEA,IAAI,IAAI,CAAC/E,SAAS,CAAC4H,IAAI,EAAE,EAAE;MACzB,IAAI,CAAClD,wBAAwB,CAAC,IAAI,CAAC1E,SAAS,CAAC;MAC7C,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACmC,GAAG,CAACkC,aAAa,EAAE;IAC1B;EACF;EAEQK,wBAAwBA,CAAC2C,QAAgB;IAC/C,IAAI,IAAI,CAACvG,oBAAoB,EAAE;MAC7B,IAAI,CAAC6H,mCAAmC,EAAE;IAC5C;IAEA,IAAI,CAAC5F,mBAAmB,CAACuE,IAAI,CAAC,yBAAyBD,QAAQ,EAAE,CAAC;IAClE,IAAI,CAAClF,GAAG,CAACkC,aAAa,EAAE;IACxB,IAAI,CAACyB,oBAAoB,EAAE;EAC7B;EAEcD,2BAA2BA,CAAA;IAAA,IAAA8D,MAAA;IAAA,OAAAzB,iBAAA;MACvC,IAAI;QACF,MAAM0B,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;QACzEJ,MAAM,CAACK,SAAS,EAAE,CAACvC,OAAO,CAACwC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MACnD,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE+E,MAAI,CAACzH,QAAQ,CAAC6C,aAAa,CAAC,oFAAoF,CAAC;MACnH;IAAC;EACH;EAEQa,aAAaA,CAAA;IACnB,MAAMwE,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAE;IAC/B,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,EAAE,GAAG,KAAK,CAAC;IAChD,OAAO,GAAGF,SAAS,IAAIE,MAAM,EAAE;EACjC;EAEA;EACQvG,oBAAoBA,CAAA;IAC1BsE,WAAW,CAAC,MAAK;MACf,MAAMoC,iBAAiB,GAAG,IAAI,CAAC3I,OAAO,CAAC1B,UAAU,EAAE;MACnD,IAAI,IAAI,CAACA,UAAU,KAAKqK,iBAAiB,EAAE;QACzC,IAAI,CAACrK,UAAU,GAAGqK,iBAAiB;QACnC,IAAI,CAACxI,GAAG,CAACkC,aAAa,EAAE;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEQ6C,4BAA4BA,CAAA;IAClC,IAAI,CAACvB,wBAAwB,EAAE;IAC/B,IAAI,CAACtH,aAAa,GAAG,EAAE;IAEvB,MAAMuM,KAAK,GAAG,IAAI,CAACpI,gBAAgB,CAACqI,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMC,aAAa,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACvI,gBAAgB,CAAC;IACxE,MAAMwI,YAAY,GAAGF,aAAa,GAAGF,KAAK,CAAC9L,MAAM;IAEjD,IAAImM,gBAAgB,GAAG,CAAC;IAExB,IAAI,CAACxI,mBAAmB,GAAG8F,WAAW,CAAC,MAAK;MAC1C,IAAI0C,gBAAgB,GAAGL,KAAK,CAAC9L,MAAM,EAAE;QACnC,IAAImM,gBAAgB,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAC5M,aAAa,GAAGuM,KAAK,CAACK,gBAAgB,CAAC;QAC9C,CAAC,MAAM;UACL,IAAI,CAAC5M,aAAa,IAAI,GAAG,GAAGuM,KAAK,CAACK,gBAAgB,CAAC;QACrD;QACAA,gBAAgB,EAAE;QAClB,IAAI,CAAC9I,GAAG,CAACkC,aAAa,EAAE;MAC1B,CAAC,MAAM;QACL,IAAI,CAACsB,wBAAwB,EAAE;MACjC;IACF,CAAC,EAAEqF,YAAY,CAAC;EAClB;EAEQD,sBAAsBA,CAACtG,IAAY;IACzC;IACA,MAAMmG,KAAK,GAAGnG,IAAI,CAACoG,KAAK,CAAC,GAAG,CAAC,CAAC/L,MAAM;IACpC,MAAMoM,cAAc,GAAG,GAAG;IAC1B,MAAMC,iBAAiB,GAAGP,KAAK,GAAGM,cAAc;IAChD,OAAQC,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAE,CAAC;EAC1C;EAEQxF,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAClD,mBAAmB,EAAE;MAC5B+F,aAAa,CAAC,IAAI,CAAC/F,mBAAmB,CAAC;MACvC,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACjC;EACF;EAEQsE,iBAAiBA,CAAA;IACvB,IAAI,CAAC5E,GAAG,CAACkC,aAAa,EAAE;IACxB,IAAI,CAAC+G,uBAAuB,EAAE;IAC9B,IAAI,CAACC,+BAA+B,EAAE;EACxC;EAEQD,uBAAuBA,CAAA;IAC7B,IAAI;MACF,MAAME,eAAe,GAAG;QACtBpI,IAAI,EAAE,IAAI,CAACD,cAAc,CAACC,IAAI;QAC9BqI,KAAK,EAAE,IAAI,CAACC,aAAa,CAAC,IAAI,CAACvI,cAAc,CAACK,cAAc,CAAC;QAC7DH,GAAG,EAAE,IAAI,CAACF,cAAc,CAACE,GAAG;QAC5BC,KAAK,EAAE,IAAI,CAACH,cAAc,CAACG,KAAK;QAChCC,QAAQ,EAAE,IAAI,CAACJ,cAAc,CAACI,QAAQ;QACtCC,cAAc,EAAE,IAAI,CAACL,cAAc,CAACK,cAAc;QAClDE,QAAQ,EAAE,IAAI,CAACP,cAAc,CAACO,QAAQ,GAAG,IAAI,CAACP,cAAc,CAACO,QAAQ,CAACqH,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC9D,IAAI,EAAE,CAAC,GAAG,EAAE;QACxG+D,cAAc,EAAE,IAAI,CAAC1I,cAAc,CAACW,WAAW,IAAI,EAAE;QACrDH,cAAc,EAAE,IAAI,CAACmI,wBAAwB,CAAC,IAAI,CAAC3I,cAAc,CAACQ,cAAc,CAAC;QACjFC,aAAa,EAAE,IAAI,CAACT,cAAc,CAACS,aAAa;QAChDH,QAAQ,EAAE,IAAI,CAACN,cAAc,CAACM,QAAQ,IAAI,EAAE;QAC5CI,cAAc,EAAE,IAAI,CAACV,cAAc,CAACU,cAAc,IAAI,EAAE;QACxDC,WAAW,EAAE,IAAI,CAACX,cAAc,CAACW,WAAW,IAAI,EAAE;QAClDiI,iBAAiB,EAAE,IAAIxB,IAAI,EAAE,CAACyB,WAAW;OAC1C;MAED3O,gBAAgB,CAAC4O,yBAAyB,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACX,eAAe,CAAC,CAAC;MACxGzG,OAAO,CAACoC,GAAG,CAAC,+CAA+C,EAAEqE,eAAe,CAAC;IAC/E,CAAC,CAAC,OAAO1G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAC1C,QAAQ,CAAC6C,aAAa,CAAC,sCAAsC,CAAC;IACrE;EACF;EAEQyG,aAAaA,CAAClI,cAAsB;IAC1C,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAE7B,IAAI;MACF,MAAM4I,UAAU,GAAG,IAAI7B,IAAI,CAAC/G,cAAc,CAAC;MAC3C,MAAM6I,IAAI,GAAG,IAAI9B,IAAI,EAAE;MACvB,IAAIkB,KAAK,GAAGY,IAAI,CAACC,WAAW,EAAE,GAAGF,UAAU,CAACE,WAAW,EAAE;MACzD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAQ,EAAE;MAChC,MAAMC,aAAa,GAAGL,UAAU,CAACI,QAAQ,EAAE;MAE3C,IAAID,QAAQ,GAAGE,aAAa,IAAKF,QAAQ,KAAKE,aAAa,IAAIJ,IAAI,CAACK,OAAO,EAAE,GAAGN,UAAU,CAACM,OAAO,EAAG,EAAE;QACrGjB,KAAK,EAAE;MACT;MAEA,OAAOA,KAAK;IACd,CAAC,CAAC,MAAM;MACN,OAAO,CAAC;IACV;EACF;EAEQK,wBAAwBA,CAACa,WAAmB;IAClD,IAAI,CAACA,WAAW,EAAE,OAAO,CAAC;IAE1B,MAAMC,KAAK,GAAGD,WAAW,CAACC,KAAK,CAAC,KAAK,CAAC;IACtC,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EAC3C;EAEQE,0BAA0BA,CAAA;IAChC,IAAI;MACF,IAAI,CAAC/K,MAAM,CAACgL,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC1C,QAAQ,CAAC6C,aAAa,CAAC,uCAAuC,CAAC;MACpE,IAAI,CAAClD,MAAM,CAACgL,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC;EACF;EAEQxB,+BAA+BA,CAAA;IACrC,MAAMyB,SAAS,GAAG,IAAI,CAAChL,MAAM,CAACiL,IAAI,CAACnQ,kCAAkC,EAAE;MACrEoQ,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE;KACb,CAAC;IAEFP,SAAS,CAACQ,WAAW,EAAE,CAAClJ,SAAS,CAACG,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEgJ,MAAM,KAAK,WAAW,EAAE;QAClC,IAAI,CAACrL,QAAQ,CAACsL,eAAe,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAACZ,0BAA0B,EAAE;MACnC,CAAC,MAAM,IAAIrI,MAAM,EAAEgJ,MAAM,KAAK,UAAU,EAAE;QACxC,IAAI,CAACrL,QAAQ,CAAC6C,aAAa,CAAC,wCAAwC,CAAC;QACrE5H,gBAAgB,CAACsQ,gCAAgC,CAAC,2BAA2B,CAAC;MAChF;MAEA,IAAI,CAACtL,GAAG,CAACkC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAxF,mBAAmBA,CAAA;IACjB,MAAM6O,MAAM,GAA8B;MACxCxK,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KACd;IAED,OAAO4D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxE,cAAc,CAAC,CACpC0K,MAAM,CAAChG,GAAG,IAAI,IAAI,CAAC1E,cAAc,CAAC0E,GAAyC,CAAC,IAC3E,IAAI,CAAC1E,cAAc,CAAC0E,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAC9E6D,GAAG,CAAC9D,GAAG,KAAK;MACXlJ,KAAK,EAAEiP,MAAM,CAAC/F,GAAG,CAAC;MAClBnJ,KAAK,EAAE,IAAI,CAACyE,cAAc,CAAC0E,GAAyC;KACrE,CAAC,CAAC;EACP;EAEArI,2BAA2BA,CAAA;IACzB,MAAMsH,KAAK,GAAG,IAAI,CAAC/H,mBAAmB,EAAE;IACxC,OAAO+H,KAAK,CAAC9H,MAAM,GAAG,CAAC,GAAG8H,KAAK,CAACA,KAAK,CAAC9H,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC1D;EAEAI,gBAAgBA,CAAA;IACd,MAAM,CAAC,6CAA6C,CAAC,CAACkG,IAAI,CAAC,CAAC;MAAEwI;IAA2B,CAAE,KAAI;MAC7F,MAAMC,UAAU,GAAG7B,IAAI,CAAC8B,KAAK,CAAC9B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChJ,cAAc,CAAC,CAAC;MAClE,IAAI,CAACnB,MAAM,CAACiL,IAAI,CAACa,2BAA2B,EAAE;QAC5CX,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,MAAM;QAChBa,IAAI,EAAE;UACJ9K,cAAc,EAAE4K,UAAU;UAC1B3L,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,GAAG,EAAE,IAAI,CAACA;SACX;QACDkL,UAAU,EAAE,sBAAsB;QAClCW,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAjP,cAAcA,CAAA;IACZ,OAAO,EAAE;EACX;EAEAJ,qBAAqBA,CAAA;IACnB,MAAMsP,KAAK,GAAG,IAAI,CAAClP,cAAc,EAAE;IACnC,MAAMmP,MAAM,GAAG,IAAI,CAACrP,mBAAmB,EAAE,CAACC,MAAM;IAChD,OAAO2L,IAAI,CAAC0D,KAAK,CAAED,MAAM,GAAGD,KAAK,GAAI,GAAG,CAAC;EAC3C;EAEAG,cAAcA,CAAClJ,KAAU;IACvB,IAAI,CAACxC,UAAU,GAAGwC,KAAK,CAACmJ,MAAM,CAAC7P,KAAK;EACtC;EAEA8P,WAAWA,CAAA;IACT,IAAI,CAAC5L,UAAU,GAAG,EAAE;EACtB;EAEA6L,YAAYA,CAACZ,MAAoB;IAC/BA,MAAM,CAAC7K,MAAM,GAAG,CAAC6K,MAAM,CAAC7K,MAAM;EAChC;EAEA0L,eAAeA,CAAA;IACb,IAAIT,IAAI,GAAG,IAAI,CAACU,2BAA2B,EAAE;IAE7C,IAAI,IAAI,CAAC/L,UAAU,EAAE;MACnB,MAAMgM,WAAW,GAAG,IAAI,CAAChM,UAAU,CAACiM,WAAW,EAAE;MACjDZ,IAAI,GAAGA,IAAI,CAACJ,MAAM,CAACiB,IAAI,IACrBA,IAAI,CAACnQ,KAAK,CAACkQ,WAAW,EAAE,CAAC7J,QAAQ,CAAC4J,WAAW,CAAC,IAC9CE,IAAI,CAACpQ,KAAK,CAACmQ,WAAW,EAAE,CAAC7J,QAAQ,CAAC4J,WAAW,CAAC,CAC/C;IACH;IAEA,MAAMG,gBAAgB,GAAG,IAAI,CAAClM,gBAAgB,CAC3CgL,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAAChM,MAAM,CAAC,CACrB2I,GAAG,CAACqD,CAAC,IAAIA,CAAC,CAAClM,IAAI,CAAC;IAEnBmL,IAAI,GAAGA,IAAI,CAACJ,MAAM,CAACiB,IAAI,IAAIC,gBAAgB,CAAC/J,QAAQ,CAAC8J,IAAI,CAACG,QAAQ,CAAC,CAAC;IAEpE,OAAOhB,IAAI;EACb;EAEAU,2BAA2BA,CAAA;IACzB,MAAMO,WAAW,GAAiF;MAChG9L,IAAI,EAAE;QAAE6L,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEpM,IAAI,EAAE;MAAQ,CAAE;MAC/EM,GAAG,EAAE;QAAE4L,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEpM,IAAI,EAAE;MAAO,CAAE;MAC7ES,cAAc,EAAE;QAAEyL,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,gBAAgB;QAAEpM,IAAI,EAAE;MAAM,CAAE;MACvFO,KAAK,EAAE;QAAE2L,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAEpM,IAAI,EAAE;MAAO,CAAE;MACvEQ,QAAQ,EAAE;QAAE0L,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,SAAS;QAAEpM,IAAI,EAAE;MAAO,CAAE;MAC1EW,QAAQ,EAAE;QAAEuL,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEpM,IAAI,EAAE;MAAkB,CAAE;MACjGY,cAAc,EAAE;QAAEsL,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEpM,IAAI,EAAE;MAAiB,CAAE;MACtGa,aAAa,EAAE;QAAEqL,QAAQ,EAAE,SAAS;QAAEE,aAAa,EAAE,qBAAqB;QAAEpM,IAAI,EAAE;MAAU,CAAE;MAC9FU,QAAQ,EAAE;QAAEwL,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAEpM,IAAI,EAAE;MAAqB,CAAE;MAC3Fe,WAAW,EAAE;QAAEmL,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,WAAW;QAAEpM,IAAI,EAAE;MAAa;KACrF;IAED,MAAM6K,MAAM,GAA8B;MACxCxK,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,oBAAoB;MACpCC,aAAa,EAAE,oBAAoB;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE;KACd;IAED,OAAO4D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxE,cAAc,CAAC,CACpC0K,MAAM,CAAChG,GAAG,IAAI,IAAI,CAAC1E,cAAc,CAAC0E,GAAyC,CAAC,IAC3E,IAAI,CAAC1E,cAAc,CAAC0E,GAAyC,CAAC,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAC9E6D,GAAG,CAAC9D,GAAG,IAAG;MACT,MAAMuH,YAAY,GAAGF,WAAW,CAACrH,GAAG,CAAC,IAAI;QAAEoH,QAAQ,EAAE,UAAU;QAAEE,aAAa,EAAE,QAAQ;QAAEpM,IAAI,EAAE;MAAM,CAAE;MACxG,OAAO;QACLpE,KAAK,EAAEiP,MAAM,CAAC/F,GAAG,CAAC,IAAIA,GAAG;QACzBnJ,KAAK,EAAE,IAAI,CAACyE,cAAc,CAAC0E,GAAyC,CAAC;QACrEoH,QAAQ,EAAEG,YAAY,CAACH,QAAQ;QAC/BE,aAAa,EAAEC,YAAY,CAACD,aAAa;QACzCpM,IAAI,EAAEqM,YAAY,CAACrM,IAAI;QACvByH,SAAS,EAAE,IAAID,IAAI,EAAE;QACrB8E,gBAAgB,EAAE,IAAI,CAACC,2BAA2B,CAACzH,GAAG;OACvD;IACH,CAAC,CAAC;EACN;EAEAyH,2BAA2BA,CAACC,KAAa;IACvC,MAAM7Q,KAAK,GAAG,IAAI,CAACyE,cAAc,CAACoM,KAA2C,CAAC;IAC9E,IAAI,CAAC7Q,KAAK,IAAIA,KAAK,CAACoJ,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,OAAO;IAEjD,IAAIyH,KAAK,KAAK,OAAO,IAAI,CAAC7Q,KAAK,CAACsG,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,SAAS;IAC/D,IAAIuK,KAAK,KAAK,KAAK,IAAI7Q,KAAK,CAACM,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS;IAE1D,OAAO,OAAO;EAChB;EAEAwQ,SAASA,CAACC,KAAa,EAAEX,IAAsB;IAC7CW,KAAK;IACL,OAAOX,IAAI,CAACnQ,KAAK,GAAGmQ,IAAI,CAACpQ,KAAK;EAChC;EAEAgR,mBAAmBA,CAAC/K,IAAY;IAC9B,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAAE,OAAO+B,IAAI;IAEjC,MAAMgL,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAI,IAAI,CAAChN,UAAU,GAAG,EAAE,IAAI,CAAC;IACtD,OAAO+B,IAAI,CAACkL,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EAC/C;EAEAG,eAAeA,CAACtF,SAAe;IAC7B,OAAOA,SAAS,CAACuF,kBAAkB,CAAC,OAAO,EAAE;MAC3CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,iBAAiBA,CAACC,MAAc;IAC9B,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,MAAM;IACxB;EACF;EAEAC,kBAAkBA,CAACD,MAAc;IAC/B,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B;QAAS,OAAO,MAAM;IACxB;EACF;EAEAE,eAAeA,CAAC1L,IAAY;IAC1BoF,SAAS,CAACuG,SAAS,CAACC,SAAS,CAAC5L,IAAI,CAAC,CAACW,IAAI,CAAC,MAAK;MAC5C,IAAI,CAAClD,QAAQ,CAACsL,eAAe,CAAC,4CAA4C,CAAC;IAC7E,CAAC,CAAC,CAACpG,KAAK,CAAC,MAAK;MACZ,IAAI,CAAClF,QAAQ,CAAC6C,aAAa,CAAC,sBAAsB,CAAC;IACrD,CAAC,CAAC;EACJ;EAEAuL,SAASA,CAAC1B,IAAsB;IAC9B,MAAM2B,QAAQ,GAAGC,MAAM,CAAC,UAAU5B,IAAI,CAACnQ,KAAK,GAAG,EAAEmQ,IAAI,CAACpQ,KAAK,CAAC;IAC5D,IAAI+R,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK3B,IAAI,CAACpQ,KAAK,EAAE;MAChD,MAAM6Q,KAAK,GAAG7H,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxE,cAAc,CAAC,CAACwN,IAAI,CAAC9I,GAAG,IAAG;QACxD,MAAM+F,MAAM,GAA8B;UACxCxK,IAAI,EAAE,MAAM;UACZC,GAAG,EAAE,KAAK;UACVC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,UAAU;UACpBC,cAAc,EAAE,oBAAoB;UACpCC,aAAa,EAAE,oBAAoB;UACnCC,cAAc,EAAE,iBAAiB;UACjCC,WAAW,EAAE;SACd;QACD,OAAO8J,MAAM,CAAC/F,GAAG,CAAC,KAAKiH,IAAI,CAACnQ,KAAK;MACnC,CAAC,CAAC;MAEF,IAAI4Q,KAAK,EAAE;QACT,IAAI,CAACpM,cAAc,CAACoM,KAA2C,CAAC,GAAGkB,QAAQ;QAC3E,IAAI,CAACrO,QAAQ,CAACsL,eAAe,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAACrL,GAAG,CAACkC,aAAa,EAAE;MAC1B;IACF;EACF;EAEAqM,YAAYA,CAAA;IACV,IAAIC,OAAO,CAAC,yDAAyD,CAAC,EAAE;MACtE,IAAI,CAAC1N,cAAc,GAAG;QACpBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,EAAE;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;OACd;MACD,IAAI,CAAC1B,QAAQ,CAACsL,eAAe,CAAC,6BAA6B,CAAC;MAC5D,IAAI,CAACrL,GAAG,CAACkC,aAAa,EAAE;IAC1B;EACF;EAEA;EACAhE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC2B,OAAO,CAAC1B,UAAU,EAAE,IAAI,IAAI,CAACO,YAAY;EACvD;EAEA+P,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC5O,OAAO,CAAC1B,UAAU,EAAE,IAAI,IAAI,CAACO,YAAY;EACvD;EAEAnB,WAAWA,CAAA;IACT,OAAO,CAAC,IAAI,CAACsC,OAAO,CAAC1B,UAAU,EAAE,IAAI,CAAC,IAAI,CAACO,YAAY,IAAI,CAAC,CAAC,IAAI,CAACb,SAAS,EAAE4H,IAAI,EAAE;EACrF;EAEA;EACcQ,kCAAkCA,CAAA;IAAA,IAAAyI,MAAA;IAAA,OAAA3I,iBAAA;MAC9C,OAAO,IAAI4I,OAAO,CAAQC,OAAO,IAAI;QACnC,MAAMC,aAAa,GAAG,EAAE,CAAC,CAAC;QAC1B,MAAMC,WAAW,GAAG,KAAK,CAAC,CAAC;QAC3B,IAAIC,WAAW,GAAG,CAAC;QAEnB,MAAM5I,UAAU,GAAGC,WAAW,CAAC,MAAK;UAClC2I,WAAW,IAAIF,aAAa;UAE5B,IAAI,CAACH,MAAI,CAAC7O,OAAO,CAAC1B,UAAU,EAAE,EAAE;YAC9BkI,aAAa,CAACF,UAAU,CAAC;YACzB;YACAjD,UAAU,CAAC,MAAK;cACd0L,OAAO,EAAE;YACX,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM,IAAIG,WAAW,IAAID,WAAW,EAAE;YACrCzI,aAAa,CAACF,UAAU,CAAC;YACzBzD,OAAO,CAACsM,IAAI,CAAC,gCAAgC,CAAC;YAC9CJ,OAAO,EAAE;UACX;QACF,CAAC,EAAEC,aAAa,CAAC;MACnB,CAAC,CAAC;IAAC;EACL;;qBAn1BWpP,gCAAgC,EAAAxE,EAAA,CAAAgU,iBAAA,CAAAC,EAAA,CAAA7V,MAAA,GAAA4B,EAAA,CAAAgU,iBAAA,CAAAE,EAAA,CAAArV,SAAA,GAAAmB,EAAA,CAAAgU,iBAAA,CAAAG,EAAA,CAAAxU,oBAAA,GAAAK,EAAA,CAAAgU,iBAAA,CAAAI,EAAA,CAAAxU,cAAA,GAAAI,EAAA,CAAAgU,iBAAA,CAAAK,EAAA,CAAAxU,wBAAA,GAAAG,EAAA,CAAAgU,iBAAA,CAAAM,EAAA,CAAAxU,cAAA,GAAAE,EAAA,CAAAgU,iBAAA,CAAAhU,EAAA,CAAA7B,iBAAA;EAAA;;UAAhCqG,gCAAgC;IAAA+P,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAzU,EAAA,CAAA0U,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC7F7ChV,EAAA,CAAAC,cAAA,aAAwC;QAgCtCD,EA9BA,CAAA+B,UAAA,IAAAmT,+CAAA,kBAAwC,IAAAC,+CAAA,mBA8BI;QAoI9CnV,EAAA,CAAAG,YAAA,EAAM;;;QAlKsBH,EAAA,CAAAe,SAAA,EAAY;QAAZf,EAAA,CAAAa,UAAA,SAAAoU,GAAA,CAAAjQ,MAAA,CAAY;QA8BThF,EAAA,CAAAe,SAAA,EAAa;QAAbf,EAAA,CAAAa,UAAA,UAAAoU,GAAA,CAAAjQ,MAAA,CAAa;;;mBDgBxC3G,YAAY,EAAA+W,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpW,mBAAmB,EAAAqW,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EACnBtW,WAAW,EAAAoW,EAAA,CAAAG,OAAA,EACX9W,kBAAkB,EAAA+W,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBvX,cAAc,EAAAwX,GAAA,CAAAC,QAAA,EACd3X,eAAe,EAAA4X,GAAA,CAAAC,aAAA,EACfxX,aAAa,EACbF,iBAAiB,EACjBC,oBAAoB,EAAA0X,GAAA,CAAAC,cAAA,EACpB9X,aAAa,EAAA+X,GAAA,CAAAC,OAAA,EACbzX,eAAe,EACfC,gBAAgB,EAAAyX,GAAA,CAAAC,UAAA,EAChBzX,cAAc,EACdC,iBAAiB;IAAAyX,MAAA;IAAA/F,IAAA;MAAAgG,SAAA,EAEP,CACVvX,OAAO,CAAC,WAAW,EAAE,CACnBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEuX,OAAO,EAAE;MAAC,CAAE,CAAC,EACrBrX,OAAO,CAAC,eAAe,EAAEF,KAAK,CAAC;QAAEuX,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAChD,CAAC,EACFtX,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;QAAEuX,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACjD,CAAC,CACH,CAAC,EACFxX,OAAO,CAAC,YAAY,EAAE,CACpBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEwX,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EACrDrX,OAAO,CAAC,wCAAwC,EAC9CF,KAAK,CAAC;QAAEwX,SAAS,EAAE,eAAe;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACrD,CAAC,EACFtX,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,eAAe,EACrBF,KAAK,CAAC;QAAEwX,SAAS,EAAE,mBAAmB;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,EACFxX,OAAO,CAAC,eAAe,EAAE,CACvBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;QAAEwX,SAAS,EAAE,YAAY;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,EAC9CrX,OAAO,CAAC,wCAAwC,EAC9CF,KAAK,CAAC;QAAEwX,SAAS,EAAE,UAAU;QAAED,OAAO,EAAE;MAAC,CAAE,CAAC,CAAC,CAChD,CAAC,CACH,CAAC;IACH;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
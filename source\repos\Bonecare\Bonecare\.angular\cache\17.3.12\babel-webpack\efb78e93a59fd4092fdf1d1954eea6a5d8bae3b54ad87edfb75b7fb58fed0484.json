{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/repos/Bonecare/Bonecare/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Router, RouterModule } from '@angular/router';\nimport { ConsultaService } from 'src/app/service/consulta.service';\nimport { SpinnerService } from 'src/app/service/spinner.service';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { ConsultaAgoraDialogComponent } from './consulta-agora-dialog/consulta-agora-dialog.component';\nimport { MedicosZeroDialogComponent } from './medicos-zero-dialog/medicos-zero-dialog.component';\nimport { SairConsultaDialogComponent } from './sair-consulta-dialog/sair-consulta-dialog.component';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { ChangeDetectorRef } from '@angular/core';\nimport { FilaEsperaPacienteService } from 'src/app/service/fila-espera-paciente.service';\n// import { SignalHubService } from 'src/app/service/signalHub.service';\nimport { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';\nimport { PatientQueueIntegrationService } from 'src/app/service/patient-queue-integration.service';\nimport { ModalColetaDadosVittaltecComponent } from './modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\nimport { firstValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/service/spinner.service\";\nimport * as i3 from \"src/app/service/consulta.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/service/fila-espera-paciente.service\";\nimport * as i6 from \"src/app/service/signalHub-guest.service\";\nimport * as i7 from \"src/app/service/patient-queue-integration.service\";\nexport class FilaEsperaComponent {\n  router;\n  spinner;\n  consultaService;\n  dialog;\n  cdr;\n  filaEsperaPacienteService;\n  signalHubGuestService;\n  patientQueueIntegrationService;\n  constructor(router, spinner, consultaService, dialog, cdr, filaEsperaPacienteService,\n  // private signalHubService: SignalHubService,\n  signalHubGuestService, patientQueueIntegrationService) {\n    this.router = router;\n    this.spinner = spinner;\n    this.consultaService = consultaService;\n    this.dialog = dialog;\n    this.cdr = cdr;\n    this.filaEsperaPacienteService = filaEsperaPacienteService;\n    this.signalHubGuestService = signalHubGuestService;\n    this.patientQueueIntegrationService = patientQueueIntegrationService;\n  }\n  PesicaoFila = 0;\n  timeInterval;\n  ConsultaAgoraTeste = false;\n  dadosQuestionario = null;\n  dadosVittalTec = null; // Data collected from VittalTec modal\n  tokenConexao = null;\n  subscriptions = [];\n  // Indicadores de status\n  signalRConectado = false;\n  cadastrandoNaFila = false;\n  pacienteRegistrado = false;\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.carregarDadosQuestionario();\n      _this.carregarDadosVittalTec();\n      if (!_this.dadosQuestionario) {\n        _this.router.navigate(['/pre-consulta-questionario']);\n        return;\n      }\n      const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\n      if (!tokenSalvo) {\n        _this.tokenConexao = CriptografarUtil.gerarHashToken(_this.dadosQuestionario.cpf ?? \"naotemcpf\", new Date().toISOString());\n        yield _this.cadastrarNaFilaDeslogado();\n      } else {\n        _this.tokenConexao = tokenSalvo;\n        yield _this.recuperarPosicaoFila();\n      }\n      _this.configurarSignalR();\n      yield _this.inicializarTokenConexao();\n      yield _this.conectarSignalRGuest();\n    })();\n  }\n  quitQueue() {\n    this.CancelarConsulta();\n  }\n  inicializarTokenConexao() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\n      if (tokenSalvo) _this2.tokenConexao = tokenSalvo;else CriptografarUtil.localStorageCriptografado('tokenFilaEspera', _this2.tokenConexao);\n    })();\n  }\n  conectarSignalRGuest() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.tokenConexao) {\n        console.error('Token de conexão não disponível');\n        return;\n      }\n      try {\n        yield _this3.aguardarSignalRPronto();\n        _this3.signalHubGuestService.connectGuestUser(_this3.tokenConexao);\n        _this3.signalRConectado = true;\n        _this3.verificarEstadoConexao();\n        // Register patient data after SignalR connection is established\n        yield _this3.garantirColetaDadosCompletos();\n        yield _this3.registrarDadosPaciente();\n      } catch (error) {\n        console.error('❌ Erro ao conectar guest ao SignalR:', error);\n        _this3.signalRConectado = false;\n      }\n    })();\n  }\n  aguardarSignalRPronto() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        if (_this4.signalHubGuestService.isHubConnected()) {\n          resolve();\n          return;\n        }\n        const subscription = _this4.signalHubGuestService.changeConsulta$.subscribe(connected => {\n          if (connected) {\n            subscription.unsubscribe();\n            resolve();\n          }\n        });\n        setTimeout(() => {\n          subscription.unsubscribe();\n          resolve();\n        }, 5000);\n      });\n    })();\n  }\n  configurarSignalR() {\n    const subAtualizaFila = this.signalHubGuestService.OnAtualizaChamaPacienteFila.subscribe(() => {\n      this.buscarPosicaoAtual();\n    });\n    const subConviteReuniao = this.signalHubGuestService.OnConviteReuniao.subscribe(convite => {\n      if (convite.token === this.tokenConexao) {\n        this.IniciarConsulta();\n      }\n    });\n    const subConvidarPacienteComToken = this.signalHubGuestService.OnConvidarPacienteComToken.subscribe(dados => {\n      if (dados.token === this.tokenConexao) {\n        this.IniciarConsulta();\n      }\n    });\n    const subChamaPaciente = this.signalHubGuestService.OnChamaPacienteFila.subscribe(() => {\n      this.buscarPosicaoAtual();\n    });\n    this.subscriptions.push(subAtualizaFila, subConviteReuniao, subConvidarPacienteComToken, subChamaPaciente);\n  }\n  carregarDadosQuestionario() {\n    const dadosStorage = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');\n    if (dadosStorage) {\n      try {\n        this.dadosQuestionario = JSON.parse(dadosStorage);\n      } catch (error) {\n        console.error('Erro ao parsing dos dados do questionário:', error);\n        this.dadosQuestionario = null;\n      }\n    }\n  }\n  getDadosQuestionario() {\n    return this.dadosQuestionario;\n  }\n  cadastrarNaFilaDeslogado() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this5.cadastrandoNaFila = true;\n        _this5.spinner.show();\n        if (_this5.tokenConexao) _this5.filaEsperaPacienteService.salvarTokenFila(_this5.tokenConexao);\n        const dadosComToken = {\n          ..._this5.dadosQuestionario,\n          tokenGerado: _this5.tokenConexao\n        };\n        const response = yield firstValueFrom(_this5.consultaService.cadastrarFilaEsperaDeslogado(dadosComToken));\n        if (response.sucesso) {\n          yield _this5.buscarPosicaoAtual();\n          _this5.iniciarMonitoramentoPosicao();\n          _this5.cadastrandoNaFila = false;\n          _this5.cdr.detectChanges();\n          _this5.spinner.hide();\n        } else {\n          console.error('❌ Erro ao cadastrar na fila:', response.Mensagem);\n          _this5.cadastrandoNaFila = false;\n          _this5.spinner.hide();\n        }\n      } catch (error) {\n        console.error('❌ Erro ao cadastrar na fila de espera:', error);\n        _this5.cadastrandoNaFila = false;\n        _this5.spinner.hide();\n      }\n    })();\n  }\n  CancelarConsulta() {\n    this.removerDaFilaEspera().then(() => {\n      this.Logoff();\n      this.spinner.hide();\n    }).catch(err => {\n      console.error('Erro ao cancelar consulta:', err);\n      this.Logoff();\n      this.spinner.hide();\n    });\n  }\n  Logoff() {\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval);\n    }\n    if (this.tokenConexao) {\n      this.removerDaFilaEspera();\n      this.filaEsperaPacienteService.limparDadosFilaEspera();\n    }\n    this.router.navigate(['pre-consulta-questionario']);\n  }\n  IniciarConsulta() {\n    if (!this.tokenConexao) {\n      console.error('❌ Token não encontrado! Não é possível iniciar a consulta.');\n      return;\n    }\n    const idConsultaTemporario = this.gerarIdConsultaTemporario();\n    this.router.navigate(['/streaming-paciente'], {\n      queryParams: {\n        idConsulta: idConsultaTemporario,\n        token: this.tokenConexao\n      }\n    });\n  }\n  gerarIdConsultaTemporario() {\n    return Date.now();\n  }\n  // Método de debug para verificar estado da conexão\n  verificarEstadoConexao() {\n    const estadoConexao = this.signalHubGuestService.getEstadoConexao();\n    // Verificar se tokens correspondem\n    if (this.tokenConexao !== estadoConexao.tokenConexao) {\n      console.warn('⚠️ PROBLEMA: Tokens não correspondem!');\n      console.warn('  - Token Componente:', this.tokenConexao);\n      console.warn('  - Token Service:', estadoConexao.tokenConexao);\n    }\n    // Testar se consegue enviar dados para o servidor\n    setTimeout(() => {\n      try {\n        this.signalHubGuestService.enviaServer('TestConnection', this.tokenConexao);\n      } catch (error) {\n        console.error('❌ Erro no teste de envio:', error);\n      }\n    }, 2000);\n  }\n  abrirDialogSairConsulta() {\n    const dialogRef = this.dialog.open(SairConsultaDialogComponent, {\n      width: '400px',\n      disableClose: true\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result === 'confirmar') {\n        this.CancelarConsulta();\n      }\n    });\n  }\n  abrirDialogMedicosZero() {\n    const dialogRef = this.dialog.open(MedicosZeroDialogComponent, {\n      width: '400px',\n      disableClose: true\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result === 'sair') {\n        this.CancelarConsulta();\n      }\n    });\n  }\n  abrirDialogConsultaAgora() {\n    const dialogRef = this.dialog.open(ConsultaAgoraDialogComponent, {\n      width: '500px',\n      disableClose: true\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result === 'iniciar') this.IniciarConsulta();else if (result === 'cancelar') {\n        this.CancelarConsulta();\n      }\n    });\n  }\n  beforeUnloadHandler() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.tokenConexao) {\n        yield _this6.removerDaFilaEspera();\n      }\n    })();\n  }\n  removerDaFilaEspera() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.tokenConexao) {\n        yield firstValueFrom(_this7.filaEsperaPacienteService.removerDaFilaEspera(_this7.tokenConexao));\n      }\n    })();\n  }\n  iniciarMonitoramentoPosicao() {\n    var _this8 = this;\n    if (this.tokenConexao) {\n      this.timeInterval = setInterval( /*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          const response = yield firstValueFrom(_this8.consultaService.consultarPosicaoFila(_this8.tokenConexao));\n          if (response.sucesso) {\n            _this8.PesicaoFila = response.posicaoFila;\n            _this8.cdr.detectChanges();\n            if (_this8.PesicaoFila === 0) {\n              _this8.ConsultaAgoraTeste = true;\n              _this8.abrirDialogConsultaAgora();\n              clearInterval(_this8.timeInterval);\n            }\n          }\n        } catch (error) {\n          console.error('Erro ao consultar posição:', error);\n        }\n      }), 30000);\n    }\n  }\n  buscarPosicaoAtual() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this9.tokenConexao) {\n        console.warn('⚠️ Token não encontrado para buscar posição');\n        return;\n      }\n      try {\n        const response = yield firstValueFrom(_this9.consultaService.consultarPosicaoFila(_this9.tokenConexao));\n        if (response.sucesso) {\n          _this9.PesicaoFila = response.posicaoFila;\n          _this9.cdr.detectChanges();\n        } else console.error('❌ Erro ao consultar posição:', response.mensagem);\n      } catch (error) {\n        console.error('❌ Erro ao buscar posição atual:', error);\n        if (error.status === 404) console.error('🚨 ERRO 404: Endpoint não encontrado!');\n      }\n    })();\n  }\n  /**\n   * Ensures all necessary data is collected before registration\n   */\n  garantirColetaDadosCompletos() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🔍 Verificando completude dos dados...');\n      // Check if questionnaire data is available\n      if (!_this10.dadosQuestionario) {\n        console.warn('⚠️ Dados do questionário não encontrados, tentando carregar...');\n        _this10.carregarDadosQuestionario();\n      }\n      // Check if VittalTec data is available\n      if (!_this10.dadosVittalTec) {\n        console.log('📊 Dados VittalTec não encontrados, tentando carregar do localStorage...');\n        _this10.carregarDadosVittalTec();\n        // If still no VittalTec data, that's okay - registration can proceed without it\n        if (!_this10.dadosVittalTec) {\n          console.log('ℹ️ Nenhum dado VittalTec disponível - registro continuará apenas com dados do questionário');\n        }\n      }\n      console.log('✅ Verificação de dados concluída:', {\n        questionario: !!_this10.dadosQuestionario,\n        vittalTec: !!_this10.dadosVittalTec,\n        token: !!_this10.tokenConexao\n      });\n    })();\n  }\n  /**\n   * Registers patient data in the database using the same token as SignalR\n   * Collects data from both questionnaire and VittalTec modal\n   */\n  registrarDadosPaciente() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this11.tokenConexao || !_this11.dadosQuestionario) {\n        console.warn('⚠️ Token ou dados do questionário não disponíveis para registro');\n        return;\n      }\n      if (_this11.pacienteRegistrado) {\n        console.log('✅ Paciente já registrado, pulando registro');\n        return;\n      }\n      try {\n        if (!_this11.dadosVittalTec) {\n          _this11.carregarDadosVittalTec();\n        }\n        // Convert questionnaire data to patient registration format (includes VittalTec if available)\n        const patientData = _this11.converterQuestionarioParaRegistro();\n        // Register patient using the integration service\n        const response = yield firstValueFrom(_this11.patientQueueIntegrationService.registerPatientInQueue(patientData));\n        if (response.success) {\n          _this11.pacienteRegistrado = true;\n          console.log('✅ Paciente registrado com sucesso:', {\n            token: response.patientToken,\n            nome: response.patientName,\n            tipoFila: response.queueType,\n            comDadosVittalTec: !!_this11.dadosVittalTec,\n            dadosEnviados: Object.keys(patientData).length\n          });\n          // Save registration status to localStorage\n          CriptografarUtil.localStorageCriptografado('paciente-registrado', JSON.stringify({\n            token: response.patientToken,\n            timestamp: new Date().toISOString(),\n            dadosCompletos: !!_this11.dadosVittalTec\n          }));\n        } else {\n          console.warn('⚠️ Falha ao registrar paciente:', response.message);\n          console.warn('Erros:', response.errors);\n          // Don't prevent queue entry if patient registration fails\n        }\n      } catch (error) {\n        console.error('❌ Erro ao registrar dados do paciente:', error);\n        // Don't prevent queue entry if patient registration fails\n      }\n    })();\n  }\n  /**\n   * Converts questionnaire data to patient registration format including VittalTec data\n   */\n  converterQuestionarioParaRegistro() {\n    if (!this.dadosQuestionario || !this.tokenConexao) {\n      throw new Error('Dados do questionário ou token não disponíveis');\n    }\n    // Format date of birth as string for API\n    let dataNascimento;\n    if (this.dadosQuestionario.dataNascimento) {\n      try {\n        const date = new Date(this.dadosQuestionario.dataNascimento);\n        if (!isNaN(date.getTime())) {\n          dataNascimento = date.toISOString().split('T')[0]; // YYYY-MM-DD format\n        }\n      } catch (error) {\n        console.warn('⚠️ Erro ao converter data de nascimento:', error);\n      }\n    }\n    let sintomas = this.dadosQuestionario.sintomasOutros;\n    let vittalTecData = {};\n    if (this.dadosVittalTec) {\n      try {\n        const data = this.dadosVittalTec.data || this.dadosVittalTec;\n        if (data) {\n          vittalTecData = {\n            pressaoSistolica: this.convertVitalSignToString(data.pressaoSistolica || data.systolic),\n            pressaoDiastolica: this.convertVitalSignToString(data.pressaoDiastolica || data.diastolic),\n            temperatura: this.convertTemperatureToString(data.temperatura),\n            saturacaoOxigenio: this.convertVitalSignToString(data.oxigenacao || data.oxygenSaturation),\n            frequenciaCardiaca: this.convertVitalSignToString(data.batimento || data.heartRate)\n          };\n          console.log('📊 Dados vitais VittalTec incluídos:', vittalTecData);\n        }\n      } catch (error) {\n        console.warn('⚠️ Erro ao processar dados VittalTec:', error);\n      }\n    }\n    // Convert questionnaire intensity and duration to strings\n    const intensidadeDorString = this.convertToStringValue(this.dadosQuestionario.intensidadeDor);\n    const duracaoSintomasString = this.convertToStringValue(this.converterTempoSintomas(this.dadosQuestionario.tempoSintomas));\n    const patientData = {\n      token: this.tokenConexao,\n      // Use the same token as SignalR\n      nome: this.dadosQuestionario.nome,\n      cpf: this.dadosQuestionario.cpf || '',\n      email: this.dadosQuestionario.email,\n      telefone: this.dadosQuestionario.telefone,\n      dataNascimento: dataNascimento,\n      sintomas: sintomas,\n      intensidadeDor: intensidadeDorString,\n      duracaoSintomas: duracaoSintomasString,\n      alergias: this.dadosQuestionario.alergias,\n      doencasPrevias: this.dadosQuestionario.doencasPrevias,\n      observacoesAdicionais: this.dadosQuestionario.observacoes,\n      queueType: 'consulta',\n      // Default queue type\n      // Include VittalTec vital signs data\n      ...vittalTecData\n    };\n    console.log('✅ Dados finais para registro:', patientData);\n    return patientData;\n  }\n  /**\n   * Converts symptom duration text to numeric value (in days)\n   */\n  converterTempoSintomas(tempoTexto) {\n    if (!tempoTexto) return undefined;\n    const texto = tempoTexto.toLowerCase();\n    if (texto.includes('hoje') || texto.includes('horas')) {\n      return 0; // Same day\n    } else if (texto.includes('ontem') || texto.includes('1 dia')) {\n      return 1;\n    } else if (texto.includes('dias')) {\n      const match = texto.match(/(\\d+)\\s*dias?/);\n      return match ? parseInt(match[1]) : undefined;\n    } else if (texto.includes('semana')) {\n      const match = texto.match(/(\\d+)\\s*semanas?/);\n      return match ? parseInt(match[1]) * 7 : 7;\n    } else if (texto.includes('mês') || texto.includes('mes')) {\n      const match = texto.match(/(\\d+)\\s*m[eê]s/);\n      return match ? parseInt(match[1]) * 30 : 30;\n    }\n    return undefined;\n  }\n  /**\n   * Helper methods for data conversion\n   */\n  convertToStringValue(value) {\n    if (value === null || value === undefined) {\n      return undefined;\n    }\n    return String(value);\n  }\n  convertVitalSignToString(value) {\n    if (value === null || value === undefined) {\n      return undefined;\n    }\n    // Handle numeric values\n    if (typeof value === 'number') {\n      return value.toString();\n    }\n    // Handle string values\n    if (typeof value === 'string') {\n      return value.trim() || undefined;\n    }\n    return String(value);\n  }\n  convertTemperatureToString(temperature) {\n    if (temperature === null || temperature === undefined) {\n      return undefined;\n    }\n    try {\n      let tempValue;\n      if (typeof temperature === 'string') {\n        tempValue = parseFloat(temperature);\n      } else if (typeof temperature === 'number') {\n        tempValue = temperature;\n      } else {\n        return undefined;\n      }\n      if (isNaN(tempValue)) {\n        return undefined;\n      }\n      // Convert to tenths (e.g., 36.5°C becomes \"365\")\n      return Math.round(tempValue * 10).toString();\n    } catch (error) {\n      console.warn('⚠️ Erro ao converter temperatura:', error);\n      return undefined;\n    }\n  }\n  /**\n   * Loads VittalTec data from encrypted localStorage if available\n   */\n  carregarDadosVittalTec() {\n    try {\n      let dadosVittalTec = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');\n      console.log(\"this.dadosVittalTec\", dadosVittalTec);\n      this.dadosVittalTec = dadosVittalTec;\n    } catch (error) {\n      console.error('❌ Erro geral ao carregar dados VittalTec:', error);\n      this.dadosVittalTec = null;\n    }\n  }\n  /**\n   * Opens VittalTec modal for data collection (public method for template access)\n   */\n  abrirModalVittalTec() {\n    return new Promise(resolve => {\n      const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\n        width: '600px',\n        height: '500px',\n        disableClose: true,\n        data: {}\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.action === 'continuar' && result.data) {\n          this.dadosVittalTec = result.data;\n          console.log('📊 Dados VittalTec coletados via modal:', this.dadosVittalTec);\n        }\n        resolve(result);\n      });\n    });\n  }\n  recuperarPosicaoFila() {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this12.tokenConexao) return;\n      try {\n        const response = yield firstValueFrom(_this12.consultaService.consultarPosicaoFila(_this12.tokenConexao));\n        if (response.sucesso) {\n          _this12.PesicaoFila = response.posicaoFila;\n          _this12.iniciarMonitoramentoPosicao();\n          _this12.cdr.detectChanges();\n        } else {\n          yield _this12.cadastrarNaFilaDeslogado();\n        }\n      } catch (error) {\n        console.error('Erro ao recuperar posição:', error);\n        yield _this12.cadastrarNaFilaDeslogado();\n      }\n    })();\n  }\n  static ɵfac = function FilaEsperaComponent_Factory(t) {\n    return new (t || FilaEsperaComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SpinnerService), i0.ɵɵdirectiveInject(i3.ConsultaService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.FilaEsperaPacienteService), i0.ɵɵdirectiveInject(i6.SignalHubGuestService), i0.ɵɵdirectiveInject(i7.PatientQueueIntegrationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FilaEsperaComponent,\n    selectors: [[\"app-fila-espera\"]],\n    hostBindings: function FilaEsperaComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"beforeunload\", function FilaEsperaComponent_beforeunload_HostBindingHandler($event) {\n          return ctx.beforeUnloadHandler($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 59,\n    vars: 2,\n    consts: [[1, \"main-container\"], [1, \"queue-card\", \"fade-in\"], [1, \"status-indicator\"], [1, \"status-dot\"], [1, \"status-text\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [1, \"user-type-indicator\"], [1, \"fas\", \"fa-user\", 2, \"margin-right\", \"8px\"], [1, \"position-container\"], [\"id\", \"queuePosition\", 2, \"display\", \"block\"], [1, \"position-label\"], [1, \"position-circle\"], [1, \"position-number\"], [\"id\", \"enterButton\", 2, \"display\", \"none\"], [\"onclick\", \"enterConsultation()\", 1, \"enter-button\"], [1, \"fas\", \"fa-video\", 2, \"margin-right\", \"8px\"], [\"id\", \"quitSection\", 2, \"display\", \"block\"], [1, \"quit-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"margin-right\", \"8px\"], [1, \"instructions-card\"], [1, \"instructions-header\"], [1, \"fas\", \"fa-info-circle\", 2, \"margin-right\", \"8px\"], [1, \"instructions-content\"], [1, \"instruction-item\"], [1, \"instruction-number\"], [1, \"instruction-text\"]],\n    template: function FilaEsperaComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵelementStart(4, \"span\", 4);\n        i0.ɵɵtext(5, \"Sistema Online\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"h1\", 5);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 6);\n        i0.ɵɵtext(9, \"Voc\\u00EA ser\\u00E1 chamado em breve para o atendimento\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 7);\n        i0.ɵɵelement(11, \"i\", 8);\n        i0.ɵɵelementStart(12, \"span\");\n        i0.ɵɵtext(13, \"Acesso R\\u00E1pido\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"p\", 11);\n        i0.ɵɵtext(17, \"Sua Posi\\u00E7\\u00E3o\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 12)(19, \"span\", 13);\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"button\", 15);\n        i0.ɵɵelement(23, \"i\", 16);\n        i0.ɵɵtext(24, \" Entrar \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(25, \"div\", 17)(26, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function FilaEsperaComponent_Template_button_click_26_listener() {\n          return ctx.quitQueue();\n        });\n        i0.ɵɵelement(27, \"i\", 19);\n        i0.ɵɵtext(28, \" Desistir da Consulta \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\", 21);\n        i0.ɵɵelement(31, \"i\", 22);\n        i0.ɵɵtext(32, \" Instru\\u00E7\\u00F5es Importantes \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24)(35, \"div\", 25);\n        i0.ɵɵtext(36, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"div\", 26);\n        i0.ɵɵtext(38, \" Feche todos os aplicativos abertos, inclusive WhatsApp, mantendo apenas o navegador aberto. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"div\", 24)(40, \"div\", 25);\n        i0.ɵɵtext(41, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"div\", 26);\n        i0.ɵɵtext(43, \" Aumente o volume do seu dispositivo e desconecte fones bluetooth. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"div\", 24)(45, \"div\", 25);\n        i0.ɵɵtext(46, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"div\", 26);\n        i0.ɵɵtext(48, \" Conecte-se a uma rede WiFi est\\u00E1vel para garantir a qualidade do atendimento. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 24)(50, \"div\", 25);\n        i0.ɵɵtext(51, \"4\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"div\", 26);\n        i0.ɵɵtext(53, \" Aceite o acesso \\u00E0 c\\u00E2mera e microfone quando solicitado pelo sistema. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 24)(55, \"div\", 25);\n        i0.ɵɵtext(56, \"5\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"div\", 26);\n        i0.ɵɵtext(58, \" Se a conex\\u00E3o falhar, acesse novamente a plataforma e solicite um novo atendimento. \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\"Bem-vindo\", (ctx.dadosQuestionario == null ? null : ctx.dadosQuestionario.nome) ? \", \" + (ctx.dadosQuestionario == null ? null : ctx.dadosQuestionario.nome) : \"\", \"!\");\n        i0.ɵɵadvance(13);\n        i0.ɵɵtextInterpolate(ctx.PesicaoFila);\n      }\n    },\n    dependencies: [MatInputModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, MatFormFieldModule, MatDialogModule],\n    styles: [\"@charset \\\"UTF-8\\\";\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.main-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f0fff0 0%, #e8f5e8 100%);\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n}\\n.main-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-image: radial-gradient(circle at 25% 25%, rgba(0, 168, 107, 0.03) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(76, 175, 80, 0.03) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: -1;\\n}\\n\\n.queue-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 24px;\\n  box-shadow: 0 10px 30px rgba(0, 168, 107, 0.1);\\n  padding: 40px;\\n  width: 100%;\\n  max-width: 70vmax;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.queue-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 15px 40px rgba(0, 168, 107, 0.2);\\n  transform: translateY(-2px);\\n}\\n.queue-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #00a86b 0%, #4caf50 50%, #00c851 100%);\\n}\\n\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  padding: 12px 20px;\\n  background: linear-gradient(135deg, rgba(0, 168, 107, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);\\n  border-radius: 50px;\\n  border: 1px solid rgba(0, 168, 107, 0.2);\\n}\\n.status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  background: #00c851;\\n  border-radius: 50%;\\n  margin-right: 10px;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 0 0 0 rgba(0, 200, 81, 0.7);\\n}\\n.status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  color: #006b47;\\n  font-weight: 600;\\n  font-size: 14px;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 200, 81, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 200, 81, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 200, 81, 0);\\n  }\\n}\\n.welcome-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #006b47;\\n  text-align: center;\\n  margin-bottom: 10px;\\n  background: linear-gradient(135deg, #00a86b 0%, #4caf50 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  text-align: center;\\n  margin-bottom: 40px;\\n  font-weight: 400;\\n  line-height: 1.5;\\n}\\n\\n.position-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n\\n#queuePosition[_ngcontent-%COMP%]   .position-label[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #006b47;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #00a86b 0%, #4caf50 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  box-shadow: 0 8px 25px rgba(0, 168, 107, 0.3);\\n  position: relative;\\n}\\n#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 3px;\\n  border-radius: 50%;\\n  background: white;\\n}\\n#queuePosition[_ngcontent-%COMP%]   .position-circle[_ngcontent-%COMP%]   .position-number[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  color: #00a86b;\\n  z-index: 1;\\n  position: relative;\\n}\\n\\n.enter-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00c851 0%, #00a86b 100%);\\n  color: white;\\n  border: none;\\n  padding: 18px 40px;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  border-radius: 50px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  box-shadow: 0 6px 20px rgba(0, 200, 81, 0.3);\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.enter-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 10px 30px rgba(0, 200, 81, 0.4);\\n  background: linear-gradient(135deg, #00a86b 0%, #00c851 100%);\\n}\\n.enter-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px);\\n}\\n.enter-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n}\\n\\n.quit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  border-radius: 50px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 30px;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.quit-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);\\n  background: linear-gradient(135deg, #ee5a5a 0%, #ff6b6b 100%);\\n}\\n.quit-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n.instructions-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(0, 168, 107, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);\\n  border-radius: 16px;\\n  padding: 25px;\\n  border: 1px solid rgba(0, 168, 107, 0.1);\\n  position: relative;\\n}\\n.instructions-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #00a86b 0%, #4caf50 100%);\\n  border-radius: 16px 16px 0 0;\\n}\\n\\n.instructions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #006b47;\\n  margin-bottom: 20px;\\n}\\n.instructions-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #00a86b;\\n  font-size: 1.3rem;\\n}\\n\\n.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 20px;\\n}\\n.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]   .instruction-number[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #00a86b 0%, #4caf50 100%);\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n  flex-shrink: 0;\\n  box-shadow: 0 3px 10px rgba(0, 168, 107, 0.2);\\n}\\n.instructions-content[_ngcontent-%COMP%]   .instruction-item[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%] {\\n  color: #555;\\n  line-height: 1.6;\\n  font-size: 0.95rem;\\n  padding-top: 4px;\\n}\\n\\n@media (max-width: 768px) {\\n  .main-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .queue-card[_ngcontent-%COMP%] {\\n    padding: 30px 20px;\\n    border-radius: 16px;\\n  }\\n  .welcome-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .welcome-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .position-circle[_ngcontent-%COMP%] {\\n    width: 100px !important;\\n    height: 100px !important;\\n  }\\n  .position-circle[_ngcontent-%COMP%]   .position-number[_ngcontent-%COMP%] {\\n    font-size: 2rem !important;\\n  }\\n  .enter-button[_ngcontent-%COMP%] {\\n    padding: 15px 30px;\\n    font-size: 1.1rem;\\n  }\\n  .instructions-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .instruction-item[_ngcontent-%COMP%]   .instruction-number[_ngcontent-%COMP%] {\\n    width: 28px !important;\\n    height: 28px !important;\\n    font-size: 0.8rem !important;\\n    margin-right: 12px !important;\\n  }\\n  .instruction-item[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .welcome-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .queue-card[_ngcontent-%COMP%] {\\n    padding: 25px 15px;\\n  }\\n  .enter-button[_ngcontent-%COMP%] {\\n    padding: 12px 25px;\\n    font-size: 1rem;\\n  }\\n}\\n\\n\\n.questionnaire-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-radius: 16px;\\n  padding: 20px;\\n  margin: 25px 0;\\n  border: 1px solid rgba(0, 168, 107, 0.1);\\n  box-shadow: 0 4px 15px rgba(0, 168, 107, 0.05);\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  color: #006b47;\\n  margin-bottom: 15px;\\n  padding-bottom: 10px;\\n  border-bottom: 2px solid rgba(0, 168, 107, 0.1);\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #00a86b;\\n  font-size: 1.2rem;\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 12px;\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 12px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 8px;\\n  border-left: 3px solid #00a86b;\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #555;\\n  font-size: 0.9rem;\\n}\\n.questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #006b47;\\n  font-size: 0.95rem;\\n  text-align: right;\\n  max-width: 60%;\\n  word-wrap: break-word;\\n}\\n\\n\\n\\n.user-type-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(0, 168, 107, 0.1);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  margin: 15px 0;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #006b47;\\n}\\n.user-type-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #00a86b;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .questionnaire-summary[_ngcontent-%COMP%] {\\n    margin: 20px 0;\\n    padding: 15px;\\n  }\\n  .questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 5px;\\n  }\\n  .questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .questionnaire-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    max-width: 100%;\\n  }\\n  .user-type-indicator[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    padding: 6px 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Router", "RouterModule", "ConsultaService", "SpinnerService", "CriptografarUtil", "CommonModule", "FormsModule", "ReactiveFormsModule", "MatFormFieldModule", "MatInputModule", "ConsultaAgoraDialogComponent", "MedicosZeroDialogComponent", "SairConsultaDialogComponent", "MatDialog", "MatDialogModule", "ChangeDetectorRef", "FilaEsperaPacienteService", "SignalHubGuestService", "PatientQueueIntegrationService", "ModalColetaDadosVittaltecComponent", "firstValueFrom", "FilaEsperaComponent", "router", "spinner", "consultaService", "dialog", "cdr", "filaEsperaPacienteService", "signalHubGuestService", "patientQueueIntegrationService", "constructor", "PesicaoFila", "timeInterval", "ConsultaAgoraTeste", "dadosQuestionario", "dadosVittalTec", "tokenConexao", "subscriptions", "signalRConectado", "cadastrandoNaFila", "pacienteReg<PERSON><PERSON>", "ngOnInit", "_this", "_asyncToGenerator", "carregarDadosQuestionario", "carregarDadosVittalTec", "navigate", "tokenSalvo", "obterLocalStorageCriptografado", "gerarHashToken", "cpf", "Date", "toISOString", "cadastrarNaFilaDeslogado", "recuperarPosicaoFila", "configurarSignalR", "inicializarTokenConexao", "conectarSignalRGuest", "quitQueue", "CancelarConsulta", "_this2", "localStorageCriptografado", "_this3", "console", "error", "aguardarSignalRPronto", "connectGuestUser", "verificarEstadoConexao", "garantirColetaDadosCompletos", "registrarDadosPaciente", "_this4", "Promise", "resolve", "isHubConnected", "subscription", "changeConsulta$", "subscribe", "connected", "unsubscribe", "setTimeout", "subAtualizaFila", "OnAtualizaChamaPacienteFila", "buscarPosicaoAtual", "subConviteReuniao", "OnConviteReuniao", "convite", "token", "IniciarConsulta", "subConvidarPacienteComToken", "OnConvidarPacienteComToken", "dados", "subChamaPaciente", "OnChamaPacienteFila", "push", "dadosStorage", "JSON", "parse", "getDadosQuestionario", "_this5", "show", "salvarTokenFila", "dadosComToken", "tokenGerado", "response", "cadastrarFilaEsperaDeslogado", "sucesso", "iniciarMonitoramentoPosicao", "detectChanges", "hide", "Mensagem", "removerDaFila<PERSON>pera", "then", "<PERSON><PERSON><PERSON>", "catch", "err", "clearInterval", "limparDadosFilaEspera", "idConsultaTemporario", "gerarIdConsultaTemporario", "queryParams", "idConsulta", "now", "estadoConexao", "getEstadoConexao", "warn", "enviaServer", "abrirDialogSairConsulta", "dialogRef", "open", "width", "disableClose", "afterClosed", "result", "abrirDialogMedicosZero", "abrirDialogConsultaAgora", "beforeUnloadHandler", "_this6", "_this7", "_this8", "setInterval", "consultarPosicaoFila", "posicaoFila", "_this9", "mensagem", "status", "_this10", "log", "questionario", "vittalTec", "_this11", "patientData", "converterQuestionarioParaRegistro", "registerPatientInQueue", "success", "patientToken", "nome", "patientName", "tipoFila", "queueType", "comDadosVittalTec", "dadosEnviados", "Object", "keys", "length", "stringify", "timestamp", "dadosCompletos", "message", "errors", "Error", "dataNascimento", "date", "isNaN", "getTime", "split", "sintomas", "sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "vittalTecData", "data", "pressaoSistolica", "convertVitalSignToString", "systolic", "pressaoDiastolica", "diastolic", "temperatura", "convertTemperatureToString", "saturacaoOxigenio", "oxigenacao", "oxygenSaturation", "frequenciaCardiaca", "batimento", "heartRate", "intensidadeDorString", "convertToStringValue", "intensidadeDor", "duracaoSintomasString", "converterTempoSintomas", "tempoSintomas", "email", "telefone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "doencasPrevia<PERSON>", "observacoesAdicionais", "observacoes", "tempoTexto", "undefined", "texto", "toLowerCase", "includes", "match", "parseInt", "value", "String", "toString", "trim", "temperature", "tempValue", "parseFloat", "Math", "round", "abrirModalVittalTec", "height", "action", "_this12", "i0", "ɵɵdirectiveInject", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "selectors", "hostBindings", "FilaEsperaComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "FilaEsperaComponent_beforeunload_HostBindingHandler", "$event", "ɵɵresolveWindow", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "FilaEsperaComponent_Template_button_click_26_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "styles"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\fila-espera\\fila-espera.component.ts", "C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\fila-espera\\fila-espera.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { ConsultaService } from 'src/app/service/consulta.service';\r\nimport { SpinnerService } from 'src/app/service/spinner.service';\r\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { ConsultaAgoraDialogComponent } from './consulta-agora-dialog/consulta-agora-dialog.component';\r\nimport { MedicosZeroDialogComponent } from './medicos-zero-dialog/medicos-zero-dialog.component';\r\nimport { SairConsultaDialogComponent } from './sair-consulta-dialog/sair-consulta-dialog.component';\r\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\r\nimport { ChangeDetectorRef } from '@angular/core';\r\nimport { FilaEsperaPacienteService } from 'src/app/service/fila-espera-paciente.service';\r\n// import { SignalHubService } from 'src/app/service/signalHub.service';\r\nimport { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';\r\nimport { PatientQueueIntegrationService } from 'src/app/service/patient-queue-integration.service';\r\nimport { ModalColetaDadosVittaltecComponent } from './modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';\r\nimport { firstValueFrom, Subscription } from 'rxjs';\r\n\r\n\r\nexport interface QuestionarioPreConsultaLocal {\r\n  nome: string;\r\n  idade: number;\r\n  cpf?: string;\r\n  email?: string;\r\n  telefone?: string;\r\n  dataNascimento?: string;\r\n  sintomas: string[];\r\n  sintomasOutros?: string;\r\n  intensidadeDor: number;\r\n  tempoSintomas: string;\r\n  alergias?: string;\r\n  doencasPrevias?: string;\r\n  observacoes?: string;\r\n  dataPreenchimento?: string;\r\n}\r\n\r\n\r\n\r\n@Component({\r\n  selector: 'app-fila-espera',\r\n  templateUrl: './fila-espera.component.html',\r\n  styleUrls: ['./fila-espera.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatInputModule,\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    RouterModule,\r\n    MatFormFieldModule,\r\n    MatDialogModule\r\n  ]\r\n})\r\n\r\nexport class FilaEsperaComponent implements OnInit {\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private spinner: SpinnerService,\r\n    private consultaService: ConsultaService,\r\n    private dialog: MatDialog,\r\n    private cdr: ChangeDetectorRef,\r\n    private filaEsperaPacienteService: FilaEsperaPacienteService,\r\n    // private signalHubService: SignalHubService,\r\n    private signalHubGuestService: SignalHubGuestService,\r\n    private patientQueueIntegrationService: PatientQueueIntegrationService\r\n  ) { }\r\n\r\n  PesicaoFila = 0;\r\n  timeInterval: any;\r\n  ConsultaAgoraTeste: boolean = false;\r\n  dadosQuestionario: QuestionarioPreConsultaLocal | null = null;\r\n  dadosVittalTec: any = null; // Data collected from VittalTec modal\r\n  tokenConexao: string | null = null;\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  // Indicadores de status\r\n  signalRConectado: boolean = false;\r\n  cadastrandoNaFila: boolean = false;\r\n  pacienteRegistrado: boolean = false;\r\n\r\n  async ngOnInit() {\r\n    this.carregarDadosQuestionario();\r\n    this.carregarDadosVittalTec();\r\n\r\n    if (!this.dadosQuestionario) {\r\n      this.router.navigate(['/pre-consulta-questionario']);\r\n      return;\r\n    }\r\n\r\n    const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\r\n\r\n    if (!tokenSalvo) {\r\n      this.tokenConexao = CriptografarUtil.gerarHashToken(this.dadosQuestionario.cpf ?? \"naotemcpf\", new Date().toISOString());\r\n      await this.cadastrarNaFilaDeslogado();\r\n    } else {\r\n      this.tokenConexao = tokenSalvo;\r\n      await this.recuperarPosicaoFila();\r\n    }\r\n\r\n    this.configurarSignalR();\r\n\r\n    await this.inicializarTokenConexao();\r\n\r\n    await this.conectarSignalRGuest();\r\n  }\r\n\r\n  quitQueue() {\r\n    this.CancelarConsulta();\r\n  }\r\n\r\n  private async inicializarTokenConexao() {\r\n    const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');\r\n\r\n    if (tokenSalvo)\r\n      this.tokenConexao = tokenSalvo;\r\n    else\r\n      CriptografarUtil.localStorageCriptografado('tokenFilaEspera', this.tokenConexao!);\r\n  }\r\n\r\n  private async conectarSignalRGuest() {\r\n    if (!this.tokenConexao) {\r\n      console.error('Token de conexão não disponível');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await this.aguardarSignalRPronto();\r\n\r\n      this.signalHubGuestService.connectGuestUser(this.tokenConexao);\r\n      this.signalRConectado = true;\r\n      this.verificarEstadoConexao();\r\n\r\n      // Register patient data after SignalR connection is established\r\n      await this.garantirColetaDadosCompletos();\r\n      await this.registrarDadosPaciente();\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro ao conectar guest ao SignalR:', error);\r\n      this.signalRConectado = false;\r\n    }\r\n  }\r\n\r\n  private async aguardarSignalRPronto(): Promise<void> {\r\n    return new Promise((resolve) => {\r\n      if (this.signalHubGuestService.isHubConnected()) {\r\n        resolve();\r\n        return;\r\n      }\r\n\r\n      const subscription = this.signalHubGuestService.changeConsulta$.subscribe((connected) => {\r\n        if (connected) {\r\n          subscription.unsubscribe();\r\n          resolve();\r\n        }\r\n      });\r\n\r\n      setTimeout(() => {\r\n        subscription.unsubscribe();\r\n        resolve();\r\n      }, 5000);\r\n    });\r\n  }\r\n\r\n  private configurarSignalR() {\r\n    const subAtualizaFila = this.signalHubGuestService.OnAtualizaChamaPacienteFila\r\n      .subscribe(() => {\r\n        this.buscarPosicaoAtual();\r\n      });\r\n\r\n    const subConviteReuniao = this.signalHubGuestService.OnConviteReuniao\r\n      .subscribe((convite: any) => {\r\n        if (convite.token === this.tokenConexao) {\r\n          this.IniciarConsulta();\r\n        }\r\n      });\r\n\r\n    const subConvidarPacienteComToken = this.signalHubGuestService.OnConvidarPacienteComToken\r\n      .subscribe((dados: any) => {\r\n        if (dados.token === this.tokenConexao) {\r\n          this.IniciarConsulta();\r\n        }\r\n      });\r\n\r\n    const subChamaPaciente = this.signalHubGuestService.OnChamaPacienteFila\r\n      .subscribe(() => {\r\n        this.buscarPosicaoAtual();\r\n      });\r\n\r\n    this.subscriptions.push(subAtualizaFila, subConviteReuniao, subConvidarPacienteComToken, subChamaPaciente);\r\n  }\r\n\r\n  carregarDadosQuestionario() {\r\n    const dadosStorage = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');\r\n    if (dadosStorage) {\r\n      try {\r\n        this.dadosQuestionario = JSON.parse(dadosStorage);\r\n      } catch (error) {\r\n        console.error('Erro ao parsing dos dados do questionário:', error);\r\n        this.dadosQuestionario = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  getDadosQuestionario(): QuestionarioPreConsultaLocal | null {\r\n    return this.dadosQuestionario;\r\n  }\r\n\r\n  private async cadastrarNaFilaDeslogado() {\r\n    try {\r\n      this.cadastrandoNaFila = true;\r\n      this.spinner.show();\r\n\r\n      if (this.tokenConexao)\r\n        this.filaEsperaPacienteService.salvarTokenFila(this.tokenConexao);\r\n\r\n      const dadosComToken = {\r\n        ...this.dadosQuestionario,\r\n        tokenGerado: this.tokenConexao\r\n      };\r\n\r\n      const response = await firstValueFrom(this.consultaService.cadastrarFilaEsperaDeslogado(dadosComToken));\r\n\r\n      if (response.sucesso) {\r\n        await this.buscarPosicaoAtual();\r\n        this.iniciarMonitoramentoPosicao();\r\n\r\n        this.cadastrandoNaFila = false;\r\n        this.cdr.detectChanges();\r\n        this.spinner.hide();\r\n      } else {\r\n        console.error('❌ Erro ao cadastrar na fila:', response.Mensagem);\r\n        this.cadastrandoNaFila = false;\r\n        this.spinner.hide();\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Erro ao cadastrar na fila de espera:', error);\r\n      this.cadastrandoNaFila = false;\r\n      this.spinner.hide();\r\n    }\r\n  }\r\n\r\n  CancelarConsulta() {\r\n    this.removerDaFilaEspera().then(() => {\r\n      this.Logoff();\r\n      this.spinner.hide();\r\n    }).catch(err => {\r\n      console.error('Erro ao cancelar consulta:', err);\r\n      this.Logoff();\r\n      this.spinner.hide();\r\n    });\r\n  }\r\n\r\n  Logoff() {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval);\r\n    }\r\n\r\n    if (this.tokenConexao) {\r\n      this.removerDaFilaEspera();\r\n      this.filaEsperaPacienteService.limparDadosFilaEspera();\r\n    }\r\n\r\n\r\n    this.router.navigate(['pre-consulta-questionario']);\r\n  }\r\n\r\n\r\n\r\n  IniciarConsulta() {\r\n    if (!this.tokenConexao) {\r\n      console.error('❌ Token não encontrado! Não é possível iniciar a consulta.');\r\n      return;\r\n    }\r\n\r\n    const idConsultaTemporario = this.gerarIdConsultaTemporario();\r\n\r\n    this.router.navigate(['/streaming-paciente'], {\r\n      queryParams: {\r\n        idConsulta: idConsultaTemporario,\r\n        token: this.tokenConexao\r\n      }\r\n    });\r\n  }\r\n\r\n  private gerarIdConsultaTemporario(): number {\r\n    return Date.now();\r\n  }\r\n\r\n  // Método de debug para verificar estado da conexão\r\n  private verificarEstadoConexao() {\r\n    const estadoConexao = this.signalHubGuestService.getEstadoConexao();\r\n    // Verificar se tokens correspondem\r\n    if (this.tokenConexao !== estadoConexao.tokenConexao) {\r\n      console.warn('⚠️ PROBLEMA: Tokens não correspondem!');\r\n      console.warn('  - Token Componente:', this.tokenConexao);\r\n      console.warn('  - Token Service:', estadoConexao.tokenConexao);\r\n    }\r\n\r\n    // Testar se consegue enviar dados para o servidor\r\n    setTimeout(() => {\r\n      try {\r\n        this.signalHubGuestService.enviaServer('TestConnection', this.tokenConexao);\r\n      } catch (error) {\r\n        console.error('❌ Erro no teste de envio:', error);\r\n      }\r\n    }, 2000);\r\n  }\r\n\r\n  abrirDialogSairConsulta() {\r\n    const dialogRef = this.dialog.open(SairConsultaDialogComponent, {\r\n      width: '400px',\r\n      disableClose: true\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result === 'confirmar') {\r\n        this.CancelarConsulta();\r\n      }\r\n    });\r\n  }\r\n\r\n  abrirDialogMedicosZero() {\r\n    const dialogRef = this.dialog.open(MedicosZeroDialogComponent, {\r\n      width: '400px',\r\n      disableClose: true\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result === 'sair') {\r\n        this.CancelarConsulta();\r\n      }\r\n    });\r\n  }\r\n\r\n  abrirDialogConsultaAgora() {\r\n    const dialogRef = this.dialog.open(ConsultaAgoraDialogComponent, {\r\n      width: '500px',\r\n      disableClose: true\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result === 'iniciar')\r\n        this.IniciarConsulta();\r\n      else if (result === 'cancelar') {\r\n        this.CancelarConsulta();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('window:beforeunload', ['$event'])\r\n  async beforeUnloadHandler() {\r\n    if (this.tokenConexao) {\r\n      await this.removerDaFilaEspera();\r\n    }\r\n  }\r\n\r\n  private async removerDaFilaEspera() {\r\n    if (this.tokenConexao) {\r\n      await firstValueFrom(this.filaEsperaPacienteService.removerDaFilaEspera(this.tokenConexao));\r\n    }\r\n  }\r\n\r\n  private iniciarMonitoramentoPosicao() {\r\n    if (this.tokenConexao) {\r\n      this.timeInterval = setInterval(async () => {\r\n        try {\r\n          const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao!));\r\n          if (response.sucesso) {\r\n            this.PesicaoFila = response.posicaoFila;\r\n            this.cdr.detectChanges();\r\n\r\n            if (this.PesicaoFila === 0) {\r\n              this.ConsultaAgoraTeste = true;\r\n              this.abrirDialogConsultaAgora();\r\n              clearInterval(this.timeInterval);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Erro ao consultar posição:', error);\r\n        }\r\n      }, 30000);\r\n    }\r\n  }\r\n\r\n  private async buscarPosicaoAtual() {\r\n    if (!this.tokenConexao) {\r\n      console.warn('⚠️ Token não encontrado para buscar posição');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao));\r\n      if (response.sucesso) {\r\n        this.PesicaoFila = response.posicaoFila;\r\n        this.cdr.detectChanges();\r\n      } else\r\n        console.error('❌ Erro ao consultar posição:', response.mensagem);\r\n    } catch (error: any) {\r\n      console.error('❌ Erro ao buscar posição atual:', error);\r\n      if (error.status === 404)\r\n        console.error('🚨 ERRO 404: Endpoint não encontrado!');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Ensures all necessary data is collected before registration\r\n   */\r\n  private async garantirColetaDadosCompletos() {\r\n    console.log('🔍 Verificando completude dos dados...');\r\n\r\n    // Check if questionnaire data is available\r\n    if (!this.dadosQuestionario) {\r\n      console.warn('⚠️ Dados do questionário não encontrados, tentando carregar...');\r\n      this.carregarDadosQuestionario();\r\n    }\r\n\r\n    // Check if VittalTec data is available\r\n    if (!this.dadosVittalTec) {\r\n      console.log('📊 Dados VittalTec não encontrados, tentando carregar do localStorage...');\r\n      this.carregarDadosVittalTec();\r\n\r\n      // If still no VittalTec data, that's okay - registration can proceed without it\r\n      if (!this.dadosVittalTec) {\r\n        console.log('ℹ️ Nenhum dado VittalTec disponível - registro continuará apenas com dados do questionário');\r\n      }\r\n    }\r\n\r\n    console.log('✅ Verificação de dados concluída:', {\r\n      questionario: !!this.dadosQuestionario,\r\n      vittalTec: !!this.dadosVittalTec,\r\n      token: !!this.tokenConexao\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Registers patient data in the database using the same token as SignalR\r\n   * Collects data from both questionnaire and VittalTec modal\r\n   */\r\n  private async registrarDadosPaciente() {\r\n    if (!this.tokenConexao || !this.dadosQuestionario) {\r\n      console.warn('⚠️ Token ou dados do questionário não disponíveis para registro');\r\n      return;\r\n    }\r\n\r\n    if (this.pacienteRegistrado) {\r\n      console.log('✅ Paciente já registrado, pulando registro');\r\n      return;\r\n    }\r\n\r\n    try {\r\n\r\n      if (!this.dadosVittalTec) {\r\n        this.carregarDadosVittalTec();\r\n      }\r\n\r\n      // Convert questionnaire data to patient registration format (includes VittalTec if available)\r\n      const patientData = this.converterQuestionarioParaRegistro();\r\n\r\n\r\n      // Register patient using the integration service\r\n      const response = await firstValueFrom(\r\n        this.patientQueueIntegrationService.registerPatientInQueue(patientData)\r\n      );\r\n\r\n      if (response.success) {\r\n        this.pacienteRegistrado = true;\r\n        console.log('✅ Paciente registrado com sucesso:', {\r\n          token: response.patientToken,\r\n          nome: response.patientName,\r\n          tipoFila: response.queueType,\r\n          comDadosVittalTec: !!this.dadosVittalTec,\r\n          dadosEnviados: Object.keys(patientData).length\r\n        });\r\n\r\n        // Save registration status to localStorage\r\n        CriptografarUtil.localStorageCriptografado('paciente-registrado', JSON.stringify({\r\n          token: response.patientToken,\r\n          timestamp: new Date().toISOString(),\r\n          dadosCompletos: !!this.dadosVittalTec\r\n        }));\r\n\r\n      } else {\r\n        console.warn('⚠️ Falha ao registrar paciente:', response.message);\r\n        console.warn('Erros:', response.errors);\r\n        // Don't prevent queue entry if patient registration fails\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Erro ao registrar dados do paciente:', error);\r\n      // Don't prevent queue entry if patient registration fails\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Converts questionnaire data to patient registration format including VittalTec data\r\n   */\r\n  private converterQuestionarioParaRegistro(): any {\r\n    if (!this.dadosQuestionario || !this.tokenConexao) {\r\n      throw new Error('Dados do questionário ou token não disponíveis');\r\n    }\r\n\r\n    // Format date of birth as string for API\r\n    let dataNascimento: string | undefined;\r\n    if (this.dadosQuestionario.dataNascimento) {\r\n      try {\r\n        const date = new Date(this.dadosQuestionario.dataNascimento);\r\n        if (!isNaN(date.getTime())) {\r\n          dataNascimento = date.toISOString().split('T')[0]; // YYYY-MM-DD format\r\n        }\r\n      } catch (error) {\r\n        console.warn('⚠️ Erro ao converter data de nascimento:', error);\r\n      }\r\n    }\r\n\r\n    let sintomas = this.dadosQuestionario.sintomasOutros;\r\n\r\n    let vittalTecData: any = {};\r\n    if (this.dadosVittalTec) {\r\n      try {\r\n        const data = this.dadosVittalTec.data || this.dadosVittalTec;\r\n\r\n        if (data) {\r\n          vittalTecData = {\r\n            pressaoSistolica: this.convertVitalSignToString(data.pressaoSistolica || data.systolic),\r\n            pressaoDiastolica: this.convertVitalSignToString(data.pressaoDiastolica || data.diastolic),\r\n            temperatura: this.convertTemperatureToString(data.temperatura),\r\n            saturacaoOxigenio: this.convertVitalSignToString(data.oxigenacao || data.oxygenSaturation),\r\n            frequenciaCardiaca: this.convertVitalSignToString(data.batimento || data.heartRate)\r\n          };\r\n\r\n          console.log('📊 Dados vitais VittalTec incluídos:', vittalTecData);\r\n        }\r\n      } catch (error) {\r\n        console.warn('⚠️ Erro ao processar dados VittalTec:', error);\r\n      }\r\n    }\r\n\r\n    // Convert questionnaire intensity and duration to strings\r\n    const intensidadeDorString = this.convertToStringValue(this.dadosQuestionario.intensidadeDor);\r\n    const duracaoSintomasString = this.convertToStringValue(this.converterTempoSintomas(this.dadosQuestionario.tempoSintomas));\r\n\r\n    const patientData = {\r\n      token: this.tokenConexao, // Use the same token as SignalR\r\n      nome: this.dadosQuestionario.nome,\r\n      cpf: this.dadosQuestionario.cpf || '',\r\n      email: this.dadosQuestionario.email,\r\n      telefone: this.dadosQuestionario.telefone,\r\n      dataNascimento: dataNascimento,\r\n      sintomas: sintomas,\r\n      intensidadeDor: intensidadeDorString,\r\n      duracaoSintomas: duracaoSintomasString,\r\n      alergias: this.dadosQuestionario.alergias,\r\n      doencasPrevias: this.dadosQuestionario.doencasPrevias,\r\n      observacoesAdicionais: this.dadosQuestionario.observacoes,\r\n      queueType: 'consulta', // Default queue type\r\n\r\n      // Include VittalTec vital signs data\r\n      ...vittalTecData\r\n    };\r\n\r\n    console.log('✅ Dados finais para registro:', patientData);\r\n    return patientData;\r\n  }\r\n\r\n  /**\r\n   * Converts symptom duration text to numeric value (in days)\r\n   */\r\n  private converterTempoSintomas(tempoTexto: string): number | undefined {\r\n    if (!tempoTexto) return undefined;\r\n\r\n    const texto = tempoTexto.toLowerCase();\r\n\r\n    if (texto.includes('hoje') || texto.includes('horas')) {\r\n      return 0; // Same day\r\n    } else if (texto.includes('ontem') || texto.includes('1 dia')) {\r\n      return 1;\r\n    } else if (texto.includes('dias')) {\r\n      const match = texto.match(/(\\d+)\\s*dias?/);\r\n      return match ? parseInt(match[1]) : undefined;\r\n    } else if (texto.includes('semana')) {\r\n      const match = texto.match(/(\\d+)\\s*semanas?/);\r\n      return match ? parseInt(match[1]) * 7 : 7;\r\n    } else if (texto.includes('mês') || texto.includes('mes')) {\r\n      const match = texto.match(/(\\d+)\\s*m[eê]s/);\r\n      return match ? parseInt(match[1]) * 30 : 30;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  /**\r\n   * Helper methods for data conversion\r\n   */\r\n  private convertToStringValue(value: any): string | undefined {\r\n    if (value === null || value === undefined) {\r\n      return undefined;\r\n    }\r\n    return String(value);\r\n  }\r\n\r\n  private convertVitalSignToString(value: any): string | undefined {\r\n    if (value === null || value === undefined) {\r\n      return undefined;\r\n    }\r\n\r\n    // Handle numeric values\r\n    if (typeof value === 'number') {\r\n      return value.toString();\r\n    }\r\n\r\n    // Handle string values\r\n    if (typeof value === 'string') {\r\n      return value.trim() || undefined;\r\n    }\r\n\r\n    return String(value);\r\n  }\r\n\r\n  private convertTemperatureToString(temperature: any): string | undefined {\r\n    if (temperature === null || temperature === undefined) {\r\n      return undefined;\r\n    }\r\n\r\n    try {\r\n      let tempValue: number;\r\n\r\n      if (typeof temperature === 'string') {\r\n        tempValue = parseFloat(temperature);\r\n      } else if (typeof temperature === 'number') {\r\n        tempValue = temperature;\r\n      } else {\r\n        return undefined;\r\n      }\r\n\r\n      if (isNaN(tempValue)) {\r\n        return undefined;\r\n      }\r\n\r\n      // Convert to tenths (e.g., 36.5°C becomes \"365\")\r\n      return Math.round(tempValue * 10).toString();\r\n    } catch (error) {\r\n      console.warn('⚠️ Erro ao converter temperatura:', error);\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads VittalTec data from encrypted localStorage if available\r\n   */\r\n  private carregarDadosVittalTec() {\r\n    try {\r\n\r\n      let dadosVittalTec = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');\r\n      console.log(\"this.dadosVittalTec\", dadosVittalTec);\r\n      this.dadosVittalTec = dadosVittalTec;\r\n    } catch (error) {\r\n      console.error('❌ Erro geral ao carregar dados VittalTec:', error);\r\n      this.dadosVittalTec = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Opens VittalTec modal for data collection (public method for template access)\r\n   */\r\n  public abrirModalVittalTec(): Promise<any> {\r\n    return new Promise((resolve) => {\r\n      const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {\r\n        width: '600px',\r\n        height: '500px',\r\n        disableClose: true,\r\n        data: {}\r\n      });\r\n\r\n      dialogRef.afterClosed().subscribe(result => {\r\n        if (result && result.action === 'continuar' && result.data) {\r\n          this.dadosVittalTec = result.data;\r\n          console.log('📊 Dados VittalTec coletados via modal:', this.dadosVittalTec);\r\n        }\r\n        resolve(result);\r\n      });\r\n    });\r\n  }\r\n\r\n  private async recuperarPosicaoFila() {\r\n    if (!this.tokenConexao) return;\r\n\r\n    try {\r\n      const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao));\r\n      if (response.sucesso) {\r\n        this.PesicaoFila = response.posicaoFila;\r\n        this.iniciarMonitoramentoPosicao();\r\n        this.cdr.detectChanges();\r\n      } else {\r\n        await this.cadastrarNaFilaDeslogado();\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro ao recuperar posição:', error);\r\n      await this.cadastrarNaFilaDeslogado();\r\n    }\r\n  }\r\n}\r\n", "<div class=\"main-container\">\r\n  <div class=\"queue-card fade-in\">\r\n    <div class=\"status-indicator\">\r\n      <div class=\"status-dot\"></div>\r\n      <span class=\"status-text\">Sistema Online</span>\r\n    </div>\r\n\r\n    <h1 class=\"welcome-title\">Bem-vindo{{ dadosQuestionario?.nome ? ', ' + dadosQuestionario?.nome : '' }}!</h1>\r\n    <p class=\"welcome-subtitle\">Você será chamado em breve para o atendimento</p>\r\n\r\n    <!-- Indicador de tipo de usuário -->\r\n    <div class=\"user-type-indicator\">\r\n      <i class=\"fas fa-user\" style=\"margin-right: 8px;\"></i>\r\n      <span>Acesso Rápido</span>\r\n    </div>\r\n\r\n    <div class=\"position-container\">\r\n      <div id=\"queuePosition\" style=\"display: block;\">\r\n        <p class=\"position-label\">Sua Posição</p>\r\n        <div class=\"position-circle\">\r\n          <span class=\"position-number\">{{ PesicaoFila  }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div id=\"enterButton\" style=\"display: none;\">\r\n        <button class=\"enter-button\" onclick=\"enterConsultation()\">\r\n          <i class=\"fas fa-video\" style=\"margin-right: 8px;\"></i>\r\n          Entrar\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <div id=\"quitSection\" style=\"display: block;\">\r\n      <button class=\"quit-button\" (click)=\"quitQueue()\">\r\n        <i class=\"fas fa-times\" style=\"margin-right: 8px;\"></i>\r\n        Desistir da Consulta\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"instructions-card\">\r\n      <div class=\"instructions-header\">\r\n        <i class=\"fas fa-info-circle\" style=\"margin-right: 8px;\"></i>\r\n        Instruções Importantes\r\n      </div>\r\n      <div class=\"instructions-content\">\r\n        <div class=\"instruction-item\">\r\n          <div class=\"instruction-number\">1</div>\r\n          <div class=\"instruction-text\">\r\n            Feche todos os aplicativos abertos, inclusive WhatsApp, mantendo apenas o navegador aberto.\r\n          </div>\r\n        </div>\r\n        <div class=\"instruction-item\">\r\n          <div class=\"instruction-number\">2</div>\r\n          <div class=\"instruction-text\">\r\n            Aumente o volume do seu dispositivo e desconecte fones bluetooth.\r\n          </div>\r\n        </div>\r\n        <div class=\"instruction-item\">\r\n          <div class=\"instruction-number\">3</div>\r\n          <div class=\"instruction-text\">\r\n            Conecte-se a uma rede WiFi estável para garantir a qualidade do atendimento.\r\n          </div>\r\n        </div>\r\n        <div class=\"instruction-item\">\r\n          <div class=\"instruction-number\">4</div>\r\n          <div class=\"instruction-text\">\r\n            Aceite o acesso à câmera e microfone quando solicitado pelo sistema.\r\n          </div>\r\n        </div>\r\n        <div class=\"instruction-item\">\r\n          <div class=\"instruction-number\">5</div>\r\n          <div class=\"instruction-text\">\r\n            Se a conexão falhar, acesse novamente a plataforma e solicite um novo atendimento.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";AACA,SAASA,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,yBAAyB,QAAQ,8CAA8C;AACxF;AACA,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,8BAA8B,QAAQ,mDAAmD;AAClG,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,cAAc,QAAsB,MAAM;;;;;;;;;AAsCnD,OAAM,MAAOC,mBAAmB;EAGpBC,MAAA;EACAC,OAAA;EACAC,eAAA;EACAC,MAAA;EACAC,GAAA;EACAC,yBAAA;EAEAC,qBAAA;EACAC,8BAAA;EATVC,YACUR,MAAc,EACdC,OAAuB,EACvBC,eAAgC,EAChCC,MAAiB,EACjBC,GAAsB,EACtBC,yBAAoD;EAC5D;EACQC,qBAA4C,EAC5CC,8BAA8D;IAR9D,KAAAP,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,yBAAyB,GAAzBA,yBAAyB;IAEzB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,8BAA8B,GAA9BA,8BAA8B;EACpC;EAEJE,WAAW,GAAG,CAAC;EACfC,YAAY;EACZC,kBAAkB,GAAY,KAAK;EACnCC,iBAAiB,GAAwC,IAAI;EAC7DC,cAAc,GAAQ,IAAI,CAAC,CAAC;EAC5BC,YAAY,GAAkB,IAAI;EAC1BC,aAAa,GAAmB,EAAE;EAE1C;EACAC,gBAAgB,GAAY,KAAK;EACjCC,iBAAiB,GAAY,KAAK;EAClCC,kBAAkB,GAAY,KAAK;EAE7BC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACE,yBAAyB,EAAE;MAChCF,KAAI,CAACG,sBAAsB,EAAE;MAE7B,IAAI,CAACH,KAAI,CAACR,iBAAiB,EAAE;QAC3BQ,KAAI,CAACpB,MAAM,CAACwB,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;QACpD;MACF;MAEA,MAAMC,UAAU,GAAG3C,gBAAgB,CAAC4C,8BAA8B,CAAC,iBAAiB,CAAC;MAErF,IAAI,CAACD,UAAU,EAAE;QACfL,KAAI,CAACN,YAAY,GAAGhC,gBAAgB,CAAC6C,cAAc,CAACP,KAAI,CAACR,iBAAiB,CAACgB,GAAG,IAAI,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAAC;QACxH,MAAMV,KAAI,CAACW,wBAAwB,EAAE;MACvC,CAAC,MAAM;QACLX,KAAI,CAACN,YAAY,GAAGW,UAAU;QAC9B,MAAML,KAAI,CAACY,oBAAoB,EAAE;MACnC;MAEAZ,KAAI,CAACa,iBAAiB,EAAE;MAExB,MAAMb,KAAI,CAACc,uBAAuB,EAAE;MAEpC,MAAMd,KAAI,CAACe,oBAAoB,EAAE;IAAC;EACpC;EAEAC,SAASA,CAAA;IACP,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEcH,uBAAuBA,CAAA;IAAA,IAAAI,MAAA;IAAA,OAAAjB,iBAAA;MACnC,MAAMI,UAAU,GAAG3C,gBAAgB,CAAC4C,8BAA8B,CAAC,iBAAiB,CAAC;MAErF,IAAID,UAAU,EACZa,MAAI,CAACxB,YAAY,GAAGW,UAAU,CAAC,KAE/B3C,gBAAgB,CAACyD,yBAAyB,CAAC,iBAAiB,EAAED,MAAI,CAACxB,YAAa,CAAC;IAAC;EACtF;EAEcqB,oBAAoBA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAAnB,iBAAA;MAChC,IAAI,CAACmB,MAAI,CAAC1B,YAAY,EAAE;QACtB2B,OAAO,CAACC,KAAK,CAAC,iCAAiC,CAAC;QAChD;MACF;MAEA,IAAI;QACF,MAAMF,MAAI,CAACG,qBAAqB,EAAE;QAElCH,MAAI,CAAClC,qBAAqB,CAACsC,gBAAgB,CAACJ,MAAI,CAAC1B,YAAY,CAAC;QAC9D0B,MAAI,CAACxB,gBAAgB,GAAG,IAAI;QAC5BwB,MAAI,CAACK,sBAAsB,EAAE;QAE7B;QACA,MAAML,MAAI,CAACM,4BAA4B,EAAE;QACzC,MAAMN,MAAI,CAACO,sBAAsB,EAAE;MAErC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DF,MAAI,CAACxB,gBAAgB,GAAG,KAAK;MAC/B;IAAC;EACH;EAEc2B,qBAAqBA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAA3B,iBAAA;MACjC,OAAO,IAAI4B,OAAO,CAAEC,OAAO,IAAI;QAC7B,IAAIF,MAAI,CAAC1C,qBAAqB,CAAC6C,cAAc,EAAE,EAAE;UAC/CD,OAAO,EAAE;UACT;QACF;QAEA,MAAME,YAAY,GAAGJ,MAAI,CAAC1C,qBAAqB,CAAC+C,eAAe,CAACC,SAAS,CAAEC,SAAS,IAAI;UACtF,IAAIA,SAAS,EAAE;YACbH,YAAY,CAACI,WAAW,EAAE;YAC1BN,OAAO,EAAE;UACX;QACF,CAAC,CAAC;QAEFO,UAAU,CAAC,MAAK;UACdL,YAAY,CAACI,WAAW,EAAE;UAC1BN,OAAO,EAAE;QACX,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IAAC;EACL;EAEQjB,iBAAiBA,CAAA;IACvB,MAAMyB,eAAe,GAAG,IAAI,CAACpD,qBAAqB,CAACqD,2BAA2B,CAC3EL,SAAS,CAAC,MAAK;MACd,IAAI,CAACM,kBAAkB,EAAE;IAC3B,CAAC,CAAC;IAEJ,MAAMC,iBAAiB,GAAG,IAAI,CAACvD,qBAAqB,CAACwD,gBAAgB,CAClER,SAAS,CAAES,OAAY,IAAI;MAC1B,IAAIA,OAAO,CAACC,KAAK,KAAK,IAAI,CAAClD,YAAY,EAAE;QACvC,IAAI,CAACmD,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;IAEJ,MAAMC,2BAA2B,GAAG,IAAI,CAAC5D,qBAAqB,CAAC6D,0BAA0B,CACtFb,SAAS,CAAEc,KAAU,IAAI;MACxB,IAAIA,KAAK,CAACJ,KAAK,KAAK,IAAI,CAAClD,YAAY,EAAE;QACrC,IAAI,CAACmD,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;IAEJ,MAAMI,gBAAgB,GAAG,IAAI,CAAC/D,qBAAqB,CAACgE,mBAAmB,CACpEhB,SAAS,CAAC,MAAK;MACd,IAAI,CAACM,kBAAkB,EAAE;IAC3B,CAAC,CAAC;IAEJ,IAAI,CAAC7C,aAAa,CAACwD,IAAI,CAACb,eAAe,EAAEG,iBAAiB,EAAEK,2BAA2B,EAAEG,gBAAgB,CAAC;EAC5G;EAEA/C,yBAAyBA,CAAA;IACvB,MAAMkD,YAAY,GAAG1F,gBAAgB,CAAC4C,8BAA8B,CAAC,2BAA2B,CAAC;IACjG,IAAI8C,YAAY,EAAE;MAChB,IAAI;QACF,IAAI,CAAC5D,iBAAiB,GAAG6D,IAAI,CAACC,KAAK,CAACF,YAAY,CAAC;MACnD,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAAC9B,iBAAiB,GAAG,IAAI;MAC/B;IACF;EACF;EAEA+D,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/D,iBAAiB;EAC/B;EAEcmB,wBAAwBA,CAAA;IAAA,IAAA6C,MAAA;IAAA,OAAAvD,iBAAA;MACpC,IAAI;QACFuD,MAAI,CAAC3D,iBAAiB,GAAG,IAAI;QAC7B2D,MAAI,CAAC3E,OAAO,CAAC4E,IAAI,EAAE;QAEnB,IAAID,MAAI,CAAC9D,YAAY,EACnB8D,MAAI,CAACvE,yBAAyB,CAACyE,eAAe,CAACF,MAAI,CAAC9D,YAAY,CAAC;QAEnE,MAAMiE,aAAa,GAAG;UACpB,GAAGH,MAAI,CAAChE,iBAAiB;UACzBoE,WAAW,EAAEJ,MAAI,CAAC9D;SACnB;QAED,MAAMmE,QAAQ,SAASnF,cAAc,CAAC8E,MAAI,CAAC1E,eAAe,CAACgF,4BAA4B,CAACH,aAAa,CAAC,CAAC;QAEvG,IAAIE,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAMP,MAAI,CAAChB,kBAAkB,EAAE;UAC/BgB,MAAI,CAACQ,2BAA2B,EAAE;UAElCR,MAAI,CAAC3D,iBAAiB,GAAG,KAAK;UAC9B2D,MAAI,CAACxE,GAAG,CAACiF,aAAa,EAAE;UACxBT,MAAI,CAAC3E,OAAO,CAACqF,IAAI,EAAE;QACrB,CAAC,MAAM;UACL7C,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEuC,QAAQ,CAACM,QAAQ,CAAC;UAChEX,MAAI,CAAC3D,iBAAiB,GAAG,KAAK;UAC9B2D,MAAI,CAAC3E,OAAO,CAACqF,IAAI,EAAE;QACrB;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9DkC,MAAI,CAAC3D,iBAAiB,GAAG,KAAK;QAC9B2D,MAAI,CAAC3E,OAAO,CAACqF,IAAI,EAAE;MACrB;IAAC;EACH;EAEAjD,gBAAgBA,CAAA;IACd,IAAI,CAACmD,mBAAmB,EAAE,CAACC,IAAI,CAAC,MAAK;MACnC,IAAI,CAACC,MAAM,EAAE;MACb,IAAI,CAACzF,OAAO,CAACqF,IAAI,EAAE;IACrB,CAAC,CAAC,CAACK,KAAK,CAACC,GAAG,IAAG;MACbnD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEkD,GAAG,CAAC;MAChD,IAAI,CAACF,MAAM,EAAE;MACb,IAAI,CAACzF,OAAO,CAACqF,IAAI,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAI,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAChF,YAAY,EAAE;MACrBmF,aAAa,CAAC,IAAI,CAACnF,YAAY,CAAC;IAClC;IAEA,IAAI,IAAI,CAACI,YAAY,EAAE;MACrB,IAAI,CAAC0E,mBAAmB,EAAE;MAC1B,IAAI,CAACnF,yBAAyB,CAACyF,qBAAqB,EAAE;IACxD;IAGA,IAAI,CAAC9F,MAAM,CAACwB,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAIAyC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACnD,YAAY,EAAE;MACtB2B,OAAO,CAACC,KAAK,CAAC,4DAA4D,CAAC;MAC3E;IACF;IAEA,MAAMqD,oBAAoB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE7D,IAAI,CAAChG,MAAM,CAACwB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,EAAE;MAC5CyE,WAAW,EAAE;QACXC,UAAU,EAAEH,oBAAoB;QAChC/B,KAAK,EAAE,IAAI,CAAClD;;KAEf,CAAC;EACJ;EAEQkF,yBAAyBA,CAAA;IAC/B,OAAOnE,IAAI,CAACsE,GAAG,EAAE;EACnB;EAEA;EACQtD,sBAAsBA,CAAA;IAC5B,MAAMuD,aAAa,GAAG,IAAI,CAAC9F,qBAAqB,CAAC+F,gBAAgB,EAAE;IACnE;IACA,IAAI,IAAI,CAACvF,YAAY,KAAKsF,aAAa,CAACtF,YAAY,EAAE;MACpD2B,OAAO,CAAC6D,IAAI,CAAC,uCAAuC,CAAC;MACrD7D,OAAO,CAAC6D,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAACxF,YAAY,CAAC;MACxD2B,OAAO,CAAC6D,IAAI,CAAC,oBAAoB,EAAEF,aAAa,CAACtF,YAAY,CAAC;IAChE;IAEA;IACA2C,UAAU,CAAC,MAAK;MACd,IAAI;QACF,IAAI,CAACnD,qBAAqB,CAACiG,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAACzF,YAAY,CAAC;MAC7E,CAAC,CAAC,OAAO4B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEA8D,uBAAuBA,CAAA;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACtG,MAAM,CAACuG,IAAI,CAACpH,2BAA2B,EAAE;MAC9DqH,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE;KACf,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAACvD,SAAS,CAACwD,MAAM,IAAG;MACzC,IAAIA,MAAM,KAAK,WAAW,EAAE;QAC1B,IAAI,CAACzE,gBAAgB,EAAE;MACzB;IACF,CAAC,CAAC;EACJ;EAEA0E,sBAAsBA,CAAA;IACpB,MAAMN,SAAS,GAAG,IAAI,CAACtG,MAAM,CAACuG,IAAI,CAACrH,0BAA0B,EAAE;MAC7DsH,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE;KACf,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAACvD,SAAS,CAACwD,MAAM,IAAG;MACzC,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAACzE,gBAAgB,EAAE;MACzB;IACF,CAAC,CAAC;EACJ;EAEA2E,wBAAwBA,CAAA;IACtB,MAAMP,SAAS,GAAG,IAAI,CAACtG,MAAM,CAACuG,IAAI,CAACtH,4BAA4B,EAAE;MAC/DuH,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE;KACf,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAACvD,SAAS,CAACwD,MAAM,IAAG;MACzC,IAAIA,MAAM,KAAK,SAAS,EACtB,IAAI,CAAC7C,eAAe,EAAE,CAAC,KACpB,IAAI6C,MAAM,KAAK,UAAU,EAAE;QAC9B,IAAI,CAACzE,gBAAgB,EAAE;MACzB;IACF,CAAC,CAAC;EACJ;EAGM4E,mBAAmBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7F,iBAAA;MACvB,IAAI6F,MAAI,CAACpG,YAAY,EAAE;QACrB,MAAMoG,MAAI,CAAC1B,mBAAmB,EAAE;MAClC;IAAC;EACH;EAEcA,mBAAmBA,CAAA;IAAA,IAAA2B,MAAA;IAAA,OAAA9F,iBAAA;MAC/B,IAAI8F,MAAI,CAACrG,YAAY,EAAE;QACrB,MAAMhB,cAAc,CAACqH,MAAI,CAAC9G,yBAAyB,CAACmF,mBAAmB,CAAC2B,MAAI,CAACrG,YAAY,CAAC,CAAC;MAC7F;IAAC;EACH;EAEQsE,2BAA2BA,CAAA;IAAA,IAAAgC,MAAA;IACjC,IAAI,IAAI,CAACtG,YAAY,EAAE;MACrB,IAAI,CAACJ,YAAY,GAAG2G,WAAW,eAAAhG,iBAAA,CAAC,aAAW;QACzC,IAAI;UACF,MAAM4D,QAAQ,SAASnF,cAAc,CAACsH,MAAI,CAAClH,eAAe,CAACoH,oBAAoB,CAACF,MAAI,CAACtG,YAAa,CAAC,CAAC;UACpG,IAAImE,QAAQ,CAACE,OAAO,EAAE;YACpBiC,MAAI,CAAC3G,WAAW,GAAGwE,QAAQ,CAACsC,WAAW;YACvCH,MAAI,CAAChH,GAAG,CAACiF,aAAa,EAAE;YAExB,IAAI+B,MAAI,CAAC3G,WAAW,KAAK,CAAC,EAAE;cAC1B2G,MAAI,CAACzG,kBAAkB,GAAG,IAAI;cAC9ByG,MAAI,CAACJ,wBAAwB,EAAE;cAC/BnB,aAAa,CAACuB,MAAI,CAAC1G,YAAY,CAAC;YAClC;UACF;QACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC,GAAE,KAAK,CAAC;IACX;EACF;EAEckB,kBAAkBA,CAAA;IAAA,IAAA4D,MAAA;IAAA,OAAAnG,iBAAA;MAC9B,IAAI,CAACmG,MAAI,CAAC1G,YAAY,EAAE;QACtB2B,OAAO,CAAC6D,IAAI,CAAC,6CAA6C,CAAC;QAC3D;MACF;MAEA,IAAI;QACF,MAAMrB,QAAQ,SAASnF,cAAc,CAAC0H,MAAI,CAACtH,eAAe,CAACoH,oBAAoB,CAACE,MAAI,CAAC1G,YAAY,CAAC,CAAC;QACnG,IAAImE,QAAQ,CAACE,OAAO,EAAE;UACpBqC,MAAI,CAAC/G,WAAW,GAAGwE,QAAQ,CAACsC,WAAW;UACvCC,MAAI,CAACpH,GAAG,CAACiF,aAAa,EAAE;QAC1B,CAAC,MACC5C,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEuC,QAAQ,CAACwC,QAAQ,CAAC;MACpE,CAAC,CAAC,OAAO/E,KAAU,EAAE;QACnBD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAIA,KAAK,CAACgF,MAAM,KAAK,GAAG,EACtBjF,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;MAC1D;IAAC;EACH;EAEA;;;EAGcI,4BAA4BA,CAAA;IAAA,IAAA6E,OAAA;IAAA,OAAAtG,iBAAA;MACxCoB,OAAO,CAACmF,GAAG,CAAC,wCAAwC,CAAC;MAErD;MACA,IAAI,CAACD,OAAI,CAAC/G,iBAAiB,EAAE;QAC3B6B,OAAO,CAAC6D,IAAI,CAAC,gEAAgE,CAAC;QAC9EqB,OAAI,CAACrG,yBAAyB,EAAE;MAClC;MAEA;MACA,IAAI,CAACqG,OAAI,CAAC9G,cAAc,EAAE;QACxB4B,OAAO,CAACmF,GAAG,CAAC,0EAA0E,CAAC;QACvFD,OAAI,CAACpG,sBAAsB,EAAE;QAE7B;QACA,IAAI,CAACoG,OAAI,CAAC9G,cAAc,EAAE;UACxB4B,OAAO,CAACmF,GAAG,CAAC,4FAA4F,CAAC;QAC3G;MACF;MAEAnF,OAAO,CAACmF,GAAG,CAAC,mCAAmC,EAAE;QAC/CC,YAAY,EAAE,CAAC,CAACF,OAAI,CAAC/G,iBAAiB;QACtCkH,SAAS,EAAE,CAAC,CAACH,OAAI,CAAC9G,cAAc;QAChCmD,KAAK,EAAE,CAAC,CAAC2D,OAAI,CAAC7G;OACf,CAAC;IAAC;EACL;EAEA;;;;EAIciC,sBAAsBA,CAAA;IAAA,IAAAgF,OAAA;IAAA,OAAA1G,iBAAA;MAClC,IAAI,CAAC0G,OAAI,CAACjH,YAAY,IAAI,CAACiH,OAAI,CAACnH,iBAAiB,EAAE;QACjD6B,OAAO,CAAC6D,IAAI,CAAC,iEAAiE,CAAC;QAC/E;MACF;MAEA,IAAIyB,OAAI,CAAC7G,kBAAkB,EAAE;QAC3BuB,OAAO,CAACmF,GAAG,CAAC,4CAA4C,CAAC;QACzD;MACF;MAEA,IAAI;QAEF,IAAI,CAACG,OAAI,CAAClH,cAAc,EAAE;UACxBkH,OAAI,CAACxG,sBAAsB,EAAE;QAC/B;QAEA;QACA,MAAMyG,WAAW,GAAGD,OAAI,CAACE,iCAAiC,EAAE;QAG5D;QACA,MAAMhD,QAAQ,SAASnF,cAAc,CACnCiI,OAAI,CAACxH,8BAA8B,CAAC2H,sBAAsB,CAACF,WAAW,CAAC,CACxE;QAED,IAAI/C,QAAQ,CAACkD,OAAO,EAAE;UACpBJ,OAAI,CAAC7G,kBAAkB,GAAG,IAAI;UAC9BuB,OAAO,CAACmF,GAAG,CAAC,oCAAoC,EAAE;YAChD5D,KAAK,EAAEiB,QAAQ,CAACmD,YAAY;YAC5BC,IAAI,EAAEpD,QAAQ,CAACqD,WAAW;YAC1BC,QAAQ,EAAEtD,QAAQ,CAACuD,SAAS;YAC5BC,iBAAiB,EAAE,CAAC,CAACV,OAAI,CAAClH,cAAc;YACxC6H,aAAa,EAAEC,MAAM,CAACC,IAAI,CAACZ,WAAW,CAAC,CAACa;WACzC,CAAC;UAEF;UACA/J,gBAAgB,CAACyD,yBAAyB,CAAC,qBAAqB,EAAEkC,IAAI,CAACqE,SAAS,CAAC;YAC/E9E,KAAK,EAAEiB,QAAQ,CAACmD,YAAY;YAC5BW,SAAS,EAAE,IAAIlH,IAAI,EAAE,CAACC,WAAW,EAAE;YACnCkH,cAAc,EAAE,CAAC,CAACjB,OAAI,CAAClH;WACxB,CAAC,CAAC;QAEL,CAAC,MAAM;UACL4B,OAAO,CAAC6D,IAAI,CAAC,iCAAiC,EAAErB,QAAQ,CAACgE,OAAO,CAAC;UACjExG,OAAO,CAAC6D,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACiE,MAAM,CAAC;UACvC;QACF;MACF,CAAC,CAAC,OAAOxG,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D;MACF;IAAC;EACH;EAEA;;;EAGQuF,iCAAiCA,CAAA;IACvC,IAAI,CAAC,IAAI,CAACrH,iBAAiB,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;MACjD,MAAM,IAAIqI,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA;IACA,IAAIC,cAAkC;IACtC,IAAI,IAAI,CAACxI,iBAAiB,CAACwI,cAAc,EAAE;MACzC,IAAI;QACF,MAAMC,IAAI,GAAG,IAAIxH,IAAI,CAAC,IAAI,CAACjB,iBAAiB,CAACwI,cAAc,CAAC;QAC5D,IAAI,CAACE,KAAK,CAACD,IAAI,CAACE,OAAO,EAAE,CAAC,EAAE;UAC1BH,cAAc,GAAGC,IAAI,CAACvH,WAAW,EAAE,CAAC0H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO9G,KAAK,EAAE;QACdD,OAAO,CAAC6D,IAAI,CAAC,0CAA0C,EAAE5D,KAAK,CAAC;MACjE;IACF;IAEA,IAAI+G,QAAQ,GAAG,IAAI,CAAC7I,iBAAiB,CAAC8I,cAAc;IAEpD,IAAIC,aAAa,GAAQ,EAAE;IAC3B,IAAI,IAAI,CAAC9I,cAAc,EAAE;MACvB,IAAI;QACF,MAAM+I,IAAI,GAAG,IAAI,CAAC/I,cAAc,CAAC+I,IAAI,IAAI,IAAI,CAAC/I,cAAc;QAE5D,IAAI+I,IAAI,EAAE;UACRD,aAAa,GAAG;YACdE,gBAAgB,EAAE,IAAI,CAACC,wBAAwB,CAACF,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACG,QAAQ,CAAC;YACvFC,iBAAiB,EAAE,IAAI,CAACF,wBAAwB,CAACF,IAAI,CAACI,iBAAiB,IAAIJ,IAAI,CAACK,SAAS,CAAC;YAC1FC,WAAW,EAAE,IAAI,CAACC,0BAA0B,CAACP,IAAI,CAACM,WAAW,CAAC;YAC9DE,iBAAiB,EAAE,IAAI,CAACN,wBAAwB,CAACF,IAAI,CAACS,UAAU,IAAIT,IAAI,CAACU,gBAAgB,CAAC;YAC1FC,kBAAkB,EAAE,IAAI,CAACT,wBAAwB,CAACF,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,SAAS;WACnF;UAEDhI,OAAO,CAACmF,GAAG,CAAC,sCAAsC,EAAE+B,aAAa,CAAC;QACpE;MACF,CAAC,CAAC,OAAOjH,KAAK,EAAE;QACdD,OAAO,CAAC6D,IAAI,CAAC,uCAAuC,EAAE5D,KAAK,CAAC;MAC9D;IACF;IAEA;IACA,MAAMgI,oBAAoB,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC/J,iBAAiB,CAACgK,cAAc,CAAC;IAC7F,MAAMC,qBAAqB,GAAG,IAAI,CAACF,oBAAoB,CAAC,IAAI,CAACG,sBAAsB,CAAC,IAAI,CAAClK,iBAAiB,CAACmK,aAAa,CAAC,CAAC;IAE1H,MAAM/C,WAAW,GAAG;MAClBhE,KAAK,EAAE,IAAI,CAAClD,YAAY;MAAE;MAC1BuH,IAAI,EAAE,IAAI,CAACzH,iBAAiB,CAACyH,IAAI;MACjCzG,GAAG,EAAE,IAAI,CAAChB,iBAAiB,CAACgB,GAAG,IAAI,EAAE;MACrCoJ,KAAK,EAAE,IAAI,CAACpK,iBAAiB,CAACoK,KAAK;MACnCC,QAAQ,EAAE,IAAI,CAACrK,iBAAiB,CAACqK,QAAQ;MACzC7B,cAAc,EAAEA,cAAc;MAC9BK,QAAQ,EAAEA,QAAQ;MAClBmB,cAAc,EAAEF,oBAAoB;MACpCQ,eAAe,EAAEL,qBAAqB;MACtCM,QAAQ,EAAE,IAAI,CAACvK,iBAAiB,CAACuK,QAAQ;MACzCC,cAAc,EAAE,IAAI,CAACxK,iBAAiB,CAACwK,cAAc;MACrDC,qBAAqB,EAAE,IAAI,CAACzK,iBAAiB,CAAC0K,WAAW;MACzD9C,SAAS,EAAE,UAAU;MAAE;MAEvB;MACA,GAAGmB;KACJ;IAEDlH,OAAO,CAACmF,GAAG,CAAC,+BAA+B,EAAEI,WAAW,CAAC;IACzD,OAAOA,WAAW;EACpB;EAEA;;;EAGQ8C,sBAAsBA,CAACS,UAAkB;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAOC,SAAS;IAEjC,MAAMC,KAAK,GAAGF,UAAU,CAACG,WAAW,EAAE;IAEtC,IAAID,KAAK,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACrD,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,OAAO,CAAC;IACV,CAAC,MAAM,IAAIF,KAAK,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACjC,MAAMC,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC,eAAe,CAAC;MAC1C,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGJ,SAAS;IAC/C,CAAC,MAAM,IAAIC,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACnC,MAAMC,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC,kBAAkB,CAAC;MAC7C,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3C,CAAC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;MACzD,MAAMC,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC,gBAAgB,CAAC;MAC3C,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;IAC7C;IAEA,OAAOJ,SAAS;EAClB;EAEA;;;EAGQb,oBAAoBA,CAACmB,KAAU;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKN,SAAS,EAAE;MACzC,OAAOA,SAAS;IAClB;IACA,OAAOO,MAAM,CAACD,KAAK,CAAC;EACtB;EAEQhC,wBAAwBA,CAACgC,KAAU;IACzC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKN,SAAS,EAAE;MACzC,OAAOA,SAAS;IAClB;IAEA;IACA,IAAI,OAAOM,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK,CAACE,QAAQ,EAAE;IACzB;IAEA;IACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK,CAACG,IAAI,EAAE,IAAIT,SAAS;IAClC;IAEA,OAAOO,MAAM,CAACD,KAAK,CAAC;EACtB;EAEQ3B,0BAA0BA,CAAC+B,WAAgB;IACjD,IAAIA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKV,SAAS,EAAE;MACrD,OAAOA,SAAS;IAClB;IAEA,IAAI;MACF,IAAIW,SAAiB;MAErB,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;QACnCC,SAAS,GAAGC,UAAU,CAACF,WAAW,CAAC;MACrC,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QAC1CC,SAAS,GAAGD,WAAW;MACzB,CAAC,MAAM;QACL,OAAOV,SAAS;MAClB;MAEA,IAAIlC,KAAK,CAAC6C,SAAS,CAAC,EAAE;QACpB,OAAOX,SAAS;MAClB;MAEA;MACA,OAAOa,IAAI,CAACC,KAAK,CAACH,SAAS,GAAG,EAAE,CAAC,CAACH,QAAQ,EAAE;IAC9C,CAAC,CAAC,OAAOtJ,KAAK,EAAE;MACdD,OAAO,CAAC6D,IAAI,CAAC,mCAAmC,EAAE5D,KAAK,CAAC;MACxD,OAAO8I,SAAS;IAClB;EACF;EAEA;;;EAGQjK,sBAAsBA,CAAA;IAC5B,IAAI;MAEF,IAAIV,cAAc,GAAG/B,gBAAgB,CAAC4C,8BAA8B,CAAC,gBAAgB,CAAC;MACtFe,OAAO,CAACmF,GAAG,CAAC,qBAAqB,EAAE/G,cAAc,CAAC;MAClD,IAAI,CAACA,cAAc,GAAGA,cAAc;IACtC,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC7B,cAAc,GAAG,IAAI;IAC5B;EACF;EAEA;;;EAGO0L,mBAAmBA,CAAA;IACxB,OAAO,IAAItJ,OAAO,CAAEC,OAAO,IAAI;MAC7B,MAAMuD,SAAS,GAAG,IAAI,CAACtG,MAAM,CAACuG,IAAI,CAAC7G,kCAAkC,EAAE;QACrE8G,KAAK,EAAE,OAAO;QACd6F,MAAM,EAAE,OAAO;QACf5F,YAAY,EAAE,IAAI;QAClBgD,IAAI,EAAE;OACP,CAAC;MAEFnD,SAAS,CAACI,WAAW,EAAE,CAACvD,SAAS,CAACwD,MAAM,IAAG;QACzC,IAAIA,MAAM,IAAIA,MAAM,CAAC2F,MAAM,KAAK,WAAW,IAAI3F,MAAM,CAAC8C,IAAI,EAAE;UAC1D,IAAI,CAAC/I,cAAc,GAAGiG,MAAM,CAAC8C,IAAI;UACjCnH,OAAO,CAACmF,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAAC/G,cAAc,CAAC;QAC7E;QACAqC,OAAO,CAAC4D,MAAM,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEc9E,oBAAoBA,CAAA;IAAA,IAAA0K,OAAA;IAAA,OAAArL,iBAAA;MAChC,IAAI,CAACqL,OAAI,CAAC5L,YAAY,EAAE;MAExB,IAAI;QACF,MAAMmE,QAAQ,SAASnF,cAAc,CAAC4M,OAAI,CAACxM,eAAe,CAACoH,oBAAoB,CAACoF,OAAI,CAAC5L,YAAY,CAAC,CAAC;QACnG,IAAImE,QAAQ,CAACE,OAAO,EAAE;UACpBuH,OAAI,CAACjM,WAAW,GAAGwE,QAAQ,CAACsC,WAAW;UACvCmF,OAAI,CAACtH,2BAA2B,EAAE;UAClCsH,OAAI,CAACtM,GAAG,CAACiF,aAAa,EAAE;QAC1B,CAAC,MAAM;UACL,MAAMqH,OAAI,CAAC3K,wBAAwB,EAAE;QACvC;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMgK,OAAI,CAAC3K,wBAAwB,EAAE;MACvC;IAAC;EACH;;qBAroBWhC,mBAAmB,EAAA4M,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAnO,MAAA,GAAAiO,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAjO,cAAA,GAAA8N,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAnO,eAAA,GAAA+N,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAzN,SAAA,GAAAoN,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAlN,iBAAA,GAAAkN,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAvN,yBAAA,GAAAiN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAvN,qBAAA,GAAAgN,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAvN,8BAAA;EAAA;;UAAnBG,mBAAmB;IAAAqN,SAAA;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAnBZ,EAAA,CAAAc,UAAA,0BAAAC,oDAAAC,MAAA;UAAA,OAAAH,GAAA,CAAAvG,mBAAA,CAAA0G,MAAA,CAA2B;QAAA,UAAAhB,EAAA,CAAAiB,eAAA,CAAR;;;;;;;;;;QCvD5BjB,EAFJ,CAAAkB,cAAA,aAA4B,aACM,aACA;QAC5BlB,EAAA,CAAAmB,SAAA,aAA8B;QAC9BnB,EAAA,CAAAkB,cAAA,cAA0B;QAAAlB,EAAA,CAAAoB,MAAA,qBAAc;QAC1CpB,EAD0C,CAAAqB,YAAA,EAAO,EAC3C;QAENrB,EAAA,CAAAkB,cAAA,YAA0B;QAAAlB,EAAA,CAAAoB,MAAA,GAA6E;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAC5GrB,EAAA,CAAAkB,cAAA,WAA4B;QAAAlB,EAAA,CAAAoB,MAAA,8DAA6C;QAAApB,EAAA,CAAAqB,YAAA,EAAI;QAG7ErB,EAAA,CAAAkB,cAAA,cAAiC;QAC/BlB,EAAA,CAAAmB,SAAA,YAAsD;QACtDnB,EAAA,CAAAkB,cAAA,YAAM;QAAAlB,EAAA,CAAAoB,MAAA,0BAAa;QACrBpB,EADqB,CAAAqB,YAAA,EAAO,EACtB;QAIFrB,EAFJ,CAAAkB,cAAA,cAAgC,eACkB,aACpB;QAAAlB,EAAA,CAAAoB,MAAA,6BAAW;QAAApB,EAAA,CAAAqB,YAAA,EAAI;QAEvCrB,EADF,CAAAkB,cAAA,eAA6B,gBACG;QAAAlB,EAAA,CAAAoB,MAAA,IAAkB;QAEpDpB,EAFoD,CAAAqB,YAAA,EAAO,EACnD,EACF;QAGJrB,EADF,CAAAkB,cAAA,eAA6C,kBACgB;QACzDlB,EAAA,CAAAmB,SAAA,aAAuD;QACvDnB,EAAA,CAAAoB,MAAA,gBACF;QAEJpB,EAFI,CAAAqB,YAAA,EAAS,EACL,EACF;QAGJrB,EADF,CAAAkB,cAAA,eAA8C,kBACM;QAAtBlB,EAAA,CAAAc,UAAA,mBAAAQ,sDAAA;UAAA,OAAST,GAAA,CAAApL,SAAA,EAAW;QAAA,EAAC;QAC/CuK,EAAA,CAAAmB,SAAA,aAAuD;QACvDnB,EAAA,CAAAoB,MAAA,8BACF;QACFpB,EADE,CAAAqB,YAAA,EAAS,EACL;QAGJrB,EADF,CAAAkB,cAAA,eAA+B,eACI;QAC/BlB,EAAA,CAAAmB,SAAA,aAA6D;QAC7DnB,EAAA,CAAAoB,MAAA,0CACF;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QAGFrB,EAFJ,CAAAkB,cAAA,eAAkC,eACF,eACI;QAAAlB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QACvCrB,EAAA,CAAAkB,cAAA,eAA8B;QAC5BlB,EAAA,CAAAoB,MAAA,qGACF;QACFpB,EADE,CAAAqB,YAAA,EAAM,EACF;QAEJrB,EADF,CAAAkB,cAAA,eAA8B,eACI;QAAAlB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QACvCrB,EAAA,CAAAkB,cAAA,eAA8B;QAC5BlB,EAAA,CAAAoB,MAAA,2EACF;QACFpB,EADE,CAAAqB,YAAA,EAAM,EACF;QAEJrB,EADF,CAAAkB,cAAA,eAA8B,eACI;QAAAlB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QACvCrB,EAAA,CAAAkB,cAAA,eAA8B;QAC5BlB,EAAA,CAAAoB,MAAA,2FACF;QACFpB,EADE,CAAAqB,YAAA,EAAM,EACF;QAEJrB,EADF,CAAAkB,cAAA,eAA8B,eACI;QAAAlB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QACvCrB,EAAA,CAAAkB,cAAA,eAA8B;QAC5BlB,EAAA,CAAAoB,MAAA,wFACF;QACFpB,EADE,CAAAqB,YAAA,EAAM,EACF;QAEJrB,EADF,CAAAkB,cAAA,eAA8B,eACI;QAAAlB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAM;QACvCrB,EAAA,CAAAkB,cAAA,eAA8B;QAC5BlB,EAAA,CAAAoB,MAAA,iGACF;QAKVpB,EALU,CAAAqB,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;;QAvEwBrB,EAAA,CAAAuB,SAAA,GAA6E;QAA7EvB,EAAA,CAAAwB,kBAAA,eAAAX,GAAA,CAAA5M,iBAAA,kBAAA4M,GAAA,CAAA5M,iBAAA,CAAAyH,IAAA,YAAAmF,GAAA,CAAA5M,iBAAA,kBAAA4M,GAAA,CAAA5M,iBAAA,CAAAyH,IAAA,YAA6E;QAanEsE,EAAA,CAAAuB,SAAA,IAAkB;QAAlBvB,EAAA,CAAAyB,iBAAA,CAAAZ,GAAA,CAAA/M,WAAA,CAAkB;;;mBD2BtDtB,cAAc,EACdJ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBN,YAAY,EACZO,kBAAkB,EAClBM,eAAe;IAAA6O,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ChangeDetectorRef } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/forms\";\nfunction HistoryModalDialogComponent_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"clear\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HistoryModalDialogComponent_mat_chip_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 39);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_mat_chip_option_39_Template_mat_chip_option_click_0_listener() {\n      const filter_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleFilter(filter_r4));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r4 = ctx.$implicit;\n    i0.ɵɵclassMap(\"filter-chip-\" + filter_r4.type);\n    i0.ɵɵproperty(\"selected\", filter_r4.active);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r4.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r4.label, \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"search_off\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"h3\", 42);\n    i0.ɵɵtext(5, \"Nenhuma informa\\u00E7\\u00E3o encontrada\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 43);\n    i0.ɵɵtext(7, \" Tente ajustar os filtros ou termo de busca \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HistoryModalDialogComponent_div_43_div_1_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatTimestamp(item_r6.timestamp), \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 49)(6, \"h4\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 52)(11, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_div_43_div_1_Template_button_click_11_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.copyToClipboard(item_r6.value));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"content_copy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_div_43_div_1_Template_button_click_14_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editValue(item_r6));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"edit\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 55)(18, \"div\", 56);\n    i0.ɵɵelement(19, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 58);\n    i0.ɵɵtemplate(21, HistoryModalDialogComponent_div_43_div_1_span_21_Template, 4, 1, \"span\", 59);\n    i0.ɵɵelementStart(22, \"span\", 60)(23, \"mat-icon\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"card-\" + item_r6.category);\n    i0.ɵɵproperty(\"@cardAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r6.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.categoryLabel);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.highlightSearchTerm(item_r6.value), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r6.timestamp);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"status-\" + item_r6.validationStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getValidationIcon(item_r6.validationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getValidationLabel(item_r6.validationStatus), \" \");\n  }\n}\nfunction HistoryModalDialogComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, HistoryModalDialogComponent_div_43_div_1_Template, 26, 12, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getFilteredData())(\"ngForTrackBy\", ctx_r1.trackByFn);\n  }\n}\nexport let HistoryModalDialogComponent = /*#__PURE__*/(() => {\n  class HistoryModalDialogComponent {\n    dialogRef;\n    data;\n    searchTerm = '';\n    availableFilters = [{\n      type: 'personal',\n      label: 'Dados Pessoais',\n      icon: 'person',\n      active: true\n    }, {\n      type: 'medical',\n      label: 'Informações Médicas',\n      icon: 'medical_services',\n      active: true\n    }, {\n      type: 'contact',\n      label: 'Contato',\n      icon: 'contact_phone',\n      active: true\n    }, {\n      type: 'optional',\n      label: 'Opcionais',\n      icon: 'info',\n      active: true\n    }];\n    constructor(dialogRef, data) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n    }\n    onClose() {\n      this.dialogRef.close();\n    }\n    getTotalCampos() {\n      return 10;\n    }\n    getDadosPreenchidos() {\n      const labels = {\n        nome: 'Nome',\n        cpf: 'CPF',\n        email: 'Email',\n        telefone: 'Telefone',\n        dataNascimento: 'Data de Nascimento',\n        alergias: 'Alergias',\n        sintomas: 'Sintomas',\n        intensidadeDor: 'Intensidade da Dor',\n        tempoSintomas: 'Tempo dos Sintomas',\n        doencasPrevias: 'Doenças Prévias',\n        observacoes: 'Observações'\n      };\n      return Object.keys(this.data.dadosColetados).filter(key => {\n        const value = this.data.dadosColetados[key];\n        return value && value.trim() !== '';\n      }).map(key => ({\n        label: labels[key],\n        value: this.data.dadosColetados[key]\n      }));\n    }\n    getProgressPercentage() {\n      const total = this.getTotalCampos();\n      const filled = this.getDadosPreenchidos().length;\n      return Math.round(filled / total * 100);\n    }\n    onSearchChange(event) {\n      this.searchTerm = event.target.value;\n    }\n    clearSearch() {\n      this.searchTerm = '';\n    }\n    toggleFilter(filter) {\n      filter.active = !filter.active;\n    }\n    getFilteredData() {\n      let data = this.getEnhancedDadosPreenchidos();\n      if (this.searchTerm) {\n        const searchLower = this.searchTerm.toLowerCase();\n        data = data.filter(item => item.label.toLowerCase().includes(searchLower) || item.value.toLowerCase().includes(searchLower));\n      }\n      const activeCategories = this.availableFilters.filter(f => f.active).map(f => f.type);\n      data = data.filter(item => activeCategories.includes(item.category));\n      return data;\n    }\n    getEnhancedDadosPreenchidos() {\n      const categoryMap = {\n        nome: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'person'\n        },\n        cpf: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'badge'\n        },\n        dataNascimento: {\n          category: 'personal',\n          categoryLabel: 'Dados Pessoais',\n          icon: 'cake'\n        },\n        email: {\n          category: 'contact',\n          categoryLabel: 'Contato',\n          icon: 'email'\n        },\n        telefone: {\n          category: 'contact',\n          categoryLabel: 'Contato',\n          icon: 'phone'\n        },\n        sintomas: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'medical_services'\n        },\n        intensidadeDor: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'personal_injury'\n        },\n        tempoSintomas: {\n          category: 'medical',\n          categoryLabel: 'Informações Médicas',\n          icon: 'schedule'\n        },\n        alergias: {\n          category: 'optional',\n          categoryLabel: 'Opcionais',\n          icon: 'medical_information'\n        },\n        observacoes: {\n          category: 'optional',\n          categoryLabel: 'Opcionais',\n          icon: 'description'\n        }\n      };\n      const labels = {\n        nome: 'Nome',\n        cpf: 'CPF',\n        email: 'Email',\n        telefone: 'Telefone',\n        dataNascimento: 'Data de Nascimento',\n        alergias: 'Alergias',\n        sintomas: 'Sintomas',\n        intensidadeDor: 'Intensidade da Dor',\n        tempoSintomas: 'Tempo dos Sintomas',\n        doencasPrevias: 'Doenças Prévias',\n        observacoes: 'Observações'\n      };\n      return Object.keys(this.data.dadosColetados).filter(key => {\n        const value = this.data.dadosColetados[key];\n        return value && value.trim() !== '';\n      }).map(key => {\n        const categoryInfo = categoryMap[key] || {\n          category: 'optional',\n          categoryLabel: 'Outros',\n          icon: 'info'\n        };\n        return {\n          label: labels[key] || key,\n          value: this.data.dadosColetados[key],\n          category: categoryInfo.category,\n          categoryLabel: categoryInfo.categoryLabel,\n          icon: categoryInfo.icon,\n          timestamp: new Date(),\n          validationStatus: this.getValidationStatusForField(key)\n        };\n      });\n    }\n    getValidationStatusForField(field) {\n      const value = this.data.dadosColetados[field];\n      if (!value || value.trim() === '') return 'error';\n      if (field === 'email' && !value.includes('@')) return 'warning';\n      if (field === 'cpf' && value.length < 11) return 'warning';\n      return 'valid';\n    }\n    trackByFn(index, item) {\n      console.log('index', index);\n      return item.label + item.value;\n    }\n    highlightSearchTerm(text) {\n      if (!this.searchTerm) return text;\n      const regex = new RegExp(`(${this.searchTerm})`, 'gi');\n      return text.replace(regex, '<mark>$1</mark>');\n    }\n    formatTimestamp(timestamp) {\n      return timestamp.toLocaleTimeString('pt-BR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getValidationIcon(status) {\n      switch (status) {\n        case 'valid':\n          return 'check_circle';\n        case 'warning':\n          return 'warning';\n        case 'error':\n          return 'error';\n        default:\n          return 'info';\n      }\n    }\n    getValidationLabel(status) {\n      switch (status) {\n        case 'valid':\n          return 'Válido';\n        case 'warning':\n          return 'Atenção';\n        case 'error':\n          return 'Erro';\n        default:\n          return 'Info';\n      }\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');\n      }).catch(() => {\n        if (this.data.snackBar) this.data.snackBar.falhaSnackbar('Erro ao copiar texto');\n      });\n    }\n    editValue(item) {\n      const newValue = prompt(`Editar ${item.label}:`, item.value);\n      if (newValue !== null && newValue !== item.value) {\n        const field = Object.keys(this.data.dadosColetados).find(key => {\n          const labels = {\n            nome: 'Nome',\n            cpf: 'CPF',\n            email: 'Email',\n            telefone: 'Telefone',\n            dataNascimento: 'Data de Nascimento',\n            alergias: 'Alergias',\n            sintomas: 'Sintomas',\n            intensidadeDor: 'Intensidade da Dor',\n            tempoSintomas: 'Tempo dos Sintomas',\n            doencasPrevias: 'Doenças Prévias',\n            observacoes: 'Observações'\n          };\n          return labels[key] === item.label;\n        });\n        if (field) {\n          this.data.dadosColetados[field] = newValue;\n          if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Valor atualizado com sucesso');\n          if (this.data.cdr) this.data.cdr.detectChanges();\n        }\n      }\n    }\n    clearAllData() {\n      if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {\n        Object.keys(this.data.dadosColetados).forEach(key => {\n          this.data.dadosColetados[key] = '';\n        });\n        if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Todos os dados foram limpos');\n        if (this.data.cdr) this.data.cdr.detectChanges();\n      }\n    }\n    exportData(event) {\n      event.stopPropagation();\n      const dataStr = JSON.stringify(this.data.dadosColetados, null, 2);\n      const blob = new Blob([dataStr], {\n        type: 'application/json'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'dados-preenchidos.json';\n      a.click();\n      window.URL.revokeObjectURL(url);\n    }\n    static ɵfac = function HistoryModalDialogComponent_Factory(t) {\n      return new (t || HistoryModalDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HistoryModalDialogComponent,\n      selectors: [[\"app-history-modal-dialog\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 59,\n      vars: 13,\n      consts: [[\"role\", \"dialog\", \"aria-modal\", \"true\", \"aria-labelledby\", \"modal-title\", \"aria-describedby\", \"modal-description\", 1, \"modern-modal-overlay\", 3, \"click\"], [1, \"modern-modal-container\", 3, \"click\"], [1, \"modal-header-modern\"], [1, \"header-content\"], [1, \"title-section\"], [1, \"icon-wrapper\"], [1, \"header-icon\"], [1, \"title-text\"], [\"id\", \"modal-title\", 1, \"modal-title\"], [\"id\", \"modal-description\", 1, \"modal-subtitle\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Exportar dados\", \"aria-label\", \"Exportar dados coletados\", 1, \"action-btn\", \"secondary\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Fechar modal\", \"aria-label\", \"Fechar modal\", 1, \"action-btn\", \"close-btn\", 3, \"click\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"progress-text\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"search-filter-section\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Digite para buscar...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matPrefix\", \"\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"aria-label\", \"Limpar busca\", 3, \"click\", 4, \"ngIf\"], [1, \"filter-chips\"], [1, \"filter-chip-list\"], [3, \"selected\", \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-body-modern\"], [1, \"content-wrapper\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"data-grid\", 4, \"ngIf\"], [1, \"modal-footer-modern\"], [1, \"footer-info\"], [1, \"info-text\"], [1, \"footer-actions\"], [\"mat-stroked-button\", \"\", 1, \"secondary-btn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"primary-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"aria-label\", \"Limpar busca\", 3, \"click\"], [3, \"click\", \"selected\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"data-grid\"], [\"class\", \"data-card\", 3, \"class\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"data-card\"], [1, \"card-header\"], [1, \"card-icon\"], [1, \"card-title-section\"], [1, \"card-title\"], [1, \"card-category\"], [1, \"card-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copiar valor\", \"aria-label\", \"Copiar valor\", 1, \"card-action-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Editar valor\", \"aria-label\", \"Editar valor\", 1, \"card-action-btn\", 3, \"click\"], [1, \"card-content\"], [1, \"value-container\"], [1, \"card-value\", 3, \"innerHTML\"], [1, \"card-metadata\"], [\"class\", \"timestamp\", 4, \"ngIf\"], [1, \"validation-status\"], [1, \"timestamp\"]],\n      template: function HistoryModalDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_div_click_0_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_div_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"header\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"mat-icon\", 6);\n          i0.ɵɵtext(7, \"history\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"h2\", 8);\n          i0.ɵɵtext(10, \"Hist\\u00F3rico de Informa\\u00E7\\u00F5es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \" Dados coletados durante a conversa \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_14_listener($event) {\n            return ctx.exportData($event);\n          });\n          i0.ɵɵelementStart(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"download\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_17_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"close\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"div\", 14)(22, \"span\", 15);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 16);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 17);\n          i0.ɵɵelement(27, \"div\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"mat-form-field\", 21)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Buscar informa\\u00E7\\u00E3o\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HistoryModalDialogComponent_Template_input_ngModelChange_33_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function HistoryModalDialogComponent_Template_input_input_33_listener($event) {\n            return ctx.onSearchChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-icon\", 23);\n          i0.ɵɵtext(35, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, HistoryModalDialogComponent_button_36_Template, 3, 0, \"button\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 25)(38, \"mat-chip-listbox\", 26);\n          i0.ɵɵtemplate(39, HistoryModalDialogComponent_mat_chip_option_39_Template, 4, 5, \"mat-chip-option\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"main\", 28)(41, \"div\", 29);\n          i0.ɵɵtemplate(42, HistoryModalDialogComponent_div_42_Template, 8, 0, \"div\", 30)(43, HistoryModalDialogComponent_div_43_Template, 2, 2, \"div\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"footer\", 32)(45, \"div\", 33)(46, \"span\", 34)(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Dados salvos automaticamente \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 35)(51, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_51_listener() {\n            return ctx.clearAllData();\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"delete_sweep\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54, \" Limpar Tudo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function HistoryModalDialogComponent_Template_button_click_55_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelementStart(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(58, \" Conclu\\u00EDdo \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@fadeInOut\", undefined);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"@slideInOut\", undefined);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate2(\" \", ctx.getDadosPreenchidos().length, \" de \", ctx.getTotalCampos(), \" campos preenchidos \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProgressPercentage(), \"% \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.getProgressPercentage(), \"%\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableFilters);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFilteredData().length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getFilteredData().length > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.getDadosPreenchidos().length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, MatButtonModule, i3.MatButton, i3.MatIconButton, MatIconModule, i4.MatIcon, MatInputModule, i5.MatInput, i6.MatFormField, i6.MatLabel, i6.MatPrefix, i6.MatSuffix, MatFormFieldModule, MatTooltipModule, i7.MatTooltip, MatChipsModule, i8.MatChipListbox, i8.MatChipOption, MatDialogModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, ReactiveFormsModule],\n      styles: [\"@charset \\\"UTF-8\\\";.ai-questionnaire-container[_ngcontent-%COMP%]{height:100vh;height:100dvh;max-height:100vh;max-height:100dvh;background:linear-gradient(135deg,#667eea,#764ba2);font-family:Inter,-apple-system,BlinkMacSystemFont,sans-serif;font-size:clamp(.75rem,1.8vw,.9rem);position:relative;overflow:hidden;display:flex;flex-direction:column}.idle-screen[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:100%;padding:clamp(.25rem,1.5vw,.5rem);overflow:hidden}.idle-content[_ngcontent-%COMP%]{text-align:center;background:#fffffff2;padding:clamp(1rem,3vw,1.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(.5rem,1.5vw,1rem);box-shadow:0 .25rem .75rem #0000001a;max-width:min(80vw,20rem);width:100%;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);border:1px solid rgba(255,255,255,.2);max-height:70vh;overflow:hidden}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2d3748;font-size:clamp(1rem,2.5vw,1.125rem);font-weight:700;margin:clamp(.25rem,1vw,.5rem) 0 clamp(.25rem,.5vw,.25rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;font-size:clamp(.75rem,2vw,.875rem);margin-bottom:clamp(.5rem,2vw,1rem);line-height:1.3}.action-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.5rem,2vw,1rem);align-items:center;width:100%}.ai-robot[_ngcontent-%COMP%]{display:inline-block;animation:_ngcontent-%COMP%_float 3s ease-in-out infinite;transform:scale(1.1)}@media (max-width: 768px){.ai-robot[_ngcontent-%COMP%]{transform:scale(1)}}@media (max-width: 480px){.ai-robot[_ngcontent-%COMP%]{transform:scale(.9)}}.robot-head[_ngcontent-%COMP%]{width:clamp(3.5rem,8vw,5rem);height:clamp(3.5rem,8vw,5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:clamp(1rem,3vw,1.25rem);position:relative;margin:0 auto clamp(.5rem,2vw,1rem);box-shadow:0 .5rem 1.25rem #667eea4d}.robot-eyes[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:clamp(1rem,4vw,1.25rem) clamp(.5rem,3vw,1rem) 0}.eye[_ngcontent-%COMP%]{width:clamp(.5rem,1.5vw,.75rem);height:clamp(.5rem,1.5vw,.75rem);background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_blink 3s infinite}.eye.left-eye[_ngcontent-%COMP%]{animation-delay:.1s}.eye.right-eye[_ngcontent-%COMP%]{animation-delay:.2s}.robot-mouth[_ngcontent-%COMP%]{width:clamp(1rem,2.5vw,1.25rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:0 0 .625rem .625rem;margin:clamp(.4rem,1vw,.5rem) auto 0}.robot-body[_ngcontent-%COMP%]{width:clamp(2.5rem,6vw,3.75rem);height:clamp(1.5rem,4vw,2.5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:clamp(.75rem,2vw,1rem);margin:0 auto;position:relative}.robot-chest[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.5rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:50%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);animation:_ngcontent-%COMP%_pulse 2s infinite}.start-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#f093fb);border:none;padding:clamp(1rem,3vw,1.25rem) clamp(2rem,5vw,2.8125rem);border-radius:3.125rem;color:#fff;font-size:clamp(1.125rem,3vw,1.5rem);font-weight:700;cursor:pointer;position:relative;overflow:hidden;transition:all .3s ease;box-shadow:0 .5rem 1.5625rem #4facfe4d}.start-btn[_ngcontent-%COMP%]:hover{transform:translateY(-.125rem);box-shadow:0 .75rem 2.1875rem #4facfe66}.start-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;z-index:2}.btn-glow[_ngcontent-%COMP%]{position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);transition:left .5s}.start-btn[_ngcontent-%COMP%]:hover   .btn-glow[_ngcontent-%COMP%]{left:100%}.chat-interface[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;position:relative;overflow:hidden}.controls-header[_ngcontent-%COMP%]{flex-shrink:0;display:flex;align-items:center;justify-content:center;gap:clamp(.25rem,1vw,.5rem);background:#fffffff2;padding:clamp(.25rem,.8vw,.5rem) clamp(.5rem,1.5vw,1rem);border-radius:0 0 clamp(.5rem,1.5vw,1rem) clamp(.5rem,1.5vw,1rem);box-shadow:0 2px 8px #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);max-width:100%;box-sizing:border-box;min-height:44px}.mode-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:clamp(1.25rem,2.5vw,1.5rem);height:clamp(1.25rem,2.5vw,1.5rem);background:linear-gradient(135deg,#667eea,#764ba2);border-radius:50%;color:#fff;flex-shrink:0}.mode-indicator[_ngcontent-%COMP%]   .mode-icon[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.875rem);width:clamp(.75rem,1.5vw,.875rem);height:clamp(.75rem,1.5vw,.875rem)}.mode-toggle[_ngcontent-%COMP%]{font-weight:500;color:#2d3748;font-size:clamp(.75rem,1.2vw,.75rem);white-space:nowrap}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch{--mdc-switch-track-width: 28px;--mdc-switch-track-height: 16px}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle{--mdc-switch-handle-width: 12px;--mdc-switch-handle-height: 12px}.mode-toggle[_ngcontent-%COMP%]     .mdc-switch__ripple{min-width:44px;min-height:44px}.main-chat-area[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;gap:clamp(.25rem,1vw,.5rem);padding:clamp(.5rem,1.5vw,1rem) clamp(.25rem,1vw,.5rem) clamp(.25rem,1vw,.5rem);max-width:100vw;margin:0;width:100%;min-height:0;overflow:hidden;box-sizing:border-box}.response-data-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);width:100%;align-items:center;max-width:100%;flex:1;overflow:hidden;min-height:0}@media (min-width: 1024px){.response-data-section[_ngcontent-%COMP%]{flex-direction:row;align-items:flex-start;justify-content:space-between;gap:clamp(.5rem,1.5vw,1rem)}}@media (min-width: 1280px){.response-data-section[_ngcontent-%COMP%]{gap:clamp(1rem,2vw,1.5rem)}}.ai-section[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.ai-avatar[_ngcontent-%COMP%]{width:clamp(2.5rem,6vw,3.5rem);height:clamp(2.5rem,6vw,3.5rem);background:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;flex-shrink:0}.ai-avatar.processing[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_processing-pulse 2s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #667eea80}.ai-avatar.listening[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_listening-pulse 1s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #f093fb80}.ai-avatar.waiting[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_waiting-pulse 2s infinite;box-shadow:0 0 clamp(.5rem,1.5vw,.75rem) #4facfe80}.ai-face[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:clamp(.15rem,.5vw,.25rem)}.ai-eyes[_ngcontent-%COMP%]{display:flex;gap:clamp(.25rem,.8vw,.4rem)}.ai-eyes[_ngcontent-%COMP%]   .eye[_ngcontent-%COMP%]{width:clamp(.2rem,.5vw,.3rem);height:clamp(.2rem,.5vw,.3rem);background:#667eea;border-radius:50%;animation:_ngcontent-%COMP%_ai-blink 4s infinite}.ai-mouth[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.6rem);height:clamp(.15rem,.4vw,.2rem);background:#667eea;border-radius:0 0 .3rem .3rem;transition:all .3s ease}.ai-mouth.talking[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_mouth-talk .5s infinite alternate}.ai-pulse[_ngcontent-%COMP%]{position:absolute;top:clamp(-.25rem,-.8vw,-.3rem);left:clamp(-.25rem,-.8vw,-.3rem);right:clamp(-.25rem,-.8vw,-.3rem);bottom:clamp(-.25rem,-.8vw,-.3rem);border:clamp(1px,.3vw,2px) solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_pulse-ring 2s infinite}.response-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:flex-start;align-items:center;width:100%;max-width:100%;flex:1;overflow:hidden;min-height:0}@media (min-width: 1024px){.response-section[_ngcontent-%COMP%]{align-items:flex-start;max-width:none}}.ai-message[_ngcontent-%COMP%]{max-width:100%;margin-bottom:clamp(.25rem,1vw,.5rem);animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out;flex-shrink:0}.message-bubble[_ngcontent-%COMP%]{background:#fff;padding:clamp(.5rem,2vw,1rem) clamp(1rem,3vw,1.5rem);border-radius:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000001a;position:relative;max-height:40vh;overflow-y:auto}.message-bubble[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:clamp(.5rem,2vw,1rem);bottom:clamp(-.25rem,-.5vw,-.3rem);width:0;height:0;border-left:clamp(.25rem,.8vw,.3rem) solid transparent;border-right:clamp(.25rem,.8vw,.3rem) solid transparent;border-top:clamp(.25rem,.8vw,.3rem) solid #ffffff}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#2d3748;font-size:clamp(.75rem,2vw,.875rem);line-height:1.4;font-weight:500}.processing-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;justify-content:center;gap:clamp(.5rem,2vw,1rem);color:#718096;padding:clamp(1rem,3vw,1.5rem)}.processing-indicator[_ngcontent-%COMP%]   .processing-text[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem);font-weight:500;color:#fff;margin-top:clamp(.25rem,1vw,.5rem)}.typing-dots[_ngcontent-%COMP%]{display:flex;gap:clamp(.2rem,.5vw,.25rem)}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:clamp(.4rem,1vw,.5rem);height:clamp(.4rem,1vw,.5rem);background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}.data-section[_ngcontent-%COMP%]{width:100%;max-width:100%;margin-top:clamp(.25rem,1vw,.5rem);flex-shrink:0;overflow:hidden;max-height:30vh}@media (min-width: 1024px){.data-section[_ngcontent-%COMP%]{margin-top:0;max-width:min(35vw,18rem);width:auto;min-width:15rem}}@media (min-width: 1280px){.data-section[_ngcontent-%COMP%]{max-width:min(30vw,20rem)}}.data-panel[_ngcontent-%COMP%]{background:#ffffffe6;border-radius:clamp(.5rem,1.5vw,1rem);padding:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000000d;width:100%;animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out;border:1px solid rgba(102,126,234,.1);max-height:100%;overflow:hidden;display:flex;flex-direction:column}.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 clamp(.25rem,1vw,.5rem) 0;color:#2d3748;font-size:clamp(.875rem,2vw,1rem);font-weight:600;display:flex;align-items:center;gap:clamp(.25rem,1vw,.5rem);border-bottom:1px solid rgba(102,126,234,.1);padding-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.data-panel[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4cb\\\";font-size:clamp(.75rem,1.5vw,.875rem)}.data-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);max-height:20vh!important;overflow-y:auto!important;flex:1;min-height:0}.data-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:clamp(.25rem,1vw,.5rem);flex-shrink:0}.data-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:44px;min-height:44px;display:flex;align-items:center;justify-content:center}.data-item[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem);background:#f8fafc;border-radius:clamp(.25rem,1vw,.5rem);border-left:clamp(2px,.3vw,3px) solid #4facfe;flex-shrink:0}.data-item[_ngcontent-%COMP%]   .descInfoCategoria[_ngcontent-%COMP%]{display:block;font-size:clamp(.75rem,1.8vw,.875rem);color:#718096;font-weight:500;margin-bottom:clamp(.1rem,.3vw,.15rem)}.data-item[_ngcontent-%COMP%]   .descInfovalue[_ngcontent-%COMP%]{display:block;color:#2d3748;font-weight:600;font-size:clamp(.75rem,2vw,.875rem);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.progress-info[_ngcontent-%COMP%]{margin-top:clamp(.25rem,1.5vw,.5rem);padding-top:clamp(.25rem,1vw,.5rem);border-top:1px solid rgba(102,126,234,.1);flex-shrink:0}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{height:clamp(3px,.8vw,4px);background:#f1f5f9;border-radius:9999px;overflow:hidden;margin-bottom:clamp(.25rem,.8vw,.25rem)}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4facfe,#667eea);border-radius:9999px;transition:width .3s cubic-bezier(.4,0,.2,1);position:relative}.progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.75rem);color:#64748b;font-weight:500}.history-btn[_ngcontent-%COMP%]{transition:all .2s ease;min-width:44px!important;min-height:44px!important}.history-btn[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea;transform:scale(1.05)}.input-section[_ngcontent-%COMP%]{flex-shrink:0;padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);background:#fffffff2;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);display:flex;justify-content:center;align-items:center;border-top:1px solid rgba(102,126,234,.1);box-shadow:0 -2px 8px #0000000d;min-height:60px}@media (max-width: 480px){.input-section[_ngcontent-%COMP%]{padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);min-height:56px}}.input-container[_ngcontent-%COMP%]{max-width:100%;width:100%;display:flex;flex-direction:column;gap:clamp(.25rem,1vw,.5rem);box-sizing:border-box}.user-input[_ngcontent-%COMP%]{width:100%}.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:clamp(.5rem,2vw,1rem);box-shadow:0 2px 8px #0000001a}.user-input[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border-radius:clamp(.5rem,2vw,1rem)}.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:clamp(.75rem,2vw,.875rem)!important;padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem)!important;min-height:44px}.user-input[_ngcontent-%COMP%]   button[matSuffix][_ngcontent-%COMP%]{min-width:44px;min-height:44px}.voice-display[_ngcontent-%COMP%]{background:#fff;border-radius:clamp(1.5rem,4vw,1.5625rem);box-shadow:0 .625rem 1.5625rem #0000001a;padding:clamp(1rem,3vw,1.25rem) clamp(1.5rem,4vw,1.875rem);text-align:center}.voice-input-field[_ngcontent-%COMP%]   .voice-placeholder[_ngcontent-%COMP%]{color:#718096;font-size:clamp(1rem,2.5vw,1.25rem);font-style:italic}.audio-visualization[_ngcontent-%COMP%]{position:fixed;bottom:clamp(4rem,8vw,6rem);left:50%;transform:translate(-50%);background:#fffffff2;padding:clamp(1rem,3vw,1.25rem) clamp(1.5rem,4vw,1.875rem);border-radius:clamp(1.5rem,4vw,1.5625rem);box-shadow:0 .625rem 1.5625rem #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);display:flex;align-items:center;gap:clamp(1rem,3vw,1.25rem);z-index:1000;max-width:min(90vw,25rem);border:1px solid rgba(102,126,234,.1)}.sound-wave[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.15rem,.4vw,.1875rem);height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]{width:clamp(.2rem,.5vw,.25rem);background:linear-gradient(to top,#667eea,#f093fb);border-radius:clamp(1px,.2vw,2px);animation:_ngcontent-%COMP%_wave-animation 1.5s infinite ease-in-out}.wave-bar[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s;height:clamp(1rem,2.5vw,1.25rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(2){animation-delay:.1s;height:clamp(1.5rem,3.5vw,1.875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(3){animation-delay:.2s;height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(4){animation-delay:.3s;height:clamp(1.75rem,4vw,2.1875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(5){animation-delay:.4s;height:clamp(1.25rem,3vw,1.5625rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(6){animation-delay:.5s;height:clamp(2rem,5vw,2.5rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(7){animation-delay:.6s;height:clamp(1.5rem,3.5vw,1.875rem)}.wave-bar[_ngcontent-%COMP%]:nth-child(8){animation-delay:.7s;height:clamp(1rem,2.5vw,1.25rem)}.recording-text[_ngcontent-%COMP%]{color:#2d3748;font-weight:600;font-size:clamp(.875rem,2.5vw,1.125rem);display:flex;align-items:center;gap:clamp(.25rem,1vw,.5rem)}.recording-text[_ngcontent-%COMP%]   .recording-icon[_ngcontent-%COMP%]{color:#ff4757;animation:_ngcontent-%COMP%_recording-pulse 1s infinite}.voice-status-indicator[_ngcontent-%COMP%]{position:fixed;bottom:clamp(1.5rem,4vw,1.875rem);right:clamp(1.5rem,4vw,1.875rem);display:flex;flex-direction:column;align-items:center;gap:clamp(.5rem,1.5vw,1rem);z-index:1000}@media (max-width: 480px){.voice-status-indicator[_ngcontent-%COMP%]{bottom:clamp(1rem,3vw,1.5rem);right:clamp(1rem,3vw,1.5rem);scale:.9}}.status-icon[_ngcontent-%COMP%]{width:clamp(3.5rem,8vw,4.375rem);height:clamp(3.5rem,8vw,4.375rem);border-radius:50%;border:none;background:linear-gradient(135deg,#718096,#a0aec0);color:#fff;display:flex;align-items:center;justify-content:center;box-shadow:0 .5rem 1.5625rem #71809666;transition:all .3s ease;position:relative;overflow:hidden}.status-icon.recording[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff4757,#ff3742);animation:_ngcontent-%COMP%_recording-pulse 1s infinite}.status-icon.processing[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);animation:_ngcontent-%COMP%_processing-pulse 2s infinite}.status-icon.waiting[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#10b981);animation:_ngcontent-%COMP%_waiting-pulse 2s infinite}.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:clamp(1.25rem,4vw,1.75rem);z-index:2;position:relative}.status-ripple[_ngcontent-%COMP%]{position:absolute;inset:0;border-radius:50%;background:#ffffff4d;animation:_ngcontent-%COMP%_ripple 1.5s infinite}.status-text[_ngcontent-%COMP%]{background:#fffffff2;padding:clamp(.25rem,1vw,.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(1rem,3vw,1.25rem);font-size:clamp(.75rem,2vw,.875rem);font-weight:600;color:#2d3748;box-shadow:0 .25rem .9375rem #0000001a;-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);white-space:nowrap}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}@keyframes _ngcontent-%COMP%_blink{0%,90%,to{transform:scaleY(1)}95%{transform:scaleY(.1)}}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}@keyframes _ngcontent-%COMP%_ai-blink{0%,90%,to{transform:scaleY(1)}95%{transform:scaleY(.1)}}@keyframes _ngcontent-%COMP%_mouth-talk{0%{transform:scaleY(1)}to{transform:scaleY(1.5)}}@keyframes _ngcontent-%COMP%_processing-pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_listening-pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.1);opacity:.8}}@keyframes _ngcontent-%COMP%_waiting-pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.9}}@keyframes _ngcontent-%COMP%_pulse-ring{0%{transform:scale(.8);opacity:1}to{transform:scale(1.3);opacity:0}}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0)}30%{transform:translateY(-10px)}}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:scaleY(.5)}50%{transform:scaleY(1)}}@keyframes _ngcontent-%COMP%_recording-pulse{0%,to{box-shadow:0 8px 25px #ff475766}50%{box-shadow:0 8px 35px #ff4757cc}}@keyframes _ngcontent-%COMP%_ripple{0%{transform:scale(.8);opacity:1}to{transform:scale(2);opacity:0}}@keyframes _ngcontent-%COMP%_waiting-pulse{0%,to{box-shadow:0 8px 25px #10b98166}50%{box-shadow:0 8px 35px #10b981cc}}@media (max-width: 480px){.ai-questionnaire-container[_ngcontent-%COMP%]{font-size:clamp(.7rem,1.8vw,.8rem);height:100vh;height:100dvh;overflow:hidden}.idle-content[_ngcontent-%COMP%]{padding:clamp(.5rem,3vw,1rem) clamp(.25rem,2vw,.5rem);margin:.25rem;max-width:min(85vw,18rem)}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:clamp(.875rem,3vw,1rem);margin:clamp(.25rem,1.5vw,.5rem) 0 clamp(.25rem,1vw,.25rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.75rem,2.2vw,.875rem);margin-bottom:clamp(.5rem,2.5vw,1rem)}.chat-interface[_ngcontent-%COMP%]{height:100vh;height:100dvh;overflow:hidden}.controls-header[_ngcontent-%COMP%]{flex-shrink:0;padding:clamp(.25rem,1vw,.25rem) clamp(.5rem,2vw,.5rem);gap:clamp(.25rem,1vw,.25rem);min-height:40px}.main-chat-area[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem) clamp(.25rem,1vw,.5rem);gap:clamp(.25rem,.8vw,.25rem);overflow:hidden}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:clamp(.25rem,1.5vw,.5rem);overflow:hidden;flex:1;min-height:0}.ai-avatar[_ngcontent-%COMP%]{width:clamp(2rem,5vw,2.5rem);height:clamp(2rem,5vw,2.5rem)}.message-bubble[_ngcontent-%COMP%]{padding:clamp(.25rem,1.8vw,.5rem) clamp(.5rem,2.5vw,1rem);border-radius:clamp(.25rem,1.5vw,.5rem);max-height:25vh;overflow-y:auto}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.75rem,2.2vw,.875rem);line-height:1.3}.data-section[_ngcontent-%COMP%]{max-width:100%;margin-top:clamp(.25rem,1vw,.25rem);max-height:20vh;overflow:hidden}.data-panel[_ngcontent-%COMP%]{padding:clamp(.25rem,1.5vw,.5rem);border-radius:clamp(.25rem,1.5vw,.5rem)}.input-section[_ngcontent-%COMP%]{padding:clamp(.25rem,1vw,.25rem) clamp(.5rem,2vw,1rem);min-height:50px}.user-input[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{border-radius:clamp(.25rem,1.5vw,.5rem)}.user-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.8vw,.875rem)!important;padding:clamp(.25rem,1.2vw,.25rem) clamp(.5rem,1.8vw,.5rem)!important}.audio-visualization[_ngcontent-%COMP%]{bottom:clamp(.25rem,1.5vw,.5rem);padding:clamp(.25rem,1.5vw,.5rem) clamp(.5rem,2vw,1rem);border-radius:clamp(.25rem,1.5vw,.5rem);max-width:75vw;min-height:40px}.voice-status-indicator[_ngcontent-%COMP%]{bottom:clamp(.25rem,1.5vw,.5rem);right:clamp(.25rem,1.5vw,.5rem);scale:.75}.status-icon[_ngcontent-%COMP%]{width:clamp(2rem,4.5vw,2.5rem);height:clamp(2rem,4.5vw,2.5rem);min-width:40px;min-height:40px}.status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.8vw,1rem)}.status-text[_ngcontent-%COMP%]{font-size:clamp(.75rem,1.5vw,.75rem);padding:clamp(.25rem,.6vw,.25rem) clamp(.25rem,1.2vw,.5rem);max-width:6rem}}@media (min-width: 481px) and (max-width: 640px){.main-chat-area[_ngcontent-%COMP%]{padding:clamp(5rem,10vw,6rem) clamp(1rem,3vw,1.5rem) clamp(1.5rem,3vw,2rem)}.controls-header[_ngcontent-%COMP%]{position:absolute;top:clamp(1rem,2.5vw,1.5rem);left:clamp(1rem,2.5vw,1.5rem)}.response-data-section[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:clamp(1.5rem,4vw,2rem)}.data-section[_ngcontent-%COMP%]{grid-column:1;margin-top:clamp(1rem,3vw,2rem)}}@media (min-width: 641px) and (max-width: 768px){.main-chat-area[_ngcontent-%COMP%]{padding:clamp(6rem,10vw,7rem) clamp(1.5rem,4vw,2rem) clamp(2rem,4vw,3rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;gap:clamp(2rem,5vw,3rem);align-items:center}.data-section[_ngcontent-%COMP%]{max-width:100%;margin-top:clamp(1.5rem,4vw,2rem);width:100%}.response-section[_ngcontent-%COMP%]{align-items:center;width:100%}.controls-header[_ngcontent-%COMP%]{top:clamp(1rem,2.5vw,1.5rem);left:clamp(1rem,2.5vw,1.5rem);right:clamp(1rem,2.5vw,1.5rem);width:auto;justify-content:center}}@media (min-width: 769px) and (max-width: 1024px){.main-chat-area[_ngcontent-%COMP%]{max-width:min(90vw,75rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:column;gap:clamp(1.5rem,3vw,3rem);align-items:center}.data-section[_ngcontent-%COMP%]{margin-top:clamp(1.5rem,3vw,2rem);max-width:100%;width:100%}.response-section[_ngcontent-%COMP%]{width:100%}}@media (min-width: 1025px){.main-chat-area[_ngcontent-%COMP%]{max-width:min(85vw,85rem)}.response-data-section[_ngcontent-%COMP%]{flex-direction:row;align-items:flex-start;gap:clamp(2rem,2vw,4rem)}.response-section[_ngcontent-%COMP%]{flex:1;max-width:none}.data-section[_ngcontent-%COMP%]{flex-shrink:0;width:auto;min-width:20rem;margin-top:0}}@media (orientation: landscape) and (max-height: 600px){.idle-screen[_ngcontent-%COMP%]{padding:clamp(.5rem,2vh,1rem)}.idle-content[_ngcontent-%COMP%]{padding:clamp(1.5rem,4vh,2rem) clamp(1.5rem,4vw,3rem)}.idle-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:clamp(1.125rem,4vw,1.25rem);margin:clamp(.5rem,2vh,1rem) 0 clamp(.25rem,1vh,.5rem) 0}.idle-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem);margin-bottom:clamp(1rem,3vh,1.5rem)}.main-chat-area[_ngcontent-%COMP%]{padding:clamp(4rem,8vh,5rem) clamp(1.5rem,4vw,2rem) clamp(1rem,2vh,1.5rem)}.ai-avatar[_ngcontent-%COMP%]{width:clamp(4rem,8vh,5rem);height:clamp(4rem,8vh,5rem)}}@media (min-resolution: 192dpi){.ai-avatar[_ngcontent-%COMP%], .robot-head[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%]{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}}@container (max-width: 480px){.message-bubble[_ngcontent-%COMP%]{padding:clamp(.5rem,2vw,1rem)}.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:clamp(.875rem,2.5vw,1rem)}}@media (prefers-reduced-motion: reduce){.ai-robot[_ngcontent-%COMP%], .ai-avatar[_ngcontent-%COMP%], .start-btn[_ngcontent-%COMP%], .status-icon[_ngcontent-%COMP%]{animation:none}.btn-glow[_ngcontent-%COMP%]{transition:none}}@media (prefers-contrast: high){.message-bubble[_ngcontent-%COMP%], .data-panel[_ngcontent-%COMP%], .controls-header[_ngcontent-%COMP%]{border:2px solid #2d3748}.start-btn[_ngcontent-%COMP%]{border:2px solid #ffffff}}.modern-modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0f172acc;-webkit-backdrop-filter:blur(8px) saturate(180%);backdrop-filter:blur(8px) saturate(180%);display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.modern-modal-overlay[_ngcontent-%COMP%]:focus-within{outline:2px solid #4facfe;outline-offset:-2px}.modern-modal-container[_ngcontent-%COMP%]{background:#fff;border-radius:1.5rem;box-shadow:0 25px 50px -12px #00000040;width:100%;max-width:56rem;max-height:90vh;display:flex;flex-direction:column;overflow:hidden;border:1px solid rgba(255,255,255,.1);background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}@media (max-width: 768px){.modern-modal-container[_ngcontent-%COMP%]{max-width:95vw;max-height:95vh;margin:.5rem}}.modal-header-modern[_ngcontent-%COMP%]{padding:2rem 2rem 1.5rem;border-bottom:1px solid #f1f5f9;background:linear-gradient(135deg,#667eea0d,#764ba20d)}.modal-header-modern[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;justify-content:space-between;gap:1.5rem;margin-bottom:1.5rem}.modal-header-modern[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;flex:1}.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:3rem;height:3rem;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:1rem;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{flex:1}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1.5rem;font-weight:700;color:#0f172a;line-height:1.2}.modal-header-modern[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%]{margin:0;font-size:.875rem;color:#475569;font-weight:500}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem;border-radius:.75rem;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#f8fafc;color:#475569}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#f1f5f9;color:#0f172a;transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]{background:#ef44441a;color:#ef4444}.modal-header-modern[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-btn.close-btn[_ngcontent-%COMP%]:hover{background:#ef444426;transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:.875rem;color:#475569;font-weight:500}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%]{font-size:.875rem;color:#4facfe;font-weight:700}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{height:.5rem;background:#f1f5f9;border-radius:9999px;overflow:hidden}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4facfe,#667eea);border-radius:9999px;transition:width .3s cubic-bezier(.4,0,.2,1);position:relative}.modal-header-modern[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.search-filter-section[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-bottom:1px solid #f1f5f9;background:#f8fafc}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{margin-bottom:1rem}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{width:100%}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:1rem;box-shadow:0 1px 2px #0000000d;border:1px solid #f1f5f9;transition:all .2s cubic-bezier(.4,0,.2,1)}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:hover{border-color:#667eea4d;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.search-filter-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]:focus-within{border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option[_ngcontent-%COMP%]{border-radius:9999px;font-weight:500;font-size:.875rem;transition:all .2s cubic-bezier(.4,0,.2,1)}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-personal[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(102, 126, 234, .1);--mdc-chip-selected-label-text-color: #667eea}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-medical[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(16, 185, 129, .1);--mdc-chip-selected-label-text-color: #10b981}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-contact[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(59, 130, 246, .1);--mdc-chip-selected-label-text-color: #3b82f6}.search-filter-section[_ngcontent-%COMP%]   .filter-chips[_ngcontent-%COMP%]   .filter-chip-list[_ngcontent-%COMP%]   .mat-mdc-chip-option.filter-chip-optional[_ngcontent-%COMP%]{--mdc-chip-selected-container-color: rgba(245, 158, 11, .1);--mdc-chip-selected-label-text-color: #f59e0b}.modal-body-modern[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1.5rem 2rem}.modal-body-modern[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{max-height:100%}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 1.5rem;text-align:center}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{width:4rem;height:4rem;background:#f1f5f9;border-radius:9999px;display:flex;align-items:center;justify-content:center;margin-bottom:1.5rem}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:2rem;color:#64748b}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.25rem;font-weight:600;color:#0f172a}.modal-body-modern[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]{margin:0;font-size:.875rem;color:#475569;max-width:24rem}.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{display:grid;gap:1rem;grid-template-columns:1fr}@media (min-width: 1024px){.modal-body-modern[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #f1f5f9;border-radius:1rem;padding:1.5rem;transition:all .2s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,#667eea,#764ba2);opacity:0;transition:opacity .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover{border-color:#667eea33;box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;transform:translateY(-2px)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover:before{opacity:1}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-personal[_ngcontent-%COMP%]{border-left:4px solid #667eea}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-medical[_ngcontent-%COMP%]{border-left:4px solid #10b981}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-contact[_ngcontent-%COMP%]{border-left:4px solid #3b82f6}.modal-body-modern[_ngcontent-%COMP%]   .data-card.card-optional[_ngcontent-%COMP%]{border-left:4px solid #f59e0b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem;background:#f8fafc;border-radius:.75rem;display:flex;align-items:center;justify-content:center;flex-shrink:0}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.25rem;color:#475569}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]{flex:1;min-width:0}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1rem;font-weight:600;color:#0f172a;line-height:1.3}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title-section[_ngcontent-%COMP%]   .card-category[_ngcontent-%COMP%]{font-size:.75rem;color:#64748b;font-weight:500;text-transform:uppercase;letter-spacing:.05em}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]{display:flex;gap:.25rem;opacity:0;transition:opacity .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]{width:2rem;height:2rem;border-radius:.5rem;background:#f8fafc;color:#475569;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   .card-action-btn[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;transform:scale(1.1)}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]:hover   .card-actions[_ngcontent-%COMP%]{opacity:1}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]{margin-bottom:1rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]{font-size:1rem;color:#0f172a;font-weight:500;line-height:1.5;word-break:break-word}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .value-container[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%]     mark{background:#4facfe33;color:#4facfe;padding:.125rem .25rem;border-radius:.375rem;font-weight:600}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:.5rem;padding-top:.5rem;border-top:1px solid #f1f5f9}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;color:#64748b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.875rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;font-weight:500;padding:.25rem .5rem;border-radius:9999px}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.875rem}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-valid[_ngcontent-%COMP%]{background:#10b9811a;color:#10b981}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-warning[_ngcontent-%COMP%]{background:#f59e0b1a;color:#f59e0b}.modal-body-modern[_ngcontent-%COMP%]   .data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-metadata[_ngcontent-%COMP%]   .validation-status.status-error[_ngcontent-%COMP%]{background:#ef44441a;color:#ef4444}.modal-footer-modern[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-top:1px solid #f1f5f9;background:#f8fafc;display:flex;align-items:center;justify-content:space-between;gap:1.5rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#475569}.modal-footer-modern[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;color:#3b82f6}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{display:flex;gap:1rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]{border-radius:.75rem;font-weight:500;transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]{border-radius:.75rem;font-weight:600;background:linear-gradient(135deg,#667eea,#764ba2);transition:all .2s cubic-bezier(.4,0,.2,1)}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;background:linear-gradient(135deg,#506be7,#694391)}@media (max-width: 640px){.modal-footer-modern[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1rem}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{justify-content:stretch}.modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .secondary-btn[_ngcontent-%COMP%], .modal-footer-modern[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%]{flex:1}}@keyframes _ngcontent-%COMP%_shimmer{0%{transform:translate(-100%)}to{transform:translate(100%)}}@media (prefers-reduced-motion: reduce){.modern-modal-container[_ngcontent-%COMP%], .data-card[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .card-action-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%], .primary-btn[_ngcontent-%COMP%]{transition:none}.progress-fill[_ngcontent-%COMP%]:after{animation:none}}@media (prefers-contrast: high){.modern-modal-container[_ngcontent-%COMP%]{border:2px solid #0f172a}.data-card[_ngcontent-%COMP%], .search-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%]{border:2px solid #475569}}.test-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9800,#f57c00)!important;color:#fff!important;border:none!important;border-radius:25px!important;padding:12px 24px!important;font-weight:600!important;font-size:.9em!important;text-transform:uppercase!important;letter-spacing:.5px!important;box-shadow:0 4px 15px #ff98004d!important;transition:all .3s ease!important;position:relative!important;overflow:hidden!important}.test-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px)!important;box-shadow:0 6px 20px #ff980066!important;background:linear-gradient(135deg,#f57c00,#e65100)!important}.test-button[_ngcontent-%COMP%]:active{transform:translateY(0)!important}.test-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px!important;font-size:1.1em!important}.test-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.test-button[_ngcontent-%COMP%]:hover:before{left:100%}.idle-content[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%]{margin-top:10px;font-size:.85em!important;padding:10px 20px!important}.controls-header[_ngcontent-%COMP%]   .test-button[_ngcontent-%COMP%]{margin-left:15px;font-size:.8em!important;padding:8px 16px!important}.scrollable-hidden[_ngcontent-%COMP%]{overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}.scrollable-hidden[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.validation-buttons[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:center;align-items:center;margin-top:1.5rem;padding:1.5rem;background:#f8fafc;border-radius:12px;border:1px solid rgba(102,126,234,.1);box-shadow:0 4px 12px #0000000d}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1.5rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#475569;font-size:1rem;font-weight:500}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .validation-message[_ngcontent-%COMP%]   .validation-icon[_ngcontent-%COMP%]{color:#667eea;font-size:1.25rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap;justify-content:center}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:140px;height:44px;border-radius:8px;font-weight:600;font-size:.875rem;text-transform:none;letter-spacing:.025em;transition:all .2s ease;box-shadow:0 2px 8px #0000001a}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:.25rem;font-size:1.1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active{transform:translateY(0)}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#10b981);color:#fff}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#10b981,#059669)}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f59e0b,#d97706);color:#fff}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#d97706,#b45309)}@media (max-width: 480px){.validation-buttons[_ngcontent-%COMP%]{padding:1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]{gap:1rem}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{flex-direction:column;width:100%}.validation-buttons[_ngcontent-%COMP%]   .validation-container[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;min-width:unset}}\"],\n      data: {\n        animation: [trigger('fadeInOut', [transition(':enter', [style({\n          opacity: 0\n        }), animate('300ms ease-in', style({\n          opacity: 1\n        }))]), transition(':leave', [animate('200ms ease-out', style({\n          opacity: 0\n        }))])]), trigger('slideInOut', [transition(':enter', [style({\n          transform: 'translateY(-50px)',\n          opacity: 0\n        }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        }))]), transition(':leave', [animate('300ms ease-in', style({\n          transform: 'translateY(-30px)',\n          opacity: 0\n        }))])]), trigger('cardAnimation', [transition(':enter', [style({\n          transform: 'scale(0.8)',\n          opacity: 0\n        }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n          transform: 'scale(1)',\n          opacity: 1\n        }))])])]\n      }\n    });\n  }\n  return HistoryModalDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
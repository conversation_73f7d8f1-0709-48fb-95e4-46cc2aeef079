{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ChangeDetectorRef } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { finalize, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { IntegrationService } from '../../pre-consulta-questionario/Service/integracao-vittal-tec.service';\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../pre-consulta-questionario/Service/integracao-vittal-tec.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-bar\";\nimport * as i7 from \"@angular/material/divider\";\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"sync\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_2_Template, 2, 0, \"mat-icon\", 25)(3, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_3_Template, 2, 0, \"mat-icon\", 25)(4, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_4_Template, 2, 0, \"mat-icon\", 26)(5, ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_5_Template, 2, 0, \"mat-icon\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"span\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.currentStep === i_r2)(\"completed\", step_r1.completed)(\"error\", step_r1.error);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r1.completed && !step_r1.error);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r1.error);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !step_r1.completed && !step_r1.error && ctx_r2.currentStep === i_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !step_r1.completed && !step_r1.error && ctx_r2.currentStep !== i_r2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r1.label);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, ModalColetaDadosVittaltecComponent_div_15_div_1_Template, 9, 11, \"div\", 21)(2, ModalColetaDadosVittaltecComponent_div_15_div_2_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"div\", 34)(3, \"mat-icon\", 35);\n    i0.ɵɵtext(4, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 36);\n    i0.ɵɵtext(6, \"Ops! Algo deu errado\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 37);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38)(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_16_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startProcess());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Tentar Novamente \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 40);\n    i0.ɵɵtext(15, \" Cancelar \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"mat-icon\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + ctx_r2.capturedData.status.toLowerCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.status === \"aprovado\" ? \"check_circle\" : \"info\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Status: \", i0.ɵɵpipeBind1(6, 3, ctx_r2.capturedData.status), \"\");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" ID do Paciente: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.IdPaciente);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"favorite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Press\\u00E3o Arterial: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"span\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 68);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 69);\n    i0.ɵɵtext(13, \"mmHg\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.pressaoSistolica || \"--\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.capturedData.data.pressaoDiastolica || \"--\");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"device_thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Temperatura: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 1, ctx_r2.capturedData.data.temperatura, \"1.1-1\"), \"\\u00B0C \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"air\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Satura\\u00E7\\u00E3o de O\\u2082: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.data.oxigenacao, \"% \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 61)(2, \"mat-icon\", 62);\n    i0.ɵɵtext(3, \"monitor_heart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Freq. Card\\u00EDaca: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8, \"bpm\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.capturedData.data.batimento, \" \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"h4\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dados do Paciente \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 57);\n    i0.ɵɵtemplate(6, ModalColetaDadosVittaltecComponent_div_17_div_9_div_6_Template, 7, 1, \"div\", 58)(7, ModalColetaDadosVittaltecComponent_div_17_div_9_div_7_Template, 14, 2, \"div\", 59)(8, ModalColetaDadosVittaltecComponent_div_17_div_9_div_8_Template, 8, 4, \"div\", 59)(9, ModalColetaDadosVittaltecComponent_div_17_div_9_div_9_Template, 7, 1, \"div\", 59)(10, ModalColetaDadosVittaltecComponent_div_17_div_9_div_10_Template, 9, 1, \"div\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.IdPaciente);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.pressaoSistolica || ctx_r2.capturedData.data.pressaoDiastolica);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.temperatura);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.oxigenacao);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData.data.batimento);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"mat-icon\", 72);\n    i0.ɵɵtext(3, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 73);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Coletado em: \", i0.ɵɵpipeBind2(6, 1, ctx_r2.capturedData.timestamp, \"dd/MM/yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"h4\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"data_object\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Dados Coletados \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 75)(6, \"pre\", 76);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"json\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 1, ctx_r2.capturedData));\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\", 78);\n    i0.ɵɵtext(2, \"info_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 79);\n    i0.ɵɵtext(4, \"Nenhum dado dispon\\u00EDvel para visualiza\\u00E7\\u00E3o.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"mat-icon\", 43);\n    i0.ɵɵtext(3, \"assignment_turned_in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 44);\n    i0.ɵɵtext(5, \"Dados Coletados com Sucesso\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-divider\");\n    i0.ɵɵelementStart(7, \"div\", 45);\n    i0.ɵɵtemplate(8, ModalColetaDadosVittaltecComponent_div_17_div_8_Template, 7, 5, \"div\", 46)(9, ModalColetaDadosVittaltecComponent_div_17_div_9_Template, 11, 5, \"div\", 47)(10, ModalColetaDadosVittaltecComponent_div_17_div_10_Template, 7, 4, \"div\", 48)(11, ModalColetaDadosVittaltecComponent_div_17_div_11_Template, 9, 3, \"div\", 49)(12, ModalColetaDadosVittaltecComponent_div_17_div_12_Template, 5, 0, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.data);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.capturedData == null ? null : ctx_r2.capturedData.timestamp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.capturedData == null ? null : ctx_r2.capturedData.data) && ctx_r2.capturedData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.capturedData);\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81)(2, \"mat-icon\", 82);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 83);\n    i0.ɵɵtext(5, \"Processo Conclu\\u00EDdo!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 84);\n    i0.ɵɵtext(7, \" Todos os dados foram coletados e processados com sucesso. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Cancelar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_20_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.retry());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Tentar Novamente \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Cancelar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.continueProcess());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Confirmar e Continuar \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function ModalColetaDadosVittaltecComponent_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onContinue());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Finalizar \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModalColetaDadosVittaltecComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 90)(2, \"mat-icon\", 91);\n    i0.ɵɵtext(3, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processando... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ModalColetaDadosVittaltecComponent {\n  dialogRef;\n  data;\n  integrationService;\n  cdr;\n  isLoading = false;\n  currentStep = 0;\n  errorMessage = '';\n  capturedData = null;\n  showDataPreview = false;\n  steps = [{\n    label: 'Verificando conectividade...',\n    completed: false,\n    error: false\n  }, {\n    label: 'Capturando dados...',\n    completed: false,\n    error: false\n  }, {\n    label: 'Lendo informações...',\n    completed: false,\n    error: false\n  }, {\n    label: 'Alternando janela do navegador...',\n    completed: false,\n    error: false\n  }];\n  constructor(dialogRef, data, integrationService, cdr) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.integrationService = integrationService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    console.clear();\n    this.startIntegrationProcess();\n  }\n  startIntegrationProcess() {\n    this.isLoading = true;\n    this.currentStep = 0;\n    this.errorMessage = '';\n    this.capturedData = null;\n    this.showDataPreview = false;\n    this.steps.forEach(step => {\n      step.completed = false;\n      step.error = false;\n    });\n    this.executeHealthCheck();\n  }\n  executeHealthCheck() {\n    this.currentStep = 0;\n    this.cdr.detectChanges();\n    this.integrationService.healthCheck().pipe(catchError(error => {\n      console.error('Health check error caught:', error);\n      this.steps[0].error = true;\n      this.errorMessage = 'Falha na verificação de conectividade. Verifique se o serviço VittalTec está rodando na porta 8080.';\n      this.isLoading = false;\n      this.registrarLog('healthCheck', 'ERRO', JSON.stringify(error));\n      this.cdr.detectChanges();\n      return of(null);\n    }), finalize(() => {\n      setTimeout(() => {\n        if (!this.steps[0].error) {\n          this.steps[0].completed = true;\n          this.cdr.detectChanges();\n          this.executeCapture();\n        }\n      }, 500);\n    })).subscribe({\n      next: response => {\n        if (response) {\n          this.registrarLog('healthCheck', 'SUCESSO', JSON.stringify(response));\n        }\n      },\n      error: error => {\n        console.error('Health check subscription error:', error);\n        this.steps[0].error = true;\n        this.errorMessage = 'Erro inesperado na verificação de conectividade.';\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  executeCapture() {\n    this.currentStep = 1;\n    this.cdr.detectChanges();\n    this.integrationService.capture().pipe(catchError(error => {\n      console.error('Capture error caught:', error);\n      this.steps[1].error = true;\n      this.errorMessage = 'Falha na captura dos dados. Verifique se o dispositivo VittalTec está conectado e funcionando.';\n      this.isLoading = false;\n      this.registrarLog('capture', 'ERRO', JSON.stringify(error));\n      this.cdr.detectChanges();\n      return of(null);\n    }), finalize(() => {\n      setTimeout(() => {\n        if (!this.steps[1].error) {\n          this.steps[1].completed = true;\n          this.cdr.detectChanges();\n          this.executeRead();\n        }\n      }, 500);\n    })).subscribe({\n      next: response => {\n        if (response) {\n          this.registrarLog('capture', 'SUCESSO', JSON.stringify(response));\n        }\n      },\n      error: error => {\n        console.error('Capture subscription error:', error);\n        this.steps[1].error = true;\n        this.errorMessage = 'Erro inesperado na captura de dados.';\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  executeRead() {\n    this.currentStep = 2;\n    this.cdr.detectChanges();\n    this.integrationService.read().pipe(catchError(error => {\n      console.error('Read error caught:', error);\n      this.steps[2].error = true;\n      this.errorMessage = 'Falha na leitura dos dados. Verifique se os dados foram capturados corretamente.';\n      this.isLoading = false;\n      this.registrarLog('read', 'ERRO', JSON.stringify(error));\n      this.cdr.detectChanges();\n      return of(null);\n    }), finalize(() => {\n      setTimeout(() => {\n        if (!this.steps[2].error) {\n          this.steps[2].completed = true;\n          this.cdr.detectChanges();\n          if (this.capturedData) {\n            this.showDataPreview = true;\n            this.isLoading = false;\n          } else {\n            this.executeSwitchWindow();\n          }\n        }\n      }, 500);\n    })).subscribe({\n      next: response => {\n        if (response) {\n          this.registrarLog('read', 'SUCESSO', JSON.stringify(response));\n          console.log(\"response\", response);\n          if (response && (Array.isArray(response) ? response.length > 0 : Object.keys(response).length > 0)) {\n            this.capturedData = response;\n            CriptografarUtil.localStorageCriptografado(\"VittalTecDados\", response.data);\n          } else {\n            this.steps[2].error = true;\n            this.errorMessage = 'Nenhum dado foi encontrado para leitura.';\n            this.isLoading = false;\n          }\n        }\n      },\n      error: error => {\n        console.error('Read subscription error:', error);\n        this.steps[2].error = true;\n        this.errorMessage = 'Erro inesperado na leitura de dados.';\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  executeSwitchWindow() {\n    this.currentStep = 3;\n    this.integrationService.switchWindowBrowser().pipe(finalize(() => {\n      setTimeout(() => {\n        if (!this.steps[3].error) {\n          this.steps[3].completed = true;\n          this.isLoading = false;\n          setTimeout(() => {\n            this.onContinue();\n          }, 1000);\n        }\n      }, 500);\n    })).subscribe({\n      next: response => {\n        this.registrarLog('switchWindowBrowser', '', JSON.stringify(response));\n      },\n      error: error => {\n        console.error('Switch window failed:', error);\n        this.steps[3].error = true;\n        this.errorMessage = 'Falha ao alternar a janela do navegador.';\n        this.isLoading = false;\n        this.registrarLog('switchWindowBrowser', '', JSON.stringify(error));\n      }\n    });\n  }\n  registrarLog(endpoint, requisicao, resposta) {\n    const logData = {\n      Id: null,\n      Requisicao: `${endpoint}: ${requisicao}`,\n      Resposta: resposta,\n      DtCadastro: new Date()\n    };\n    this.integrationService.RegistraLogRequisicao(logData).subscribe({\n      next: response => {\n        response;\n      },\n      error: error => {\n        console.error('Erro ao registrar log:', error);\n      }\n    });\n  }\n  continueProcess() {\n    this.showDataPreview = false;\n    this.isLoading = true;\n    this.executeSwitchWindow();\n  }\n  areAllStepsCompleted() {\n    return this.steps.every(step => step.completed);\n  }\n  retry() {\n    this.startIntegrationProcess();\n  }\n  startProcess() {\n    this.startIntegrationProcess();\n  }\n  onContinue() {\n    this.dialogRef.close({\n      action: 'continuar',\n      data: this.capturedData\n    });\n  }\n  onCancel() {\n    this.dialogRef.close({\n      action: 'cancelar'\n    });\n  }\n  onClose() {\n    this.dialogRef.close();\n  }\n  static ɵfac = function ModalColetaDadosVittaltecComponent_Factory(t) {\n    return new (t || ModalColetaDadosVittaltecComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.IntegrationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalColetaDadosVittaltecComponent,\n    selectors: [[\"app-modal-coleta-dados-vittaltec\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 24,\n    vars: 8,\n    consts: [[1, \"modal-overlay\"], [1, \"modal-container\"], [1, \"modal-card\"], [1, \"modal-header\"], [1, \"header-left\"], [1, \"icon-wrapper\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z\", \"stroke\", \"#00ff80\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"fill\", \"none\"], [\"d\", \"M9 12L11 14L15 10\", \"stroke\", \"#00ff80\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"header-text\"], [1, \"modal-title\"], [1, \"modal-subtitle\"], [1, \"modal-content\"], [\"class\", \"steps-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"data-preview-container\", 4, \"ngIf\"], [\"class\", \"success-container\", 4, \"ngIf\"], [1, \"modal-actions\"], [\"class\", \"action-group\", 4, \"ngIf\"], [\"class\", \"action-group loading-actions\", 4, \"ngIf\"], [1, \"steps-container\"], [\"class\", \"step-item\", 3, \"active\", \"completed\", \"error\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"progress-container\", 4, \"ngIf\"], [1, \"step-item\"], [1, \"step-icon\"], [4, \"ngIf\"], [\"class\", \"rotating\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-label\"], [1, \"rotating\"], [1, \"progress-container\"], [\"mode\", \"indeterminate\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-title\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"mat-dialog-close\", \"\", 1, \"cancel-button\"], [1, \"data-preview-container\"], [1, \"preview-header\"], [1, \"preview-icon\"], [1, \"preview-title\"], [1, \"preview-content\"], [\"class\", \"status-section\", 4, \"ngIf\"], [\"class\", \"patient-data-section\", 4, \"ngIf\"], [\"class\", \"timestamp-section\", 4, \"ngIf\"], [\"class\", \"raw-data-section\", 4, \"ngIf\"], [\"class\", \"no-data-section\", 4, \"ngIf\"], [1, \"status-section\"], [1, \"status-item\", 3, \"ngClass\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"patient-data-section\"], [1, \"section-title\"], [1, \"data-grid\"], [\"class\", \"data-item\", 4, \"ngIf\"], [\"class\", \"data-item vital-sign\", 4, \"ngIf\"], [1, \"data-item\"], [1, \"data-label\"], [1, \"data-icon\"], [1, \"data-value\"], [1, \"data-item\", \"vital-sign\"], [1, \"data-value\", \"pressure-value\"], [1, \"systolic\"], [1, \"separator\"], [1, \"diastolic\"], [1, \"unit\"], [1, \"timestamp-section\"], [1, \"timestamp-item\"], [1, \"timestamp-icon\"], [1, \"timestamp-text\"], [1, \"raw-data-section\"], [1, \"raw-data-container\"], [1, \"raw-data\"], [1, \"no-data-section\"], [1, \"no-data-icon\"], [1, \"no-data-text\"], [1, \"success-container\"], [1, \"success-card\"], [1, \"success-icon\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"action-group\"], [\"mat-stroked-button\", \"\", 1, \"btn-secondary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-primary\", \"full-width\", 3, \"click\"], [1, \"action-group\", \"loading-actions\"], [1, \"loading-text\"], [1, \"loading-icon\"]],\n    template: function ModalColetaDadosVittaltecComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(6, \"svg\", 6);\n        i0.ɵɵelement(7, \"path\", 7)(8, \"path\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(9, \"div\", 9)(10, \"h2\", 10);\n        i0.ɵɵtext(11, \"Coleta de Dados VittalTec\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\", 11);\n        i0.ɵɵtext(13, \"Integra\\u00E7\\u00E3o com dispositivo de an\\u00E1lise\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(14, \"div\", 12);\n        i0.ɵɵtemplate(15, ModalColetaDadosVittaltecComponent_div_15_Template, 3, 2, \"div\", 13)(16, ModalColetaDadosVittaltecComponent_div_16_Template, 16, 1, \"div\", 14)(17, ModalColetaDadosVittaltecComponent_div_17_Template, 13, 5, \"div\", 15)(18, ModalColetaDadosVittaltecComponent_div_18_Template, 8, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 17);\n        i0.ɵɵtemplate(20, ModalColetaDadosVittaltecComponent_div_20_Template, 9, 0, \"div\", 18)(21, ModalColetaDadosVittaltecComponent_div_21_Template, 9, 0, \"div\", 18)(22, ModalColetaDadosVittaltecComponent_div_22_Template, 5, 0, \"div\", 18)(23, ModalColetaDadosVittaltecComponent_div_23_Template, 5, 0, \"div\", 19);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading || !ctx.showDataPreview);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showDataPreview && ctx.capturedData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.areAllStepsCompleted() && !ctx.showDataPreview && !ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showDataPreview);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.areAllStepsCompleted() && !ctx.showDataPreview && !ctx.isLoading && !ctx.errorMessage);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.JsonPipe, i3.DecimalPipe, i3.TitleCasePipe, i3.DatePipe, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatButtonModule, i4.MatButton, MatCardModule, MatSelectModule, MatCheckboxModule, MatIconModule, i5.MatIcon, MatDialogModule, i1.MatDialogClose, MatProgressBarModule, i6.MatProgressBar, MatDividerModule, i7.MatDivider],\n    styles: [\".modal-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.6);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n\\n.modal-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 24px;\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n  overflow: hidden;\\n  width: 100%;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_modalEnter 0.4s ease-out;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);\\n  padding: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom: 1px solid rgba(0, 255, 128, 0.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.modal-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  transition: all 0.2s ease;\\n  width: 40px;\\n  height: 40px;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: rgba(0, 0, 0, 0.04);\\n  transform: scale(1.1);\\n}\\n.modal-header[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 60px;\\n  height: 60px;\\n  background: rgba(0, 255, 128, 0.1);\\n  border-radius: 50%;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n}\\n.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgba(0, 255, 128, 0.15);\\n}\\n.modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #343a40;\\n  margin: 0 0 0.25rem 0;\\n  line-height: 1.2;\\n}\\n@media (max-width: 768px) {\\n  .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n}\\n.modal-header[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #6c757d;\\n  line-height: 1.6;\\n  margin: 0;\\n  max-width: 400px;\\n  margin: 0 auto;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  min-height: 150px;\\n  max-height: calc(70vh - 200px);\\n  overflow-y: auto;\\n}\\n@media (max-width: 768px) {\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 0;\\n  border-left: 3px solid transparent;\\n  padding-left: 0.75rem;\\n  margin-left: 0.75rem;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:not(:last-child) {\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  border-left-color: #00ff80;\\n  background: rgba(0, 255, 128, 0.05);\\n  border-radius: 0 12px 12px 0;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  color: #000000 !important;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  border-left-color: #28a745;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.error[_ngcontent-%COMP%] {\\n  border-left-color: #dc3545;\\n  background: rgba(220, 53, 69, 0.05);\\n  border-radius: 0 12px 12px 0;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item.error[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon.rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n  color: #00ff80;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #343a40;\\n  font-size: 0.9rem;\\n}\\n.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]     .mat-progress-bar-fill::after {\\n  background-color: #00ff80;\\n}\\n.steps-container[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]     .mat-progress-bar-buffer {\\n  background: rgba(0, 255, 128, 0.2);\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem 0;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.05);\\n  border: 1px solid rgba(220, 53, 69, 0.2);\\n  border-radius: 12px;\\n  padding: 2rem;\\n  max-width: 450px;\\n  margin: 0 auto;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #dc3545;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #dc3545;\\n  margin: 0;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #343a40;\\n  line-height: 1.6;\\n  margin: 0 0 1.5rem 0;\\n  font-size: 0.95rem;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: white;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%]:hover {\\n  background: #bd2130;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .retry-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #ddd;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.04);\\n}\\n\\n.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 1.5rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  margin-bottom: 1rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  color: #343a40;\\n  margin: 0 0 0.5rem 0;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  border-radius: 12px;\\n  margin-bottom: 0.5rem;\\n  background: #f8f9fa;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 255, 128, 0.05);\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #343a40;\\n  flex: 1;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #00ff80;\\n  text-align: right;\\n  font-size: 1.1rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .array-display[_ngcontent-%COMP%]   .array-items[_ngcontent-%COMP%]   .array-item[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  padding: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .array-display[_ngcontent-%COMP%]   .array-items[_ngcontent-%COMP%]   .array-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #343a40;\\n  display: block;\\n  margin-bottom: 0.5rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .json-container[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  padding: 1rem;\\n}\\n.data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .json-display[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9rem;\\n  color: #343a40;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n.success-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem 0;\\n}\\n.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%] {\\n  background: rgba(40, 167, 69, 0.05);\\n  border: 1px solid rgba(40, 167, 69, 0.2);\\n  border-radius: 12px;\\n  padding: 2rem;\\n}\\n.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  margin-bottom: 1rem;\\n}\\n.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  color: #28a745;\\n  margin: 0 0 1rem 0;\\n}\\n.success-container[_ngcontent-%COMP%]   .success-card[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  color: #343a40;\\n  line-height: 1.6;\\n  margin: 0;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\\n  background: rgba(248, 249, 250, 0.5);\\n}\\n@media (max-width: 768px) {\\n  .modal-actions[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n.modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .modal-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\n.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.modal-actions[_ngcontent-%COMP%]   .action-group.loading-actions[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%]   .loading-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n  padding: 0.75rem 1.5rem;\\n  font-weight: 600;\\n  border-radius: 12px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  color: #ffffff;\\n  transform: translateY(-1px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #00ff80;\\n  color: #343a40;\\n  padding: 0.75rem 2rem;\\n  font-weight: 600;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 255, 128, 0.3);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #00cc66;\\n  box-shadow: 0 6px 20px rgba(0, 255, 128, 0.4);\\n  transform: translateY(-2px);\\n}\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.btn-primary.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  justify-content: center;\\n}\\n\\n.close-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.close-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #ffffff;\\n  transform: scale(1.1);\\n}\\n.close-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.close-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalEnter {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n  .mat-progress-bar {\\n  height: 6px;\\n  border-radius: 3px;\\n  overflow: hidden;\\n}\\n  .mat-divider {\\n  border-top-color: rgba(0, 255, 128, 0.2);\\n  margin: 1.5rem 0;\\n}\\n  .vittaltec-modal-panel .mat-dialog-container {\\n  padding: 0 !important;\\n  border-radius: 16px !important;\\n  overflow: hidden !important;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;\\n}\\n  .mat-raised-button {\\n  border-radius: 8px !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;\\n}\\n  .mat-raised-button:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\\n}\\n  .mat-stroked-button {\\n  border-radius: 8px !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n}\\n  .mat-icon-button {\\n  border-radius: 8px !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .modal-overlay[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .modal-container[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    max-height: 85vh;\\n  }\\n  .modal-card[_ngcontent-%COMP%] {\\n    margin: 0;\\n    max-height: 85vh;\\n  }\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .modal-subtitle[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .steps-container[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    margin-left: 0.5rem;\\n    padding-left: 0.75rem;\\n  }\\n  .data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.25rem;\\n  }\\n  .data-preview-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .data-row[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .modal-header[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .modal-actions[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n.preview-content[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  background-color: #f5f5f5;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item.status-aprovado[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item.status-aprovado[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 0 16px 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #666;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 12px;\\n}\\n@media (min-width: 600px) {\\n  .preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 12px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.2s ease;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n  border-color: #d0d0d0;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item.vital-sign[_ngcontent-%COMP%] {\\n  border-left: 4px solid #2196f3;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #666;\\n  margin-bottom: 4px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]   .data-icon[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n  font-size: 16px;\\n  color: #2196f3;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: #666;\\n  margin-left: 4px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .systolic[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .diastolic[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .pressure-value[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  margin: 0 4px;\\n  color: #666;\\n  font-weight: 400;\\n}\\n.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%]   .timestamp-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  font-size: 12px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .timestamp-section[_ngcontent-%COMP%]   .timestamp-item[_ngcontent-%COMP%]   .timestamp-icon[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n  font-size: 16px;\\n}\\n.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #666;\\n}\\n.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .raw-data-container[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  border-radius: 8px;\\n  padding: 16px;\\n  overflow-x: auto;\\n}\\n.preview-content[_ngcontent-%COMP%]   .raw-data-section[_ngcontent-%COMP%]   .raw-data-container[_ngcontent-%COMP%]   .raw-data[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 12px;\\n  color: #333;\\n  margin: 0;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 32px 16px;\\n  text-align: center;\\n  color: #666;\\n}\\n.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 12px;\\n  opacity: 0.5;\\n}\\n.preview-content[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-text[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 599px) {\\n  .preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .preview-content[_ngcontent-%COMP%]   .patient-data-section[_ngcontent-%COMP%]   .data-item[_ngcontent-%COMP%]   .data-value[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "ChangeDetectorRef", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatCheckboxModule", "MatDialogRef", "MAT_DIALOG_DATA", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSelectModule", "MatProgressBarModule", "MatDividerModule", "finalize", "catchError", "of", "IntegrationService", "CriptografarUtil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_2_Template", "ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_3_Template", "ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_4_Template", "ModalColetaDadosVittaltecComponent_div_15_div_1_mat_icon_5_Template", "ɵɵclassProp", "ctx_r2", "currentStep", "i_r2", "step_r1", "completed", "error", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "label", "ɵɵelement", "ModalColetaDadosVittaltecComponent_div_15_div_1_Template", "ModalColetaDadosVittaltecComponent_div_15_div_2_Template", "steps", "isLoading", "ɵɵlistener", "ModalColetaDadosVittaltecComponent_div_16_Template_button_click_10_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "startProcess", "errorMessage", "capturedData", "status", "toLowerCase", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "data", "IdPaciente", "pressaoSistolica", "pressaoDiastolica", "ɵɵpipeBind2", "temperatura", "oxigenacao", "batimento", "ModalColetaDadosVittaltecComponent_div_17_div_9_div_6_Template", "ModalColetaDadosVittaltecComponent_div_17_div_9_div_7_Template", "ModalColetaDadosVittaltecComponent_div_17_div_9_div_8_Template", "ModalColetaDadosVittaltecComponent_div_17_div_9_div_9_Template", "ModalColetaDadosVittaltecComponent_div_17_div_9_div_10_Template", "timestamp", "ModalColetaDadosVittaltecComponent_div_17_div_8_Template", "ModalColetaDadosVittaltecComponent_div_17_div_9_Template", "ModalColetaDadosVittaltecComponent_div_17_div_10_Template", "ModalColetaDadosVittaltecComponent_div_17_div_11_Template", "ModalColetaDadosVittaltecComponent_div_17_div_12_Template", "ModalColetaDadosVittaltecComponent_div_20_Template_button_click_1_listener", "_r5", "onCancel", "ModalColetaDadosVittaltecComponent_div_20_Template_button_click_5_listener", "retry", "ModalColetaDadosVittaltecComponent_div_21_Template_button_click_1_listener", "_r6", "ModalColetaDadosVittaltecComponent_div_21_Template_button_click_5_listener", "continueProcess", "ModalColetaDadosVittaltecComponent_div_22_Template_button_click_1_listener", "_r7", "onContinue", "ModalColetaDadosVittaltecComponent", "dialogRef", "integrationService", "cdr", "showDataPreview", "constructor", "ngOnInit", "console", "clear", "startIntegrationProcess", "for<PERSON>ach", "step", "executeHealthCheck", "detectChanges", "healthCheck", "pipe", "registrarLog", "JSON", "stringify", "setTimeout", "executeCapture", "subscribe", "next", "response", "capture", "executeRead", "read", "executeSwitchWindow", "log", "Array", "isArray", "length", "Object", "keys", "localStorageCriptografado", "switchWindowBrowser", "endpoint", "requisicao", "resposta", "logData", "Id", "Requisicao", "Resposta", "DtCadastro", "Date", "RegistraLogRequisicao", "areAllStepsCompleted", "every", "close", "action", "onClose", "ɵɵdirectiveInject", "i1", "i2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ModalColetaDadosVittaltecComponent_Template", "rf", "ctx", "ModalColetaDadosVittaltecComponent_div_15_Template", "ModalColetaDadosVittaltecComponent_div_16_Template", "ModalColetaDadosVittaltecComponent_div_17_Template", "ModalColetaDadosVittaltecComponent_div_18_Template", "ModalColetaDadosVittaltecComponent_div_20_Template", "ModalColetaDadosVittaltecComponent_div_21_Template", "ModalColetaDadosVittaltecComponent_div_22_Template", "ModalColetaDadosVittaltecComponent_div_23_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "JsonPipe", "DecimalPipe", "TitleCasePipe", "DatePipe", "i4", "MatButton", "i5", "MatIcon", "MatDialogClose", "i6", "MatProgressBar", "i7", "<PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\fila-espera\\modal-coleta-dados-vittaltec\\modal-coleta-dados-vittaltec.component.ts", "C:\\Users\\<USER>\\source\\repos\\Bonecare\\Bonecare\\src\\app\\acesso-rapido-consulta\\fila-espera\\modal-coleta-dados-vittaltec\\modal-coleta-dados-vittaltec.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, Inject, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { MatDividerModule } from '@angular/material/divider';\r\nimport { finalize, catchError } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\nimport { LogRequisicaoExameModelView } from 'src/app/model/IntegrationApiExame';\r\nimport { IntegrationService } from '../../pre-consulta-questionario/Service/integracao-vittal-tec.service';\r\nimport { CriptografarUtil } from 'src/app/Util/Criptografar.util';\r\n\r\n// Interface for the vital signs response\r\ninterface VitalSignsResponse {\r\n  status: string;\r\n  data: {\r\n    idPaciente: string;\r\n    pressaoSistolica: number;\r\n    pressaoDiastolica: number;\r\n    temperatura: number;\r\n    oxigenacao: number;\r\n    batimento: number;\r\n  };\r\n}\r\n\r\n// Interface for the vital signs data to be stored\r\ninterface VitalSignsData {\r\n  idPaciente: string;\r\n  pressaoSistolica: number;\r\n  pressaoDiastolica: number;\r\n  temperatura: number;\r\n  oxigenacao: number;\r\n  batimento: number;\r\n  timestamp?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-modal-coleta-dados-vittaltec',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatSelectModule,\r\n    MatCheckboxModule,\r\n    MatIconModule,\r\n    MatDialogModule,\r\n    MatProgressBarModule,\r\n    MatDividerModule\r\n  ],\r\n  templateUrl: './modal-coleta-dados-vittaltec.component.html',\r\n  styleUrl: './modal-coleta-dados-vittaltec.component.scss'\r\n})\r\nexport class ModalColetaDadosVittaltecComponent implements OnInit {\r\n  isLoading = false;\r\n  currentStep = 0;\r\n  errorMessage = '';\r\n  capturedData: any = null;\r\n  showDataPreview = false;\r\n  steps = [\r\n    { label: 'Verificando conectividade...', completed: false, error: false },\r\n    { label: 'Capturando dados...', completed: false, error: false },\r\n    { label: 'Lendo informações...', completed: false, error: false },\r\n    { label: 'Alternando janela do navegador...', completed: false, error: false }\r\n  ];\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ModalColetaDadosVittaltecComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: any,\r\n    private integrationService: IntegrationService,\r\n    private cdr: ChangeDetectorRef\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.clear();\r\n    this.startIntegrationProcess();\r\n  }\r\n\r\n  startIntegrationProcess(): void {\r\n    this.isLoading = true;\r\n    this.currentStep = 0;\r\n    this.errorMessage = '';\r\n    this.capturedData = null;\r\n    this.showDataPreview = false;\r\n    this.steps.forEach(step => {\r\n      step.completed = false;\r\n      step.error = false;\r\n    });\r\n    this.executeHealthCheck();\r\n  }\r\n\r\n  private executeHealthCheck(): void {\r\n    this.currentStep = 0;\r\n    this.cdr.detectChanges();\r\n\r\n    this.integrationService.healthCheck()\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Health check error caught:', error);\r\n          this.steps[0].error = true;\r\n          this.errorMessage = 'Falha na verificação de conectividade. Verifique se o serviço VittalTec está rodando na porta 8080.';\r\n          this.isLoading = false;\r\n          this.registrarLog('healthCheck', 'ERRO', JSON.stringify(error));\r\n          this.cdr.detectChanges();\r\n          return of(null);\r\n        }),\r\n        finalize(() => {\r\n          setTimeout(() => {\r\n            if (!this.steps[0].error) {\r\n              this.steps[0].completed = true;\r\n              this.cdr.detectChanges();\r\n              this.executeCapture();\r\n            }\r\n          }, 500);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.registrarLog('healthCheck', 'SUCESSO', JSON.stringify(response));\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Health check subscription error:', error);\r\n          this.steps[0].error = true;\r\n          this.errorMessage = 'Erro inesperado na verificação de conectividade.';\r\n          this.isLoading = false;\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n  }\r\n\r\n  private executeCapture(): void {\r\n    this.currentStep = 1;\r\n    this.cdr.detectChanges();\r\n\r\n    this.integrationService.capture()\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Capture error caught:', error);\r\n          this.steps[1].error = true;\r\n          this.errorMessage = 'Falha na captura dos dados. Verifique se o dispositivo VittalTec está conectado e funcionando.';\r\n          this.isLoading = false;\r\n          this.registrarLog('capture', 'ERRO', JSON.stringify(error));\r\n          this.cdr.detectChanges();\r\n          return of(null);\r\n        }),\r\n        finalize(() => {\r\n          setTimeout(() => {\r\n            if (!this.steps[1].error) {\r\n              this.steps[1].completed = true;\r\n              this.cdr.detectChanges();\r\n              this.executeRead();\r\n            }\r\n          }, 500);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.registrarLog('capture', 'SUCESSO', JSON.stringify(response));\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Capture subscription error:', error);\r\n          this.steps[1].error = true;\r\n          this.errorMessage = 'Erro inesperado na captura de dados.';\r\n          this.isLoading = false;\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n  }\r\n\r\n  private executeRead(): void {\r\n    this.currentStep = 2;\r\n    this.cdr.detectChanges();\r\n\r\n    this.integrationService.read()\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.error('Read error caught:', error);\r\n          this.steps[2].error = true;\r\n          this.errorMessage = 'Falha na leitura dos dados. Verifique se os dados foram capturados corretamente.';\r\n          this.isLoading = false;\r\n          this.registrarLog('read', 'ERRO', JSON.stringify(error));\r\n          this.cdr.detectChanges();\r\n          return of(null);\r\n        }),\r\n        finalize(() => {\r\n          setTimeout(() => {\r\n            if (!this.steps[2].error) {\r\n              this.steps[2].completed = true;\r\n              this.cdr.detectChanges();\r\n              if (this.capturedData) {\r\n                this.showDataPreview = true;\r\n                this.isLoading = false;\r\n              } else {\r\n                this.executeSwitchWindow();\r\n              }\r\n            }\r\n          }, 500);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.registrarLog('read', 'SUCESSO', JSON.stringify(response));\r\n            console.log(\"response\", response);\r\n            if (response && (Array.isArray(response) ? response.length > 0 : Object.keys(response).length > 0)) {\r\n              this.capturedData = response;\r\n\r\n              \r\n\r\n              CriptografarUtil.localStorageCriptografado(\"VittalTecDados\", response.data);\r\n            } else {\r\n              this.steps[2].error = true;\r\n              this.errorMessage = 'Nenhum dado foi encontrado para leitura.';\r\n              this.isLoading = false;\r\n            }\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Read subscription error:', error);\r\n          this.steps[2].error = true;\r\n          this.errorMessage = 'Erro inesperado na leitura de dados.';\r\n          this.isLoading = false;\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n  }\r\n\r\n  private executeSwitchWindow(): void {\r\n    this.currentStep = 3;\r\n\r\n    this.integrationService.switchWindowBrowser()\r\n      .pipe(\r\n        finalize(() => {\r\n          setTimeout(() => {\r\n            if (!this.steps[3].error) {\r\n              this.steps[3].completed = true;\r\n              this.isLoading = false;\r\n              setTimeout(() => {\r\n                this.onContinue();\r\n              }, 1000);\r\n            }\r\n          }, 500);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.registrarLog('switchWindowBrowser', '', JSON.stringify(response));\r\n        },\r\n        error: (error) => {\r\n          console.error('Switch window failed:', error);\r\n          this.steps[3].error = true;\r\n          this.errorMessage = 'Falha ao alternar a janela do navegador.';\r\n          this.isLoading = false;\r\n          this.registrarLog('switchWindowBrowser', '', JSON.stringify(error));\r\n        }\r\n      });\r\n  }\r\n\r\n  private registrarLog(endpoint: string, requisicao: string, resposta: string): void {\r\n    const logData: LogRequisicaoExameModelView = {\r\n      Id: null,\r\n      Requisicao: `${endpoint}: ${requisicao}`,\r\n      Resposta: resposta,\r\n      DtCadastro: new Date()\r\n    };\r\n\r\n    this.integrationService.RegistraLogRequisicao(logData).subscribe({\r\n      next: (response) => {\r\n        response;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao registrar log:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  continueProcess(): void {\r\n    this.showDataPreview = false;\r\n    this.isLoading = true;\r\n    this.executeSwitchWindow();\r\n  }\r\n\r\n  areAllStepsCompleted(): boolean {\r\n    return this.steps.every(step => step.completed);\r\n  }\r\n\r\n  retry(): void {\r\n    this.startIntegrationProcess();\r\n  }\r\n\r\n  startProcess(): void {\r\n    this.startIntegrationProcess();\r\n  }\r\n\r\n  onContinue(): void {\r\n    this.dialogRef.close({ action: 'continuar', data: this.capturedData });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close({ action: 'cancelar' });\r\n  }\r\n\r\n  onClose(): void {\r\n    this.dialogRef.close();\r\n  }\r\n}", "<div class=\"modal-overlay\">\r\n    <div class=\"modal-container\">\r\n        <div class=\"modal-card\">\r\n\r\n            <div class=\"modal-header\">\r\n                <div class=\"header-left\">\r\n                    <div class=\"icon-wrapper\">\r\n                        <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                            <path d=\"M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z\" stroke=\"#00ff80\"\r\n                                stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" />\r\n                            <path d=\"M9 12L11 14L15 10\" stroke=\"#00ff80\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                stroke-linejoin=\"round\" />\r\n                        </svg>\r\n                    </div>\r\n                    <div class=\"header-text\">\r\n                        <h2 class=\"modal-title\">Coleta de Dados VittalTec</h2>\r\n                        <p class=\"modal-subtitle\">Integração com dispositivo de análise</p>\r\n                    </div>\r\n                </div>\r\n                <!-- <button mat-icon-button (click)=\"onClose()\" class=\"close-button\">\r\n                    <mat-icon>close</mat-icon>\r\n                </button> -->\r\n            </div>\r\n\r\n            <div class=\"modal-content\">\r\n\r\n                <div class=\"steps-container\" *ngIf=\"isLoading || !showDataPreview\">\r\n                    <div class=\"step-item\" *ngFor=\"let step of steps; let i = index\" [class.active]=\"currentStep === i\"\r\n                        [class.completed]=\"step.completed\" [class.error]=\"step.error\">\r\n\r\n                        <div class=\"step-icon\">\r\n                            <mat-icon *ngIf=\"step.completed && !step.error\">check_circle</mat-icon>\r\n                            <mat-icon *ngIf=\"step.error\">error</mat-icon>\r\n                            <mat-icon *ngIf=\"!step.completed && !step.error && currentStep === i\"\r\n                                class=\"rotating\">sync</mat-icon>\r\n                            <mat-icon\r\n                                *ngIf=\"!step.completed && !step.error && currentStep !== i\">radio_button_unchecked</mat-icon>\r\n                        </div>\r\n\r\n                        <div class=\"step-content\">\r\n                            <span class=\"step-label\">{{step.label}}</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"progress-container\" *ngIf=\"isLoading\">\r\n                        <mat-progress-bar mode=\"indeterminate\"></mat-progress-bar>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"error-container\" *ngIf=\"errorMessage && !isLoading\">\r\n                    <div class=\"error-card\">\r\n                        <div class=\"error-header\">\r\n                            <mat-icon class=\"error-icon\">error_outline</mat-icon>\r\n                            <h3 class=\"error-title\">Ops! Algo deu errado</h3>\r\n                        </div>\r\n                        <p class=\"error-message\">{{errorMessage}}</p>\r\n                        <div class=\"error-actions\">\r\n                            <button mat-raised-button color=\"primary\" (click)=\"startProcess()\" class=\"retry-button\">\r\n                                <mat-icon>refresh</mat-icon>\r\n                                Tentar Novamente\r\n                            </button>\r\n                            <button mat-stroked-button mat-dialog-close class=\"cancel-button\">\r\n                                Cancelar\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"data-preview-container\" *ngIf=\"showDataPreview && capturedData\">\r\n                    <div class=\"preview-header\">\r\n                        <mat-icon class=\"preview-icon\">assignment_turned_in</mat-icon>\r\n                        <h3 class=\"preview-title\">Dados Coletados com Sucesso</h3>\r\n                    </div>\r\n\r\n                    <mat-divider></mat-divider>\r\n\r\n                    <div class=\"preview-content\">\r\n                        <!-- Status do processo -->\r\n                        <div class=\"status-section\" *ngIf=\"capturedData?.status\">\r\n                            <div class=\"status-item\" [ngClass]=\"'status-' + capturedData.status.toLowerCase()\">\r\n                                <mat-icon class=\"status-icon\">\r\n                                    {{ capturedData.status === 'aprovado' ? 'check_circle' : 'info' }}\r\n                                </mat-icon>\r\n                                <span class=\"status-text\">Status: {{ capturedData.status | titlecase }}</span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Dados do paciente -->\r\n                        <div class=\"patient-data-section\" *ngIf=\"capturedData?.data\">\r\n                            <h4 class=\"section-title\">\r\n                                <mat-icon>person</mat-icon>\r\n                                Dados do Paciente\r\n                            </h4>\r\n\r\n                            <div class=\"data-grid\">\r\n                                <!-- ID do Paciente -->\r\n                                <div class=\"data-item\" *ngIf=\"capturedData.data.IdPaciente\">\r\n                                    <div class=\"data-label\">\r\n                                        <mat-icon class=\"data-icon\">badge</mat-icon>\r\n                                        ID do Paciente:\r\n                                    </div>\r\n                                    <div class=\"data-value\">{{ capturedData.data.IdPaciente }}</div>\r\n                                </div>\r\n\r\n                                <!-- Pressão Arterial -->\r\n                                <div class=\"data-item vital-sign\"\r\n                                    *ngIf=\"capturedData.data.pressaoSistolica || capturedData.data.pressaoDiastolica\">\r\n                                    <div class=\"data-label\">\r\n                                        <mat-icon class=\"data-icon\">favorite</mat-icon>\r\n                                        Pressão Arterial:\r\n                                    </div>\r\n                                    <div class=\"data-value pressure-value\">\r\n                                        <span class=\"systolic\">{{ capturedData.data.pressaoSistolica || '--' }}</span>\r\n                                        <span class=\"separator\">/</span>\r\n                                        <span class=\"diastolic\">{{ capturedData.data.pressaoDiastolica || '--' }}</span>\r\n                                        <span class=\"unit\">mmHg</span>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- Temperatura -->\r\n                                <div class=\"data-item vital-sign\" *ngIf=\"capturedData.data.temperatura\">\r\n                                    <div class=\"data-label\">\r\n                                        <mat-icon class=\"data-icon\">device_thermostat</mat-icon>\r\n                                        Temperatura:\r\n                                    </div>\r\n                                    <div class=\"data-value\">\r\n                                        {{ capturedData.data.temperatura | number:'1.1-1' }}°C\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- Oxigenação -->\r\n                                <div class=\"data-item vital-sign\" *ngIf=\"capturedData.data.oxigenacao\">\r\n                                    <div class=\"data-label\">\r\n                                        <mat-icon class=\"data-icon\">air</mat-icon>\r\n                                        Saturação de O₂:\r\n                                    </div>\r\n                                    <div class=\"data-value\">\r\n                                        {{ capturedData.data.oxigenacao }}%\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- Batimentos Cardíacos -->\r\n                                <div class=\"data-item vital-sign\" *ngIf=\"capturedData.data.batimento\">\r\n                                    <div class=\"data-label\">\r\n                                        <mat-icon class=\"data-icon\">monitor_heart</mat-icon>\r\n                                        Freq. Cardíaca:\r\n                                    </div>\r\n                                    <div class=\"data-value\">\r\n                                        {{ capturedData.data.batimento }} <span class=\"unit\">bpm</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Timestamp de coleta (se disponível) -->\r\n                        <div class=\"timestamp-section\" *ngIf=\"capturedData?.timestamp\">\r\n                            <div class=\"timestamp-item\">\r\n                                <mat-icon class=\"timestamp-icon\">schedule</mat-icon>\r\n                                <span class=\"timestamp-text\">\r\n                                    Coletado em: {{ capturedData.timestamp | date:'dd/MM/yyyy HH:mm:ss' }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Fallback para dados não estruturados -->\r\n                        <div class=\"raw-data-section\" *ngIf=\"!capturedData?.data && capturedData\">\r\n                            <h4 class=\"section-title\">\r\n                                <mat-icon>data_object</mat-icon>\r\n                                Dados Coletados\r\n                            </h4>\r\n                            <div class=\"raw-data-container\">\r\n                                <pre class=\"raw-data\">{{ capturedData | json }}</pre>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- Mensagem quando não há dados -->\r\n                        <div class=\"no-data-section\" *ngIf=\"!capturedData\">\r\n                            <mat-icon class=\"no-data-icon\">info_outline</mat-icon>\r\n                            <p class=\"no-data-text\">Nenhum dado disponível para visualização.</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"success-container\" *ngIf=\"areAllStepsCompleted() && !showDataPreview && !isLoading\">\r\n                    <div class=\"success-card\">\r\n                        <mat-icon class=\"success-icon\">check_circle</mat-icon>\r\n                        <h3 class=\"success-title\">Processo Concluído!</h3>\r\n                        <p class=\"success-message\">\r\n                            Todos os dados foram coletados e processados com sucesso.\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n            <div class=\"modal-actions\">\r\n                <div class=\"action-group\" *ngIf=\"errorMessage && !isLoading\">\r\n                    <button mat-stroked-button (click)=\"onCancel()\" class=\"btn-secondary\">\r\n                        <mat-icon>close</mat-icon>\r\n                        Cancelar\r\n                    </button>\r\n                    <button mat-raised-button (click)=\"retry()\" color=\"primary\" class=\"btn-primary\">\r\n                        <mat-icon>refresh</mat-icon>\r\n                        Tentar Novamente\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"action-group\" *ngIf=\"showDataPreview\">\r\n                    <button mat-stroked-button (click)=\"onCancel()\" class=\"btn-secondary\">\r\n                        <mat-icon>close</mat-icon>\r\n                        Cancelar\r\n                    </button>\r\n                    <button mat-raised-button (click)=\"continueProcess()\" color=\"primary\" class=\"btn-primary\">\r\n                        <mat-icon>arrow_forward</mat-icon>\r\n                        Confirmar e Continuar\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"action-group\"\r\n                    *ngIf=\"areAllStepsCompleted() && !showDataPreview && !isLoading && !errorMessage\">\r\n                    <button mat-raised-button (click)=\"onContinue()\" color=\"primary\" class=\"btn-primary full-width\">\r\n                        <mat-icon>check</mat-icon>\r\n                        Finalizar\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"action-group loading-actions\" *ngIf=\"isLoading\">\r\n                    <p class=\"loading-text\">\r\n                        <mat-icon class=\"loading-icon\">hourglass_empty</mat-icon>\r\n                        Processando...\r\n                    </p>\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoCC,iBAAiB,QAAQ,eAAe;AAC5E,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,YAAY,EAAEC,eAAe,EAAEC,eAAe,QAAQ,0BAA0B;AACzF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AACrD,SAASC,EAAE,QAAQ,MAAM;AAEzB,SAASC,kBAAkB,QAAQ,uEAAuE;AAC1G,SAASC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;ICcrCC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACvEH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7CH,EAAA,CAAAC,cAAA,mBACqB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpCH,EAAA,CAAAC,cAAA,eACgE;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IANrGH,EAHJ,CAAAC,cAAA,cACkE,cAEvC;IAKnBD,EAJA,CAAAI,UAAA,IAAAC,mEAAA,uBAAgD,IAAAC,mEAAA,uBACnB,IAAAC,mEAAA,uBAER,IAAAC,mEAAA,uBAE2C;IACpER,EAAA,CAAAG,YAAA,EAAM;IAGFH,EADJ,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC5C,EACJ;;;;;;IAdiCH,EAD0B,CAAAS,WAAA,WAAAC,MAAA,CAAAC,WAAA,KAAAC,IAAA,CAAkC,cAAAC,OAAA,CAAAC,SAAA,CAC7D,UAAAD,OAAA,CAAAE,KAAA,CAA2B;IAG9Cf,EAAA,CAAAgB,SAAA,GAAmC;IAAnChB,EAAA,CAAAiB,UAAA,SAAAJ,OAAA,CAAAC,SAAA,KAAAD,OAAA,CAAAE,KAAA,CAAmC;IACnCf,EAAA,CAAAgB,SAAA,EAAgB;IAAhBhB,EAAA,CAAAiB,UAAA,SAAAJ,OAAA,CAAAE,KAAA,CAAgB;IAChBf,EAAA,CAAAgB,SAAA,EAAyD;IAAzDhB,EAAA,CAAAiB,UAAA,UAAAJ,OAAA,CAAAC,SAAA,KAAAD,OAAA,CAAAE,KAAA,IAAAL,MAAA,CAAAC,WAAA,KAAAC,IAAA,CAAyD;IAG/DZ,EAAA,CAAAgB,SAAA,EAAyD;IAAzDhB,EAAA,CAAAiB,UAAA,UAAAJ,OAAA,CAAAC,SAAA,KAAAD,OAAA,CAAAE,KAAA,IAAAL,MAAA,CAAAC,WAAA,KAAAC,IAAA,CAAyD;IAIrCZ,EAAA,CAAAgB,SAAA,GAAc;IAAdhB,EAAA,CAAAkB,iBAAA,CAAAL,OAAA,CAAAM,KAAA,CAAc;;;;;IAI/CnB,EAAA,CAAAC,cAAA,cAAkD;IAC9CD,EAAA,CAAAoB,SAAA,2BAA0D;IAC9DpB,EAAA,CAAAG,YAAA,EAAM;;;;;IApBVH,EAAA,CAAAC,cAAA,cAAmE;IAkB/DD,EAjBA,CAAAI,UAAA,IAAAiB,wDAAA,mBACkE,IAAAC,wDAAA,kBAgBhB;IAGtDtB,EAAA,CAAAG,YAAA,EAAM;;;;IApBsCH,EAAA,CAAAgB,SAAA,EAAU;IAAVhB,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAAa,KAAA,CAAU;IAiBjBvB,EAAA,CAAAgB,SAAA,EAAe;IAAfhB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAc,SAAA,CAAe;;;;;;IAQxCxB,EAHZ,CAAAC,cAAA,cAAgE,cACpC,cACM,mBACO;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAChDF,EADgD,CAAAG,YAAA,EAAK,EAC/C;IACNH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzCH,EADJ,CAAAC,cAAA,cAA2B,kBACiE;IAA9CD,EAAA,CAAAyB,UAAA,mBAAAC,4EAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAlB,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAAqB,YAAA,EAAc;IAAA,EAAC;IAC9D/B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkE;IAC9DD,EAAA,CAAAE,MAAA,kBACJ;IAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ;;;;IAX2BH,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAkB,iBAAA,CAAAR,MAAA,CAAAsB,YAAA,CAAgB;;;;;IAyBjChC,EAFR,CAAAC,cAAA,cAAyD,cAC8B,mBACjD;IAC1BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA6C;;IAE/EF,EAF+E,CAAAG,YAAA,EAAO,EAC5E,EACJ;;;;IANuBH,EAAA,CAAAgB,SAAA,EAAyD;IAAzDhB,EAAA,CAAAiB,UAAA,wBAAAP,MAAA,CAAAuB,YAAA,CAAAC,MAAA,CAAAC,WAAA,GAAyD;IAE1EnC,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAoC,kBAAA,MAAA1B,MAAA,CAAAuB,YAAA,CAAAC,MAAA,+CACJ;IAC0BlC,EAAA,CAAAgB,SAAA,GAA6C;IAA7ChB,EAAA,CAAAoC,kBAAA,aAAApC,EAAA,CAAAqC,WAAA,OAAA3B,MAAA,CAAAuB,YAAA,CAAAC,MAAA,MAA6C;;;;;IAe/DlC,EAFR,CAAAC,cAAA,cAA4D,cAChC,mBACQ;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAE,MAAA,wBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAC9DF,EAD8D,CAAAG,YAAA,EAAM,EAC9D;;;;IADsBH,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAkB,iBAAA,CAAAR,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAC,UAAA,CAAkC;;;;;IAOtDvC,EAHR,CAAAC,cAAA,cACsF,cAC1D,mBACQ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAE,MAAA,+BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAAuC,eACZ;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9EH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAE/BF,EAF+B,CAAAG,YAAA,EAAO,EAC5B,EACJ;;;;IALyBH,EAAA,CAAAgB,SAAA,GAAgD;IAAhDhB,EAAA,CAAAkB,iBAAA,CAAAR,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAE,gBAAA,SAAgD;IAE/CxC,EAAA,CAAAgB,SAAA,GAAiD;IAAjDhB,EAAA,CAAAkB,iBAAA,CAAAR,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAG,iBAAA,SAAiD;;;;;IAQzEzC,EAFR,CAAAC,cAAA,cAAwE,cAC5C,mBACQ;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxDH,EAAA,CAAAE,MAAA,qBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAE,MAAA,GACJ;;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAA0C,WAAA,OAAAhC,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAK,WAAA,uBACJ;;;;;IAMI3C,EAFR,CAAAC,cAAA,cAAuE,cAC3C,mBACQ;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAE,MAAA,wCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAoC,kBAAA,MAAA1B,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAM,UAAA,OACJ;;;;;IAMI5C,EAFR,CAAAC,cAAA,cAAsE,cAC1C,mBACQ;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAEhEF,EAFgE,CAAAG,YAAA,EAAO,EAC7D,EACJ;;;;IAFEH,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAoC,kBAAA,MAAA1B,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAO,SAAA,MAAkC;;;;;IA1D1C7C,EAFR,CAAAC,cAAA,cAA6D,aAC/B,eACZ;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAuB;IAgDnBD,EA9CA,CAAAI,UAAA,IAAA0C,8DAAA,kBAA4D,IAAAC,8DAAA,mBAU0B,IAAAC,8DAAA,kBAcd,IAAAC,8DAAA,kBAWD,KAAAC,+DAAA,kBAWD;IAU9ElD,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAxD0BH,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAC,UAAA,CAAkC;IAUrDvC,EAAA,CAAAgB,SAAA,EAA+E;IAA/EhB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAE,gBAAA,IAAA9B,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAG,iBAAA,CAA+E;IAcjDzC,EAAA,CAAAgB,SAAA,EAAmC;IAAnChB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAK,WAAA,CAAmC;IAWnC3C,EAAA,CAAAgB,SAAA,EAAkC;IAAlChB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAM,UAAA,CAAkC;IAWlC5C,EAAA,CAAAgB,SAAA,EAAiC;IAAjChB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAAO,SAAA,CAAiC;;;;;IAepE7C,EAFR,CAAAC,cAAA,cAA+D,cAC/B,mBACS;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,GACJ;;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACL,EACJ;;;;IAHMH,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAoC,kBAAA,mBAAApC,EAAA,CAAA0C,WAAA,OAAAhC,MAAA,CAAAuB,YAAA,CAAAkB,SAAA,8BACJ;;;;;IAOAnD,EAFR,CAAAC,cAAA,cAA0E,aAC5C,eACZ;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,wBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,cAAgC,cACN;IAAAD,EAAA,CAAAE,MAAA,GAAyB;;IAEvDF,EAFuD,CAAAG,YAAA,EAAM,EACnD,EACJ;;;;IAFwBH,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAqC,WAAA,OAAA3B,MAAA,CAAAuB,YAAA,EAAyB;;;;;IAMnDjC,EADJ,CAAAC,cAAA,cAAmD,mBAChB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,+DAAyC;IACrEF,EADqE,CAAAG,YAAA,EAAI,EACnE;;;;;IA7GNH,EAFR,CAAAC,cAAA,cAA4E,cAC5C,mBACO;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9DH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IACzDF,EADyD,CAAAG,YAAA,EAAK,EACxD;IAENH,EAAA,CAAAoB,SAAA,kBAA2B;IAE3BpB,EAAA,CAAAC,cAAA,cAA6B;IAoGzBD,EAlGA,CAAAI,UAAA,IAAAgD,wDAAA,kBAAyD,IAAAC,wDAAA,mBAUI,KAAAC,yDAAA,kBAmEE,KAAAC,yDAAA,kBAUW,KAAAC,yDAAA,kBAWvB;IAK3DxD,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAvG+BH,EAAA,CAAAgB,SAAA,GAA0B;IAA1BhB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,kBAAAvB,MAAA,CAAAuB,YAAA,CAAAC,MAAA,CAA0B;IAUpBlC,EAAA,CAAAgB,SAAA,EAAwB;IAAxBhB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,kBAAAvB,MAAA,CAAAuB,YAAA,CAAAK,IAAA,CAAwB;IAmE3BtC,EAAA,CAAAgB,SAAA,EAA6B;IAA7BhB,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAuB,YAAA,kBAAAvB,MAAA,CAAAuB,YAAA,CAAAkB,SAAA,CAA6B;IAU9BnD,EAAA,CAAAgB,SAAA,EAAyC;IAAzChB,EAAA,CAAAiB,UAAA,WAAAP,MAAA,CAAAuB,YAAA,kBAAAvB,MAAA,CAAAuB,YAAA,CAAAK,IAAA,KAAA5B,MAAA,CAAAuB,YAAA,CAAyC;IAW1CjC,EAAA,CAAAgB,SAAA,EAAmB;IAAnBhB,EAAA,CAAAiB,UAAA,UAAAP,MAAA,CAAAuB,YAAA,CAAmB;;;;;IASjDjC,EAFR,CAAAC,cAAA,cAAgG,cAClE,mBACS;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,+BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,YAA2B;IACvBD,EAAA,CAAAE,MAAA,kEACJ;IAERF,EAFQ,CAAAG,YAAA,EAAI,EACF,EACJ;;;;;;IAMFH,EADJ,CAAAC,cAAA,cAA6D,iBACa;IAA3CD,EAAA,CAAAyB,UAAA,mBAAAgC,2EAAA;MAAAzD,EAAA,CAAA2B,aAAA,CAAA+B,GAAA;MAAA,MAAAhD,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAAiD,QAAA,EAAU;IAAA,EAAC;IAC3C3D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,iBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgF;IAAtDD,EAAA,CAAAyB,UAAA,mBAAAmC,2EAAA;MAAA5D,EAAA,CAAA2B,aAAA,CAAA+B,GAAA;MAAA,MAAAhD,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAAmD,KAAA,EAAO;IAAA,EAAC;IACvC7D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,yBACJ;IACJF,EADI,CAAAG,YAAA,EAAS,EACP;;;;;;IAGFH,EADJ,CAAAC,cAAA,cAAkD,iBACwB;IAA3CD,EAAA,CAAAyB,UAAA,mBAAAqC,2EAAA;MAAA9D,EAAA,CAAA2B,aAAA,CAAAoC,GAAA;MAAA,MAAArD,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAAiD,QAAA,EAAU;IAAA,EAAC;IAC3C3D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,iBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA0F;IAAhED,EAAA,CAAAyB,UAAA,mBAAAuC,2EAAA;MAAAhE,EAAA,CAAA2B,aAAA,CAAAoC,GAAA;MAAA,MAAArD,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAAuD,eAAA,EAAiB;IAAA,EAAC;IACjDjE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,8BACJ;IACJF,EADI,CAAAG,YAAA,EAAS,EACP;;;;;;IAIFH,EAFJ,CAAAC,cAAA,cACsF,iBACc;IAAtED,EAAA,CAAAyB,UAAA,mBAAAyC,2EAAA;MAAAlE,EAAA,CAAA2B,aAAA,CAAAwC,GAAA;MAAA,MAAAzD,MAAA,GAAAV,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASpB,MAAA,CAAA0D,UAAA,EAAY;IAAA,EAAC;IAC5CpE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,kBACJ;IACJF,EADI,CAAAG,YAAA,EAAS,EACP;;;;;IAIEH,EAFR,CAAAC,cAAA,cAA4D,YAChC,mBACW;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzDH,EAAA,CAAAE,MAAA,uBACJ;IACJF,EADI,CAAAG,YAAA,EAAI,EACF;;;ADxKtB,OAAM,MAAOkE,kCAAkC;EAcpCC,SAAA;EACyBhC,IAAA;EACxBiC,kBAAA;EACAC,GAAA;EAhBVhD,SAAS,GAAG,KAAK;EACjBb,WAAW,GAAG,CAAC;EACfqB,YAAY,GAAG,EAAE;EACjBC,YAAY,GAAQ,IAAI;EACxBwC,eAAe,GAAG,KAAK;EACvBlD,KAAK,GAAG,CACN;IAAEJ,KAAK,EAAE,8BAA8B;IAAEL,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,EACzE;IAAEI,KAAK,EAAE,qBAAqB;IAAEL,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,EAChE;IAAEI,KAAK,EAAE,sBAAsB;IAAEL,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,EACjE;IAAEI,KAAK,EAAE,mCAAmC;IAAEL,SAAS,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,CAC/E;EAED2D,YACSJ,SAA2D,EAClChC,IAAS,EACjCiC,kBAAsC,EACtCC,GAAsB;IAHvB,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAhC,IAAI,GAAJA,IAAI;IAC5B,KAAAiC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;EACT;EAEJG,QAAQA,CAAA;IACNC,OAAO,CAACC,KAAK,EAAE;IACf,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAACtD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACb,WAAW,GAAG,CAAC;IACpB,IAAI,CAACqB,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACwC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAClD,KAAK,CAACwD,OAAO,CAACC,IAAI,IAAG;MACxBA,IAAI,CAAClE,SAAS,GAAG,KAAK;MACtBkE,IAAI,CAACjE,KAAK,GAAG,KAAK;IACpB,CAAC,CAAC;IACF,IAAI,CAACkE,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,IAAI,CAACtE,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC6D,GAAG,CAACU,aAAa,EAAE;IAExB,IAAI,CAACX,kBAAkB,CAACY,WAAW,EAAE,CAClCC,IAAI,CACHxF,UAAU,CAAEmB,KAAK,IAAI;MACnB6D,OAAO,CAAC7D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;MAC1B,IAAI,CAACiB,YAAY,GAAG,qGAAqG;MACzH,IAAI,CAACR,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC6D,YAAY,CAAC,aAAa,EAAE,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACxE,KAAK,CAAC,CAAC;MAC/D,IAAI,CAACyD,GAAG,CAACU,aAAa,EAAE;MACxB,OAAOrF,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,EACFF,QAAQ,CAAC,MAAK;MACZ6F,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,EAAE;UACxB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACT,SAAS,GAAG,IAAI;UAC9B,IAAI,CAAC0D,GAAG,CAACU,aAAa,EAAE;UACxB,IAAI,CAACO,cAAc,EAAE;QACvB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CACAC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACP,YAAY,CAAC,aAAa,EAAE,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACK,QAAQ,CAAC,CAAC;QACvE;MACF,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;QAC1B,IAAI,CAACiB,YAAY,GAAG,kDAAkD;QACtE,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgD,GAAG,CAACU,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQO,cAAcA,CAAA;IACpB,IAAI,CAAC9E,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC6D,GAAG,CAACU,aAAa,EAAE;IAExB,IAAI,CAACX,kBAAkB,CAACsB,OAAO,EAAE,CAC9BT,IAAI,CACHxF,UAAU,CAAEmB,KAAK,IAAI;MACnB6D,OAAO,CAAC7D,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;MAC1B,IAAI,CAACiB,YAAY,GAAG,gGAAgG;MACpH,IAAI,CAACR,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC6D,YAAY,CAAC,SAAS,EAAE,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACxE,KAAK,CAAC,CAAC;MAC3D,IAAI,CAACyD,GAAG,CAACU,aAAa,EAAE;MACxB,OAAOrF,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,EACFF,QAAQ,CAAC,MAAK;MACZ6F,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,EAAE;UACxB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACT,SAAS,GAAG,IAAI;UAC9B,IAAI,CAAC0D,GAAG,CAACU,aAAa,EAAE;UACxB,IAAI,CAACY,WAAW,EAAE;QACpB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CACAJ,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACP,YAAY,CAAC,SAAS,EAAE,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACK,QAAQ,CAAC,CAAC;QACnE;MACF,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;QAC1B,IAAI,CAACiB,YAAY,GAAG,sCAAsC;QAC1D,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgD,GAAG,CAACU,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQY,WAAWA,CAAA;IACjB,IAAI,CAACnF,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC6D,GAAG,CAACU,aAAa,EAAE;IAExB,IAAI,CAACX,kBAAkB,CAACwB,IAAI,EAAE,CAC3BX,IAAI,CACHxF,UAAU,CAAEmB,KAAK,IAAI;MACnB6D,OAAO,CAAC7D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;MAC1B,IAAI,CAACiB,YAAY,GAAG,kFAAkF;MACtG,IAAI,CAACR,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC6D,YAAY,CAAC,MAAM,EAAE,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACxE,KAAK,CAAC,CAAC;MACxD,IAAI,CAACyD,GAAG,CAACU,aAAa,EAAE;MACxB,OAAOrF,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,EACFF,QAAQ,CAAC,MAAK;MACZ6F,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,EAAE;UACxB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACT,SAAS,GAAG,IAAI;UAC9B,IAAI,CAAC0D,GAAG,CAACU,aAAa,EAAE;UACxB,IAAI,IAAI,CAACjD,YAAY,EAAE;YACrB,IAAI,CAACwC,eAAe,GAAG,IAAI;YAC3B,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB,CAAC,MAAM;YACL,IAAI,CAACwE,mBAAmB,EAAE;UAC5B;QACF;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CACAN,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACP,YAAY,CAAC,MAAM,EAAE,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACK,QAAQ,CAAC,CAAC;UAC9DhB,OAAO,CAACqB,GAAG,CAAC,UAAU,EAAEL,QAAQ,CAAC;UACjC,IAAIA,QAAQ,KAAKM,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,CAACQ,MAAM,GAAG,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACV,QAAQ,CAAC,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAE;YAClG,IAAI,CAACnE,YAAY,GAAG2D,QAAQ;YAI5B7F,gBAAgB,CAACwG,yBAAyB,CAAC,gBAAgB,EAAEX,QAAQ,CAACtD,IAAI,CAAC;UAC7E,CAAC,MAAM;YACL,IAAI,CAACf,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;YAC1B,IAAI,CAACiB,YAAY,GAAG,0CAA0C;YAC9D,IAAI,CAACR,SAAS,GAAG,KAAK;UACxB;QACF;MACF,CAAC;MACDT,KAAK,EAAGA,KAAU,IAAI;QACpB6D,OAAO,CAAC7D,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;QAC1B,IAAI,CAACiB,YAAY,GAAG,sCAAsC;QAC1D,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgD,GAAG,CAACU,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQc,mBAAmBA,CAAA;IACzB,IAAI,CAACrF,WAAW,GAAG,CAAC;IAEpB,IAAI,CAAC4D,kBAAkB,CAACiC,mBAAmB,EAAE,CAC1CpB,IAAI,CACHzF,QAAQ,CAAC,MAAK;MACZ6F,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,EAAE;UACxB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACT,SAAS,GAAG,IAAI;UAC9B,IAAI,CAACU,SAAS,GAAG,KAAK;UACtBgE,UAAU,CAAC,MAAK;YACd,IAAI,CAACpB,UAAU,EAAE;UACnB,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CACAsB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACP,YAAY,CAAC,qBAAqB,EAAE,EAAE,EAAEC,IAAI,CAACC,SAAS,CAACK,QAAQ,CAAC,CAAC;MACxE,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,GAAG,IAAI;QAC1B,IAAI,CAACiB,YAAY,GAAG,0CAA0C;QAC9D,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6D,YAAY,CAAC,qBAAqB,EAAE,EAAE,EAAEC,IAAI,CAACC,SAAS,CAACxE,KAAK,CAAC,CAAC;MACrE;KACD,CAAC;EACN;EAEQsE,YAAYA,CAACoB,QAAgB,EAAEC,UAAkB,EAAEC,QAAgB;IACzE,MAAMC,OAAO,GAAgC;MAC3CC,EAAE,EAAE,IAAI;MACRC,UAAU,EAAE,GAAGL,QAAQ,KAAKC,UAAU,EAAE;MACxCK,QAAQ,EAAEJ,QAAQ;MAClBK,UAAU,EAAE,IAAIC,IAAI;KACrB;IAED,IAAI,CAAC1C,kBAAkB,CAAC2C,qBAAqB,CAACN,OAAO,CAAC,CAAClB,SAAS,CAAC;MAC/DC,IAAI,EAAGC,QAAQ,IAAI;QACjBA,QAAQ;MACV,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAkD,eAAeA,CAAA;IACb,IAAI,CAACQ,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACjD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwE,mBAAmB,EAAE;EAC5B;EAEAmB,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC5F,KAAK,CAAC6F,KAAK,CAACpC,IAAI,IAAIA,IAAI,CAAClE,SAAS,CAAC;EACjD;EAEA+C,KAAKA,CAAA;IACH,IAAI,CAACiB,uBAAuB,EAAE;EAChC;EAEA/C,YAAYA,CAAA;IACV,IAAI,CAAC+C,uBAAuB,EAAE;EAChC;EAEAV,UAAUA,CAAA;IACR,IAAI,CAACE,SAAS,CAAC+C,KAAK,CAAC;MAAEC,MAAM,EAAE,WAAW;MAAEhF,IAAI,EAAE,IAAI,CAACL;IAAY,CAAE,CAAC;EACxE;EAEA0B,QAAQA,CAAA;IACN,IAAI,CAACW,SAAS,CAAC+C,KAAK,CAAC;MAAEC,MAAM,EAAE;IAAU,CAAE,CAAC;EAC9C;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACjD,SAAS,CAAC+C,KAAK,EAAE;EACxB;;qBA/PWhD,kCAAkC,EAAArE,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAvI,YAAA,GAAAc,EAAA,CAAAwH,iBAAA,CAenCrI,eAAe,GAAAa,EAAA,CAAAwH,iBAAA,CAAAE,EAAA,CAAA5H,kBAAA,GAAAE,EAAA,CAAAwH,iBAAA,CAAAxH,EAAA,CAAAnB,iBAAA;EAAA;;UAfdwF,kCAAkC;IAAAsD,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA7H,EAAA,CAAA8H,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzD3BpI,EANpB,CAAAC,cAAA,aAA2B,aACM,aACD,aAEM,aACG,aACK;;QACtBD,EAAA,CAAAC,cAAA,aAA+F;QAG3FD,EAFA,CAAAoB,SAAA,cACkF,cAEpD;QAEtCpB,EADI,CAAAG,YAAA,EAAM,EACJ;;QAEFH,EADJ,CAAAC,cAAA,aAAyB,cACG;QAAAD,EAAA,CAAAE,MAAA,iCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtDH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAE,MAAA,4DAAqC;QAM3EF,EAN2E,CAAAG,YAAA,EAAI,EACjE,EACJ,EAIJ;QAENH,EAAA,CAAAC,cAAA,eAA2B;QA+JvBD,EA7JA,CAAAI,UAAA,KAAAkI,kDAAA,kBAAmE,KAAAC,kDAAA,mBAuBH,KAAAC,kDAAA,mBAmBY,KAAAC,kDAAA,kBAmHoB;QAUpGzI,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA2B;QA+BvBD,EA9BA,CAAAI,UAAA,KAAAsI,kDAAA,kBAA6D,KAAAC,kDAAA,kBAWX,KAAAC,kDAAA,kBAYoC,KAAAC,kDAAA,kBAO1B;QAU5E7I,EAJY,CAAAG,YAAA,EAAM,EAEJ,EACJ,EACJ;;;QAlNwCH,EAAA,CAAAgB,SAAA,IAAmC;QAAnChB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAA7G,SAAA,KAAA6G,GAAA,CAAA5D,eAAA,CAAmC;QAuBnCzE,EAAA,CAAAgB,SAAA,EAAgC;QAAhChB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAArG,YAAA,KAAAqG,GAAA,CAAA7G,SAAA,CAAgC;QAmBzBxB,EAAA,CAAAgB,SAAA,EAAqC;QAArChB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAA5D,eAAA,IAAA4D,GAAA,CAAApG,YAAA,CAAqC;QAmH1CjC,EAAA,CAAAgB,SAAA,EAA8D;QAA9DhB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAAlB,oBAAA,OAAAkB,GAAA,CAAA5D,eAAA,KAAA4D,GAAA,CAAA7G,SAAA,CAA8D;QAanExB,EAAA,CAAAgB,SAAA,GAAgC;QAAhChB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAArG,YAAA,KAAAqG,GAAA,CAAA7G,SAAA,CAAgC;QAWhCxB,EAAA,CAAAgB,SAAA,EAAqB;QAArBhB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAA5D,eAAA,CAAqB;QAY3CzE,EAAA,CAAAgB,SAAA,EAA+E;QAA/EhB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAAlB,oBAAA,OAAAkB,GAAA,CAAA5D,eAAA,KAAA4D,GAAA,CAAA7G,SAAA,KAAA6G,GAAA,CAAArG,YAAA,CAA+E;QAOzChC,EAAA,CAAAgB,SAAA,EAAe;QAAfhB,EAAA,CAAAiB,UAAA,SAAAoH,GAAA,CAAA7G,SAAA,CAAe;;;mBDnLtE5C,YAAY,EAAAkK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EAAAJ,EAAA,CAAAK,WAAA,EAAAL,EAAA,CAAAM,aAAA,EAAAN,EAAA,CAAAO,QAAA,EACZvK,mBAAmB,EACnBO,kBAAkB,EAClBE,cAAc,EACdR,eAAe,EAAAuK,EAAA,CAAAC,SAAA,EACfvK,aAAa,EACbQ,eAAe,EACfP,iBAAiB,EACjBK,aAAa,EAAAkK,EAAA,CAAAC,OAAA,EACbrK,eAAe,EAAAqI,EAAA,CAAAiC,cAAA,EACfjK,oBAAoB,EAAAkK,EAAA,CAAAC,cAAA,EACpBlK,gBAAgB,EAAAmK,EAAA,CAAAC,UAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
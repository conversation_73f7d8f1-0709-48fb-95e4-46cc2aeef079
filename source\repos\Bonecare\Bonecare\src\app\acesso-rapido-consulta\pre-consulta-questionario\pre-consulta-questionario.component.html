<div class="ai-questionnaire-container">
  <!-- <PERSON><PERSON> -->
  <div class="idle-screen" *ngIf="isIdle">
    <div class="idle-content">
      <!-- IA Robot Custom CSS -->
      <div class="ai-robot" style="margin-bottom: 9px;">
        <div class="robot-head">
          <div class="robot-eyes">
            <div class="eye left-eye"></div>
            <div class="eye right-eye"></div>
          </div>
          <div class="robot-mouth"></div>
        </div>
        <div class="robot-body">
          <div class="robot-chest"></div>
        </div>
      </div>

      <p>Vou coletar suas informações através de uma conversa natural</p>

      <div class="action-buttons">
        <button class="start-btn" (click)="iniciarAtendimento()">
          <span>Iniciar Atendimento</span>
          <div class="btn-glow"></div>
        </button>

        <!-- <PERSON><PERSON><PERSON> de teste na tela inicial -->
      </div>
    </div>
  </div>

  <!-- Tela de Atendimento -->
  <div class="chat-interface" *ngIf="!isIdle">
    <!-- Controls Header - Position Absolute Top Left -->
    <div class="controls-header">
      <div class="mode-indicator">
        <mat-icon class="mode-icon">{{ isTextMode ? 'keyboard' : 'mic' }}</mat-icon>
      </div>
      <mat-slide-toggle
        [(ngModel)]="isTextMode"
        (change)="toggleTextMode()"
        class="mode-toggle"
        [disabled]="isProcessing || isSpeaking">
        Modo Texto
      </mat-slide-toggle>

      <!-- Manual Microphone Controls -->
      <div class="microphone-controls" *ngIf="!isTextMode" [@fadeInOut]>
        <button
          mat-mini-fab
          [color]="isRecording ? 'warn' : 'primary'"
          (click)="toggleRecordingManual()"
          [disabled]="isProcessing || isSpeaking"
          class="mic-control-btn"
          [matTooltip]="isRecording ? 'Pausar Microfone' : 'Iniciar Microfone'"
          matTooltipPosition="below">
          <mat-icon>{{ isRecording ? 'mic_off' : 'mic' }}</mat-icon>
        </button>

        <button
          mat-mini-fab
          color="accent"
          (click)="recalibrarMicrofone()"
          [disabled]="isProcessing || isSpeaking"
          class="calibrate-btn"
          matTooltip="Recalibrar Microfone"
          matTooltipPosition="below">
          <mat-icon>tune</mat-icon>
        </button>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="main-chat-area">
      <!-- IA Section -->
      <div class="ai-section">
        <div class="ai-avatar"
             [class.processing]="isProcessing"
             [class.listening]="isRecording"
             [class.speaking]="isSpeaking"
             [class.waiting]="isAguardandoResposta && !isRecording && !isSpeaking">
          <div class="ai-face">
            <div class="ai-eyes">
              <div class="eye"></div>
              <div class="eye"></div>
            </div>
            <div class="ai-mouth" [class.talking]="isProcessing || isSpeaking"></div>
          </div>
          <div class="ai-pulse" *ngIf="isProcessing || isRecording || isAguardandoResposta || isSpeaking"></div>

        </div>
      </div>

      <!-- Response and Data Section -->
      <div class="response-data-section">
        <div class="response-section">
          <div class="ai-message" *ngIf="aiResponse" [@fadeInOut]>
            <div class="message-bubble scrollable-hidden">
              <p>{{ displayedText || aiResponse }}</p>
            </div>
          </div>

          <!-- Processing Indicator -->
          <div class="processing-indicator" *ngIf="isProcessing" [@fadeInOut]>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            <span class="processing-text">Processando sua resposta...</span>
          </div>

          <!-- Validation Confirmation Buttons -->
          <div class="validation-buttons" *ngIf="showValidationButtons && !isProcessing" [@fadeInOut]>
            <div class="validation-container">
              <div class="validation-message">
                <mat-icon class="validation-icon">help_outline</mat-icon>
                <span>A informação está correta?</span>
              </div>
              <div class="button-group">
                <button
                  mat-raised-button
                  color="primary"
                  class="confirm-btn"
                  (click)="confirmarInformacao()"
                  [disabled]="isProcessing">
                  <mat-icon>check</mat-icon>
                  Confirmar
                </button>
                <button
                  mat-raised-button
                  color="warn"
                  class="retry-btn"
                  (click)="tentarNovamente()"
                  [disabled]="isProcessing">
                  <mat-icon>refresh</mat-icon>
                  Tentar Novamente
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Dados Preenchidos -->
        <div class="data-section" *ngIf="getDadosPreenchidos().length > 0" [@slideInOut]>
          <div class="data-panel">
            <div class="data-header">
              <h3>Última Informação Coletada</h3>
              <button
                mat-icon-button
                (click)="openHistoryModal()"
                matTooltip="Ver todas as variáveis"
                class="history-btn">
                <mat-icon>history</mat-icon>
              </button>
            </div>
            <div class="data-item" *ngIf="getUltimaVariavelPreenchida() as lastItem" [matTooltip]="lastItem.value">
              <span class="descInfoCategoria">{{ lastItem.label }}</span>
              <span class="descInfovalue">{{ lastItem.value }}</span>
            </div>
            
            <!-- Progress indicator -->
            <div class="progress-info" *ngIf="getDadosPreenchidos().length > 0">
              <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="getProgressPercentage()"></div>
              </div>
              <span class="progress-text">{{ getDadosPreenchidos().length }}/{{ getTotalCampos() }} campos preenchidos</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area - Only show when in text mode -->
    <div class="input-section" *ngIf="isTextMode">
      <div class="input-container">
        <!-- Text Input -->
        <mat-form-field appearance="outline" class="user-input">
          <mat-label>Sua resposta</mat-label>
          <input matInput [(ngModel)]="userInput" placeholder="Digite aqui..." (keyup.enter)="enviarTexto()"
            [disabled]="isInputDisabled()">
          <button mat-icon-button matSuffix *ngIf="canSendText()" (click)="enviarTexto()" [disabled]="!canSendText()">
            <mat-icon>send</mat-icon>
          </button>
          <!-- Indicador de que está aguardando fala terminar -->
          <mat-hint *ngIf="isSpeaking" class="speaking-hint">
            <mat-icon class="hint-icon">volume_up</mat-icon>
            Aguarde o término da fala...
          </mat-hint>
        </mat-form-field>
      </div>
    </div>

    <!-- Audio Visualization -->
    <div class="audio-visualization" *ngIf="isRecording && !isTextMode" [@fadeInOut]>
      <div class="sound-wave">
        <div class="wave-bar" *ngFor="let bar of [1,2,3,4,5,6,7,8]"></div>
      </div>
      <span class="recording-text">
        <mat-icon class="recording-icon">mic</mat-icon>
        Gravando...
      </span>
    </div>

    <!-- Voice Status Indicator (Floating) -->
    <div class="voice-status-indicator" *ngIf="!isTextMode" [@fadeInOut]>
      <div class="status-icon"
           [class.recording]="isRecording"
           [class.processing]="isProcessing"
           [class.waiting]="isAguardandoResposta && !isRecording && !isProcessing">
        <mat-icon>{{ isRecording ? 'mic' : isProcessing ? 'hourglass_empty' : isAguardandoResposta ? 'hearing' : 'mic_off' }}</mat-icon>
        <div class="status-ripple" *ngIf="isRecording"></div>
      </div>
      <span class="status-text">
        {{ isRecording ? 'Ouvindo...' :
        isProcessing ? 'Processando...' :
        isAguardandoResposta ? 'Carregando microfone...' :
        isSpeaking ? 'Falando...' :
        'Aguardando...' }}
      </span>
    </div>
  </div>

</div>
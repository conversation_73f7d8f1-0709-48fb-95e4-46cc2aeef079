import { Component, OnInit, HostListener } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { ConsultaService } from 'src/app/service/consulta.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CriptografarUtil } from 'src/app/Util/Criptografar.util';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ConsultaAgoraDialogComponent } from './consulta-agora-dialog/consulta-agora-dialog.component';
import { MedicosZeroDialogComponent } from './medicos-zero-dialog/medicos-zero-dialog.component';
import { SairConsultaDialogComponent } from './sair-consulta-dialog/sair-consulta-dialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { FilaEsperaPacienteService } from 'src/app/service/fila-espera-paciente.service';
// import { SignalHubService } from 'src/app/service/signalHub.service';
import { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';
import { PatientQueueIntegrationService } from 'src/app/service/patient-queue-integration.service';
import { ModalColetaDadosVittaltecComponent } from './modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';
import { firstValueFrom, Subscription } from 'rxjs';


export interface QuestionarioPreConsultaLocal {
  nome: string;
  idade: number;
  cpf?: string;
  email?: string;
  telefone?: string;
  dataNascimento?: string;
  sintomas: string[];
  sintomasOutros?: string;
  intensidadeDor: number;
  tempoSintomas: string;
  alergias?: string;
  doencasPrevias?: string;
  observacoes?: string;
  dataPreenchimento?: string;
}



@Component({
  selector: 'app-fila-espera',
  templateUrl: './fila-espera.component.html',
  styleUrls: ['./fila-espera.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatFormFieldModule,
    MatDialogModule
  ]
})

export class FilaEsperaComponent implements OnInit {

  constructor(
    private router: Router,
    private spinner: SpinnerService,
    private consultaService: ConsultaService,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private filaEsperaPacienteService: FilaEsperaPacienteService,
    // private signalHubService: SignalHubService,
    private signalHubGuestService: SignalHubGuestService,
    private patientQueueIntegrationService: PatientQueueIntegrationService
  ) { }

  PesicaoFila = 0;
  timeInterval: any;
  ConsultaAgoraTeste: boolean = false;
  dadosQuestionario: QuestionarioPreConsultaLocal | null = null;
  dadosVittalTec: any = null; // Data collected from VittalTec modal
  tokenConexao: string | null = null;
  private subscriptions: Subscription[] = [];

  // Indicadores de status
  signalRConectado: boolean = false;
  cadastrandoNaFila: boolean = false;
  pacienteRegistrado: boolean = false;

  async ngOnInit() {
    this.carregarDadosQuestionario();
    this.carregarDadosVittalTec();

    if (!this.dadosQuestionario) {
      this.router.navigate(['/pre-consulta-questionario']);
      return;
    }

    const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');

    if (!tokenSalvo) {
      this.tokenConexao = CriptografarUtil.gerarHashToken(this.dadosQuestionario.cpf ?? "naotemcpf", new Date().toISOString());
      await this.cadastrarNaFilaDeslogado();
    } else {
      this.tokenConexao = tokenSalvo;
      await this.recuperarPosicaoFila();
    }

    this.configurarSignalR();

    await this.inicializarTokenConexao();

    await this.conectarSignalRGuest();
  }

  quitQueue() {
    this.CancelarConsulta();
  }

  private async inicializarTokenConexao() {
    const tokenSalvo = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');

    if (tokenSalvo)
      this.tokenConexao = tokenSalvo;
    else
      CriptografarUtil.localStorageCriptografado('tokenFilaEspera', this.tokenConexao!);
  }

  private async conectarSignalRGuest() {
    if (!this.tokenConexao) {
      console.error('Token de conexão não disponível');
      return;
    }

    try {
      await this.aguardarSignalRPronto();

      this.signalHubGuestService.connectGuestUser(this.tokenConexao);
      this.signalRConectado = true;
      this.verificarEstadoConexao();

      // Register patient data after SignalR connection is established
      await this.garantirColetaDadosCompletos();
      await this.registrarDadosPaciente();

    } catch (error) {
      console.error('❌ Erro ao conectar guest ao SignalR:', error);
      this.signalRConectado = false;
    }
  }

  private async aguardarSignalRPronto(): Promise<void> {
    return new Promise((resolve) => {
      if (this.signalHubGuestService.isHubConnected()) {
        resolve();
        return;
      }

      const subscription = this.signalHubGuestService.changeConsulta$.subscribe((connected) => {
        if (connected) {
          subscription.unsubscribe();
          resolve();
        }
      });

      setTimeout(() => {
        subscription.unsubscribe();
        resolve();
      }, 5000);
    });
  }

  private configurarSignalR() {
    const subAtualizaFila = this.signalHubGuestService.OnAtualizaChamaPacienteFila
      .subscribe(() => {
        this.buscarPosicaoAtual();
      });

    const subConviteReuniao = this.signalHubGuestService.OnConviteReuniao
      .subscribe((convite: any) => {
        if (convite.token === this.tokenConexao) {
          this.IniciarConsulta();
        }
      });

    const subConvidarPacienteComToken = this.signalHubGuestService.OnConvidarPacienteComToken
      .subscribe((dados: any) => {
        if (dados.token === this.tokenConexao) {
          this.IniciarConsulta();
        }
      });

    const subChamaPaciente = this.signalHubGuestService.OnChamaPacienteFila
      .subscribe(() => {
        this.buscarPosicaoAtual();
      });

    this.subscriptions.push(subAtualizaFila, subConviteReuniao, subConvidarPacienteComToken, subChamaPaciente);
  }

  carregarDadosQuestionario() {
    const dadosStorage = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');
    if (dadosStorage) {
      try {
        this.dadosQuestionario = JSON.parse(dadosStorage);
      } catch (error) {
        console.error('Erro ao parsing dos dados do questionário:', error);
        this.dadosQuestionario = null;
      }
    }
  }

  getDadosQuestionario(): QuestionarioPreConsultaLocal | null {
    return this.dadosQuestionario;
  }

  private async cadastrarNaFilaDeslogado() {
    try {
      this.cadastrandoNaFila = true;
      this.spinner.show();

      if (this.tokenConexao)
        this.filaEsperaPacienteService.salvarTokenFila(this.tokenConexao);

      const dadosComToken = {
        ...this.dadosQuestionario,
        tokenGerado: this.tokenConexao
      };

      const response = await firstValueFrom(this.consultaService.cadastrarFilaEsperaDeslogado(dadosComToken));

      if (response.sucesso) {
        await this.buscarPosicaoAtual();
        this.iniciarMonitoramentoPosicao();

        this.cadastrandoNaFila = false;
        this.cdr.detectChanges();
        this.spinner.hide();
      } else {
        console.error('❌ Erro ao cadastrar na fila:', response.Mensagem);
        this.cadastrandoNaFila = false;
        this.spinner.hide();
      }
    } catch (error) {
      console.error('❌ Erro ao cadastrar na fila de espera:', error);
      this.cadastrandoNaFila = false;
      this.spinner.hide();
    }
  }

  CancelarConsulta() {
    this.removerDaFilaEspera().then(() => {
      this.Logoff();
      this.spinner.hide();
    }).catch(err => {
      console.error('Erro ao cancelar consulta:', err);
      this.Logoff();
      this.spinner.hide();
    });
  }

  Logoff() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }

    if (this.tokenConexao) {
      this.removerDaFilaEspera();
      this.filaEsperaPacienteService.limparDadosFilaEspera();
    }


    this.router.navigate(['pre-consulta-questionario']);
  }



  IniciarConsulta() {
    if (!this.tokenConexao) {
      console.error('❌ Token não encontrado! Não é possível iniciar a consulta.');
      return;
    }

    const idConsultaTemporario = this.gerarIdConsultaTemporario();

    this.router.navigate(['/streaming-paciente'], {
      queryParams: {
        idConsulta: idConsultaTemporario,
        token: this.tokenConexao
      }
    });
  }

  private gerarIdConsultaTemporario(): number {
    return Date.now();
  }

  // Método de debug para verificar estado da conexão
  private verificarEstadoConexao() {
    const estadoConexao = this.signalHubGuestService.getEstadoConexao();
    // Verificar se tokens correspondem
    if (this.tokenConexao !== estadoConexao.tokenConexao) {
      console.warn('⚠️ PROBLEMA: Tokens não correspondem!');
      console.warn('  - Token Componente:', this.tokenConexao);
      console.warn('  - Token Service:', estadoConexao.tokenConexao);
    }

    // Testar se consegue enviar dados para o servidor
    setTimeout(() => {
      try {
        this.signalHubGuestService.enviaServer('TestConnection', this.tokenConexao);
      } catch (error) {
        console.error('❌ Erro no teste de envio:', error);
      }
    }, 2000);
  }

  abrirDialogSairConsulta() {
    const dialogRef = this.dialog.open(SairConsultaDialogComponent, {
      width: '400px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'confirmar') {
        this.CancelarConsulta();
      }
    });
  }

  abrirDialogMedicosZero() {
    const dialogRef = this.dialog.open(MedicosZeroDialogComponent, {
      width: '400px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'sair') {
        this.CancelarConsulta();
      }
    });
  }

  abrirDialogConsultaAgora() {
    const dialogRef = this.dialog.open(ConsultaAgoraDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'iniciar')
        this.IniciarConsulta();
      else if (result === 'cancelar') {
        this.CancelarConsulta();
      }
    });
  }

  @HostListener('window:beforeunload', ['$event'])
  async beforeUnloadHandler() {
    if (this.tokenConexao) {
      await this.removerDaFilaEspera();
    }
  }

  private async removerDaFilaEspera() {
    if (this.tokenConexao) {
      await firstValueFrom(this.filaEsperaPacienteService.removerDaFilaEspera(this.tokenConexao));
    }
  }

  private iniciarMonitoramentoPosicao() {
    if (this.tokenConexao) {
      this.timeInterval = setInterval(async () => {
        try {
          const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao!));
          if (response.sucesso) {
            this.PesicaoFila = response.posicaoFila;
            this.cdr.detectChanges();

            if (this.PesicaoFila === 0) {
              this.ConsultaAgoraTeste = true;
              this.abrirDialogConsultaAgora();
              clearInterval(this.timeInterval);
            }
          }
        } catch (error) {
          console.error('Erro ao consultar posição:', error);
        }
      }, 30000);
    }
  }

  private async buscarPosicaoAtual() {
    if (!this.tokenConexao) {
      console.warn('⚠️ Token não encontrado para buscar posição');
      return;
    }

    try {
      const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao));
      if (response.sucesso) {
        this.PesicaoFila = response.posicaoFila;
        this.cdr.detectChanges();
      } else
        console.error('❌ Erro ao consultar posição:', response.mensagem);
    } catch (error: any) {
      console.error('❌ Erro ao buscar posição atual:', error);
      if (error.status === 404)
        console.error('🚨 ERRO 404: Endpoint não encontrado!');
    }
  }

  /**
   * Ensures all necessary data is collected before registration
   */
  private async garantirColetaDadosCompletos() {
    debugger

    if (!this.dadosQuestionario) {
      console.warn('⚠️ Dados do questionário não encontrados, tentando carregar...');
      this.carregarDadosQuestionario();
    }

    if (!this.dadosVittalTec) {
      console.log('📊 Dados VittalTec não encontrados, tentando carregar do localStorage...');
      this.carregarDadosVittalTec();

      if (!this.dadosVittalTec) {
        console.log('ℹ️ Nenhum dado VittalTec disponível - registro continuará apenas com dados do questionário');
      }
    }

    console.log('✅ Verificação de dados concluída:', {
      questionario: !!this.dadosQuestionario,
      vittalTec: !!this.dadosVittalTec,
      token: !!this.tokenConexao
    });
  }

  /**
   * Registers patient data in the database using the same token as SignalR
   * Collects data from both questionnaire and VittalTec modal
   */
  private async registrarDadosPaciente() {
    if (!this.tokenConexao || !this.dadosQuestionario) {
      console.warn('⚠️ Token ou dados do questionário não disponíveis para registro');
      return;
    }

    if (this.pacienteRegistrado) {
      console.log('✅ Paciente já registrado, pulando registro');
      return;
    }

    try {

      if (!this.dadosVittalTec) {
        this.carregarDadosVittalTec();
      }

      const patientData = this.converterQuestionarioParaRegistro();


      const response = await firstValueFrom(
        this.patientQueueIntegrationService.registerPatientInQueue(patientData)
      );

      if (response.success) {
        this.pacienteRegistrado = true;
        console.log('✅ Paciente registrado com sucesso:', {
          token: response.patientToken,
          nome: response.patientName,
          tipoFila: response.queueType,
          comDadosVittalTec: !!this.dadosVittalTec,
          dadosEnviados: Object.keys(patientData).length
        });

        // Save registration status to localStorage
        CriptografarUtil.localStorageCriptografado('paciente-registrado', JSON.stringify({
          token: response.patientToken,
          timestamp: new Date().toISOString(),
          dadosCompletos: !!this.dadosVittalTec
        }));

      } else {
        console.warn('⚠️ Falha ao registrar paciente:', response.message);
        console.warn('Erros:', response.errors);
        // Don't prevent queue entry if patient registration fails
      }
    } catch (error) {
      console.error('❌ Erro ao registrar dados do paciente:', error);
      // Don't prevent queue entry if patient registration fails
    }
  }

  /**
   * Converts questionnaire data to patient registration format including VittalTec data
   */
  private converterQuestionarioParaRegistro(): any {
    debugger

    if (!this.dadosQuestionario || !this.tokenConexao) {
      throw new Error('Dados do questionário ou token não disponíveis');
    }

    let sintomas = this.dadosQuestionario.sintomasOutros;

    let vittalTecData: any = {};
    if (this.dadosVittalTec) {
      try {
        const data = this.dadosVittalTec.data || this.dadosVittalTec;

        if (data) {
          vittalTecData = {
            pressaoSistolica: data.pressaoSistolica,
            pressaoDiastolica: data.pressaoDiastolica,
            temperatura: data.temperatura,
            saturacaoOxigenio: data.oxigenacao,
            frequenciaCardiaca: data.batimento
          };

          console.log('📊 Dados vitais VittalTec incluídos:', vittalTecData);
        }
      } catch (error) {
        console.warn('⚠️ Erro ao processar dados VittalTec:', error);
      }
    }

    // Convert questionnaire intensity and duration to strings
    const intensidadeDorString = this.convertToStringValue(this.dadosQuestionario.intensidadeDor);
    const duracaoSintomasString = this.convertToStringValue(this.converterTempoSintomas(this.dadosQuestionario.tempoSintomas));

    const patientData = {
      token: this.tokenConexao, // Use the same token as SignalR
      nome: this.dadosQuestionario.nome,
      cpf: this.dadosQuestionario.cpf || '',
      email: this.dadosQuestionario.email,
      telefone: this.dadosQuestionario.telefone,
      dataNascimento: this.dadosQuestionario.dataNascimento,
      sintomas: sintomas,
      intensidadeDor: intensidadeDorString,
      duracaoSintomas: duracaoSintomasString,
      alergias: this.dadosQuestionario.alergias,
      doencasPrevias: this.dadosQuestionario.doencasPrevias,
      observacoesAdicionais: this.dadosQuestionario.observacoes,
      queueType: 'consulta', 
      ...vittalTecData
    };

    console.log('✅ Dados finais para registro:', patientData);
    return patientData;
  }

  /**
   * Converts symptom duration text to numeric value (in days)
   */
  private converterTempoSintomas(tempoTexto: string): number | undefined {
    if (!tempoTexto) return undefined;

    const texto = tempoTexto.toLowerCase();

    if (texto.includes('hoje') || texto.includes('horas')) {
      return 0; // Same day
    } else if (texto.includes('ontem') || texto.includes('1 dia')) {
      return 1;
    } else if (texto.includes('dias')) {
      const match = texto.match(/(\d+)\s*dias?/);
      return match ? parseInt(match[1]) : undefined;
    } else if (texto.includes('semana')) {
      const match = texto.match(/(\d+)\s*semanas?/);
      return match ? parseInt(match[1]) * 7 : 7;
    } else if (texto.includes('mês') || texto.includes('mes')) {
      const match = texto.match(/(\d+)\s*m[eê]s/);
      return match ? parseInt(match[1]) * 30 : 30;
    }

    return undefined;
  }

  /**
   * Helper methods for data conversion
   */
  private convertToStringValue(value: any): string | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }
    return String(value);
  }

  /**
   * Loads VittalTec data from encrypted localStorage if available
   */
  private carregarDadosVittalTec() {
    try {

      let dadosVittalTec = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');
      console.log("this.dadosVittalTec", dadosVittalTec);
      this.dadosVittalTec = dadosVittalTec;
    } catch (error) {
      console.error('❌ Erro geral ao carregar dados VittalTec:', error);
      this.dadosVittalTec = null;
    }
  }

  /**
   * Opens VittalTec modal for data collection (public method for template access)
   */
  public abrirModalVittalTec(): Promise<any> {
    return new Promise((resolve) => {
      const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {
        width: '600px',
        height: '500px',
        disableClose: true,
        data: {}
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result && result.action === 'continuar' && result.data) {
          this.dadosVittalTec = result.data;
          console.log('📊 Dados VittalTec coletados via modal:', this.dadosVittalTec);
        }
        resolve(result);
      });
    });
  }

  private async recuperarPosicaoFila() {
    if (!this.tokenConexao) return;

    try {
      const response = await firstValueFrom(this.consultaService.consultarPosicaoFila(this.tokenConexao));
      if (response.sucesso) {
        this.PesicaoFila = response.posicaoFila;
        this.iniciarMonitoramentoPosicao();
        this.cdr.detectChanges();
      } else {
        await this.cadastrarNaFilaDeslogado();
      }
    } catch (error) {
      console.error('Erro ao recuperar posição:', error);
      await this.cadastrarNaFilaDeslogado();
    }
  }
}

(self.webpackChunkTeleMedicina=self.webpackChunkTeleMedicina||[]).push([[147],{40644:function(d){(function(){var S,n,h,f,c,g;typeof performance<"u"&&null!==performance&&performance.now?d.exports=function(){return performance.now()}:typeof process<"u"&&null!==process&&process.hrtime?(d.exports=function(){return(S()-c)/1e6},n=process.hrtime,f=(S=function(){var y;return 1e9*(y=n())[0]+y[1]})(),g=1e9*process.uptime(),c=f-g):Date.now?(d.exports=function(){return Date.now()-h},h=Date.now()):(d.exports=function(){return(new Date).getTime()-h},h=(new Date).getTime())}).call(this)},54361:(d,S,n)=>{for(var h=n(40644),f=typeof window>"u"?global:window,c=["moz","webkit"],g="AnimationFrame",y=f["request"+g],O=f["cancel"+g]||f["cancelRequest"+g],x=0;!y&&x<c.length;x++)y=f[c[x]+"Request"+g],O=f[c[x]+"Cancel"+g]||f[c[x]+"CancelRequest"+g];if(!y||!O){var b=0,C=0,A=[];y=function(P){if(0===A.length){var w=h(),M=Math.max(0,16.666666666666668-(w-b));b=M+w,setTimeout(function(){var I=A.slice(0);A.length=0;for(var B=0;B<I.length;B++)if(!I[B].cancelled)try{I[B].callback(b)}catch(H){setTimeout(function(){throw H},0)}},Math.round(M))}return A.push({handle:++C,callback:P,cancelled:!1}),C},O=function(P){for(var w=0;w<A.length;w++)A[w].handle===P&&(A[w].cancelled=!0)}}d.exports=function(P){return y.call(f,P)},d.exports.cancel=function(){O.apply(f,arguments)},d.exports.polyfill=function(P){P||(P=f),P.requestAnimationFrame=y,P.cancelAnimationFrame=O}},96310:d=>{d.exports=function(S){this.ok=!1,this.alpha=1,"#"==S.charAt(0)&&(S=S.substr(1,6)),S=(S=S.replace(/ /g,"")).toLowerCase();var n={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};S=n[S]||S;for(var h=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(x){return[parseInt(x[1]),parseInt(x[2]),parseInt(x[3]),parseFloat(x[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(x){return[parseInt(x[1]),parseInt(x[2]),parseInt(x[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(x){return[parseInt(x[1],16),parseInt(x[2],16),parseInt(x[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(x){return[parseInt(x[1]+x[1],16),parseInt(x[2]+x[2],16),parseInt(x[3]+x[3],16)]}}],f=0;f<h.length;f++){var g=h[f].process,y=h[f].re.exec(S);if(y){var O=g(y);this.r=O[0],this.g=O[1],this.b=O[2],O.length>3&&(this.alpha=O[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var x=this.r.toString(16),b=this.g.toString(16),C=this.b.toString(16);return 1==x.length&&(x="0"+x),1==b.length&&(b="0"+b),1==C.length&&(C="0"+C),"#"+x+b+C},this.getHelpXML=function(){for(var x=new Array,b=0;b<h.length;b++)for(var C=h[b].example,A=0;A<C.length;A++)x[x.length]=C[A];for(var R in n)x[x.length]=R;var P=document.createElement("ul");for(P.setAttribute("id","rgbcolor-examples"),b=0;b<x.length;b++)try{var w=document.createElement("li"),M=new RGBColor(x[b]),I=document.createElement("div");I.style.cssText="margin: 3px; border: 1px solid black; background:"+M.toHex()+"; color:"+M.toHex(),I.appendChild(document.createTextNode("test"));var B=document.createTextNode(" "+x[b]+" -> "+M.toRGB()+" -> "+M.toHex());w.appendChild(I),w.appendChild(B),P.appendChild(w)}catch{}return P}}},26547:(d,S,n)=>{"use strict";var h=n(6214),f=n(13460),c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(f(g)+" is not a function")}},18679:(d,S,n)=>{"use strict";var h=n(74512),f=n(13460),c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(f(g)+" is not a constructor")}},935:(d,S,n)=>{"use strict";var h=n(50150),f=String,c=TypeError;d.exports=function(g){if(h(g))return g;throw new c("Can't set "+f(g)+" as a prototype")}},54080:(d,S,n)=>{"use strict";var h=n(28292),f=n(97847),c=n(57696).f,g=h("unscopables"),y=Array.prototype;void 0===y[g]&&c(y,g,{configurable:!0,value:f(null)}),d.exports=function(O){y[g][O]=!0}},55428:(d,S,n)=>{"use strict";var h=n(20650).charAt;d.exports=function(f,c,g){return c+(g?h(f,c).length:1)}},46784:(d,S,n)=>{"use strict";var h=n(51180),f=TypeError;d.exports=function(c,g){if(h(g,c))return c;throw new f("Incorrect invocation")}},36092:(d,S,n)=>{"use strict";var h=n(18785),f=String,c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(f(g)+" is not an object")}},67408:(d,S,n)=>{"use strict";var h=n(39538),f=n(95429),c=n(31083),g=function(y){return function(O,x,b){var C=h(O),A=c(C);if(0===A)return!y&&-1;var P,R=f(b,A);if(y&&x!=x){for(;A>R;)if((P=C[R++])!=P)return!0}else for(;A>R;R++)if((y||R in C)&&C[R]===x)return y||R||0;return!y&&-1}};d.exports={includes:g(!0),indexOf:g(!1)}},18307:(d,S,n)=>{"use strict";var h=n(35536);d.exports=function(f,c){var g=[][f];return!!g&&h(function(){g.call(null,c||function(){return 1},1)})}},19031:(d,S,n)=>{"use strict";var h=n(26547),f=n(81622),c=n(85254),g=n(31083),y=TypeError,O="Reduce of empty array with no initial value",x=function(b){return function(C,A,R,P){var w=f(C),M=c(w),I=g(w);if(h(A),0===I&&R<2)throw new y(O);var B=b?I-1:0,H=b?-1:1;if(R<2)for(;;){if(B in M){P=M[B],B+=H;break}if(B+=H,b?B<0:I<=B)throw new y(O)}for(;b?B>=0:I>B;B+=H)B in M&&(P=A(P,M[B],B,w));return P}};d.exports={left:x(!1),right:x(!0)}},57519:(d,S,n)=>{"use strict";var h=n(28407);d.exports=h([].slice)},67301:(d,S,n)=>{"use strict";var f=n(28292)("iterator"),c=!1;try{var g=0,y={next:function(){return{done:!!g++}},return:function(){c=!0}};y[f]=function(){return this},Array.from(y,function(){throw 2})}catch{}d.exports=function(O,x){try{if(!x&&!c)return!1}catch{return!1}var b=!1;try{var C={};C[f]=function(){return{next:function(){return{done:b=!0}}}},O(C)}catch{}return b}},69547:(d,S,n)=>{"use strict";var h=n(28407),f=h({}.toString),c=h("".slice);d.exports=function(g){return c(f(g),8,-1)}},35736:(d,S,n)=>{"use strict";var h=n(54363),f=n(6214),c=n(69547),y=n(28292)("toStringTag"),O=Object,x="Arguments"===c(function(){return arguments}());d.exports=h?c:function(C){var A,R,P;return void 0===C?"Undefined":null===C?"Null":"string"==typeof(R=function(C,A){try{return C[A]}catch{}}(A=O(C),y))?R:x?c(A):"Object"===(P=c(A))&&f(A.callee)?"Arguments":P}},40951:(d,S,n)=>{"use strict";var h=n(91960),f=n(1206),c=n(20290),g=n(57696);d.exports=function(y,O,x){for(var b=f(O),C=g.f,A=c.f,R=0;R<b.length;R++){var P=b[R];!h(y,P)&&(!x||!h(x,P))&&C(y,P,A(O,P))}}},51979:(d,S,n)=>{"use strict";var f=n(28292)("match");d.exports=function(c){var g=/./;try{"/./"[c](g)}catch{try{return g[f]=!1,"/./"[c](g)}catch{}}return!1}},71658:(d,S,n)=>{"use strict";var h=n(35536);d.exports=!h(function(){function f(){}return f.prototype.constructor=null,Object.getPrototypeOf(new f)!==f.prototype})},24186:d=>{"use strict";d.exports=function(S,n){return{value:S,done:n}}},96590:(d,S,n)=>{"use strict";var h=n(75515),f=n(57696),c=n(61285);d.exports=h?function(g,y,O){return f.f(g,y,c(1,O))}:function(g,y,O){return g[y]=O,g}},61285:d=>{"use strict";d.exports=function(S,n){return{enumerable:!(1&S),configurable:!(2&S),writable:!(4&S),value:n}}},67615:(d,S,n)=>{"use strict";var h=n(97660),f=n(57696);d.exports=function(c,g,y){return y.get&&h(y.get,g,{getter:!0}),y.set&&h(y.set,g,{setter:!0}),f.f(c,g,y)}},36403:(d,S,n)=>{"use strict";var h=n(6214),f=n(57696),c=n(97660),g=n(30928);d.exports=function(y,O,x,b){b||(b={});var C=b.enumerable,A=void 0!==b.name?b.name:O;if(h(x)&&c(x,A,b),b.global)C?y[O]=x:g(O,x);else{try{b.unsafe?y[O]&&(C=!0):delete y[O]}catch{}C?y[O]=x:f.f(y,O,{value:x,enumerable:!1,configurable:!b.nonConfigurable,writable:!b.nonWritable})}return y}},30928:(d,S,n)=>{"use strict";var h=n(69555),f=Object.defineProperty;d.exports=function(c,g){try{f(h,c,{value:g,configurable:!0,writable:!0})}catch{h[c]=g}return g}},75515:(d,S,n)=>{"use strict";var h=n(35536);d.exports=!h(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},89820:(d,S,n)=>{"use strict";var h=n(69555),f=n(18785),c=h.document,g=f(c)&&f(c.createElement);d.exports=function(y){return g?c.createElement(y):{}}},58379:d=>{"use strict";d.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},337:(d,S,n)=>{"use strict";var f=n(89820)("span").classList,c=f&&f.constructor&&f.constructor.prototype;d.exports=c===Object.prototype?void 0:c},30324:d=>{"use strict";d.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},58034:(d,S,n)=>{"use strict";var h=n(54174);d.exports=/ipad|iphone|ipod/i.test(h)&&typeof Pebble<"u"},98705:(d,S,n)=>{"use strict";var h=n(54174);d.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(h)},51806:(d,S,n)=>{"use strict";var h=n(31740);d.exports="NODE"===h},58383:(d,S,n)=>{"use strict";var h=n(54174);d.exports=/web0s(?!.*chrome)/i.test(h)},54174:(d,S,n)=>{"use strict";var f=n(69555).navigator,c=f&&f.userAgent;d.exports=c?String(c):""},11450:(d,S,n)=>{"use strict";var x,b,h=n(69555),f=n(54174),c=h.process,g=h.Deno,y=c&&c.versions||g&&g.version,O=y&&y.v8;O&&(b=(x=O.split("."))[0]>0&&x[0]<4?1:+(x[0]+x[1])),!b&&f&&(!(x=f.match(/Edge\/(\d+)/))||x[1]>=74)&&(x=f.match(/Chrome\/(\d+)/))&&(b=+x[1]),d.exports=b},31740:(d,S,n)=>{"use strict";var h=n(69555),f=n(54174),c=n(69547),g=function(y){return f.slice(0,y.length)===y};d.exports=g("Bun/")?"BUN":g("Cloudflare-Workers")?"CLOUDFLARE":g("Deno/")?"DENO":g("Node.js/")?"NODE":h.Bun&&"string"==typeof Bun.version?"BUN":h.Deno&&"object"==typeof Deno.version?"DENO":"process"===c(h.process)?"NODE":h.window&&h.document?"BROWSER":"REST"},72679:(d,S,n)=>{"use strict";var h=n(69555),f=n(20290).f,c=n(96590),g=n(36403),y=n(30928),O=n(40951),x=n(12131);d.exports=function(b,C){var M,I,B,H,Y,A=b.target,R=b.global,P=b.stat;if(M=R?h:P?h[A]||y(A,{}):h[A]&&h[A].prototype)for(I in C){if(H=C[I],B=b.dontCallGetSet?(Y=f(M,I))&&Y.value:M[I],!x(R?I:A+(P?".":"#")+I,b.forced)&&void 0!==B){if(typeof H==typeof B)continue;O(H,B)}(b.sham||B&&B.sham)&&c(H,"sham",!0),g(M,I,H,b)}}},35536:d=>{"use strict";d.exports=function(S){try{return!!S()}catch{return!0}}},46641:(d,S,n)=>{"use strict";n(56954);var h=n(65798),f=n(36403),c=n(14664),g=n(35536),y=n(28292),O=n(96590),x=y("species"),b=RegExp.prototype;d.exports=function(C,A,R,P){var w=y(C),M=!g(function(){var Y={};return Y[w]=function(){return 7},7!==""[C](Y)}),I=M&&!g(function(){var Y=!1,F=/a/;return"split"===C&&((F={}).constructor={},F.constructor[x]=function(){return F},F.flags="",F[w]=/./[w]),F.exec=function(){return Y=!0,null},F[w](""),!Y});if(!M||!I||R){var B=/./[w],H=A(w,""[C],function(Y,F,$,X,k){var rt=F.exec;return rt===c||rt===b.exec?M&&!k?{done:!0,value:h(B,F,$,X)}:{done:!0,value:h(Y,$,F,X)}:{done:!1}});f(String.prototype,C,H[0]),f(b,w,H[1])}P&&O(b[w],"sham",!0)}},39524:(d,S,n)=>{"use strict";var h=n(90509),f=Function.prototype,c=f.apply,g=f.call;d.exports="object"==typeof Reflect&&Reflect.apply||(h?g.bind(c):function(){return g.apply(c,arguments)})},31571:(d,S,n)=>{"use strict";var h=n(75013),f=n(26547),c=n(90509),g=h(h.bind);d.exports=function(y,O){return f(y),void 0===O?y:c?g(y,O):function(){return y.apply(O,arguments)}}},90509:(d,S,n)=>{"use strict";var h=n(35536);d.exports=!h(function(){var f=function(){}.bind();return"function"!=typeof f||f.hasOwnProperty("prototype")})},65798:(d,S,n)=>{"use strict";var h=n(90509),f=Function.prototype.call;d.exports=h?f.bind(f):function(){return f.apply(f,arguments)}},95573:(d,S,n)=>{"use strict";var h=n(75515),f=n(91960),c=Function.prototype,g=h&&Object.getOwnPropertyDescriptor,y=f(c,"name"),O=y&&"something"===function(){}.name,x=y&&(!h||h&&g(c,"name").configurable);d.exports={EXISTS:y,PROPER:O,CONFIGURABLE:x}},81699:(d,S,n)=>{"use strict";var h=n(28407),f=n(26547);d.exports=function(c,g,y){try{return h(f(Object.getOwnPropertyDescriptor(c,g)[y]))}catch{}}},75013:(d,S,n)=>{"use strict";var h=n(69547),f=n(28407);d.exports=function(c){if("Function"===h(c))return f(c)}},28407:(d,S,n)=>{"use strict";var h=n(90509),f=Function.prototype,c=f.call,g=h&&f.bind.bind(c,c);d.exports=h?g:function(y){return function(){return c.apply(y,arguments)}}},96578:(d,S,n)=>{"use strict";var h=n(69555),f=n(6214);d.exports=function(g,y){return arguments.length<2?function(g){return f(g)?g:void 0}(h[g]):h[g]&&h[g][y]}},13532:(d,S,n)=>{"use strict";var h=n(35736),f=n(77851),c=n(42412),g=n(30786),O=n(28292)("iterator");d.exports=function(x){if(!c(x))return f(x,O)||f(x,"@@iterator")||g[h(x)]}},16904:(d,S,n)=>{"use strict";var h=n(65798),f=n(26547),c=n(36092),g=n(13460),y=n(13532),O=TypeError;d.exports=function(x,b){var C=arguments.length<2?y(x):b;if(f(C))return c(h(C,x));throw new O(g(x)+" is not iterable")}},77851:(d,S,n)=>{"use strict";var h=n(26547),f=n(42412);d.exports=function(c,g){var y=c[g];return f(y)?void 0:h(y)}},29083:(d,S,n)=>{"use strict";var h=n(28407),f=n(81622),c=Math.floor,g=h("".charAt),y=h("".replace),O=h("".slice),x=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,b=/\$([$&'`]|\d{1,2})/g;d.exports=function(C,A,R,P,w,M){var I=R+C.length,B=P.length,H=b;return void 0!==w&&(w=f(w),H=x),y(M,H,function(Y,F){var $;switch(g(F,0)){case"$":return"$";case"&":return C;case"`":return O(A,0,R);case"'":return O(A,I);case"<":$=w[O(F,1,-1)];break;default:var X=+F;if(0===X)return Y;if(X>B){var k=c(X/10);return 0===k?Y:k<=B?void 0===P[k-1]?g(F,1):P[k-1]+g(F,1):Y}$=P[X-1]}return void 0===$?"":$})}},69555:function(d){"use strict";var S=function(n){return n&&n.Math===Math&&n};d.exports=S("object"==typeof globalThis&&globalThis)||S("object"==typeof window&&window)||S("object"==typeof self&&self)||S("object"==typeof global&&global)||S("object"==typeof this&&this)||function(){return this}()||Function("return this")()},91960:(d,S,n)=>{"use strict";var h=n(28407),f=n(81622),c=h({}.hasOwnProperty);d.exports=Object.hasOwn||function(y,O){return c(f(y),O)}},50838:d=>{"use strict";d.exports={}},21956:d=>{"use strict";d.exports=function(S,n){try{1===arguments.length?console.error(S):console.error(S,n)}catch{}}},13940:(d,S,n)=>{"use strict";var h=n(96578);d.exports=h("document","documentElement")},96500:(d,S,n)=>{"use strict";var h=n(75515),f=n(35536),c=n(89820);d.exports=!h&&!f(function(){return 7!==Object.defineProperty(c("div"),"a",{get:function(){return 7}}).a})},85254:(d,S,n)=>{"use strict";var h=n(28407),f=n(35536),c=n(69547),g=Object,y=h("".split);d.exports=f(function(){return!g("z").propertyIsEnumerable(0)})?function(O){return"String"===c(O)?y(O,""):g(O)}:g},73067:(d,S,n)=>{"use strict";var h=n(28407),f=n(6214),c=n(40140),g=h(Function.toString);f(c.inspectSource)||(c.inspectSource=function(y){return g(y)}),d.exports=c.inspectSource},29192:(d,S,n)=>{"use strict";var P,w,M,h=n(80091),f=n(69555),c=n(18785),g=n(96590),y=n(91960),O=n(40140),x=n(68806),b=n(50838),C="Object already initialized",A=f.TypeError;if(h||O.state){var H=O.state||(O.state=new(0,f.WeakMap));H.get=H.get,H.has=H.has,H.set=H.set,P=function(F,$){if(H.has(F))throw new A(C);return $.facade=F,H.set(F,$),$},w=function(F){return H.get(F)||{}},M=function(F){return H.has(F)}}else{var Y=x("state");b[Y]=!0,P=function(F,$){if(y(F,Y))throw new A(C);return $.facade=F,g(F,Y,$),$},w=function(F){return y(F,Y)?F[Y]:{}},M=function(F){return y(F,Y)}}d.exports={set:P,get:w,has:M,enforce:function(F){return M(F)?w(F):P(F,{})},getterFor:function(F){return function($){var X;if(!c($)||(X=w($)).type!==F)throw new A("Incompatible receiver, "+F+" required");return X}}}},71200:(d,S,n)=>{"use strict";var h=n(28292),f=n(30786),c=h("iterator"),g=Array.prototype;d.exports=function(y){return void 0!==y&&(f.Array===y||g[c]===y)}},14901:(d,S,n)=>{"use strict";var h=n(69547);d.exports=Array.isArray||function(c){return"Array"===h(c)}},6214:d=>{"use strict";var S="object"==typeof document&&document.all;d.exports=typeof S>"u"&&void 0!==S?function(n){return"function"==typeof n||n===S}:function(n){return"function"==typeof n}},74512:(d,S,n)=>{"use strict";var h=n(28407),f=n(35536),c=n(6214),g=n(35736),y=n(96578),O=n(73067),x=function(){},b=y("Reflect","construct"),C=/^\s*(?:class|function)\b/,A=h(C.exec),R=!C.test(x),P=function(I){if(!c(I))return!1;try{return b(x,[],I),!0}catch{return!1}},w=function(I){if(!c(I))return!1;switch(g(I)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return R||!!A(C,O(I))}catch{return!0}};w.sham=!0,d.exports=!b||f(function(){var M;return P(P.call)||!P(Object)||!P(function(){M=!0})||M})?w:P},12131:(d,S,n)=>{"use strict";var h=n(35536),f=n(6214),c=/#|\.prototype\./,g=function(C,A){var R=O[y(C)];return R===b||R!==x&&(f(A)?h(A):!!A)},y=g.normalize=function(C){return String(C).replace(c,".").toLowerCase()},O=g.data={},x=g.NATIVE="N",b=g.POLYFILL="P";d.exports=g},42412:d=>{"use strict";d.exports=function(S){return null==S}},18785:(d,S,n)=>{"use strict";var h=n(6214);d.exports=function(f){return"object"==typeof f?null!==f:h(f)}},50150:(d,S,n)=>{"use strict";var h=n(18785);d.exports=function(f){return h(f)||null===f}},80572:d=>{"use strict";d.exports=!1},63035:(d,S,n)=>{"use strict";var h=n(18785),f=n(69547),g=n(28292)("match");d.exports=function(y){var O;return h(y)&&(void 0!==(O=y[g])?!!O:"RegExp"===f(y))}},94062:(d,S,n)=>{"use strict";var h=n(96578),f=n(6214),c=n(51180),g=n(6939),y=Object;d.exports=g?function(O){return"symbol"==typeof O}:function(O){var x=h("Symbol");return f(x)&&c(x.prototype,y(O))}},32283:(d,S,n)=>{"use strict";var h=n(31571),f=n(65798),c=n(36092),g=n(13460),y=n(71200),O=n(31083),x=n(51180),b=n(16904),C=n(13532),A=n(5558),R=TypeError,P=function(M,I){this.stopped=M,this.result=I},w=P.prototype;d.exports=function(M,I,B){var rt,J,ht,vt,st,ft,Z,Y=!(!B||!B.AS_ENTRIES),F=!(!B||!B.IS_RECORD),$=!(!B||!B.IS_ITERATOR),X=!(!B||!B.INTERRUPTED),k=h(I,B&&B.that),ut=function(tt){return rt&&A(rt,"normal",tt),new P(!0,tt)},K=function(tt){return Y?(c(tt),X?k(tt[0],tt[1],ut):k(tt[0],tt[1])):X?k(tt,ut):k(tt)};if(F)rt=M.iterator;else if($)rt=M;else{if(!(J=C(M)))throw new R(g(M)+" is not iterable");if(y(J)){for(ht=0,vt=O(M);vt>ht;ht++)if((st=K(M[ht]))&&x(w,st))return st;return new P(!1)}rt=b(M,J)}for(ft=F?M.next:rt.next;!(Z=f(ft,rt)).done;){try{st=K(Z.value)}catch(tt){A(rt,"throw",tt)}if("object"==typeof st&&st&&x(w,st))return st}return new P(!1)}},5558:(d,S,n)=>{"use strict";var h=n(65798),f=n(36092),c=n(77851);d.exports=function(g,y,O){var x,b;f(g);try{if(!(x=c(g,"return"))){if("throw"===y)throw O;return O}x=h(x,g)}catch(C){b=!0,x=C}if("throw"===y)throw O;if(b)throw x;return f(x),O}},10137:(d,S,n)=>{"use strict";var h=n(84112).IteratorPrototype,f=n(97847),c=n(61285),g=n(55996),y=n(30786),O=function(){return this};d.exports=function(x,b,C,A){var R=b+" Iterator";return x.prototype=f(h,{next:c(+!A,C)}),g(x,R,!1,!0),y[R]=O,x}},80459:(d,S,n)=>{"use strict";var h=n(72679),f=n(65798),c=n(80572),g=n(95573),y=n(6214),O=n(10137),x=n(33336),b=n(70116),C=n(55996),A=n(96590),R=n(36403),P=n(28292),w=n(30786),M=n(84112),I=g.PROPER,B=g.CONFIGURABLE,H=M.IteratorPrototype,Y=M.BUGGY_SAFARI_ITERATORS,F=P("iterator"),$="keys",X="values",k="entries",rt=function(){return this};d.exports=function(J,ht,vt,st,ft,Z,ut){O(vt,ht,st);var xt,Mt,D,K=function(Pt){if(Pt===ft&&ct)return ct;if(!Y&&Pt&&Pt in et)return et[Pt];switch(Pt){case $:case X:case k:return function(){return new vt(this,Pt)}}return function(){return new vt(this)}},tt=ht+" Iterator",Ft=!1,et=J.prototype,ot=et[F]||et["@@iterator"]||ft&&et[ft],ct=!Y&&ot||K(ft),bt="Array"===ht&&et.entries||ot;if(bt&&(xt=x(bt.call(new J)))!==Object.prototype&&xt.next&&(!c&&x(xt)!==H&&(b?b(xt,H):y(xt[F])||R(xt,F,rt)),C(xt,tt,!0,!0),c&&(w[tt]=rt)),I&&ft===X&&ot&&ot.name!==X&&(!c&&B?A(et,"name",X):(Ft=!0,ct=function(){return f(ot,this)})),ft)if(Mt={values:K(X),keys:Z?ct:K($),entries:K(k)},ut)for(D in Mt)(Y||Ft||!(D in et))&&R(et,D,Mt[D]);else h({target:ht,proto:!0,forced:Y||Ft},Mt);return(!c||ut)&&et[F]!==ct&&R(et,F,ct,{name:ft}),w[ht]=ct,Mt}},84112:(d,S,n)=>{"use strict";var R,P,w,h=n(35536),f=n(6214),c=n(18785),g=n(97847),y=n(33336),O=n(36403),x=n(28292),b=n(80572),C=x("iterator"),A=!1;[].keys&&("next"in(w=[].keys())?(P=y(y(w)))!==Object.prototype&&(R=P):A=!0),!c(R)||h(function(){var I={};return R[C].call(I)!==I})?R={}:b&&(R=g(R)),f(R[C])||O(R,C,function(){return this}),d.exports={IteratorPrototype:R,BUGGY_SAFARI_ITERATORS:A}},30786:d=>{"use strict";d.exports={}},31083:(d,S,n)=>{"use strict";var h=n(47925);d.exports=function(f){return h(f.length)}},97660:(d,S,n)=>{"use strict";var h=n(28407),f=n(35536),c=n(6214),g=n(91960),y=n(75515),O=n(95573).CONFIGURABLE,x=n(73067),b=n(29192),C=b.enforce,A=b.get,R=String,P=Object.defineProperty,w=h("".slice),M=h("".replace),I=h([].join),B=y&&!f(function(){return 8!==P(function(){},"length",{value:8}).length}),H=String(String).split("String"),Y=d.exports=function(F,$,X){"Symbol("===w(R($),0,7)&&($="["+M(R($),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),X&&X.getter&&($="get "+$),X&&X.setter&&($="set "+$),(!g(F,"name")||O&&F.name!==$)&&(y?P(F,"name",{value:$,configurable:!0}):F.name=$),B&&X&&g(X,"arity")&&F.length!==X.arity&&P(F,"length",{value:X.arity});try{X&&g(X,"constructor")&&X.constructor?y&&P(F,"prototype",{writable:!1}):F.prototype&&(F.prototype=void 0)}catch{}var k=C(F);return g(k,"source")||(k.source=I(H,"string"==typeof $?$:"")),F};Function.prototype.toString=Y(function(){return c(this)&&A(this).source||x(this)},"toString")},1652:d=>{"use strict";var S=Math.ceil,n=Math.floor;d.exports=Math.trunc||function(f){var c=+f;return(c>0?n:S)(c)}},79440:(d,S,n)=>{"use strict";var I,B,H,Y,F,h=n(69555),f=n(78166),c=n(31571),g=n(31316).set,y=n(8209),O=n(98705),x=n(58034),b=n(58383),C=n(51806),A=h.MutationObserver||h.WebKitMutationObserver,R=h.document,P=h.process,w=h.Promise,M=f("queueMicrotask");if(!M){var $=new y,X=function(){var k,rt;for(C&&(k=P.domain)&&k.exit();rt=$.get();)try{rt()}catch(J){throw $.head&&I(),J}k&&k.enter()};O||C||b||!A||!R?!x&&w&&w.resolve?((Y=w.resolve(void 0)).constructor=w,F=c(Y.then,Y),I=function(){F(X)}):C?I=function(){P.nextTick(X)}:(g=c(g,h),I=function(){g(X)}):(B=!0,H=R.createTextNode(""),new A(X).observe(H,{characterData:!0}),I=function(){H.data=B=!B}),M=function(k){$.head||I(),$.add(k)}}d.exports=M},18090:(d,S,n)=>{"use strict";var h=n(26547),f=TypeError,c=function(g){var y,O;this.promise=new g(function(x,b){if(void 0!==y||void 0!==O)throw new f("Bad Promise constructor");y=x,O=b}),this.resolve=h(y),this.reject=h(O)};d.exports.f=function(g){return new c(g)}},26582:(d,S,n)=>{"use strict";var h=n(63035),f=TypeError;d.exports=function(c){if(h(c))throw new f("The method doesn't accept regular expressions");return c}},97847:(d,S,n)=>{"use strict";var H,h=n(36092),f=n(14776),c=n(30324),g=n(50838),y=n(13940),O=n(89820),x=n(68806),A="prototype",R="script",P=x("IE_PROTO"),w=function(){},M=function(F){return"<"+R+">"+F+"</"+R+">"},I=function(F){F.write(M("")),F.close();var $=F.parentWindow.Object;return F=null,$},Y=function(){try{H=new ActiveXObject("htmlfile")}catch{}Y=typeof document<"u"?document.domain&&H?I(H):function(){var X,F=O("iframe"),$="java"+R+":";return F.style.display="none",y.appendChild(F),F.src=String($),(X=F.contentWindow.document).open(),X.write(M("document.F=Object")),X.close(),X.F}():I(H);for(var F=c.length;F--;)delete Y[A][c[F]];return Y()};g[P]=!0,d.exports=Object.create||function($,X){var k;return null!==$?(w[A]=h($),k=new w,w[A]=null,k[P]=$):k=Y(),void 0===X?k:f.f(k,X)}},14776:(d,S,n)=>{"use strict";var h=n(75515),f=n(35721),c=n(57696),g=n(36092),y=n(39538),O=n(69527);S.f=h&&!f?Object.defineProperties:function(b,C){g(b);for(var M,A=y(C),R=O(C),P=R.length,w=0;P>w;)c.f(b,M=R[w++],A[M]);return b}},57696:(d,S,n)=>{"use strict";var h=n(75515),f=n(96500),c=n(35721),g=n(36092),y=n(97322),O=TypeError,x=Object.defineProperty,b=Object.getOwnPropertyDescriptor,C="enumerable",A="configurable",R="writable";S.f=h?c?function(w,M,I){if(g(w),M=y(M),g(I),"function"==typeof w&&"prototype"===M&&"value"in I&&R in I&&!I[R]){var B=b(w,M);B&&B[R]&&(w[M]=I.value,I={configurable:A in I?I[A]:B[A],enumerable:C in I?I[C]:B[C],writable:!1})}return x(w,M,I)}:x:function(w,M,I){if(g(w),M=y(M),g(I),f)try{return x(w,M,I)}catch{}if("get"in I||"set"in I)throw new O("Accessors not supported");return"value"in I&&(w[M]=I.value),w}},20290:(d,S,n)=>{"use strict";var h=n(75515),f=n(65798),c=n(58610),g=n(61285),y=n(39538),O=n(97322),x=n(91960),b=n(96500),C=Object.getOwnPropertyDescriptor;S.f=h?C:function(R,P){if(R=y(R),P=O(P),b)try{return C(R,P)}catch{}if(x(R,P))return g(!f(c.f,R,P),R[P])}},25335:(d,S,n)=>{"use strict";var h=n(21097),c=n(30324).concat("length","prototype");S.f=Object.getOwnPropertyNames||function(y){return h(y,c)}},99086:(d,S)=>{"use strict";S.f=Object.getOwnPropertySymbols},33336:(d,S,n)=>{"use strict";var h=n(91960),f=n(6214),c=n(81622),g=n(68806),y=n(71658),O=g("IE_PROTO"),x=Object,b=x.prototype;d.exports=y?x.getPrototypeOf:function(C){var A=c(C);if(h(A,O))return A[O];var R=A.constructor;return f(R)&&A instanceof R?R.prototype:A instanceof x?b:null}},51180:(d,S,n)=>{"use strict";var h=n(28407);d.exports=h({}.isPrototypeOf)},21097:(d,S,n)=>{"use strict";var h=n(28407),f=n(91960),c=n(39538),g=n(67408).indexOf,y=n(50838),O=h([].push);d.exports=function(x,b){var P,C=c(x),A=0,R=[];for(P in C)!f(y,P)&&f(C,P)&&O(R,P);for(;b.length>A;)f(C,P=b[A++])&&(~g(R,P)||O(R,P));return R}},69527:(d,S,n)=>{"use strict";var h=n(21097),f=n(30324);d.exports=Object.keys||function(g){return h(g,f)}},58610:(d,S)=>{"use strict";var n={}.propertyIsEnumerable,h=Object.getOwnPropertyDescriptor,f=h&&!n.call({1:2},1);S.f=f?function(g){var y=h(this,g);return!!y&&y.enumerable}:n},70116:(d,S,n)=>{"use strict";var h=n(81699),f=n(18785),c=n(66283),g=n(935);d.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var x,y=!1,O={};try{(x=h(Object.prototype,"__proto__","set"))(O,[]),y=O instanceof Array}catch{}return function(C,A){return c(C),g(A),f(C)&&(y?x(C,A):C.__proto__=A),C}}():void 0)},90049:(d,S,n)=>{"use strict";var h=n(65798),f=n(6214),c=n(18785),g=TypeError;d.exports=function(y,O){var x,b;if("string"===O&&f(x=y.toString)&&!c(b=h(x,y))||f(x=y.valueOf)&&!c(b=h(x,y))||"string"!==O&&f(x=y.toString)&&!c(b=h(x,y)))return b;throw new g("Can't convert object to primitive value")}},1206:(d,S,n)=>{"use strict";var h=n(96578),f=n(28407),c=n(25335),g=n(99086),y=n(36092),O=f([].concat);d.exports=h("Reflect","ownKeys")||function(b){var C=c.f(y(b)),A=g.f;return A?O(C,A(b)):C}},18360:d=>{"use strict";d.exports=function(S){try{return{error:!1,value:S()}}catch(n){return{error:!0,value:n}}}},67107:(d,S,n)=>{"use strict";var h=n(69555),f=n(39051),c=n(6214),g=n(12131),y=n(73067),O=n(28292),x=n(31740),b=n(80572),C=n(11450),A=f&&f.prototype,R=O("species"),P=!1,w=c(h.PromiseRejectionEvent),M=g("Promise",function(){var I=y(f),B=I!==String(f);if(!B&&66===C||b&&(!A.catch||!A.finally))return!0;if(!C||C<51||!/native code/.test(I)){var H=new f(function($){$(1)}),Y=function($){$(function(){},function(){})};if((H.constructor={})[R]=Y,!(P=H.then(function(){})instanceof Y))return!0}return!(B||"BROWSER"!==x&&"DENO"!==x||w)});d.exports={CONSTRUCTOR:M,REJECTION_EVENT:w,SUBCLASSING:P}},39051:(d,S,n)=>{"use strict";var h=n(69555);d.exports=h.Promise},3517:(d,S,n)=>{"use strict";var h=n(36092),f=n(18785),c=n(18090);d.exports=function(g,y){if(h(g),f(y)&&y.constructor===g)return y;var O=c.f(g);return(0,O.resolve)(y),O.promise}},43262:(d,S,n)=>{"use strict";var h=n(39051),f=n(67301),c=n(67107).CONSTRUCTOR;d.exports=c||!f(function(g){h.all(g).then(void 0,function(){})})},8209:d=>{"use strict";var S=function(){this.head=null,this.tail=null};S.prototype={add:function(n){var h={item:n,next:null},f=this.tail;f?f.next=h:this.head=h,this.tail=h},get:function(){var n=this.head;if(n)return null===(this.head=n.next)&&(this.tail=null),n.item}},d.exports=S},99199:(d,S,n)=>{"use strict";var h=n(65798),f=n(36092),c=n(6214),g=n(69547),y=n(14664),O=TypeError;d.exports=function(x,b){var C=x.exec;if(c(C)){var A=h(C,x,b);return null!==A&&f(A),A}if("RegExp"===g(x))return h(y,x,b);throw new O("RegExp#exec called on incompatible receiver")}},14664:(d,S,n)=>{"use strict";var k,rt,h=n(65798),f=n(28407),c=n(99540),g=n(78602),y=n(49906),O=n(86980),x=n(97847),b=n(29192).get,C=n(38130),A=n(13231),R=O("native-string-replace",String.prototype.replace),P=RegExp.prototype.exec,w=P,M=f("".charAt),I=f("".indexOf),B=f("".replace),H=f("".slice),Y=(rt=/b*/g,h(P,k=/a/,"a"),h(P,rt,"a"),0!==k.lastIndex||0!==rt.lastIndex),F=y.BROKEN_CARET,$=void 0!==/()??/.exec("")[1];(Y||$||F||C||A)&&(w=function(rt){var ft,Z,ut,K,tt,Ft,et,J=this,ht=b(J),vt=c(rt),st=ht.raw;if(st)return st.lastIndex=J.lastIndex,ft=h(w,st,vt),J.lastIndex=st.lastIndex,ft;var ot=ht.groups,ct=F&&J.sticky,bt=h(g,J),xt=J.source,Mt=0,D=vt;if(ct&&(bt=B(bt,"y",""),-1===I(bt,"g")&&(bt+="g"),D=H(vt,J.lastIndex),J.lastIndex>0&&(!J.multiline||J.multiline&&"\n"!==M(vt,J.lastIndex-1))&&(xt="(?: "+xt+")",D=" "+D,Mt++),Z=new RegExp("^(?:"+xt+")",bt)),$&&(Z=new RegExp("^"+xt+"$(?!\\s)",bt)),Y&&(ut=J.lastIndex),K=h(P,ct?Z:J,D),ct?K?(K.input=H(K.input,Mt),K[0]=H(K[0],Mt),K.index=J.lastIndex,J.lastIndex+=K[0].length):J.lastIndex=0:Y&&K&&(J.lastIndex=J.global?K.index+K[0].length:ut),$&&K&&K.length>1&&h(R,K[0],Z,function(){for(tt=1;tt<arguments.length-2;tt++)void 0===arguments[tt]&&(K[tt]=void 0)}),K&&ot)for(K.groups=Ft=x(null),tt=0;tt<ot.length;tt++)Ft[(et=ot[tt])[0]]=K[et[1]];return K}),d.exports=w},78602:(d,S,n)=>{"use strict";var h=n(36092);d.exports=function(){var f=h(this),c="";return f.hasIndices&&(c+="d"),f.global&&(c+="g"),f.ignoreCase&&(c+="i"),f.multiline&&(c+="m"),f.dotAll&&(c+="s"),f.unicode&&(c+="u"),f.unicodeSets&&(c+="v"),f.sticky&&(c+="y"),c}},98859:(d,S,n)=>{"use strict";var h=n(65798),f=n(91960),c=n(51180),g=n(78602),y=RegExp.prototype;d.exports=function(O){var x=O.flags;return void 0!==x||"flags"in y||f(O,"flags")||!c(y,O)?x:h(g,O)}},49906:(d,S,n)=>{"use strict";var h=n(35536),c=n(69555).RegExp,g=h(function(){var x=c("a","y");return x.lastIndex=2,null!==x.exec("abcd")}),y=g||h(function(){return!c("a","y").sticky}),O=g||h(function(){var x=c("^r","gy");return x.lastIndex=2,null!==x.exec("str")});d.exports={BROKEN_CARET:O,MISSED_STICKY:y,UNSUPPORTED_Y:g}},38130:(d,S,n)=>{"use strict";var h=n(35536),c=n(69555).RegExp;d.exports=h(function(){var g=c(".","s");return!(g.dotAll&&g.test("\n")&&"s"===g.flags)})},13231:(d,S,n)=>{"use strict";var h=n(35536),c=n(69555).RegExp;d.exports=h(function(){var g=c("(?<a>b)","g");return"b"!==g.exec("b").groups.a||"bc"!=="b".replace(g,"$<a>c")})},66283:(d,S,n)=>{"use strict";var h=n(42412),f=TypeError;d.exports=function(c){if(h(c))throw new f("Can't call method on "+c);return c}},78166:(d,S,n)=>{"use strict";var h=n(69555),f=n(75515),c=Object.getOwnPropertyDescriptor;d.exports=function(g){if(!f)return h[g];var y=c(h,g);return y&&y.value}},27466:(d,S,n)=>{"use strict";var h=n(96578),f=n(67615),c=n(28292),g=n(75515),y=c("species");d.exports=function(O){var x=h(O);g&&x&&!x[y]&&f(x,y,{configurable:!0,get:function(){return this}})}},55996:(d,S,n)=>{"use strict";var h=n(57696).f,f=n(91960),g=n(28292)("toStringTag");d.exports=function(y,O,x){y&&!x&&(y=y.prototype),y&&!f(y,g)&&h(y,g,{configurable:!0,value:O})}},68806:(d,S,n)=>{"use strict";var h=n(86980),f=n(97927),c=h("keys");d.exports=function(g){return c[g]||(c[g]=f(g))}},40140:(d,S,n)=>{"use strict";var h=n(80572),f=n(69555),c=n(30928),g="__core-js_shared__",y=d.exports=f[g]||c(g,{});(y.versions||(y.versions=[])).push({version:"3.40.0",mode:h?"pure":"global",copyright:"\xa9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.40.0/LICENSE",source:"https://github.com/zloirock/core-js"})},86980:(d,S,n)=>{"use strict";var h=n(40140);d.exports=function(f,c){return h[f]||(h[f]=c||{})}},70814:(d,S,n)=>{"use strict";var h=n(36092),f=n(18679),c=n(42412),y=n(28292)("species");d.exports=function(O,x){var C,b=h(O).constructor;return void 0===b||c(C=h(b)[y])?x:f(C)}},20650:(d,S,n)=>{"use strict";var h=n(28407),f=n(76878),c=n(99540),g=n(66283),y=h("".charAt),O=h("".charCodeAt),x=h("".slice),b=function(C){return function(A,R){var I,B,P=c(g(A)),w=f(R),M=P.length;return w<0||w>=M?C?"":void 0:(I=O(P,w))<55296||I>56319||w+1===M||(B=O(P,w+1))<56320||B>57343?C?y(P,w):I:C?x(P,w,w+2):B-56320+(I-55296<<10)+65536}};d.exports={codeAt:b(!1),charAt:b(!0)}},46991:(d,S,n)=>{"use strict";var h=n(95573).PROPER,f=n(35536),c=n(90431);d.exports=function(y){return f(function(){return!!c[y]()||"\u200b\x85\u180e"!=="\u200b\x85\u180e"[y]()||h&&c[y].name!==y})}},69405:(d,S,n)=>{"use strict";var h=n(28407),f=n(66283),c=n(99540),g=n(90431),y=h("".replace),O=RegExp("^["+g+"]+"),x=RegExp("(^|[^"+g+"])["+g+"]+$"),b=function(C){return function(A){var R=c(f(A));return 1&C&&(R=y(R,O,"")),2&C&&(R=y(R,x,"$1")),R}};d.exports={start:b(1),end:b(2),trim:b(3)}},47506:(d,S,n)=>{"use strict";var h=n(11450),f=n(35536),g=n(69555).String;d.exports=!!Object.getOwnPropertySymbols&&!f(function(){var y=Symbol("symbol detection");return!g(y)||!(Object(y)instanceof Symbol)||!Symbol.sham&&h&&h<41})},31316:(d,S,n)=>{"use strict";var rt,J,ht,vt,h=n(69555),f=n(39524),c=n(31571),g=n(6214),y=n(91960),O=n(35536),x=n(13940),b=n(57519),C=n(89820),A=n(41239),R=n(98705),P=n(51806),w=h.setImmediate,M=h.clearImmediate,I=h.process,B=h.Dispatch,H=h.Function,Y=h.MessageChannel,F=h.String,$=0,X={},k="onreadystatechange";O(function(){rt=h.location});var st=function(K){if(y(X,K)){var tt=X[K];delete X[K],tt()}},ft=function(K){return function(){st(K)}},Z=function(K){st(K.data)},ut=function(K){h.postMessage(F(K),rt.protocol+"//"+rt.host)};(!w||!M)&&(w=function(tt){A(arguments.length,1);var Ft=g(tt)?tt:H(tt),et=b(arguments,1);return X[++$]=function(){f(Ft,void 0,et)},J($),$},M=function(tt){delete X[tt]},P?J=function(K){I.nextTick(ft(K))}:B&&B.now?J=function(K){B.now(ft(K))}:Y&&!R?(vt=(ht=new Y).port2,ht.port1.onmessage=Z,J=c(vt.postMessage,vt)):h.addEventListener&&g(h.postMessage)&&!h.importScripts&&rt&&"file:"!==rt.protocol&&!O(ut)?(J=ut,h.addEventListener("message",Z,!1)):J=k in C("script")?function(K){x.appendChild(C("script"))[k]=function(){x.removeChild(this),st(K)}}:function(K){setTimeout(ft(K),0)}),d.exports={set:w,clear:M}},95429:(d,S,n)=>{"use strict";var h=n(76878),f=Math.max,c=Math.min;d.exports=function(g,y){var O=h(g);return O<0?f(O+y,0):c(O,y)}},39538:(d,S,n)=>{"use strict";var h=n(85254),f=n(66283);d.exports=function(c){return h(f(c))}},76878:(d,S,n)=>{"use strict";var h=n(1652);d.exports=function(f){var c=+f;return c!=c||0===c?0:h(c)}},47925:(d,S,n)=>{"use strict";var h=n(76878),f=Math.min;d.exports=function(c){var g=h(c);return g>0?f(g,9007199254740991):0}},81622:(d,S,n)=>{"use strict";var h=n(66283),f=Object;d.exports=function(c){return f(h(c))}},99472:(d,S,n)=>{"use strict";var h=n(65798),f=n(18785),c=n(94062),g=n(77851),y=n(90049),O=n(28292),x=TypeError,b=O("toPrimitive");d.exports=function(C,A){if(!f(C)||c(C))return C;var P,R=g(C,b);if(R){if(void 0===A&&(A="default"),P=h(R,C,A),!f(P)||c(P))return P;throw new x("Can't convert object to primitive value")}return void 0===A&&(A="number"),y(C,A)}},97322:(d,S,n)=>{"use strict";var h=n(99472),f=n(94062);d.exports=function(c){var g=h(c,"string");return f(g)?g:g+""}},54363:(d,S,n)=>{"use strict";var c={};c[n(28292)("toStringTag")]="z",d.exports="[object z]"===String(c)},99540:(d,S,n)=>{"use strict";var h=n(35736),f=String;d.exports=function(c){if("Symbol"===h(c))throw new TypeError("Cannot convert a Symbol value to a string");return f(c)}},13460:d=>{"use strict";var S=String;d.exports=function(n){try{return S(n)}catch{return"Object"}}},97927:(d,S,n)=>{"use strict";var h=n(28407),f=0,c=Math.random(),g=h(1..toString);d.exports=function(y){return"Symbol("+(void 0===y?"":y)+")_"+g(++f+c,36)}},6939:(d,S,n)=>{"use strict";var h=n(47506);d.exports=h&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},35721:(d,S,n)=>{"use strict";var h=n(75515),f=n(35536);d.exports=h&&f(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},41239:d=>{"use strict";var S=TypeError;d.exports=function(n,h){if(n<h)throw new S("Not enough arguments");return n}},80091:(d,S,n)=>{"use strict";var h=n(69555),f=n(6214),c=h.WeakMap;d.exports=f(c)&&/native code/.test(String(c))},28292:(d,S,n)=>{"use strict";var h=n(69555),f=n(86980),c=n(91960),g=n(97927),y=n(47506),O=n(6939),x=h.Symbol,b=f("wks"),C=O?x.for||x:x&&x.withoutSetter||g;d.exports=function(A){return c(b,A)||(b[A]=y&&c(x,A)?x[A]:C("Symbol."+A)),b[A]}},90431:d=>{"use strict";d.exports="\t\n\v\f\r \xa0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029\ufeff"},50635:(d,S,n)=>{"use strict";var h=n(72679),f=n(75013),c=n(67408).indexOf,g=n(18307),y=f([].indexOf),O=!!y&&1/y([1],1,-0)<0;h({target:"Array",proto:!0,forced:O||!g("indexOf")},{indexOf:function(C){var A=arguments.length>1?arguments[1]:void 0;return O?y(this,C,A)||0:c(this,C,A)}})},87495:(d,S,n)=>{"use strict";var h=n(39538),f=n(54080),c=n(30786),g=n(29192),y=n(57696).f,O=n(80459),x=n(24186),b=n(80572),C=n(75515),A="Array Iterator",R=g.set,P=g.getterFor(A);d.exports=O(Array,"Array",function(M,I){R(this,{type:A,target:h(M),index:0,kind:I})},function(){var M=P(this),I=M.target,B=M.index++;if(!I||B>=I.length)return M.target=null,x(void 0,!0);switch(M.kind){case"keys":return x(B,!1);case"values":return x(I[B],!1)}return x([B,I[B]],!1)},"values");var w=c.Arguments=c.Array;if(f("keys"),f("values"),f("entries"),!b&&C&&"values"!==w.name)try{y(w,"name",{value:"values"})}catch{}},43751:(d,S,n)=>{"use strict";var h=n(72679),f=n(19031).left,c=n(18307),g=n(11450);h({target:"Array",proto:!0,forced:!n(51806)&&g>79&&g<83||!c("reduce")},{reduce:function(C){var A=arguments.length;return f(this,C,A,A>1?arguments[1]:void 0)}})},54491:(d,S,n)=>{"use strict";var h=n(72679),f=n(28407),c=n(14901),g=f([].reverse),y=[1,2];h({target:"Array",proto:!0,forced:String(y)===String(y.reverse())},{reverse:function(){return c(this)&&(this.length=this.length),g(this)}})},81594:(d,S,n)=>{"use strict";var h=n(72679),f=n(65798),c=n(26547),g=n(18090),y=n(18360),O=n(32283);h({target:"Promise",stat:!0,forced:n(43262)},{all:function(C){var A=this,R=g.f(A),P=R.resolve,w=R.reject,M=y(function(){var I=c(A.resolve),B=[],H=0,Y=1;O(C,function(F){var $=H++,X=!1;Y++,f(I,A,F).then(function(k){X||(X=!0,B[$]=k,--Y||P(B))},w)}),--Y||P(B)});return M.error&&w(M.value),R.promise}})},57110:(d,S,n)=>{"use strict";var h=n(72679),f=n(80572),c=n(67107).CONSTRUCTOR,g=n(39051),y=n(96578),O=n(6214),x=n(36403),b=g&&g.prototype;if(h({target:"Promise",proto:!0,forced:c,real:!0},{catch:function(A){return this.then(void 0,A)}}),!f&&O(g)){var C=y("Promise").prototype.catch;b.catch!==C&&x(b,"catch",C,{unsafe:!0})}},96309:(d,S,n)=>{"use strict";var kt,Ee,re,h=n(72679),f=n(80572),c=n(51806),g=n(69555),y=n(65798),O=n(36403),x=n(70116),b=n(55996),C=n(27466),A=n(26547),R=n(6214),P=n(18785),w=n(46784),M=n(70814),I=n(31316).set,B=n(79440),H=n(21956),Y=n(18360),F=n(8209),$=n(29192),X=n(39051),k=n(67107),rt=n(18090),J="Promise",ht=k.CONSTRUCTOR,vt=k.REJECTION_EVENT,st=k.SUBCLASSING,ft=$.getterFor(J),Z=$.set,ut=X&&X.prototype,K=X,tt=ut,Ft=g.TypeError,et=g.document,ot=g.process,ct=rt.f,bt=ct,xt=!!(et&&et.createEvent&&g.dispatchEvent),Mt="unhandledrejection",ue=function(G){var it;return!(!P(G)||!R(it=G.then))&&it},le=function(G,it){var ce,ze,Ge,pt=it.value,dt=1===it.state,Ct=dt?G.ok:G.fail,fe=G.resolve,Me=G.reject,ve=G.domain;try{Ct?(dt||(2===it.rejection&&nr(it),it.rejection=1),!0===Ct?ce=pt:(ve&&ve.enter(),ce=Ct(pt),ve&&(ve.exit(),Ge=!0)),ce===G.promise?Me(new Ft("Promise-chain cycle")):(ze=ue(ce))?y(ze,ce,fe,Me):fe(ce)):Me(pt)}catch(ar){ve&&!Ge&&ve.exit(),Me(ar)}},he=function(G,it){G.notified||(G.notified=!0,B(function(){for(var dt,pt=G.reactions;dt=pt.get();)le(dt,G);G.notified=!1,it&&!G.rejection&&rr(G)}))},qt=function(G,it,pt){var dt,Ct;xt?((dt=et.createEvent("Event")).promise=it,dt.reason=pt,dt.initEvent(G,!1,!0),g.dispatchEvent(dt)):dt={promise:it,reason:pt},!vt&&(Ct=g["on"+G])?Ct(dt):G===Mt&&H("Unhandled promise rejection",pt)},rr=function(G){y(I,g,function(){var Ct,it=G.facade,pt=G.value;if(je(G)&&(Ct=Y(function(){c?ot.emit("unhandledRejection",pt,it):qt(Mt,it,pt)}),G.rejection=c||je(G)?2:1,Ct.error))throw Ct.value})},je=function(G){return 1!==G.rejection&&!G.parent},nr=function(G){y(I,g,function(){var it=G.facade;c?ot.emit("rejectionHandled",it):qt("rejectionhandled",it,G.value)})},$t=function(G,it,pt){return function(dt){G(it,dt,pt)}},ne=function(G,it,pt){G.done||(G.done=!0,pt&&(G=pt),G.value=it,G.state=2,he(G,!0))},Ie=function(G,it,pt){if(!G.done){G.done=!0,pt&&(G=pt);try{if(G.facade===it)throw new Ft("Promise can't be resolved itself");var dt=ue(it);dt?B(function(){var Ct={done:!1};try{y(dt,it,$t(Ie,Ct,G),$t(ne,Ct,G))}catch(fe){ne(Ct,fe,G)}}):(G.value=it,G.state=1,he(G,!1))}catch(Ct){ne({done:!1},Ct,G)}}};if(ht&&(K=function(it){w(this,tt),A(it),y(kt,this);var pt=ft(this);try{it($t(Ie,pt),$t(ne,pt))}catch(dt){ne(pt,dt)}},(kt=function(it){Z(this,{type:J,done:!1,notified:!1,parent:!1,reactions:new F,rejection:!1,state:0,value:null})}).prototype=O(tt=K.prototype,"then",function(it,pt){var dt=ft(this),Ct=ct(M(this,K));return dt.parent=!0,Ct.ok=!R(it)||it,Ct.fail=R(pt)&&pt,Ct.domain=c?ot.domain:void 0,0===dt.state?dt.reactions.add(Ct):B(function(){le(Ct,dt)}),Ct.promise}),Ee=function(){var G=new kt,it=ft(G);this.promise=G,this.resolve=$t(Ie,it),this.reject=$t(ne,it)},rt.f=ct=function(G){return G===K||void 0===G?new Ee(G):bt(G)},!f&&R(X)&&ut!==Object.prototype)){re=ut.then,st||O(ut,"then",function(it,pt){var dt=this;return new K(function(Ct,fe){y(re,dt,Ct,fe)}).then(it,pt)},{unsafe:!0});try{delete ut.constructor}catch{}x&&x(ut,tt)}h({global:!0,constructor:!0,wrap:!0,forced:ht},{Promise:K}),b(K,J,!1,!0),C(J)},24151:(d,S,n)=>{"use strict";n(96309),n(81594),n(57110),n(32420),n(23082),n(12533)},32420:(d,S,n)=>{"use strict";var h=n(72679),f=n(65798),c=n(26547),g=n(18090),y=n(18360),O=n(32283);h({target:"Promise",stat:!0,forced:n(43262)},{race:function(C){var A=this,R=g.f(A),P=R.reject,w=y(function(){var M=c(A.resolve);O(C,function(I){f(M,A,I).then(R.resolve,P)})});return w.error&&P(w.value),R.promise}})},23082:(d,S,n)=>{"use strict";var h=n(72679),f=n(18090);h({target:"Promise",stat:!0,forced:n(67107).CONSTRUCTOR},{reject:function(y){var O=f.f(this);return(0,O.reject)(y),O.promise}})},12533:(d,S,n)=>{"use strict";var h=n(72679),f=n(96578),c=n(80572),g=n(39051),y=n(67107).CONSTRUCTOR,O=n(3517),x=f("Promise"),b=c&&!y;h({target:"Promise",stat:!0,forced:c||y},{resolve:function(A){return O(b&&this===x?g:this,A)}})},56954:(d,S,n)=>{"use strict";var h=n(72679),f=n(14664);h({target:"RegExp",proto:!0,forced:/./.exec!==f},{exec:f})},26286:(d,S,n)=>{"use strict";var h=n(95573).PROPER,f=n(36403),c=n(36092),g=n(99540),y=n(35536),O=n(98859),x="toString",b=RegExp.prototype,C=b[x];(y(function(){return"/a/b"!==C.call({source:"a",flags:"b"})})||h&&C.name!==x)&&f(b,x,function(){var w=c(this);return"/"+g(w.source)+"/"+g(O(w))},{unsafe:!0})},54010:(d,S,n)=>{"use strict";var M,h=n(72679),f=n(75013),c=n(20290).f,g=n(47925),y=n(99540),O=n(26582),x=n(66283),b=n(51979),C=n(80572),A=f("".slice),R=Math.min,P=b("endsWith");h({target:"String",proto:!0,forced:!(!C&&!P&&(M=c(String.prototype,"endsWith"),M&&!M.writable)||P)},{endsWith:function(I){var B=y(x(this));O(I);var H=arguments.length>1?arguments[1]:void 0,Y=B.length,F=void 0===H?Y:R(g(H),Y),$=y(I);return A(B,F-$.length,F)===$}})},89958:(d,S,n)=>{"use strict";var h=n(72679),f=n(28407),c=n(26582),g=n(66283),y=n(99540),O=n(51979),x=f("".indexOf);h({target:"String",proto:!0,forced:!O("includes")},{includes:function(C){return!!~x(y(g(this)),y(c(C)),arguments.length>1?arguments[1]:void 0)}})},73018:(d,S,n)=>{"use strict";var h=n(65798),f=n(46641),c=n(36092),g=n(42412),y=n(47925),O=n(99540),x=n(66283),b=n(77851),C=n(55428),A=n(99199);f("match",function(R,P,w){return[function(I){var B=x(this),H=g(I)?void 0:b(I,R);return H?h(H,I,B):new RegExp(I)[R](O(B))},function(M){var I=c(this),B=O(M),H=w(P,I,B);if(H.done)return H.value;if(!I.global)return A(I,B);var Y=I.unicode;I.lastIndex=0;for(var X,F=[],$=0;null!==(X=A(I,B));){var k=O(X[0]);F[$]=k,""===k&&(I.lastIndex=C(B,y(I.lastIndex),Y)),$++}return 0===$?null:F}]})},20191:(d,S,n)=>{"use strict";var h=n(39524),f=n(65798),c=n(28407),g=n(46641),y=n(35536),O=n(36092),x=n(6214),b=n(42412),C=n(76878),A=n(47925),R=n(99540),P=n(66283),w=n(55428),M=n(77851),I=n(29083),B=n(99199),Y=n(28292)("replace"),F=Math.max,$=Math.min,X=c([].concat),k=c([].push),rt=c("".indexOf),J=c("".slice),ht=function(Z){return void 0===Z?Z:String(Z)},vt="$0"==="a".replace(/./,"$0"),st=!!/./[Y]&&""===/./[Y]("a","$0");g("replace",function(Z,ut,K){var tt=st?"$":"$0";return[function(et,ot){var ct=P(this),bt=b(et)?void 0:M(et,Y);return bt?f(bt,et,ct,ot):f(ut,R(ct),et,ot)},function(Ft,et){var ot=O(this),ct=R(Ft);if("string"==typeof et&&-1===rt(et,tt)&&-1===rt(et,"$<")){var bt=K(ut,ot,ct,et);if(bt.done)return bt.value}var xt=x(et);xt||(et=R(et));var D,Mt=ot.global;Mt&&(D=ot.unicode,ot.lastIndex=0);for(var jt,Pt=[];null!==(jt=B(ot,ct))&&(k(Pt,jt),Mt);)""===R(jt[0])&&(ot.lastIndex=w(ct,A(ot.lastIndex),D));for(var oe="",Zt=0,kt=0;kt<Pt.length;kt++){for(var ue,Ee=R((jt=Pt[kt])[0]),Jt=F($(C(jt.index),ct.length),0),re=[],le=1;le<jt.length;le++)k(re,ht(jt[le]));var he=jt.groups;if(xt){var qt=X([Ee],re,Jt,ct);void 0!==he&&k(qt,he),ue=R(h(et,void 0,qt))}else ue=I(Ee,ct,Jt,re,he,et);Jt>=Zt&&(oe+=J(ct,Zt,Jt)+ue,Zt=Jt+Ee.length)}return oe+J(ct,Zt)}]},!!y(function(){var Z=/./;return Z.exec=function(){var ut=[];return ut.groups={a:"7"},ut},"7"!=="".replace(Z,"$<a>")})||!vt||st)},99903:(d,S,n)=>{"use strict";var h=n(65798),f=n(28407),c=n(46641),g=n(36092),y=n(42412),O=n(66283),x=n(70814),b=n(55428),C=n(47925),A=n(99540),R=n(77851),P=n(99199),w=n(49906),M=n(35536),I=w.UNSUPPORTED_Y,H=Math.min,Y=f([].push),F=f("".slice),$=!M(function(){var k=/(?:)/,rt=k.exec;k.exec=function(){return rt.apply(this,arguments)};var J="ab".split(k);return 2!==J.length||"a"!==J[0]||"b"!==J[1]}),X="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;c("split",function(k,rt,J){var ht="0".split(void 0,0).length?function(vt,st){return void 0===vt&&0===st?[]:h(rt,this,vt,st)}:rt;return[function(st,ft){var Z=O(this),ut=y(st)?void 0:R(st,k);return ut?h(ut,st,Z,ft):h(ht,A(Z),st,ft)},function(vt,st){var ft=g(this),Z=A(vt);if(!X){var ut=J(ht,ft,Z,st,ht!==rt);if(ut.done)return ut.value}var K=x(ft,RegExp),tt=ft.unicode,et=new K(I?"^(?:"+ft.source+")":ft,(ft.ignoreCase?"i":"")+(ft.multiline?"m":"")+(ft.unicode?"u":"")+(I?"g":"y")),ot=void 0===st?4294967295:st>>>0;if(0===ot)return[];if(0===Z.length)return null===P(et,Z)?[Z]:[];for(var ct=0,bt=0,xt=[];bt<Z.length;){et.lastIndex=I?0:bt;var D,Mt=P(et,I?F(Z,bt):Z);if(null===Mt||(D=H(C(et.lastIndex+(I?bt:0)),Z.length))===ct)bt=b(Z,bt,tt);else{if(Y(xt,F(Z,ct,bt)),xt.length===ot)return xt;for(var Pt=1;Pt<=Mt.length-1;Pt++)if(Y(xt,Mt[Pt]),xt.length===ot)return xt;bt=ct=D}}return Y(xt,F(Z,ct)),xt}]},X||!$,I)},57471:(d,S,n)=>{"use strict";var M,h=n(72679),f=n(75013),c=n(20290).f,g=n(47925),y=n(99540),O=n(26582),x=n(66283),b=n(51979),C=n(80572),A=f("".slice),R=Math.min,P=b("startsWith");h({target:"String",proto:!0,forced:!(!C&&!P&&(M=c(String.prototype,"startsWith"),M&&!M.writable)||P)},{startsWith:function(I){var B=y(x(this));O(I);var H=g(R(arguments.length>1?arguments[1]:void 0,B.length)),Y=y(I);return A(B,H,H+Y.length)===Y}})},283:(d,S,n)=>{"use strict";var h=n(72679),f=n(69405).trim;h({target:"String",proto:!0,forced:n(46991)("trim")},{trim:function(){return f(this)}})},50620:(d,S,n)=>{"use strict";var h=n(69555),f=n(58379),c=n(337),g=n(87495),y=n(96590),O=n(55996),b=n(28292)("iterator"),C=g.values,A=function(P,w){if(P){if(P[b]!==C)try{y(P,b,C)}catch{P[b]=C}if(O(P,w,!0),f[w])for(var M in g)if(P[M]!==g[M])try{y(P,M,g[M])}catch{P[M]=g[M]}}};for(var R in f)A(h[R]&&h[R].prototype,R);A(c,"DOMTokenList")},20147:(d,S,n)=>{"use strict";n.r(S),n.d(S,{AElement:()=>sn,AnimateColorElement:()=>_r,AnimateElement:()=>We,AnimateTransformElement:()=>tn,BoundingBox:()=>Qt,CB1:()=>or,CB2:()=>ur,CB3:()=>lr,CB4:()=>hr,Canvg:()=>Ce,CircleElement:()=>Hr,ClipPathElement:()=>pn,DefsElement:()=>kr,DescElement:()=>Sn,Document:()=>Cn,Element:()=>Nt,EllipseElement:()=>Yr,FeColorMatrixElement:()=>Tr,FeCompositeElement:()=>En,FeDropShadowElement:()=>mn,FeGaussianBlurElement:()=>Tn,FeMorphologyElement:()=>xn,FilterElement:()=>yn,Font:()=>Se,FontElement:()=>en,FontFaceElement:()=>rn,GElement:()=>Xe,GlyphElement:()=>yr,GradientElement:()=>Er,ImageElement:()=>ln,LineElement:()=>Xr,LinearGradientElement:()=>Zr,MarkerElement:()=>Kr,MaskElement:()=>gn,Matrix:()=>dr,MissingGlyphElement:()=>nn,Mouse:()=>Dr,PSEUDO_ZERO:()=>Oe,Parser:()=>He,PathElement:()=>Et,PathParser:()=>nt,PatternElement:()=>Qr,Point:()=>Rt,PolygonElement:()=>Wr,PolylineElement:()=>xr,Property:()=>_,QB1:()=>fr,QB2:()=>vr,QB3:()=>cr,RadialGradientElement:()=>Jr,RectElement:()=>mr,RenderedElement:()=>ge,Rotate:()=>Ur,SVGElement:()=>we,SVGFontLoader:()=>fn,Scale:()=>jr,Screen:()=>gr,Skew:()=>pr,SkewX:()=>zr,SkewY:()=>Gr,StopElement:()=>qr,StyleElement:()=>vn,SymbolElement:()=>hn,TRefElement:()=>an,TSpanElement:()=>Fe,TextElement:()=>ae,TextPathElement:()=>un,TitleElement:()=>On,Transform:()=>Ye,Translate:()=>Fr,UnknownElement:()=>$r,UseElement:()=>cn,ViewPort:()=>Vr,compressSpaces:()=>$t,default:()=>Ce,getSelectorSpecificity:()=>wr,normalizeAttributeName:()=>pt,normalizeColor:()=>Ct,parseExternalUrl:()=>dt,presets:()=>nr,toNumbers:()=>G,trimLeft:()=>ne,trimRight:()=>Ie,vectorMagnitude:()=>ir,vectorsAngle:()=>sr,vectorsRatio:()=>$e}),n(24151);var f=n(10467),b=(n(73018),n(20191),n(57471),n(87495),n(50620),n(82284));function R(l,t,e){return(t=function A(l){var t=function C(l,t){if("object"!=(0,b.A)(l)||!l)return l;var e=l[Symbol.toPrimitive];if(void 0!==e){var r=e.call(l,t||"default");if("object"!=(0,b.A)(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(l)}(l,"string");return"symbol"==(0,b.A)(t)?t:String(t)}(t))in l?Object.defineProperty(l,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):l[t]=e,l}n(43751),n(54010),n(99903);var I=n(54361),H=(n(283),n(96310)),X=(n(50635),n(89958),n(54491),function(l,t){return(X=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])})(l,t)});function k(l,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=l}X(l,t),l.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e)}function J(l,t){var e=l[0],r=l[1];return[e*Math.cos(t)-r*Math.sin(t),e*Math.sin(t)+r*Math.cos(t)]}function ht(){for(var l=[],t=0;t<arguments.length;t++)l[t]=arguments[t];for(var e=0;e<l.length;e++)if("number"!=typeof l[e])throw new Error("assertNumbers arguments["+e+"] is not a number. "+typeof l[e]+" == typeof "+l[e]);return!0}var vt=Math.PI;function st(l,t,e){l.lArcFlag=0===l.lArcFlag?0:1,l.sweepFlag=0===l.sweepFlag?0:1;var r=l.rX,a=l.rY,i=l.x,o=l.y;r=Math.abs(l.rX),a=Math.abs(l.rY);var s=J([(t-i)/2,(e-o)/2],-l.xRot/180*vt),u=s[0],v=s[1],p=Math.pow(u,2)/Math.pow(r,2)+Math.pow(v,2)/Math.pow(a,2);1<p&&(r*=Math.sqrt(p),a*=Math.sqrt(p)),l.rX=r,l.rY=a;var E=Math.pow(r,2)*Math.pow(v,2)+Math.pow(a,2)*Math.pow(u,2),T=(l.lArcFlag!==l.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(r,2)*Math.pow(a,2)-E)/E)),m=r*v/a*T,N=-a*u/r*T,V=J([m,N],l.xRot/180*vt);l.cX=V[0]+(t+i)/2,l.cY=V[1]+(e+o)/2,l.phi1=Math.atan2((v-N)/a,(u-m)/r),l.phi2=Math.atan2((-v-N)/a,(-u-m)/r),0===l.sweepFlag&&l.phi2>l.phi1&&(l.phi2-=2*vt),1===l.sweepFlag&&l.phi2<l.phi1&&(l.phi2+=2*vt),l.phi1*=180/vt,l.phi2*=180/vt}function ft(l,t,e){ht(l,t,e);var r=l*l+t*t-e*e;if(0>r)return[];if(0===r)return[[l*e/(l*l+t*t),t*e/(l*l+t*t)]];var a=Math.sqrt(r);return[[(l*e+t*a)/(l*l+t*t),(t*e-l*a)/(l*l+t*t)],[(l*e-t*a)/(l*l+t*t),(t*e+l*a)/(l*l+t*t)]]}var Z,ut=Math.PI/180;function K(l,t,e){return(1-e)*l+e*t}function tt(l,t,e,r){return l+Math.cos(r/180*vt)*t+Math.sin(r/180*vt)*e}function Ft(l,t,e,r){var a=1e-6,i=t-l,o=e-t,s=3*i+3*(r-e)-6*o,u=6*(o-i),v=3*i;return Math.abs(s)<a?[-v/u]:function(p,E,T){void 0===T&&(T=1e-6);var m=p*p/4-E;if(m<-T)return[];if(m<=T)return[-p/2];var N=Math.sqrt(m);return[-p/2-N,-p/2+N]}(u/s,v/s,a)}function et(l,t,e,r,a){var i=1-a;return l*(i*i*i)+t*(3*i*i*a)+e*(3*i*a*a)+r*(a*a*a)}!function(l){function t(){return a(function(s,u,v){return s.relative&&(void 0!==s.x1&&(s.x1+=u),void 0!==s.y1&&(s.y1+=v),void 0!==s.x2&&(s.x2+=u),void 0!==s.y2&&(s.y2+=v),void 0!==s.x&&(s.x+=u),void 0!==s.y&&(s.y+=v),s.relative=!1),s})}function e(){var s=NaN,u=NaN,v=NaN,p=NaN;return a(function(E,T,m){return E.type&D.SMOOTH_CURVE_TO&&(E.type=D.CURVE_TO,s=isNaN(s)?T:s,u=isNaN(u)?m:u,E.x1=E.relative?T-s:2*T-s,E.y1=E.relative?m-u:2*m-u),E.type&D.CURVE_TO?(s=E.relative?T+E.x2:E.x2,u=E.relative?m+E.y2:E.y2):(s=NaN,u=NaN),E.type&D.SMOOTH_QUAD_TO&&(E.type=D.QUAD_TO,v=isNaN(v)?T:v,p=isNaN(p)?m:p,E.x1=E.relative?T-v:2*T-v,E.y1=E.relative?m-p:2*m-p),E.type&D.QUAD_TO?(v=E.relative?T+E.x1:E.x1,p=E.relative?m+E.y1:E.y1):(v=NaN,p=NaN),E})}function r(){var s=NaN,u=NaN;return a(function(v,p,E){if(v.type&D.SMOOTH_QUAD_TO&&(v.type=D.QUAD_TO,s=isNaN(s)?p:s,u=isNaN(u)?E:u,v.x1=v.relative?p-s:2*p-s,v.y1=v.relative?E-u:2*E-u),v.type&D.QUAD_TO){s=v.relative?p+v.x1:v.x1,u=v.relative?E+v.y1:v.y1;var T=v.x1,m=v.y1;v.type=D.CURVE_TO,v.x1=((v.relative?0:p)+2*T)/3,v.y1=((v.relative?0:E)+2*m)/3,v.x2=(v.x+2*T)/3,v.y2=(v.y+2*m)/3}else s=NaN,u=NaN;return v})}function a(s){var u=0,v=0,p=NaN,E=NaN;return function(T){if(isNaN(p)&&!(T.type&D.MOVE_TO))throw new Error("path must start with moveto");var m=s(T,u,v,p,E);return T.type&D.CLOSE_PATH&&(u=p,v=E),void 0!==T.x&&(u=T.relative?u+T.x:T.x),void 0!==T.y&&(v=T.relative?v+T.y:T.y),T.type&D.MOVE_TO&&(p=u,E=v),m}}function i(s,u,v,p,E,T){return ht(s,u,v,p,E,T),a(function(m,N,V,L){var U=m.x1,Q=m.x2,z=m.relative&&!isNaN(L),j=void 0!==m.x?m.x:z?0:N,q=void 0!==m.y?m.y:z?0:V;function at(zt){return zt*zt}m.type&D.HORIZ_LINE_TO&&0!==u&&(m.type=D.LINE_TO,m.y=m.relative?0:V),m.type&D.VERT_LINE_TO&&0!==v&&(m.type=D.LINE_TO,m.x=m.relative?0:N),void 0!==m.x&&(m.x=m.x*s+q*v+(z?0:E)),void 0!==m.y&&(m.y=j*u+m.y*p+(z?0:T)),void 0!==m.x1&&(m.x1=m.x1*s+m.y1*v+(z?0:E)),void 0!==m.y1&&(m.y1=U*u+m.y1*p+(z?0:T)),void 0!==m.x2&&(m.x2=m.x2*s+m.y2*v+(z?0:E)),void 0!==m.y2&&(m.y2=Q*u+m.y2*p+(z?0:T));var W=s*p-u*v;if(void 0!==m.xRot&&(1!==s||0!==u||0!==v||1!==p))if(0===W)delete m.rX,delete m.rY,delete m.xRot,delete m.lArcFlag,delete m.sweepFlag,m.type=D.LINE_TO;else{var lt=m.xRot*Math.PI/180,gt=Math.sin(lt),mt=Math.cos(lt),Tt=1/at(m.rX),St=1/at(m.rY),Dt=at(mt)*Tt+at(gt)*St,Lt=2*gt*mt*(Tt-St),At=at(gt)*Tt+at(mt)*St,wt=Dt*p*p-Lt*u*p+At*u*u,It=Lt*(s*p+u*v)-2*(Dt*v*p+At*s*u),Ut=Dt*v*v-Lt*s*v+At*s*s,Ot=(Math.atan2(It,wt-Ut)+Math.PI)%Math.PI/2,yt=Math.sin(Ot),Bt=Math.cos(Ot);m.rX=Math.abs(W)/Math.sqrt(wt*at(Bt)+It*yt*Bt+Ut*at(yt)),m.rY=Math.abs(W)/Math.sqrt(wt*at(yt)-It*yt*Bt+Ut*at(Bt)),m.xRot=180*Ot/Math.PI}return void 0!==m.sweepFlag&&0>W&&(m.sweepFlag=+!m.sweepFlag),m})}l.ROUND=function(s){function u(v){return Math.round(v*s)/s}return void 0===s&&(s=1e13),ht(s),function(v){return void 0!==v.x1&&(v.x1=u(v.x1)),void 0!==v.y1&&(v.y1=u(v.y1)),void 0!==v.x2&&(v.x2=u(v.x2)),void 0!==v.y2&&(v.y2=u(v.y2)),void 0!==v.x&&(v.x=u(v.x)),void 0!==v.y&&(v.y=u(v.y)),void 0!==v.rX&&(v.rX=u(v.rX)),void 0!==v.rY&&(v.rY=u(v.rY)),v}},l.TO_ABS=t,l.TO_REL=function(){return a(function(s,u,v){return s.relative||(void 0!==s.x1&&(s.x1-=u),void 0!==s.y1&&(s.y1-=v),void 0!==s.x2&&(s.x2-=u),void 0!==s.y2&&(s.y2-=v),void 0!==s.x&&(s.x-=u),void 0!==s.y&&(s.y-=v),s.relative=!0),s})},l.NORMALIZE_HVZ=function(s,u,v){return void 0===s&&(s=!0),void 0===u&&(u=!0),void 0===v&&(v=!0),a(function(p,E,T,m,N){if(isNaN(m)&&!(p.type&D.MOVE_TO))throw new Error("path must start with moveto");return u&&p.type&D.HORIZ_LINE_TO&&(p.type=D.LINE_TO,p.y=p.relative?0:T),v&&p.type&D.VERT_LINE_TO&&(p.type=D.LINE_TO,p.x=p.relative?0:E),s&&p.type&D.CLOSE_PATH&&(p.type=D.LINE_TO,p.x=p.relative?m-E:m,p.y=p.relative?N-T:N),p.type&D.ARC&&(0===p.rX||0===p.rY)&&(p.type=D.LINE_TO,delete p.rX,delete p.rY,delete p.xRot,delete p.lArcFlag,delete p.sweepFlag),p})},l.NORMALIZE_ST=e,l.QT_TO_C=r,l.INFO=a,l.SANITIZE=function(s){void 0===s&&(s=0),ht(s);var u=NaN,v=NaN,p=NaN,E=NaN;return a(function(T,m,N,V,L){var U=Math.abs,Q=!1,z=0,j=0;if(T.type&D.SMOOTH_CURVE_TO&&(z=isNaN(u)?0:m-u,j=isNaN(v)?0:N-v),T.type&(D.CURVE_TO|D.SMOOTH_CURVE_TO)?(u=T.relative?m+T.x2:T.x2,v=T.relative?N+T.y2:T.y2):(u=NaN,v=NaN),T.type&D.SMOOTH_QUAD_TO?(p=isNaN(p)?m:2*m-p,E=isNaN(E)?N:2*N-E):T.type&D.QUAD_TO?(p=T.relative?m+T.x1:T.x1,E=T.relative?N+T.y1:T.y2):(p=NaN,E=NaN),T.type&D.LINE_COMMANDS||T.type&D.ARC&&(0===T.rX||0===T.rY||!T.lArcFlag)||T.type&D.CURVE_TO||T.type&D.SMOOTH_CURVE_TO||T.type&D.QUAD_TO||T.type&D.SMOOTH_QUAD_TO){var q=void 0===T.x?0:T.relative?T.x:T.x-m,at=void 0===T.y?0:T.relative?T.y:T.y-N;z=isNaN(p)?void 0===T.x1?z:T.relative?T.x:T.x1-m:p-m,j=isNaN(E)?void 0===T.y1?j:T.relative?T.y:T.y1-N:E-N;var W=void 0===T.x2?0:T.relative?T.x:T.x2-m,lt=void 0===T.y2?0:T.relative?T.y:T.y2-N;U(q)<=s&&U(at)<=s&&U(z)<=s&&U(j)<=s&&U(W)<=s&&U(lt)<=s&&(Q=!0)}return T.type&D.CLOSE_PATH&&U(m-V)<=s&&U(N-L)<=s&&(Q=!0),Q?[]:T})},l.MATRIX=i,l.ROTATE=function(s,u,v){void 0===u&&(u=0),void 0===v&&(v=0),ht(s,u,v);var p=Math.sin(s),E=Math.cos(s);return i(E,p,-p,E,u-u*E+v*p,v-u*p-v*E)},l.TRANSLATE=function(s,u){return void 0===u&&(u=0),ht(s,u),i(1,0,0,1,s,u)},l.SCALE=function(s,u){return void 0===u&&(u=s),ht(s,u),i(s,0,0,u,0,0)},l.SKEW_X=function(s){return ht(s),i(1,0,Math.atan(s),1,0,0)},l.SKEW_Y=function(s){return ht(s),i(1,Math.atan(s),0,1,0,0)},l.X_AXIS_SYMMETRY=function(s){return void 0===s&&(s=0),ht(s),i(-1,0,0,1,s,0)},l.Y_AXIS_SYMMETRY=function(s){return void 0===s&&(s=0),ht(s),i(1,0,0,-1,0,s)},l.A_TO_C=function(){return a(function(s,u,v){return D.ARC===s.type?function(p,E,T){var m,N,V,L;p.cX||st(p,E,T);for(var U=Math.min(p.phi1,p.phi2),Q=Math.max(p.phi1,p.phi2)-U,z=Math.ceil(Q/90),j=new Array(z),q=E,at=T,W=0;W<z;W++){var lt=K(p.phi1,p.phi2,W/z),gt=K(p.phi1,p.phi2,(W+1)/z),Tt=4/3*Math.tan((gt-lt)*ut/4),St=[Math.cos(lt*ut)-Tt*Math.sin(lt*ut),Math.sin(lt*ut)+Tt*Math.cos(lt*ut)],Dt=St[0],Lt=St[1],At=[Math.cos(gt*ut),Math.sin(gt*ut)],wt=At[0],It=At[1],Ut=[wt+Tt*Math.sin(gt*ut),It-Tt*Math.cos(gt*ut)],Ot=Ut[0],yt=Ut[1];j[W]={relative:p.relative,type:D.CURVE_TO};var Bt=function(zt,Ht){var Gt=J([zt*p.rX,Ht*p.rY],p.xRot);return[p.cX+Gt[0],p.cY+Gt[1]]};m=Bt(Dt,Lt),j[W].x1=m[0],j[W].y1=m[1],N=Bt(Ot,yt),j[W].x2=N[0],j[W].y2=N[1],V=Bt(wt,It),j[W].x=V[0],j[W].y=V[1],p.relative&&(j[W].x1-=q,j[W].y1-=at,j[W].x2-=q,j[W].y2-=at,j[W].x-=q,j[W].y-=at),q=(L=[j[W].x,j[W].y])[0],at=L[1]}return j}(s,s.relative?0:u,s.relative?0:v):s})},l.ANNOTATE_ARCS=function(){return a(function(s,u,v){return s.relative&&(u=0,v=0),D.ARC===s.type&&st(s,u,v),s})},l.CLONE=function o(){return function(s){var u={};for(var v in s)u[v]=s[v];return u}},l.CALCULATE_BOUNDS=function(){var u=t(),v=r(),p=e(),E=a(function(T,m,N){var V=p(v(u(function(T){var m={};for(var N in T)m[N]=T[N];return m}(T))));function L(yt){yt>E.maxX&&(E.maxX=yt),yt<E.minX&&(E.minX=yt)}function U(yt){yt>E.maxY&&(E.maxY=yt),yt<E.minY&&(E.minY=yt)}if(V.type&D.DRAWING_COMMANDS&&(L(m),U(N)),V.type&D.HORIZ_LINE_TO&&L(V.x),V.type&D.VERT_LINE_TO&&U(V.y),V.type&D.LINE_TO&&(L(V.x),U(V.y)),V.type&D.CURVE_TO){L(V.x),U(V.y);for(var Q=0,z=Ft(m,V.x1,V.x2,V.x);Q<z.length;Q++)0<(Ot=z[Q])&&1>Ot&&L(et(m,V.x1,V.x2,V.x,Ot));for(var j=0,q=Ft(N,V.y1,V.y2,V.y);j<q.length;j++)0<(Ot=q[j])&&1>Ot&&U(et(N,V.y1,V.y2,V.y,Ot))}if(V.type&D.ARC){L(V.x),U(V.y),st(V,m,N);for(var at=V.xRot/180*Math.PI,W=Math.cos(at)*V.rX,lt=Math.sin(at)*V.rX,gt=-Math.sin(at)*V.rY,mt=Math.cos(at)*V.rY,Tt=V.phi1<V.phi2?[V.phi1,V.phi2]:-180>V.phi2?[V.phi2+360,V.phi1+360]:[V.phi2,V.phi1],St=Tt[0],Dt=Tt[1],Lt=function(yt){var Ht=180*Math.atan2(yt[1],yt[0])/Math.PI;return Ht<St?Ht+360:Ht},At=0,wt=ft(gt,-W,0).map(Lt);At<wt.length;At++)(Ot=wt[At])>St&&Ot<Dt&&L(tt(V.cX,W,gt,Ot));for(var It=0,Ut=ft(mt,-lt,0).map(Lt);It<Ut.length;It++){var Ot;(Ot=Ut[It])>St&&Ot<Dt&&U(tt(V.cY,lt,mt,Ot))}}return T});return E.minX=1/0,E.maxX=-1/0,E.minY=1/0,E.maxY=-1/0,E}}(Z||(Z={}));var ot,ct=function(){function l(){}return l.prototype.round=function(t){return this.transform(Z.ROUND(t))},l.prototype.toAbs=function(){return this.transform(Z.TO_ABS())},l.prototype.toRel=function(){return this.transform(Z.TO_REL())},l.prototype.normalizeHVZ=function(t,e,r){return this.transform(Z.NORMALIZE_HVZ(t,e,r))},l.prototype.normalizeST=function(){return this.transform(Z.NORMALIZE_ST())},l.prototype.qtToC=function(){return this.transform(Z.QT_TO_C())},l.prototype.aToC=function(){return this.transform(Z.A_TO_C())},l.prototype.sanitize=function(t){return this.transform(Z.SANITIZE(t))},l.prototype.translate=function(t,e){return this.transform(Z.TRANSLATE(t,e))},l.prototype.scale=function(t,e){return this.transform(Z.SCALE(t,e))},l.prototype.rotate=function(t,e,r){return this.transform(Z.ROTATE(t,e,r))},l.prototype.matrix=function(t,e,r,a,i,o){return this.transform(Z.MATRIX(t,e,r,a,i,o))},l.prototype.skewX=function(t){return this.transform(Z.SKEW_X(t))},l.prototype.skewY=function(t){return this.transform(Z.SKEW_Y(t))},l.prototype.xSymmetry=function(t){return this.transform(Z.X_AXIS_SYMMETRY(t))},l.prototype.ySymmetry=function(t){return this.transform(Z.Y_AXIS_SYMMETRY(t))},l.prototype.annotateArcs=function(){return this.transform(Z.ANNOTATE_ARCS())},l}(),bt=function(l){return" "===l||"\t"===l||"\r"===l||"\n"===l},xt=function(l){return 48<=l.charCodeAt(0)&&l.charCodeAt(0)<=57},Mt=function(l){function t(){var e=l.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return k(t,l),t.prototype.finish=function(e){if(void 0===e&&(e=[]),this.parse(" ",e),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return e},t.prototype.parse=function(e,r){var a=this;void 0===r&&(r=[]);for(var i=function(E){r.push(E),a.curArgs.length=0,a.canParseCommandOrComma=!0},o=0;o<e.length;o++){var s=e[o],u=!(this.curCommandType!==D.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),v=xt(s)&&("0"===this.curNumber&&"0"===s||u);if(!xt(s)||v)if("e"!==s&&"E"!==s)if("-"!==s&&"+"!==s||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==s||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&-1!==this.curCommandType){var p=Number(this.curNumber);if(isNaN(p))throw new SyntaxError("Invalid number ending at "+o);if(this.curCommandType===D.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>p)throw new SyntaxError('Expected positive number, got "'+p+'" at index "'+o+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+o+'"');this.curArgs.push(p),this.curArgs.length===Pt[this.curCommandType]&&(D.HORIZ_LINE_TO===this.curCommandType?i({type:D.HORIZ_LINE_TO,relative:this.curCommandRelative,x:p}):D.VERT_LINE_TO===this.curCommandType?i({type:D.VERT_LINE_TO,relative:this.curCommandRelative,y:p}):this.curCommandType===D.MOVE_TO||this.curCommandType===D.LINE_TO||this.curCommandType===D.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),D.MOVE_TO===this.curCommandType&&(this.curCommandType=D.LINE_TO)):this.curCommandType===D.CURVE_TO?i({type:D.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===D.SMOOTH_CURVE_TO?i({type:D.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===D.QUAD_TO?i({type:D.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===D.ARC&&i({type:D.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!bt(s))if(","===s&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==s&&"-"!==s&&"."!==s)if(v)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+o+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==s&&"Z"!==s)if("h"===s||"H"===s)this.curCommandType=D.HORIZ_LINE_TO,this.curCommandRelative="h"===s;else if("v"===s||"V"===s)this.curCommandType=D.VERT_LINE_TO,this.curCommandRelative="v"===s;else if("m"===s||"M"===s)this.curCommandType=D.MOVE_TO,this.curCommandRelative="m"===s;else if("l"===s||"L"===s)this.curCommandType=D.LINE_TO,this.curCommandRelative="l"===s;else if("c"===s||"C"===s)this.curCommandType=D.CURVE_TO,this.curCommandRelative="c"===s;else if("s"===s||"S"===s)this.curCommandType=D.SMOOTH_CURVE_TO,this.curCommandRelative="s"===s;else if("q"===s||"Q"===s)this.curCommandType=D.QUAD_TO,this.curCommandRelative="q"===s;else if("t"===s||"T"===s)this.curCommandType=D.SMOOTH_QUAD_TO,this.curCommandRelative="t"===s;else{if("a"!==s&&"A"!==s)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+".");this.curCommandType=D.ARC,this.curCommandRelative="a"===s}else r.push({type:D.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal="."===s}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return r},t.prototype.transform=function(e){return Object.create(this,{parse:{value:function(r,a){void 0===a&&(a=[]);for(var i=0,o=Object.getPrototypeOf(this).parse.call(this,r);i<o.length;i++){var u=e(o[i]);Array.isArray(u)?a.push.apply(a,u):a.push(u)}return a}}})},t}(ct),D=function(l){function t(e){var r=l.call(this)||this;return r.commands="string"==typeof e?t.parse(e):e,r}return k(t,l),t.prototype.encode=function(){return t.encode(this.commands)},t.prototype.getBounds=function(){var e=Z.CALCULATE_BOUNDS();return this.transform(e),e},t.prototype.transform=function(e){for(var r=[],a=0,i=this.commands;a<i.length;a++){var o=e(i[a]);Array.isArray(o)?r.push.apply(r,o):r.push(o)}return this.commands=r,this},t.encode=function(e){return function rt(l){var t="";Array.isArray(l)||(l=[l]);for(var e=0;e<l.length;e++){var r=l[e];if(r.type===D.CLOSE_PATH)t+="z";else if(r.type===D.HORIZ_LINE_TO)t+=(r.relative?"h":"H")+r.x;else if(r.type===D.VERT_LINE_TO)t+=(r.relative?"v":"V")+r.y;else if(r.type===D.MOVE_TO)t+=(r.relative?"m":"M")+r.x+" "+r.y;else if(r.type===D.LINE_TO)t+=(r.relative?"l":"L")+r.x+" "+r.y;else if(r.type===D.CURVE_TO)t+=(r.relative?"c":"C")+r.x1+" "+r.y1+" "+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===D.SMOOTH_CURVE_TO)t+=(r.relative?"s":"S")+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===D.QUAD_TO)t+=(r.relative?"q":"Q")+r.x1+" "+r.y1+" "+r.x+" "+r.y;else if(r.type===D.SMOOTH_QUAD_TO)t+=(r.relative?"t":"T")+r.x+" "+r.y;else{if(r.type!==D.ARC)throw new Error('Unexpected command type "'+r.type+'" at index '+e+".");t+=(r.relative?"a":"A")+r.rX+" "+r.rY+" "+r.xRot+" "+ +r.lArcFlag+" "+ +r.sweepFlag+" "+r.x+" "+r.y}}return t}(e)},t.parse=function(e){var r=new Mt,a=[];return r.parse(e,a),r.finish(a),a},t.CLOSE_PATH=1,t.MOVE_TO=2,t.HORIZ_LINE_TO=4,t.VERT_LINE_TO=8,t.LINE_TO=16,t.CURVE_TO=32,t.SMOOTH_CURVE_TO=64,t.QUAD_TO=128,t.SMOOTH_QUAD_TO=256,t.ARC=512,t.LINE_COMMANDS=t.LINE_TO|t.HORIZ_LINE_TO|t.VERT_LINE_TO,t.DRAWING_COMMANDS=t.HORIZ_LINE_TO|t.VERT_LINE_TO|t.LINE_TO|t.CURVE_TO|t.SMOOTH_CURVE_TO|t.QUAD_TO|t.SMOOTH_QUAD_TO|t.ARC,t}(ct),Pt=((ot={})[D.MOVE_TO]=2,ot[D.LINE_TO]=2,ot[D.HORIZ_LINE_TO]=1,ot[D.VERT_LINE_TO]=1,ot[D.CLOSE_PATH]=0,ot[D.QUAD_TO]=4,ot[D.SMOOTH_QUAD_TO]=2,ot[D.CURVE_TO]=6,ot[D.SMOOTH_CURVE_TO]=4,ot[D.ARC]=7,ot);function se(l){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(l)}n(26286);var Zt=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],kt=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function re(l,t,e,r,a,i){if(!(isNaN(i)||i<1)){i|=0;var o=function Jt(l,t,e,r,a){if("string"==typeof l&&(l=document.getElementById(l)),!l||"object"!==se(l)||!("getContext"in l))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var i=l.getContext("2d");try{return i.getImageData(t,e,r,a)}catch(o){throw new Error("unable to access image data: "+o)}}(l,t,e,r,a);o=function ue(l,t,e,r,a,i){for(var N,o=l.data,s=2*i+1,u=r-1,v=a-1,p=i+1,E=p*(p+1)/2,T=new qt,m=T,V=1;V<s;V++)m=m.next=new qt,V===p&&(N=m);m.next=T;for(var L=null,U=null,Q=0,z=0,j=Zt[i],q=kt[i],at=0;at<a;at++){m=T;for(var W=o[z],lt=o[z+1],gt=o[z+2],mt=o[z+3],Tt=0;Tt<p;Tt++)m.r=W,m.g=lt,m.b=gt,m.a=mt,m=m.next;for(var St=0,Dt=0,Lt=0,At=0,wt=p*W,It=p*lt,Ut=p*gt,Ot=p*mt,yt=E*W,Bt=E*lt,zt=E*gt,Ht=E*mt,Gt=1;Gt<p;Gt++){var Yt=z+((u<Gt?u:Gt)<<2),Kt=o[Yt],_t=o[Yt+1],Ve=o[Yt+2],De=o[Yt+3],de=p-Gt;yt+=(m.r=Kt)*de,Bt+=(m.g=_t)*de,zt+=(m.b=Ve)*de,Ht+=(m.a=De)*de,St+=Kt,Dt+=_t,Lt+=Ve,At+=De,m=m.next}L=T,U=N;for(var pe=0;pe<r;pe++){var ye=Ht*j>>>q;if(o[z+3]=ye,0!==ye){var me=255/ye;o[z]=(yt*j>>>q)*me,o[z+1]=(Bt*j>>>q)*me,o[z+2]=(zt*j>>>q)*me}else o[z]=o[z+1]=o[z+2]=0;yt-=wt,Bt-=It,zt-=Ut,Ht-=Ot,wt-=L.r,It-=L.g,Ut-=L.b,Ot-=L.a;var te=pe+i+1;yt+=St+=L.r=o[te=Q+(te<u?te:u)<<2],Bt+=Dt+=L.g=o[te+1],zt+=Lt+=L.b=o[te+2],Ht+=At+=L.a=o[te+3],L=L.next;var Le=U.r,Be=U.g,Ae=U.b,Ue=U.a;wt+=Le,It+=Be,Ut+=Ae,Ot+=Ue,St-=Le,Dt-=Be,Lt-=Ae,At-=Ue,U=U.next,z+=4}Q+=r}for(var ee=0;ee<r;ee++){var Pe=o[z=ee<<2],Re=o[z+1],Ne=o[z+2],Xt=o[z+3],Sr=p*Pe,br=p*Re,Cr=p*Ne,Ar=p*Xt,Ze=E*Pe,Je=E*Re,qe=E*Ne,_e=E*Xt;m=T;for(var Pn=0;Pn<p;Pn++)m.r=Pe,m.g=Re,m.b=Ne,m.a=Xt,m=m.next;for(var Rn=r,Pr=0,Rr=0,Nr=0,Ir=0,tr=1;tr<=i;tr++){var er=p-tr;Ze+=(m.r=Pe=o[z=Rn+ee<<2])*er,Je+=(m.g=Re=o[z+1])*er,qe+=(m.b=Ne=o[z+2])*er,_e+=(m.a=Xt=o[z+3])*er,Ir+=Pe,Pr+=Re,Rr+=Ne,Nr+=Xt,m=m.next,tr<v&&(Rn+=r)}z=ee,L=T,U=N;for(var Mr=0;Mr<a;Mr++){var Wt=z<<2;o[Wt+3]=Xt=_e*j>>>q,Xt>0?(o[Wt]=(Ze*j>>>q)*(Xt=255/Xt),o[Wt+1]=(Je*j>>>q)*Xt,o[Wt+2]=(qe*j>>>q)*Xt):o[Wt]=o[Wt+1]=o[Wt+2]=0,Ze-=Sr,Je-=br,qe-=Cr,_e-=Ar,Sr-=L.r,br-=L.g,Cr-=L.b,Ar-=L.a,Wt=ee+((Wt=Mr+p)<v?Wt:v)*r<<2,Ze+=Ir+=L.r=o[Wt],Je+=Pr+=L.g=o[Wt+1],qe+=Rr+=L.b=o[Wt+2],_e+=Nr+=L.a=o[Wt+3],L=L.next,Sr+=Pe=U.r,br+=Re=U.g,Cr+=Ne=U.b,Ar+=Xt=U.a,Ir-=Pe,Pr-=Re,Rr-=Ne,Nr-=Xt,U=U.next,z+=r}}return l}(o,0,0,r,a,i),l.getContext("2d").putImageData(o,t,e)}}var qt=function l(){(function oe(l,t){if(!(l instanceof t))throw new TypeError("Cannot call a class as a function")})(this,l),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},nr=Object.freeze({__proto__:null,offscreen:function rr(){var{DOMParser:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:l,createCanvas:(e,r)=>new OffscreenCanvas(e,r),createImage:e=>(0,f.A)(function*(){var r=yield fetch(e),a=yield r.blob();return yield createImageBitmap(a)})()};return(typeof DOMParser<"u"||typeof l>"u")&&Reflect.deleteProperty(t,"DOMParser"),t},node:function je(l){var{DOMParser:t,canvas:e,fetch:r}=l;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,fetch:r,createCanvas:e.createCanvas,createImage:e.loadImage}}});function $t(l){return l.replace(/(?!\u3000)\s+/gm," ")}function ne(l){return l.replace(/^[\n \t]+/,"")}function Ie(l){return l.replace(/[\n \t]+$/,"")}function G(l){return((l||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var it=/^[A-Z-]+$/;function pt(l){return it.test(l)?l.toLowerCase():l}function dt(l){var t=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(l)||[];return t[2]||t[3]||t[4]}function Ct(l){if(!l.startsWith("rgb"))return l;var t=3;return l.replace(/\d+(\.\d+)?/g,(r,a)=>t--&&a?String(Math.round(parseFloat(r))):r)}var fe=/(\[[^\]]+\])/g,Me=/(#[^\s+>~.[:]+)/g,ve=/(\.[^\s+>~.[:]+)/g,ce=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,ze=/(:[\w-]+\([^)]*\))/gi,Ge=/(:[^\s+>~.[:]+)/g,ar=/([^\s+>~.[:]+)/g;function Te(l,t){var e=t.exec(l);return e?[l.replace(t," "),e.length]:[l,0]}function wr(l){var t=[0,0,0],e=l.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[e,r]=Te(e,fe),t[1]+=r,[e,r]=Te(e,Me),t[0]+=r,[e,r]=Te(e,ve),t[1]+=r,[e,r]=Te(e,ce),t[2]+=r,[e,r]=Te(e,ze),t[1]+=r,[e,r]=Te(e,Ge),t[1]+=r,e=e.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[e,r]=Te(e,ar),t[2]+=r,t.join("")}var Oe=1e-8;function ir(l){return Math.sqrt(Math.pow(l[0],2)+Math.pow(l[1],2))}function $e(l,t){return(l[0]*t[0]+l[1]*t[1])/(ir(l)*ir(t))}function sr(l,t){return(l[0]*t[1]<l[1]*t[0]?-1:1)*Math.acos($e(l,t))}function or(l){return l*l*l}function ur(l){return 3*l*l*(1-l)}function lr(l){return 3*l*(1-l)*(1-l)}function hr(l){return(1-l)*(1-l)*(1-l)}function fr(l){return l*l}function vr(l){return 2*l*(1-l)}function cr(l){return(1-l)*(1-l)}let _=(()=>{class l{constructor(e,r,a){this.document=e,this.name=r,this.value=a,this.isNormalizedColor=!1}static empty(e){return new l(e,"EMPTY","")}split(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:r,name:a}=this;return $t(this.getString()).trim().split(e).map(i=>new l(r,a,i))}hasValue(e){var{value:r}=this;return null!==r&&""!==r&&(e||0!==r)&&typeof r<"u"}isString(e){var{value:r}=this,a="string"==typeof r;return a&&e?e.test(r):a}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var e=this.getString();switch(!0){case e.endsWith("px"):case/^[0-9]+$/.test(e):return!0;default:return!1}}setValue(e){return this.value=e,this}getValue(e){return typeof e>"u"||this.hasValue()?this.value:e}getNumber(e){if(!this.hasValue())return typeof e>"u"?0:parseFloat(e);var{value:r}=this,a=parseFloat(r);return this.isString(/%$/)&&(a/=100),a}getString(e){return typeof e>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(e)}getColor(e){var r=this.getString(e);return this.isNormalizedColor||(this.isNormalizedColor=!0,r=Ct(r),this.value=r),r}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[a,i]="boolean"==typeof e?[void 0,e]:[e],{viewPort:o}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(o.computeSize("x"),o.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(o.computeSize("x"),o.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*o.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*o.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*o.computeSize(a);default:var s=this.getNumber();return r&&s<1?s*o.computeSize(a):s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var e=this.getString(),r=/#([^)'"]+)/.exec(e);return r&&(r=r[1]),r||(r=e),this.document.definitions[r]}getFillStyleDefinition(e,r){var a=this.getDefinition();if(!a)return null;if("function"==typeof a.createGradient)return a.createGradient(this.document.ctx,e,r);if("function"==typeof a.createPattern){if(a.getHrefAttribute().hasValue()){var i=a.getAttribute("patternTransform");a=a.getHrefAttribute().getDefinition(),i.hasValue()&&a.getAttribute("patternTransform",!0).setValue(i.value)}return a.createPattern(this.document.ctx,e,r)}return null}getTextBaseline(){return this.hasValue()?l.textBaselineMapping[this.getString()]:null}addOpacity(e){for(var r=this.getColor(),a=r.length,i=0,o=0;o<a&&(","===r[o]&&i++,3!==i);o++);if(e.hasValue()&&this.isString()&&3!==i){var s=new H(r);s.ok&&(s.alpha=e.getNumber(),r=s.toRGBA())}return new l(this.document,this.name,r)}}return l.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"},l})();class Vr{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class Rt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,a=e]=G(t);return new Rt(r,a)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,a=r]=G(t);return new Rt(r,a)}static parsePath(t){for(var e=G(t),r=e.length,a=[],i=0;i<r;i+=2)a.push(new Rt(e[i],e[i+1]));return a}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[1]+r*t[3]+t[5];this.x=e*t[0]+r*t[2]+t[4],this.y=i}}class Dr{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,a=t.ctx.canvas;a.onclick=e,a.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:a}=t.ctx.canvas;a&&(a.cursor=""),e.forEach((i,o)=>{for(var{run:s}=i,u=r[o];u;)s(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:a}=this;r.forEach((i,o)=>{var{x:s,y:u}=i;!a[o]&&e.isPointInPath&&e.isPointInPath(s,u)&&(a[o]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:a}=this;r.forEach((i,o)=>{var{x:s,y:u}=i;!a[o]&&e.isPointInBox(s,u)&&(a[o]=t)})}}mapXY(t,e){for(var{window:r,ctx:a}=this.screen,i=new Rt(t,e),o=a.canvas;o;)i.x-=o.offsetLeft,i.y-=o.offsetTop,o=o.offsetParent;return r.scrollX&&(i.x+=r.scrollX),r.scrollY&&(i.y+=r.scrollY),i}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(a){a.onClick&&a.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(a){a.onMouseMove&&a.onMouseMove()}})}}var Lr=typeof window<"u"?window:null,Br=typeof fetch<"u"?fetch.bind(void 0):null;let gr=(()=>{class l{constructor(e){var{fetch:r=Br,window:a=Lr}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new Vr,this.mouse=new Dr(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=a,this.fetch=r}wait(e){this.waits.push(e)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var e=this.waits.every(r=>r());return e&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=e,e}setDefaults(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4}setViewBox(e){var{document:r,ctx:a,aspectRatio:i,width:o,desiredWidth:s,height:u,desiredHeight:v,minX:p=0,minY:E=0,refX:T,refY:m,clip:N=!1,clipX:V=0,clipY:L=0}=e,U=$t(i).replace(/^defer\s/,""),[Q,z]=U.split(" "),j=Q||"xMidYMid",q=z||"meet",at=o/s,W=u/v,lt=Math.min(at,W),gt=Math.max(at,W),mt=s,Tt=v;"meet"===q&&(mt*=lt,Tt*=lt),"slice"===q&&(mt*=gt,Tt*=gt);var St=new _(r,"refX",T),Dt=new _(r,"refY",m),Lt=St.hasValue()&&Dt.hasValue();if(Lt&&a.translate(-lt*St.getPixels("x"),-lt*Dt.getPixels("y")),N){var At=lt*V,wt=lt*L;a.beginPath(),a.moveTo(At,wt),a.lineTo(o,wt),a.lineTo(o,u),a.lineTo(At,u),a.closePath(),a.clip()}if(!Lt){var It="meet"===q&&lt===W,Ut="slice"===q&&gt===W,Ot="meet"===q&&lt===at,yt="slice"===q&&gt===at;j.startsWith("xMid")&&(It||Ut)&&a.translate(o/2-mt/2,0),j.endsWith("YMid")&&(Ot||yt)&&a.translate(0,u/2-Tt/2),j.startsWith("xMax")&&(It||Ut)&&a.translate(o-mt,0),j.endsWith("YMax")&&(Ot||yt)&&a.translate(0,u-Tt)}switch(!0){case"none"===j:a.scale(at,W);break;case"meet"===q:a.scale(lt,lt);break;case"slice"===q:a.scale(gt,gt)}a.translate(-p,-E)}start(e){var{enableRedraw:r=!1,ignoreMouse:a=!1,ignoreAnimation:i=!1,ignoreDimensions:o=!1,ignoreClear:s=!1,forceRedraw:u,scaleWidth:v,scaleHeight:p,offsetX:E,offsetY:T}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:m,mouse:N}=this,V=1e3/m;if(this.frameDuration=V,this.readyPromise=new Promise(j=>{this.resolveReady=j}),this.isReady()&&this.render(e,o,s,v,p,E,T),r){var L=Date.now(),U=L,Q=0,z=()=>{L=Date.now(),(Q=L-U)>=V&&(U=L-Q%V,this.shouldUpdate(i,u)&&(this.render(e,o,s,v,p,E,T),N.runEvents())),this.intervalId=I(z)};a||N.start(),this.intervalId=I(z)}}stop(){this.intervalId&&(I.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(e,r){if(!e){var{frameDuration:a}=this;if(this.animations.reduce((o,s)=>s.update(a)||o,!1))return!0}return!!("function"==typeof r&&r()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(e,r,a,i,o,s,u){var{CLIENT_WIDTH:v,CLIENT_HEIGHT:p,viewPort:E,ctx:T,isFirstRender:m}=this,N=T.canvas;E.clear(),N.width&&N.height?E.setCurrent(N.width,N.height):E.setCurrent(v,p);var V=e.getStyle("width"),L=e.getStyle("height");!r&&(m||"number"!=typeof i&&"number"!=typeof o)&&(V.hasValue()&&(N.width=V.getPixels("x"),N.style&&(N.style.width="".concat(N.width,"px"))),L.hasValue()&&(N.height=L.getPixels("y"),N.style&&(N.style.height="".concat(N.height,"px"))));var U=N.clientWidth||N.width,Q=N.clientHeight||N.height;if(r&&V.hasValue()&&L.hasValue()&&(U=V.getPixels("x"),Q=L.getPixels("y")),E.setCurrent(U,Q),"number"==typeof s&&e.getAttribute("x",!0).setValue(s),"number"==typeof u&&e.getAttribute("y",!0).setValue(u),"number"==typeof i||"number"==typeof o){var z=G(e.getAttribute("viewBox").getString()),j=0,q=0;if("number"==typeof i){var at=e.getStyle("width");at.hasValue()?j=at.getPixels("x")/i:isNaN(z[2])||(j=z[2]/i)}if("number"==typeof o){var W=e.getStyle("height");W.hasValue()?q=W.getPixels("y")/o:isNaN(z[3])||(q=z[3]/o)}j||(j=q),q||(q=j),e.getAttribute("width",!0).setValue(i),e.getAttribute("height",!0).setValue(o);var lt=e.getStyle("transform",!0,!0);lt.setValue("".concat(lt.getString()," scale(").concat(1/j,", ").concat(1/q,")"))}a||T.clearRect(0,0,U,Q),e.render(T),m&&(this.isFirstRender=!1)}}return l.defaultWindow=Lr,l.defaultFetch=Br,l})();var{defaultFetch:Nn}=gr,In=typeof DOMParser<"u"?DOMParser:null;class He{constructor(){var{fetch:t=Nn,DOMParser:e=In}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return(0,f.A)(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch{return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return(0,f.A)(function*(){var r=yield e.fetch(t),a=yield r.text();return e.parseFromString(a)})()}}class Fr{constructor(t,e){this.type="translate",this.point=null,this.point=Rt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class Ur{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var a=G(e);this.angle=new _(t,"angle",a[0]),this.originX=r[0],this.originY=r[1],this.cx=a[1]||0,this.cy=a[2]||0}apply(t){var{cx:e,cy:r,originX:a,originY:i,angle:o}=this,s=e+a.getPixels("x"),u=r+i.getPixels("y");t.translate(s,u),t.rotate(o.getRadians()),t.translate(-s,-u)}unapply(t){var{cx:e,cy:r,originX:a,originY:i,angle:o}=this,s=e+a.getPixels("x"),u=r+i.getPixels("y");t.translate(s,u),t.rotate(-1*o.getRadians()),t.translate(-s,-u)}applyToPoint(t){var{cx:e,cy:r,angle:a}=this,i=a.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(i),Math.sin(i),-Math.sin(i),Math.cos(i),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class jr{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var a=Rt.parseScale(e);(0===a.x||0===a.y)&&(a.x=Oe,a.y=Oe),this.scale=a,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:a,originY:i}=this,o=a.getPixels("x"),s=i.getPixels("y");t.translate(o,s),t.scale(e,r||e),t.translate(-o,-s)}unapply(t){var{scale:{x:e,y:r},originX:a,originY:i}=this,o=a.getPixels("x"),s=i.getPixels("y");t.translate(o,s),t.scale(1/e,1/r||e),t.translate(-o,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class dr{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=G(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:a}=this,i=e.getPixels("x"),o=r.getPixels("y");t.translate(i,o),t.transform(a[0],a[1],a[2],a[3],a[4],a[5]),t.translate(-i,-o)}unapply(t){var{originX:e,originY:r,matrix:a}=this,i=a[0],o=a[2],s=a[4],u=a[1],v=a[3],p=a[5],N=1/(i*(1*v-0*p)-o*(1*u-0*p)+s*(0*u-0*v)),V=e.getPixels("x"),L=r.getPixels("y");t.translate(V,L),t.transform(N*(1*v-0*p),N*(0*p-1*u),N*(0*s-1*o),N*(1*i-0*s),N*(o*p-s*v),N*(s*u-i*p)),t.translate(-V,-L)}applyToPoint(t){t.applyTransform(this.matrix)}}class pr extends dr{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new _(t,"angle",e)}}class zr extends pr{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class Gr extends pr{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}let Ye=(()=>{class l{constructor(e,r,a){this.document=e,this.transforms=[];var i=function Mn(l){return $t(l).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(r);i.forEach(o=>{if("none"!==o){var[s,u]=function wn(l){var[t,e]=l.split("(");return[t.trim(),e.trim().replace(")","")]}(o),v=l.transformTypes[s];typeof v<"u"&&this.transforms.push(new v(this.document,u,a))}})}static fromElement(e,r){var a=r.getStyle("transform",!1,!0),[i,o=i]=r.getStyle("transform-origin",!1,!0).split(),s=[i,o];return a.hasValue()?new l(e,a.getString(),s):null}apply(e){for(var{transforms:r}=this,a=r.length,i=0;i<a;i++)r[i].apply(e)}unapply(e){for(var{transforms:r}=this,i=r.length-1;i>=0;i--)r[i].unapply(e)}applyToPoint(e){for(var{transforms:r}=this,a=r.length,i=0;i<a;i++)r[i].applyToPoint(e)}}return l.transformTypes={translate:Fr,rotate:Ur,scale:jr,matrix:dr,skewX:zr,skewY:Gr},l})(),Nt=(()=>{class l{constructor(e,r){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=e,this.node=r,this.captureTextNodes=a,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],r&&1===r.nodeType){Array.from(r.attributes).forEach(u=>{var v=pt(u.nodeName);this.attributes[v]=new _(e,v,u.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map(u=>u.trim()).forEach(u=>{if(u){var[v,p]=u.split(":").map(E=>E.trim());this.styles[v]=new _(e,v,p)}});var{definitions:o}=e,s=this.getAttribute("id");s.hasValue()&&(o[s.getString()]||(o[s.getString()]=this)),Array.from(r.childNodes).forEach(u=>{if(1===u.nodeType)this.addChild(u);else if(a&&(3===u.nodeType||4===u.nodeType)){var v=e.createTextNode(u);v.getText().length>0&&this.addChild(v)}})}}getAttribute(e){var a=this.attributes[e];if(!a&&arguments.length>1&&void 0!==arguments[1]&&arguments[1]){var i=new _(this.document,e,"");return this.attributes[e]=i,i}return a||_.empty(this.document)}getHrefAttribute(){for(var e in this.attributes)if("href"===e||e.endsWith(":href"))return this.attributes[e];return _.empty(this.document)}getStyle(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[e];if(i)return i;var o=this.getAttribute(e);if(null!=o&&o.hasValue())return this.styles[e]=o,o;if(!a){var{parent:s}=this;if(s){var u=s.getStyle(e);if(null!=u&&u.hasValue())return u}}if(r){var v=new _(this.document,e,"");return this.styles[e]=v,v}return i||_.empty(this.document)}render(e){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(e.save(),this.getStyle("mask").hasValue()){var r=this.getStyle("mask").getDefinition();r&&(this.applyEffects(e),r.apply(e,this))}else if("none"!==this.getStyle("filter").getValue("none")){var a=this.getStyle("filter").getDefinition();a&&(this.applyEffects(e),a.apply(e,this))}else this.setContext(e),this.renderChildren(e),this.clearContext(e);e.restore()}}setContext(e){}applyEffects(e){var r=Ye.fromElement(this.document,this);r&&r.apply(e);var a=this.getStyle("clip-path",!1,!0);if(a.hasValue()){var i=a.getDefinition();i&&i.apply(e)}}clearContext(e){}renderChildren(e){this.children.forEach(r=>{r.render(e)})}addChild(e){var r=e instanceof l?e:this.document.createElement(e);r.parent=this,l.ignoreChildTypes.includes(r.type)||this.children.push(r)}matchesSelector(e){var r,{node:a}=this;if("function"==typeof a.matches)return a.matches(e);var i=null===(r=a.getAttribute)||void 0===r?void 0:r.call(a,"class");return!(!i||""===i)&&i.split(" ").some(o=>".".concat(o)===e)}addStylesFromStyleDefinition(){var{styles:e,stylesSpecificity:r}=this.document;for(var a in e)if(!a.startsWith("@")&&this.matchesSelector(a)){var i=e[a],o=r[a];if(i)for(var s in i){var u=this.stylesSpecificity[s];typeof u>"u"&&(u="000"),o>=u&&(this.styles[s]=i[s],this.stylesSpecificity[s]=o)}}}removeStyles(e,r){return r.reduce((i,o)=>{var s=e.getStyle(o);if(!s.hasValue())return i;var u=s.getString();return s.setValue(""),[...i,[o,u]]},[])}restoreStyles(e,r){r.forEach(a=>{var[i,o]=a;e.getStyle(i,!0).setValue(o)})}isFirstChild(){var e;return 0===(null===(e=this.parent)||void 0===e?void 0:e.children.indexOf(this))}}return l.ignoreChildTypes=["title"],l})();class $r extends Nt{constructor(t,e,r){super(t,e,r)}}function Vn(l){var t=l.trim();return/^('|")/.test(t)?t:'"'.concat(t,'"')}function Dn(l){return typeof process>"u"?l:l.trim().split(",").map(Vn).join(",")}function Ln(l){if(!l)return"";var t=l.trim().toLowerCase();switch(t){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return t;default:return/^oblique\s+(-|)\d+deg$/.test(t)?t:""}}function Bn(l){if(!l)return"";var t=l.trim().toLowerCase();switch(t){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return t;default:return/^[\d.]+$/.test(t)?t:""}}let Se=(()=>{class l{constructor(e,r,a,i,o,s){var u=s?"string"==typeof s?l.parse(s):s:{};this.fontFamily=o||u.fontFamily,this.fontSize=i||u.fontSize,this.fontStyle=e||u.fontStyle,this.fontWeight=a||u.fontWeight,this.fontVariant=r||u.fontVariant}static parse(){var r=arguments.length>1?arguments[1]:void 0,a="",i="",o="",s="",u="",v=$t(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),p={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return v.forEach(E=>{switch(!0){case!p.fontStyle&&l.styles.includes(E):"inherit"!==E&&(a=E),p.fontStyle=!0;break;case!p.fontVariant&&l.variants.includes(E):"inherit"!==E&&(i=E),p.fontStyle=!0,p.fontVariant=!0;break;case!p.fontWeight&&l.weights.includes(E):"inherit"!==E&&(o=E),p.fontStyle=!0,p.fontVariant=!0,p.fontWeight=!0;break;case!p.fontSize:"inherit"!==E&&([s]=E.split("/")),p.fontStyle=!0,p.fontVariant=!0,p.fontWeight=!0,p.fontSize=!0;break;default:"inherit"!==E&&(u+=E)}}),new l(a,i,o,s,u,r)}toString(){return[Ln(this.fontStyle),this.fontVariant,Bn(this.fontWeight),this.fontSize,Dn(this.fontFamily)].join(" ").trim()}}return l.styles="normal|italic|oblique|inherit",l.variants="normal|small-caps|inherit",l.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit",l})();class Qt{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=a,this.addPoint(t,e),this.addPoint(r,a)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){typeof t<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),typeof e<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:a,y2:i}=t;this.addPoint(e,r),this.addPoint(a,i)}}sumCubic(t,e,r,a,i){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*a+Math.pow(t,3)*i}bezierCurveAdd(t,e,r,a,i){var o=6*e-12*r+6*a,s=-3*e+9*r-9*a+3*i,u=3*r-3*e;if(0!==s){var p=Math.pow(o,2)-4*u*s;if(!(p<0)){var E=(-o+Math.sqrt(p))/(2*s);0<E&&E<1&&(t?this.addX(this.sumCubic(E,e,r,a,i)):this.addY(this.sumCubic(E,e,r,a,i)));var T=(-o-Math.sqrt(p))/(2*s);0<T&&T<1&&(t?this.addX(this.sumCubic(T,e,r,a,i)):this.addY(this.sumCubic(T,e,r,a,i)))}}else{if(0===o)return;var v=-u/o;0<v&&v<1&&(t?this.addX(this.sumCubic(v,e,r,a,i)):this.addY(this.sumCubic(v,e,r,a,i)))}}addBezierCurve(t,e,r,a,i,o,s,u){this.addPoint(t,e),this.addPoint(s,u),this.bezierCurveAdd(!0,t,r,i,s),this.bezierCurveAdd(!1,e,a,o,u)}addQuadraticCurve(t,e,r,a,i,o){var s=t+.6666666666666666*(r-t),u=e+2/3*(a-e);this.addBezierCurve(t,e,s,s+1/3*(i-t),u,u+1/3*(o-e),i,o)}isPointInBox(t,e){var{x1:r,y1:a,x2:i,y2:o}=this;return r<=t&&t<=i&&a<=e&&e<=o}}class nt extends D{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new Rt(0,0),this.control=new Rt(0,0),this.current=new Rt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var r=new Rt(this.command[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x"],this.command[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y"]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==D.CURVE_TO&&t!==D.SMOOTH_CURVE_TO&&t!==D.QUAD_TO&&t!==D.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:a,y:i}}=this;return new Rt(2*e-a,2*r-i)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:a,angles:i}=this;r&&i.length>0&&!i[i.length-1]&&(i[i.length-1]=a[a.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var a=r+1;a<e;a++)if(t[a]){t[r]=t[a];break}return t}}class ge extends Nt{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),a=this.getStyle("fill-opacity"),i=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,a);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var u=r.getColor();"inherit"!==u&&(t.fillStyle="none"===u?"rgba(0,0,0,0)":u)}if(a.hasValue()){var v=new _(this.document,"fill",t.fillStyle).addOpacity(a).getColor();t.fillStyle=v}if(i.isUrlDefinition()){var p=i.getFillStyleDefinition(this,o);p&&(t.strokeStyle=p)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var E=i.getString();"inherit"!==E&&(t.strokeStyle="none"===E?"rgba(0,0,0,0)":E)}if(o.hasValue()){var T=new _(this.document,"stroke",t.strokeStyle).addOpacity(o).getString();t.strokeStyle=T}var m=this.getStyle("stroke-width");if(m.hasValue()){var N=m.getPixels();t.lineWidth=N||Oe}var V=this.getStyle("stroke-linecap"),L=this.getStyle("stroke-linejoin"),U=this.getStyle("stroke-miterlimit"),Q=this.getStyle("stroke-dasharray"),z=this.getStyle("stroke-dashoffset");if(V.hasValue()&&(t.lineCap=V.getString()),L.hasValue()&&(t.lineJoin=L.getString()),U.hasValue()&&(t.miterLimit=U.getNumber()),Q.hasValue()&&"none"!==Q.getString()){var j=G(Q.getString());typeof t.setLineDash<"u"?t.setLineDash(j):typeof t.webkitLineDash<"u"?t.webkitLineDash=j:typeof t.mozDash<"u"&&(1!==j.length||0!==j[0])&&(t.mozDash=j);var q=z.getPixels();typeof t.lineDashOffset<"u"?t.lineDashOffset=q:typeof t.webkitLineDashOffset<"u"?t.webkitLineDashOffset=q:typeof t.mozDashOffset<"u"&&(t.mozDashOffset=q)}}if(this.modifiedEmSizeStack=!1,typeof t.font<"u"){var at=this.getStyle("font"),W=this.getStyle("font-style"),lt=this.getStyle("font-variant"),gt=this.getStyle("font-weight"),mt=this.getStyle("font-size"),Tt=this.getStyle("font-family"),St=new Se(W.getString(),lt.getString(),gt.getString(),mt.hasValue()?"".concat(mt.getPixels(!0),"px"):"",Tt.getString(),Se.parse(at.getString(),t.font));W.setValue(St.fontStyle),lt.setValue(St.fontVariant),gt.setValue(St.fontWeight),mt.setValue(St.fontSize),Tt.setValue(St.fontFamily),t.font=St.toString(),mt.isPixels()&&(this.document.emSize=mt.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class Et extends ge{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new nt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new Qt;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case nt.MOVE_TO:this.pathM(t,r);break;case nt.LINE_TO:this.pathL(t,r);break;case nt.HORIZ_LINE_TO:this.pathH(t,r);break;case nt.VERT_LINE_TO:this.pathV(t,r);break;case nt.CURVE_TO:this.pathC(t,r);break;case nt.SMOOTH_CURVE_TO:this.pathS(t,r);break;case nt.QUAD_TO:this.pathQ(t,r);break;case nt.SMOOTH_QUAD_TO:this.pathT(t,r);break;case nt.ARC:this.pathA(t,r);break;case nt.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((i,o)=>[i,r[o]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var a=r.length-1,i=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(i.isUrlDefinition()){var u=i.getDefinition(),[v,p]=r[0];u.render(t,v,p)}if(o.isUrlDefinition())for(var E=o.getDefinition(),T=1;T<a;T++){var[m,N]=r[T];E.render(t,m,N)}if(s.isUrlDefinition()){var V=s.getDefinition(),[L,U]=r[a];V.render(t,L,U)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:a}=Et.pathM(r),{x:i,y:o}=a;r.addMarker(a),e.addPoint(i,o),t&&t.moveTo(i,o)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:a,point:i}=Et.pathL(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathH(t){var{current:e,command:r}=t,a=new Rt((r.relative?e.x:0)+r.x,e.y);return t.current=a,{current:e,point:a}}pathH(t,e){var{pathParser:r}=this,{current:a,point:i}=Et.pathH(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathV(t){var{current:e,command:r}=t,a=new Rt(e.x,(r.relative?e.y:0)+r.y);return t.current=a,{current:e,point:a}}pathV(t,e){var{pathParser:r}=this,{current:a,point:i}=Et.pathV(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:a,point:i,controlPoint:o,currentPoint:s}=Et.pathC(r);r.addMarker(s,o,i),e.addBezierCurve(a.x,a.y,i.x,i.y,o.x,o.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,o.x,o.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:a,point:i,controlPoint:o,currentPoint:s}=Et.pathS(r);r.addMarker(s,o,i),e.addBezierCurve(a.x,a.y,i.x,i.y,o.x,o.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,o.x,o.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:a,controlPoint:i,currentPoint:o}=Et.pathQ(r);r.addMarker(o,i,i),e.addQuadraticCurve(a.x,a.y,i.x,i.y,o.x,o.y),t&&t.quadraticCurveTo(i.x,i.y,o.x,o.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:a,controlPoint:i,currentPoint:o}=Et.pathT(r);r.addMarker(o,i,i),e.addQuadraticCurve(a.x,a.y,i.x,i.y,o.x,o.y),t&&t.quadraticCurveTo(i.x,i.y,o.x,o.y)}static pathA(t){var{current:e,command:r}=t,{rX:a,rY:i,xRot:o,lArcFlag:s,sweepFlag:u}=r,v=o*(Math.PI/180),p=t.getAsCurrentPoint(),E=new Rt(Math.cos(v)*(e.x-p.x)/2+Math.sin(v)*(e.y-p.y)/2,-Math.sin(v)*(e.x-p.x)/2+Math.cos(v)*(e.y-p.y)/2),T=Math.pow(E.x,2)/Math.pow(a,2)+Math.pow(E.y,2)/Math.pow(i,2);T>1&&(a*=Math.sqrt(T),i*=Math.sqrt(T));var m=(s===u?-1:1)*Math.sqrt((Math.pow(a,2)*Math.pow(i,2)-Math.pow(a,2)*Math.pow(E.y,2)-Math.pow(i,2)*Math.pow(E.x,2))/(Math.pow(a,2)*Math.pow(E.y,2)+Math.pow(i,2)*Math.pow(E.x,2)));isNaN(m)&&(m=0);var N=new Rt(m*a*E.y/i,m*-i*E.x/a),V=new Rt((e.x+p.x)/2+Math.cos(v)*N.x-Math.sin(v)*N.y,(e.y+p.y)/2+Math.sin(v)*N.x+Math.cos(v)*N.y),L=sr([1,0],[(E.x-N.x)/a,(E.y-N.y)/i]),U=[(E.x-N.x)/a,(E.y-N.y)/i],Q=[(-E.x-N.x)/a,(-E.y-N.y)/i],z=sr(U,Q);return $e(U,Q)<=-1&&(z=Math.PI),$e(U,Q)>=1&&(z=0),{currentPoint:p,rX:a,rY:i,sweepFlag:u,xAxisRotation:v,centp:V,a1:L,ad:z}}pathA(t,e){var{pathParser:r}=this,{currentPoint:a,rX:i,rY:o,sweepFlag:s,xAxisRotation:u,centp:v,a1:p,ad:E}=Et.pathA(r),T=1-s?1:-1,m=p+T*(E/2),N=new Rt(v.x+i*Math.cos(m),v.y+o*Math.sin(m));if(r.addMarkerAngle(N,m-T*Math.PI/2),r.addMarkerAngle(a,m-T*Math.PI),e.addPoint(a.x,a.y),t&&!isNaN(p)&&!isNaN(E)){var V=i>o?i:o,L=i>o?1:i/o,U=i>o?o/i:1;t.translate(v.x,v.y),t.rotate(u),t.scale(L,U),t.arc(0,0,V,p,p+E,!!(1-s)),t.scale(1/L,1/U),t.rotate(-u),t.translate(-v.x,-v.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){Et.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class yr extends Et{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class ae extends ge{constructor(t,e,r){super(t,e,new.target===ae||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){super.setContext(t,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,a)=>{var i=this.getChildBoundingBox(t,this,this,a);e?e.addBoundingBox(i):e=i}),e}getFontSize(){var{document:t,parent:e}=this,r=Se.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new Qt(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var a=e[r],i=null;if(t.isArabic){var o=e.length,s=e[r-1],u=e[r+1],v="isolated";if((0===r||" "===s)&&r<o-1&&" "!==u&&(v="terminal"),r>0&&" "!==s&&r<o-1&&" "!==u&&(v="medial"),r>0&&" "!==s&&(r===o-1||" "===u)&&(v="initial"),typeof t.glyphs[a]<"u"){var p=t.glyphs[a];i=p instanceof yr?p:p[v]}}else i=t.glyphs[a];return i||(i=t.missingGlyph),i}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),a=r.indexOf(e),i=r.length-1,o=$t(e.textContent||"");return 0===a&&(o=ne(o)),a===i&&(o=Ie(o)),o}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((r,a)=>{this.renderChild(t,this,this,a)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,a=this.getText(),i=r.getStyle("font-family").getDefinition();if(i)for(var{unitsPerEm:o}=i.fontFace,s=Se.parse(e.ctx.font),u=r.getStyle("font-size").getNumber(s.fontSize),v=r.getStyle("font-style").getString(s.fontStyle),p=u/o,E=i.isRTL?a.split("").reverse().join(""):a,T=G(r.getAttribute("dx").getString()),m=E.length,N=0;N<m;N++){var V=this.getGlyph(i,E,N);t.translate(this.x,this.y),t.scale(p,-p);var L=t.lineWidth;t.lineWidth=t.lineWidth*o/u,"italic"===v&&t.transform(1,0,.4,1,0,0),V.render(t),"italic"===v&&t.transform(1,0,-.4,1,0,0),t.lineWidth=L,t.scale(1/p,-1/p),t.translate(-this.x,-this.y),this.x+=u*(V.horizAdvX||i.horizAdvX)/o,typeof T[N]<"u"&&!isNaN(T[N])&&(this.x+=T[N])}else{var{x:U,y:Q}=this;t.fillStyle&&t.fillText(a,U,Q),t.strokeStyle&&t.strokeText(a,U,Q)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var a,t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start");a="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=a;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,a){var i=r.children[a];i.children.length>0?i.children.forEach((o,s)=>{e.adjustChildCoordinatesRecursiveCore(t,e,i,s)}):this.adjustChildCoordinates(t,e,r,a)}adjustChildCoordinates(t,e,r,a){var i=r.children[a];if("function"!=typeof i.measureText)return i;t.save(),i.setContext(t,!0);var o=i.getAttribute("x"),s=i.getAttribute("y"),u=i.getAttribute("dx"),v=i.getAttribute("dy"),p=i.getStyle("font-family").getDefinition(),E=!!p&&p.isRTL;0===a&&(o.hasValue()||o.setValue(i.getInheritedAttribute("x")),s.hasValue()||s.setValue(i.getInheritedAttribute("y")),u.hasValue()||u.setValue(i.getInheritedAttribute("dx")),v.hasValue()||v.setValue(i.getInheritedAttribute("dy")));var T=i.measureText(t);return E&&(e.x-=T),o.hasValue()?(e.applyAnchoring(),i.x=o.getPixels("x"),u.hasValue()&&(i.x+=u.getPixels("x"))):(u.hasValue()&&(e.x+=u.getPixels("x")),i.x=e.x),e.x=i.x,E||(e.x+=T),s.hasValue()?(i.y=s.getPixels("y"),v.hasValue()&&(i.y+=v.getPixels("y"))):(v.hasValue()&&(e.y+=v.getPixels("y")),i.y=e.y),e.y=i.y,e.leafTexts.push(i),e.minX=Math.min(e.minX,i.x,i.x+T),e.maxX=Math.max(e.maxX,i.x,i.x+T),i.clearContext(t),t.restore(),i}getChildBoundingBox(t,e,r,a){var i=r.children[a];if("function"!=typeof i.getBoundingBox)return null;var o=i.getBoundingBox(t);return o?(i.children.forEach((s,u)=>{var v=e.getChildBoundingBox(t,e,i,u);o.addBoundingBox(v)}),o):null}renderChild(t,e,r,a){var i=r.children[a];i.render(t),i.children.forEach((o,s)=>{e.renderChild(t,e,i,s)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),a=this.measureTargetText(t,r);return this.measureCache=a,a}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,a=r.getStyle("font-family").getDefinition();if(a){for(var i=this.getFontSize(),o=a.isRTL?e.split("").reverse().join(""):e,s=G(r.getAttribute("dx").getString()),u=o.length,v=0,p=0;p<u;p++)v+=(this.getGlyph(a,o,p).horizAdvX||a.horizAdvX)*i/a.fontFace.unitsPerEm,typeof s[p]<"u"&&!isNaN(s[p])&&(v+=s[p]);return v}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:T}=t.measureText(e);return this.clearContext(t),t.restore(),T}getInheritedAttribute(t){for(var e=this;e instanceof ae&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Fe extends ae{constructor(t,e,r){super(t,e,new.target===Fe||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Fn extends Fe{constructor(){super(...arguments),this.type="textNode"}}class we extends ge{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:a,window:i}=r,o=t.canvas;if(a.setDefaults(t),o.style&&typeof t.font<"u"&&i&&typeof i.getComputedStyle<"u"){t.font=i.getComputedStyle(o).getPropertyValue("font");var s=new _(r,"fontSize",Se.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:v}=a.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var p=this.getAttribute("refX"),E=this.getAttribute("refY"),T=this.getAttribute("viewBox"),m=T.hasValue()?G(T.getString()):null,N=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),V=0,L=0,U=0,Q=0;m&&(V=m[0],L=m[1]),this.root||(u=this.getStyle("width").getPixels("x"),v=this.getStyle("height").getPixels("y"),"marker"===this.type&&(U=V,Q=L,V=0,L=0)),a.viewPort.setCurrent(u,v),this.node&&(!this.parent||"foreignObject"===(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName))&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),m&&(u=m[2],v=m[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a.viewPort.width,desiredWidth:u,height:a.viewPort.height,desiredHeight:v,minX:V,minY:L,refX:p.getValue(),refY:E.getValue(),clip:N,clipX:U,clipY:Q}),m&&(a.viewPort.removeCurrent(),a.viewPort.setCurrent(u,v))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this.getAttribute("width",!0),i=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),s=this.getAttribute("style"),u=a.getNumber(0),v=i.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var p=this.getAttribute("preserveAspectRatio");p.hasValue()&&p.setValue(p.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(a.setValue(t),i.setValue(e),o.hasValue()||o.setValue("0 0 ".concat(u||t," ").concat(v||e)),s.hasValue()){var E=this.getStyle("width"),T=this.getStyle("height");E.hasValue()&&E.setValue("".concat(t,"px")),T.hasValue()&&T.setValue("".concat(e,"px"))}}}class mr extends Et{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),a=this.getStyle("width",!1,!0).getPixels("x"),i=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),s=this.getAttribute("ry"),u=o.getPixels("x"),v=s.getPixels("y");if(o.hasValue()&&!s.hasValue()&&(v=u),s.hasValue()&&!o.hasValue()&&(u=v),u=Math.min(u,a/2),v=Math.min(v,i/2),t){var p=(Math.sqrt(2)-1)/3*4;t.beginPath(),i>0&&a>0&&(t.moveTo(e+u,r),t.lineTo(e+a-u,r),t.bezierCurveTo(e+a-u+p*u,r,e+a,r+v-p*v,e+a,r+v),t.lineTo(e+a,r+i-v),t.bezierCurveTo(e+a,r+i-v+p*v,e+a-u+p*u,r+i,e+a-u,r+i),t.lineTo(e+u,r+i),t.bezierCurveTo(e+u-p*u,r+i,e,r+i-v+p*v,e,r+i-v),t.lineTo(e,r+v),t.bezierCurveTo(e,r+v-p*v,e+u-p*u,r,e+u,r),t.closePath())}return new Qt(e,r,e+a,r+i)}getMarkers(){return null}}class Hr extends Et{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),a=this.getAttribute("r").getPixels();return t&&a>0&&(t.beginPath(),t.arc(e,r,a,0,2*Math.PI,!1),t.closePath()),new Qt(e-a,r-a,e+a,r+a)}getMarkers(){return null}}class Yr extends Et{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),a=this.getAttribute("ry").getPixels("y"),i=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return t&&r>0&&a>0&&(t.beginPath(),t.moveTo(i+r,o),t.bezierCurveTo(i+r,o+e*a,i+e*r,o+a,i,o+a),t.bezierCurveTo(i-e*r,o+a,i-r,o+e*a,i-r,o),t.bezierCurveTo(i-r,o-e*a,i-e*r,o-a,i,o-a),t.bezierCurveTo(i+e*r,o-a,i+r,o-e*a,i+r,o),t.closePath()),new Qt(i-r,o-a,i+r,o+a)}getMarkers(){return null}}class Xr extends Et{constructor(){super(...arguments),this.type="line"}getPoints(){return[new Rt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new Rt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:a,y:i}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(a,i)),new Qt(e,r,a,i)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class xr extends Et{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=Rt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:a}]=e,i=new Qt(r,a);return t&&(t.beginPath(),t.moveTo(r,a)),e.forEach(o=>{var{x:s,y:u}=o;i.addPoint(s,u),t&&t.lineTo(s,u)}),i}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((a,i)=>{i!==e&&r.push([a,a.angleTo(t[i+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Wr extends xr{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:a}]=this.points;return t&&(t.lineTo(r,a),t.closePath()),e}}class Qr extends Nt{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var a=this.getStyle("width").getPixels("x",!0),i=this.getStyle("height").getPixels("y",!0),o=new we(this.document,null);o.attributes.viewBox=new _(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new _(this.document,"width","".concat(a,"px")),o.attributes.height=new _(this.document,"height","".concat(i,"px")),o.attributes.transform=new _(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var s=this.document.createCanvas(a,i),u=s.getContext("2d"),v=this.getAttribute("x"),p=this.getAttribute("y");v.hasValue()&&p.hasValue()&&u.translate(v.getPixels("x",!0),p.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var E=-1;E<=1;E++)for(var T=-1;T<=1;T++)u.save(),o.attributes.x=new _(this.document,"x",E*s.width),o.attributes.y=new _(this.document,"y",T*s.height),o.render(u),u.restore();return t.createPattern(s,"repeat")}}class Kr extends Nt{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:a,y:i}=e,o=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(a,i),"auto"===o&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var u=new we(this.document,null);u.type=this.type,u.attributes.viewBox=new _(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new _(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new _(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new _(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new _(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new _(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new _(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new _(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===o&&t.rotate(-r),t.translate(-a,-i)}}}class kr extends Nt{constructor(){super(...arguments),this.type="defs"}render(){}}class Xe extends ge{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new Qt;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Er extends Nt{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:a,children:i}=this;i.forEach(o=>{"stop"===o.type&&a.push(o)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var a=this;this.getHrefAttribute().hasValue()&&(a=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(a));var{stops:i}=a,o=this.getGradient(t,e);if(!o)return this.addParentOpacity(r,i[i.length-1].color);if(i.forEach(L=>{o.addColorStop(L.offset,this.addParentOpacity(r,L.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:v}=s.screen,[p]=v.viewPorts,E=new mr(s,null);E.attributes.x=new _(s,"x",-u/3),E.attributes.y=new _(s,"y",-u/3),E.attributes.width=new _(s,"width",u),E.attributes.height=new _(s,"height",u);var T=new Xe(s,null);T.attributes.transform=new _(s,"transform",this.getAttribute("gradientTransform").getValue()),T.children=[E];var m=new we(s,null);m.attributes.x=new _(s,"x",0),m.attributes.y=new _(s,"y",0),m.attributes.width=new _(s,"width",p.width),m.attributes.height=new _(s,"height",p.height),m.children=[T];var N=s.createCanvas(p.width,p.height),V=N.getContext("2d");return V.fillStyle=o,m.render(V),V.createPattern(N,"no-repeat")}return o}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new _(this.document,"color",e).addOpacity(t).getColor():e}}class Zr extends Er{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),a=r?e.getBoundingBox(t):null;if(r&&!a)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var i=r?a.x+a.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=r?a.y+a.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?a.x+a.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=r?a.y+a.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return i===s&&o===u?null:t.createLinearGradient(i,o,s,u)}}class Jr extends Er{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),a=e.getBoundingBox(t);if(r&&!a)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var i=r?a.x+a.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=r?a.y+a.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=i,u=o;this.getAttribute("fx").hasValue()&&(s=r?a.x+a.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=r?a.y+a.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var v=r?(a.width+a.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),p=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,u,p,i,o,v)}}class qr extends Nt{constructor(t,e,r){super(t,e,r),this.type="stop";var a=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),i=this.getStyle("stop-opacity"),o=this.getStyle("stop-color",!0);""===o.getString()&&o.setValue("#000"),i.hasValue()&&(o=o.addOpacity(i)),this.offset=a,this.color=o.getColor()}}class We extends Nt{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new _(t,"values",null);var a=this.getAttribute("values");a.hasValue()&&this.values.setValue(a.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:a}=this.getProgress(),i=r.getNumber()+(a.getNumber()-r.getNumber())*e;return"%"===t&&(i*=100),"".concat(i).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var a=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==a||this.frozen){if("remove"===a&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var i=!1;if(this.begin<this.duration){var o=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var u=s.getString();o="".concat(u,"(").concat(o,")")}r.setValue(o),i=!0}return i}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var a=r.progress*(e.getValue().length-1),i=Math.floor(a),o=Math.ceil(a);r.from=new _(t,"from",parseFloat(e.getValue()[i])),r.to=new _(t,"to",parseFloat(e.getValue()[o])),r.progress=(a-i)/(o-i)}else r.from=this.from,r.to=this.to;return r}}class _r extends We{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),a=new H(e.getColor()),i=new H(r.getColor());if(a.ok&&i.ok){var s=a.g+(i.g-a.g)*t,u=a.b+(i.b-a.b)*t;return"rgb(".concat(Math.floor(a.r+(i.r-a.r)*t),", ").concat(Math.floor(s),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class tn extends We{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),a=G(e.getString()),i=G(r.getString());return a.map((s,u)=>s+(i[u]-s)*t).join(" ")}}class en extends Nt{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:a}=t,{children:i}=this;for(var o of i)switch(o.type){case"font-face":this.fontFace=o;var s=o.getStyle("font-family");s.hasValue()&&(a[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=o;break;case"glyph":var u=o;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]={}),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u}}render(){}}class rn extends Nt{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class nn extends Et{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class an extends ae{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class sn extends ae{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:a}=e,i=a[0],o=a.length>0&&Array.from(a).every(s=>3===s.nodeType);this.hasText=o,this.text=o?this.getTextFromNode(i):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:a}=this,{mouse:i}=e.screen,o=new _(e,"fontSize",Se.parse(e.ctx.font).fontSize);i.isWorking()&&i.checkBoundingBox(this,new Qt(r,a-o.getPixels("y"),r+this.measureText(t),a))}else if(this.children.length>0){var s=new Xe(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function on(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function Qe(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?on(Object(e),!0).forEach(function(r){R(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):on(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}class un extends ae{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var a=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(a)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(r=>{var{type:a,points:i}=r;switch(a){case nt.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case nt.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case nt.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case nt.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case nt.ARC:var[o,s,u,v,p,E,T,m]=i,N=u>v?u:v,V=u>v?1:u/v,L=u>v?v/u:1;t&&(t.translate(o,s),t.rotate(T),t.scale(V,L),t.arc(0,0,N,p,p+E,!!(1-m)),t.scale(1/V,1/L),t.rotate(-T),t.translate(-o,-s));break;case nt.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:a}=this,i=t.fillStyle;"underline"===e&&t.beginPath(),a.forEach((o,s)=>{var{p0:u,p1:v,rotation:p,text:E}=o;t.save(),t.translate(u.x,u.y),t.rotate(p),t.fillStyle&&t.fillText(E,0,0),t.strokeStyle&&t.strokeText(E,0,0),t.restore(),"underline"===e&&(0===s&&t.moveTo(u.x,u.y+r/8),t.lineTo(v.x,v.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=i,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){return this.letterSpacingCache[arguments.length>0&&void 0!==arguments[0]?arguments[0]:0]||0}findSegmentToFitChar(t,e,r,a,i,o,s,u,v){var p=o,E=this.measureText(t,u);" "===u&&"justify"===e&&r<a&&(E+=(a-r)/i),v>-1&&(p+=this.getLetterSpacingAt(v));var T=this.textHeight/20,m=this.getEquidistantPointOnPath(p,T,0),N=this.getEquidistantPointOnPath(p+E,T,0),V={p0:m,p1:N},L=m&&N?Math.atan2(N.y-m.y,N.x-m.x):0;if(s){var U=Math.cos(Math.PI/2+L)*s,Q=Math.cos(-L)*s;V.p0=Qe(Qe({},m),{},{x:m.x+U,y:m.y+Q}),V.p1=Qe(Qe({},N),{},{x:N.x+U,y:N.y+Q})}return{offset:p+=E,segment:V,rotation:L}}measureText(t,e){var{measuresCache:r}=this,a=e||this.getText();if(r.has(a))return r.get(a);var i=this.measureTargetText(t,a);return r.set(a,i),i}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),a=e.split(" ").length-1,i=this.parent.getAttribute("dx").split().map(j=>j.getPixels("x")),o=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),v=this.parent.getStyle("letter-spacing"),p=0;u.hasValue()&&"inherit"!==u.getValue()?u.hasValue()&&"initial"!==u.getValue()&&"unset"!==u.getValue()&&(p=u.getPixels()):p=v.getPixels();var E=[],T=e.length;this.letterSpacingCache=E;for(var m=0;m<T;m++)E.push(typeof i[m]<"u"?i[m]:p);var N=E.reduce((j,q,at)=>0===at?0:j+q||0,0),V=this.measureText(t),L=Math.max(V+N,0);this.textWidth=V,this.textHeight=this.getFontSize(),this.glyphInfo=[];var U=this.getPathLength(),Q=this.getStyle("startOffset").getNumber(0)*U,z=0;("middle"===s||"center"===s)&&(z=-L/2),("end"===s||"right"===s)&&(z=-L),z+=Q,r.forEach((j,q)=>{var{offset:at,segment:W,rotation:lt}=this.findSegmentToFitChar(t,s,L,U,a,z,o,j,q);z=at,W.p0&&W.p1&&this.glyphInfo.push({text:r[q],p0:W.p0,p1:W.p1,rotation:lt})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:a}=r,i=a?a.x:0,o=a?a.y:0,s=r.next(),u=s.type,v=[];switch(s.type){case nt.MOVE_TO:this.pathM(r,v);break;case nt.LINE_TO:u=this.pathL(r,v);break;case nt.HORIZ_LINE_TO:u=this.pathH(r,v);break;case nt.VERT_LINE_TO:u=this.pathV(r,v);break;case nt.CURVE_TO:this.pathC(r,v);break;case nt.SMOOTH_CURVE_TO:u=this.pathS(r,v);break;case nt.QUAD_TO:this.pathQ(r,v);break;case nt.SMOOTH_QUAD_TO:u=this.pathT(r,v);break;case nt.ARC:v=this.pathA(r);break;case nt.CLOSE_PATH:Et.pathZ(r)}e.push(s.type!==nt.CLOSE_PATH?{type:u,points:v,start:{x:i,y:o},pathLength:this.calcLength(i,o,u,v)}:{type:nt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:a}=Et.pathM(t).point;e.push(r,a)}pathL(t,e){var{x:r,y:a}=Et.pathL(t).point;return e.push(r,a),nt.LINE_TO}pathH(t,e){var{x:r,y:a}=Et.pathH(t).point;return e.push(r,a),nt.LINE_TO}pathV(t,e){var{x:r,y:a}=Et.pathV(t).point;return e.push(r,a),nt.LINE_TO}pathC(t,e){var{point:r,controlPoint:a,currentPoint:i}=Et.pathC(t);e.push(r.x,r.y,a.x,a.y,i.x,i.y)}pathS(t,e){var{point:r,controlPoint:a,currentPoint:i}=Et.pathS(t);return e.push(r.x,r.y,a.x,a.y,i.x,i.y),nt.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:a}=Et.pathQ(t);e.push(r.x,r.y,a.x,a.y)}pathT(t,e){var{controlPoint:r,currentPoint:a}=Et.pathT(t);return e.push(r.x,r.y,a.x,a.y),nt.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:a,xAxisRotation:i,centp:o,a1:s,ad:u}=Et.pathA(t);return 0===a&&u>0&&(u-=2*Math.PI),1===a&&u<0&&(u+=2*Math.PI),[o.x,o.y,e,r,s,u,i,a]}calcLength(t,e,r,a){var i=0,o=null,s=null,u=0;switch(r){case nt.LINE_TO:return this.getLineLength(t,e,a[0],a[1]);case nt.CURVE_TO:for(i=0,o=this.getPointOnCubicBezier(0,t,e,a[0],a[1],a[2],a[3],a[4],a[5]),u=.01;u<=1;u+=.01)s=this.getPointOnCubicBezier(u,t,e,a[0],a[1],a[2],a[3],a[4],a[5]),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return i;case nt.QUAD_TO:for(i=0,o=this.getPointOnQuadraticBezier(0,t,e,a[0],a[1],a[2],a[3]),u=.01;u<=1;u+=.01)s=this.getPointOnQuadraticBezier(u,t,e,a[0],a[1],a[2],a[3]),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return i;case nt.ARC:i=0;var v=a[4],p=a[5],E=a[4]+p,T=Math.PI/180;if(Math.abs(v-E)<T&&(T=Math.abs(v-E)),o=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],v,0),p<0)for(u=v-T;u>E;u-=T)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;else for(u=v+T;u<E;u+=T)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],E,0),i+this.getLineLength(o.x,o.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,a,i){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,u=(i-r)/(a-e+Oe),v=Math.sqrt(t*t/(1+u*u));a<e&&(v*=-1);var p=u*v,E=null;if(a===e)E={x:o,y:s+p};else if((s-r)/(o-e+Oe)===u)E={x:o+v,y:s+p};else{var T,m,N=this.getLineLength(e,r,a,i);if(N<Oe)return null;var V=(o-e)*(a-e)+(s-r)*(i-r),L=this.getLineLength(o,s,T=e+(V/=N*N)*(a-e),m=r+V*(i-r)),U=Math.sqrt(t*t-L*L);v=Math.sqrt(U*U/(1+u*u)),a<e&&(v*=-1),E={x:T+v,y:m+(p=u*v)}}return E}getPointOnPath(t){var e=this.getPathLength(),r=0,a=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:i}=this;for(var o of i){if(!o||!(o.pathLength<5e-5||r+o.pathLength+5e-5<t)){var s=t-r,u=0;switch(o.type){case nt.LINE_TO:a=this.getPointOnLine(s,o.start.x,o.start.y,o.points[0],o.points[1],o.start.x,o.start.y);break;case nt.ARC:var p=o.points[5],E=o.points[4]+p;if(u=o.points[4]+s/o.pathLength*p,p<0&&u<E||p>=0&&u>E)break;a=this.getPointOnEllipticalArc(o.points[0],o.points[1],o.points[2],o.points[3],u,o.points[6]);break;case nt.CURVE_TO:(u=s/o.pathLength)>1&&(u=1),a=this.getPointOnCubicBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3],o.points[4],o.points[5]);break;case nt.QUAD_TO:(u=s/o.pathLength)>1&&(u=1),a=this.getPointOnQuadraticBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3])}if(a)return a;break}r+=o.pathLength}return null}getLineLength(t,e,r,a){return Math.sqrt((r-t)*(r-t)+(a-e)*(a-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,a,i,o,s,u,v){return{x:u*or(t)+o*ur(t)+a*lr(t)+e*hr(t),y:v*or(t)+s*ur(t)+i*lr(t)+r*hr(t)}}getPointOnQuadraticBezier(t,e,r,a,i,o,s){return{x:o*fr(t)+a*vr(t)+e*cr(t),y:s*fr(t)+i*vr(t)+r*cr(t)}}getPointOnEllipticalArc(t,e,r,a,i,o){var s=Math.cos(o),u=Math.sin(o),v_x=r*Math.cos(i),v_y=a*Math.sin(i);return{x:t+(v_x*s-v_y*u),y:e+(v_x*u+v_y*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),a=e||.25,i=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==i||this.equidistantCache.precision!==a){this.equidistantCache={step:i,precision:a,points:[]};for(var o=0,s=0;s<=r;s+=a){var u=this.getPointOnPath(s),v=this.getPointOnPath(s+a);!u||!v||(o+=this.getLineLength(u.x,u.y,v.x,v.y))>=i&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:s}),o-=i)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var a=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[a]||null}}var Un=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class ln extends ge{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var a=this.getHrefAttribute().getString();if(a){var i=a.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(a);t.images.push(this),i?this.loadSvg(a):this.loadImage(a),this.isSvg=i}}loadImage(t){var e=this;return(0,f.A)(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(a){console.error('Error while loading image "'.concat(t,'":'),a)}e.loaded=!0})()}loadSvg(t){var e=this;return(0,f.A)(function*(){var r=Un.exec(t);if(r){var a=r[5];e.image="base64"===r[4]?atob(a):decodeURIComponent(a)}else try{var i=yield e.document.fetch(t),o=yield i.text();e.image=o}catch(s){console.error('Error while loading image "'.concat(t,'":'),s)}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:a}=this,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(a&&r&&s&&u){if(t.save(),t.translate(i,o),this.isSvg){var v=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:u});v.document.documentElement.parent=this,v.render()}else{var p=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:p.width,height:u,desiredHeight:p.height}),this.loaded&&(typeof p.complete>"u"||p.complete)&&t.drawImage(p,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");return new Qt(t,e,t+r,e+a)}}class hn extends ge{constructor(){super(...arguments),this.type="symbol"}render(t){}}class fn{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return(0,f.A)(function*(){try{var{document:a}=r,o=(yield a.canvg.parser.load(e)).getElementsByTagName("font");Array.from(o).forEach(s=>{var u=a.createElement(s);a.definitions[t]=u})}catch(s){console.error('Error while loading font "'.concat(e,'":'),s)}r.loaded=!0})()}}let vn=(()=>{class l extends Nt{constructor(e,r,a){super(e,r,a),this.type="style",$t(Array.from(r.childNodes).map(s=>s.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(s=>{var u=s.trim();if(u){var v=u.split("{"),p=v[0].split(","),E=v[1].split(";");p.forEach(T=>{var m=T.trim();if(m){var N=e.styles[m]||{};if(E.forEach(U=>{var Q=U.indexOf(":"),z=U.substr(0,Q).trim(),j=U.substr(Q+1,U.length-Q).trim();z&&j&&(N[z]=new _(e,z,j))}),e.styles[m]=N,e.stylesSpecificity[m]=wr(m),"@font-face"===m){var V=N["font-family"].getString().replace(/"|'/g,"");N.src.getString().split(",").forEach(U=>{if(U.indexOf('format("svg")')>0){var Q=dt(U);Q&&new fn(e).load(V,Q)}})}}})}})}}return l.parseExternalUrl=dt,l})();class cn extends ge{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var a=r;if("symbol"===r.type&&((a=new we(e,null)).attributes.viewBox=new _(e,"viewBox",r.getAttribute("viewBox").getString()),a.attributes.preserveAspectRatio=new _(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),a.attributes.overflow=new _(e,"overflow",r.getAttribute("overflow").getString()),a.children=r.children,r.styles.opacity=new _(e,"opacity",this.calculateOpacity())),"svg"===a.type){var i=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);i.hasValue()&&(a.attributes.width=new _(e,"width",i.getString())),o.hasValue()&&(a.attributes.height=new _(e,"height",o.getString()))}var s=a.parent;a.parent=this,a.render(t),a.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return Ye.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Ke(l,t,e,r,a,i){return l[e*r*4+4*t+i]}function ke(l,t,e,r,a,i,o){l[e*r*4+4*t+i]=o}function Vt(l,t,e){return l[t]*e}function ie(l,t,e,r){return t+Math.cos(l)*e+Math.sin(l)*r}class Tr extends Nt{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var a=G(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var i=a[0];a=[.213+.787*i,.715-.715*i,.072-.072*i,0,0,.213-.213*i,.715+.285*i,.072-.072*i,0,0,.213-.213*i,.715-.715*i,.072+.928*i,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var o=a[0]*Math.PI/180;a=[ie(o,.213,.787,-.213),ie(o,.715,-.715,-.715),ie(o,.072,-.072,.928),0,0,ie(o,.213,-.213,.143),ie(o,.715,.285,.14),ie(o,.072,-.072,-.283),0,0,ie(o,.213,-.213,-.787),ie(o,.715,-.715,.715),ie(o,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=a,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,a,i){for(var{includeOpacity:o,matrix:s}=this,u=t.getImageData(0,0,a,i),v=0;v<i;v++)for(var p=0;p<a;p++){var E=Ke(u.data,p,v,a,0,0),T=Ke(u.data,p,v,a,0,1),m=Ke(u.data,p,v,a,0,2),N=Ke(u.data,p,v,a,0,3),V=Vt(s,0,E)+Vt(s,1,T)+Vt(s,2,m)+Vt(s,3,N)+Vt(s,4,1),L=Vt(s,5,E)+Vt(s,6,T)+Vt(s,7,m)+Vt(s,8,N)+Vt(s,9,1),U=Vt(s,10,E)+Vt(s,11,T)+Vt(s,12,m)+Vt(s,13,N)+Vt(s,14,1),Q=Vt(s,15,E)+Vt(s,16,T)+Vt(s,17,m)+Vt(s,18,N)+Vt(s,19,1);o&&(V=0,L=0,U=0,Q*=N/255),ke(u.data,p,v,a,0,0,V),ke(u.data,p,v,a,0,1,L),ke(u.data,p,v,a,0,2,U),ke(u.data,p,v,a,0,3,Q)}t.clearRect(0,0,a,i),t.putImageData(u,0,0)}}let gn=(()=>{class l extends Nt{constructor(){super(...arguments),this.type="mask"}apply(e,r){var{document:a}=this,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!s&&!u){var v=new Qt;this.children.forEach(V=>{v.addBoundingBox(V.getBoundingBox(e))}),i=Math.floor(v.x1),o=Math.floor(v.y1),s=Math.floor(v.width),u=Math.floor(v.height)}var p=this.removeStyles(r,l.ignoreStyles),E=a.createCanvas(i+s,o+u),T=E.getContext("2d");a.screen.setDefaults(T),this.renderChildren(T),new Tr(a,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(T,0,0,i+s,o+u);var m=a.createCanvas(i+s,o+u),N=m.getContext("2d");a.screen.setDefaults(N),r.render(N),N.globalCompositeOperation="destination-in",N.fillStyle=T.createPattern(E,"no-repeat"),N.fillRect(0,0,i+s,o+u),e.fillStyle=N.createPattern(m,"no-repeat"),e.fillRect(0,0,i+s,o+u),this.restoreStyles(r,p)}render(e){}}return l.ignoreStyles=["mask","transform","clip-path"],l})();var dn=()=>{};class pn extends Nt{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:a,closePath:i}=t;r&&(r.beginPath=dn,r.closePath=dn),Reflect.apply(a,t,[]),this.children.forEach(o=>{if(!(typeof o.path>"u")){var s=typeof o.elementTransform<"u"?o.elementTransform():null;s||(s=Ye.fromElement(e,o)),s&&s.apply(t),o.path(t),r&&(r.closePath=i),s&&s.unapply(t)}}),Reflect.apply(i,t,[]),t.clip(),r&&(r.beginPath=a,r.closePath=i)}render(t){}}let yn=(()=>{class l extends Nt{constructor(){super(...arguments),this.type="filter"}apply(e,r){var{document:a,children:i}=this,o=r.getBoundingBox(e);if(o){var s=0,u=0;i.forEach(Q=>{var z=Q.extraFilterDistance||0;s=Math.max(s,z),u=Math.max(u,z)});var v=Math.floor(o.width),p=Math.floor(o.height),E=v+2*s,T=p+2*u;if(!(E<1||T<1)){var m=Math.floor(o.x),N=Math.floor(o.y),V=this.removeStyles(r,l.ignoreStyles),L=a.createCanvas(E,T),U=L.getContext("2d");a.screen.setDefaults(U),U.translate(-m+s,-N+u),r.render(U),i.forEach(Q=>{"function"==typeof Q.apply&&Q.apply(U,0,0,E,T)}),e.drawImage(L,0,0,E,T,m-s,N-u,E,T),this.restoreStyles(r,V)}}}render(e){}}return l.ignoreStyles=["filter","transform","clip-path"],l})();class mn extends Nt{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,a,i){}}class xn extends Nt{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,a,i){}}class En extends Nt{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,a,i){}}class Tn extends Nt{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,a,i){var{document:o,blurRadius:s}=this,u=o.window?o.window.document.body:null,v=t.canvas;v.id=o.getUniqueId(),u&&(v.style.display="none",u.appendChild(v)),re(v,e,r,a,i,s),u&&u.removeChild(v)}}class On extends Nt{constructor(){super(...arguments),this.type="title"}}class Sn extends Nt{constructor(){super(...arguments),this.type="desc"}}var jn={svg:we,rect:mr,circle:Hr,ellipse:Yr,line:Xr,polyline:xr,polygon:Wr,path:Et,pattern:Qr,marker:Kr,defs:kr,linearGradient:Zr,radialGradient:Jr,stop:qr,animate:We,animateColor:_r,animateTransform:tn,font:en,"font-face":rn,"missing-glyph":nn,glyph:yr,text:ae,tspan:Fe,tref:an,a:sn,textPath:un,image:ln,g:Xe,symbol:hn,style:vn,use:cn,mask:gn,clipPath:pn,filter:yn,feDropShadow:mn,feMorphology:xn,feComposite:En,feColorMatrix:Tr,feGaussianBlur:Tn,title:On,desc:Sn};function bn(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function Gn(l,t){var e=document.createElement("canvas");return e.width=l,e.height=t,e}function $n(l){return Or.apply(this,arguments)}function Or(){return Or=(0,f.A)(function*(l){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e=document.createElement("img");return t&&(e.crossOrigin="Anonymous"),new Promise((r,a)=>{e.onload=()=>{r(e)},e.onerror=(i,o,s,u,v)=>{a(v)},e.src=l})}),Or.apply(this,arguments)}let Cn=(()=>{class l{constructor(e){var{rootEmSize:r=12,emSize:a=12,createCanvas:i=l.createCanvas,createImage:o=l.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=e,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=r,this.emSize=a,this.createCanvas=i,this.createImage=this.bindCreateImage(o,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(e,r){return"boolean"==typeof r?(a,i)=>e(a,"boolean"==typeof i?i:r):e}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:e}=this;return e[e.length-1]}set emSize(e){var{emSizeStack:r}=this;r.push(e)}popEmSize(){var{emSizeStack:e}=this;e.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(e=>e.loaded)}isFontsLoaded(){return this.fonts.every(e=>e.loaded)}createDocumentElement(e){var r=this.createElement(e.documentElement);return r.root=!0,r.addStylesFromStyleDefinition(),this.documentElement=r,r}createElement(e){var r=e.nodeName.replace(/^[^:]+:/,""),a=l.elementTypes[r];return typeof a<"u"?new a(this,e):new $r(this,e)}createTextNode(e){return new Fn(this,e)}setViewBox(e){this.screen.setViewBox(function zn(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?bn(Object(e),!0).forEach(function(r){R(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):bn(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}({document:this},e))}}return l.createCanvas=Gn,l.createImage=$n,l.elementTypes=jn,l})();function An(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function be(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?An(Object(e),!0).forEach(function(r){R(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):An(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}class Ce{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new He(r),this.screen=new gr(t,r),this.options=r;var a=new Cn(this,r),i=a.createDocumentElement(e);this.document=a,this.documentElement=i}static from(t,e){var r=arguments;return(0,f.A)(function*(){var a=r.length>2&&void 0!==r[2]?r[2]:{},i=new He(a),o=yield i.parse(e);return new Ce(t,o,a)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new He(r).parseFromString(e);return new Ce(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ce.from(t,e,be(be({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ce.fromString(t,e,be(be({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return(0,f.A)(function*(){e.start(be({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},t.length>0&&void 0!==t[0]?t[0]:{})),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:a}=this;r.start(e,be(be({enableRedraw:!0},a),t))}stop(){this.screen.stop()}resize(t){this.documentElement.resize(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,arguments.length>2&&void 0!==arguments[2]&&arguments[2])}}}}]);